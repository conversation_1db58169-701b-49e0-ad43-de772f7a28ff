/*
* 
* ==========================================================
* ADMIN.SCSS
* ==========================================================
*
* Main style file of the administration page. Written in SCSS. 
*
*/

@import "shared.scss";

@font-face {
    font-family: "Masi Chat Font";
    src: url("../media/fonts/bold.woff2") format("woff2"), url("../media/fonts/bold.woff") format("woff");
    font-weight: 600;
    font-style: normal;
}

@keyframes mc-fade-out {
    0% {
        opacity: 1;
    }

    90% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

@keyframes mc-typing {
    0% {
        width: 0;
    }

    100% {
        width: 15px;
    }
}

@keyframes mc-fade-in-out {
    0% {
        opacity: 0;
    }

    20% {
        opacity: 1;
    }

    80% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}

.mc-fade-out {
    opacity: 0;
    animation: mc-fade-out .5s;
}

/* 
 *
 * -----------------------------------------------------------
 * GLOBAL
 * -----------------------------------------------------------
 *
*/

body, html {
    margin: 0;
    padding: 0;
    min-height: 100%;
    background: rgb(245, 247, 250);
}

.mc-admin, .mc-admin input, .mc-admin textarea, .mc-admin select, .mc-title, .daterangepicker, .ct__content {
    font-family: "Masi Chat Font", "Helvetica Neue", "Apple Color Emoji", Helvetica, Arial, sans-serif;
    color: $color-black;
}

.mc-admin, .mc-admin * {
    box-sizing: content-box;
}

.mc-admin ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

.mc-admin li {
    margin: 0;
}

.mc-title {
    font-size: 17px;
    font-weight: 600;

    &.mc-title-search {
        position: relative;

        .mc-search-btn {
            position: absolute;
            right: 0;
            top: -12px;
            width: 30px;
        }
    }
}

.cp-color-picker {
    z-index: 9;
}

.mc-tooltip > div, .mc-admin > .mc-header > .mc-admin-nav > div > a > span, .mc-admin > .mc-header > .mc-admin-nav-right .mc-account > div,
.mc-lightbox, .mc-color-palette ul, .mc-flows-blocks-nav-box .mc-close {
    background: $white;
    border-radius: 4px;
    padding: 10px 0;
    box-shadow: $box-shadow;
    z-index: 99995;
    list-style: none;
}

hr {
    background: #e6e6e6;
    height: 1px;
    border: none;
    margin: 30px 0;
}

.mc-admin-nav-right [data-value="status"], td.mc-online, td.mc-offline {
    position: relative;

    &:before {
        content: "";
        width: 8px;
        height: 8px;
        position: absolute;
        border-radius: 50%;
        margin-top: -4px;
        top: 50%;
        right: 3px;
        background: rgb(19, 202, 126);
    }

    &:after {
        content: "";
        width: 14px;
        height: 14px;
        position: absolute;
        border-radius: 50%;
        margin-top: -7px;
        top: 50%;
        right: 0;
        background: rgba(19, 202, 126, .2);
    }

    &.mc-offline:after {
        display: none !important;
    }
}

td.mc-offline:after {
    display: none !important;
}

.mc-offline:before {
    background: #d6d6d6 !important;
}

.mc-flex {
    display: flex;
    align-items: center;
    align-content: center;

    .mc-btn + .mc-btn, .mc-btn + .mc-btn-icon {
        margin-left: 15px;
    }
}

.mc-menu-wide ul, .mc-tab > .mc-nav > ul {
    display: flex;

    li {
        font-weight: 600;
        color: $color-black;
        font-size: 15px;
        line-height: 38px;
        margin: 0 30px 0 0;
        transition: $transition;
        letter-spacing: 0.3px;
        cursor: pointer;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        user-select: none;

        span {
            font-weight: 400;
            opacity: 0.7;
            font-size: 12px;
            display: inline-block;
            letter-spacing: 1px;
            transform: translateY(-2px);
        }
    }

    li.mc-active, li:hover {
        color: $color-blue;
    }

    li.mc-active {
        border-bottom: 2px solid $color-blue;
        transition: none;
    }

    li.mc-tab-nav-title {
        font-size: 16px;
        font-weight: 600;
        cursor: default;
        color: $color-black;
    }

    li + li.mc-tab-nav-title {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #e1e1e1;
    }
}

.mc-menu-wide {
    > div {
        display: none;
    }
}

.mc-menu li {
    ul {
        position: absolute;
        left: 100%;
        margin-top: -36px;
        display: none;

        li {
            color: $color-black;
        }
    }

    &:hover ul {
        display: block;
        animation: mc-fade-animation 0.4s;
    }
}

.mc-tab {
    display: flex;

    > div {

        > div, .mc-nav > div {
            display: none;

            &.mc-active, &.mc-menu-wide {
                display: block;
            }
        }

        &.mc-nav-only + div > div {
            display: block;

            &.mc-tab {
                display: flex;
            }
        }
    }

    > .mc-nav {
        min-width: 200px;
        border-right: 1px solid $border-color;
        padding: 15px 20px 20px 20px;

        > ul {
            display: block;
            max-width: 200px;

            li {
                margin-right: 0;

                &.mc-active {
                    border-bottom: none;
                }
            }
        }
    }

    > .mc-nav.mc-scroll-area {
        border-right: none;
    }

    .mc-content {
        width: 100%;
        padding: 20px 15px 30px 30px;

        > .mc-active {
            animation: mc-fade-animation 0.4s;
        }

        > div:last-child {
            margin-bottom: 0;
        }
    }

    h2 {
        font-size: 15px;
        letter-spacing: .3px;
        line-height: 27px;
        margin: 2px 0 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    > .mc-nav > .mc-menu-wide {
        margin-bottom: 15px;
    }
}

#mc-table-chatbot-files, #mc-table-chatbot-website {
    &:empty, &:empty + hr {
        display: none;
    }
}

#mc-table-chatbot-files td:last-child i, #mc-table-chatbot-website td:last-child i, .mc-inner-tab .mc-nav > ul li i {
    width: 30px;
    height: 30px;
    line-height: 35px;
    font-size: 15px;
    text-align: center;
    color: $color-red;
    position: absolute;
    display: none;
    cursor: pointer;
    right: -5px;
    top: 4px;
}

#mc-table-chatbot-files tr:hover td:last-child i, #mc-table-chatbot-website tr:hover td:last-child i, .mc-inner-tab .mc-nav > ul li:hover i {
    display: block;
    animation: mc-fade-animation 0.5s;
}

#mc-table-chatbot-files td:last-child i.mc-loading, #mc-table-chatbot-website td:last-child i.mc-loading, .mc-inner-tab .mc-nav > ul li i.mc-loading {
    display: block;
}

.mc-inner-tab {
    .mc-nav {
        > ul {
            li {
                position: relative;
                padding-right: 25px;
                font-weight: 500;
                font-size: 13px;
                color: $color-gray;

                span {
                    position: absolute;
                    right: 32px;
                    top: 2px;
                    padding-left: 10px;
                    background: $white;
                    opacity: 1;
                }

                i {
                    background: $white;
                }
            }

            > .mc-no-results {
                opacity: 0.6;
                font-weight: 400;
                font-size: 13px;
                cursor: default;
                color: $color-black !important;
                cursor: default !important;
            }
        }

        .mc-btn {
            display: inline-block;
            margin-top: 15px;
        }
    }
}

.mc-menu-mobile {

    > ul {
        display: flex;

        li a {
            margin: 0 3px;
        }

        li:empty {
            display: none;
        }

        .mc-loading:before {
            line-height: 30px;
        }
    }

    > i, .mc-mobile {
        display: none;
    }

    [data-value="panel"]:not(.mc-active) {
        display: none;
    }
}

.mc-list-thumbs li {
    display: flex;
    cursor: pointer;
    transition: $transition;
    padding: 5px 15px;

    > div.mc-image {
        background-size: cover;
        width: 40px;
        min-width: 40px;
        height: 40px;
        margin-right: 15px;
        background-position: center center;
        border: 1px solid $border-color;
    }

    > div {
        min-width: 0;

        span {
            display: block;
            font-weight: 500;
            font-size: 13px;
            line-height: 20px;
            letter-spacing: .2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: left;
            transition: $transition;
        }

        span:last-child {
            color: $color-gray;
            font-weight: 400;
        }
    }

    &:hover {
        background-color: $background-gray;

        span:first-child {
            color: $color-blue;
        }
    }
}

.mc-table {
    margin: 0 0 20px 0;
    width: 100%;
    max-width: 100%;
    border-collapse: collapse;
    table-layout: fixed;

    th {
        white-space: nowrap;
        padding: 9px 15px;
        text-align: left;
        border-bottom: 1px solid $border-color;
        font-size: 15px;
        line-height: 20px;
        font-weight: 600;
        color: rgb(115, 115, 118);
    }

    td {
        white-space: nowrap;
        padding: 9px 15px;
        text-align: left;
        border-bottom: 1px solid $border-color;
        color: $color-black;
        font-size: 14px;
        transition: $transition;

        a {
            position: relative;
            text-decoration: none;
            color: $color-black;
        }

        .mc-profile span {
            font-size: 15px;
            padding-top: 2px;
        }

        &.mc-td-ut {
            text-transform: uppercase;
            font-size: 13px;
        }

        &.mc-td-language {
            text-transform: uppercase;
        }
    }

    tr:last-child td {
        border-bottom: none;
    }

    input[type="checkbox"] {
        background: $white;
        clear: none;
        cursor: pointer;
        display: inline-block;
        line-height: 0;
        outline: 0;
        padding: 0;
        margin: 8px 0;
        text-align: center;
        vertical-align: middle;
        width: 20px;
        height: 20px;
        border: 1px solid $border-color;
        border-radius: 3px;
        outline: none;
        box-shadow: none;
        -webkit-appearance: none;
    }

    input[type="checkbox"]:checked,
    input[type="checkbox"]:hover {
        border-color: $color-blue;
    }

    input[type="checkbox"]:checked:before {
        content: "\77";
        font-family: "Masi Chat Icons" !important;
        font-style: normal !important;
        font-weight: normal !important;
        font-variant: normal !important;
        text-transform: none !important;
        line-height: 22px;
        font-size: 10px;
        margin: 0;
        width: 100%;
        height: 100%;
        color: $color-blue;
    }

    tr:hover td {
        cursor: pointer;
        background-color: $background-gray;
    }

    .mc-no-results {
        position: absolute;
        white-space: nowrap;
        margin: 30px 15px;
    }

    tr + p {
        display: none;
    }

    &.mc-loading {
        text-indent: unset;
        height: 175px;

        tbody:empty, thead {
            display: none;
        }
    }
}

.mc-profile {
    position: relative;
    color: $color-black;
    line-height: 30px;
    padding-left: 45px;
    text-decoration: none;
    display: flex;
    align-items: center;

    img {
        position: absolute;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
    }

    > span {
        font-size: 16px;
        font-weight: 600;
        letter-spacing: 0.3px;
    }
}

.mc-profile-list > ul > li > span, .mc-panel-details .mc-title {
    font-weight: 500;
    padding-right: 10px;
    font-size: 11px;
    letter-spacing: .3px;
    color: #88969e;
    text-transform: uppercase;
    transition: $transition;

    span {
        color: $color-black;
    }
}

.mc-profile-list > ul > li:hover > span, .mc-panel-details .mc-split > div:hover > .mc-title {
    color: #2a3a43;
}

.mc-profile-list > ul > li, .mc-panel-details .mc-list-items > div, .mc-panel-details .mc-list-items > a {
    position: relative;
    font-size: 13px;
    line-height: 27px;
    letter-spacing: 0.3px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
}

.mc-list-items:not(:empty) + .mc-btn {
    margin-top: 15px;
}

.mc-profile-list {
    padding: 0;

    > ul {
        margin-top: 15px;

        > li {
            padding-left: 30px;

            .mc-icon, > img {
                position: absolute;
                left: 0;
                top: 7px;
                font-size: 14px;
                color: $color-dark-blue;

                &:before {
                    width: 16px;
                    height: 16px;
                    opacity: 0.8;
                    position: absolute;
                }
            }

            > img {
                width: 15px;
                height: 15px;
            }

            > span {
                position: relative;
                top: 1px;

                &:empty {
                    display: none;
                }
            }

            label {
                font-weight: 400;
                margin: 0;
            }

            &:first-child span {
                text-transform: uppercase;
            }
        }
    }

    [data-id="wp-id"]:hover,
    [data-id="wp-id"]:hover label,
    [data-id="conversation-source"]:hover,
    [data-id="conversation-source"]:hover label,
    [data-id="location"]:hover,
    [data-id="location"]:hover label,
    [data-id="timezone"]:hover,
    [data-id="timezone"]:hover label,
    [data-id="current_url"]:hover,
    [data-id="current_url"]:hover label,
    [data-id="envato-purchase-code"]:hover,
    [data-id="envato-purchase-code"]:hover label {
        color: $color-blue;
        cursor: pointer;
    }

    [data-id="browser_language"] img, [data-id="language"] img {
        border-radius: 50%;
    }

    [data-id="country_code"] img {
        border-radius: 2px;
        height: 10px;
        margin-top: 3px !important;
    }

    [data-id="language"] label {
        text-transform: uppercase;
    }

    [data-id="rating"] {
        .mc-icon-like {
            color: $color-green;
        }

        .mc-icon-dislike {
            color: $color-red;
        }
    }
}

.mc-panel-details {
    border-top: 1px solid $border-color;
    padding-bottom: 15px;
    position: relative;
    overflow: hidden;

    h3 {
        justify-content: space-between;

        img {
            height: 11px;
            margin-right: 5px;
        }

        .mc-select {
            padding: 0 !important;

            p {
                opacity: .7;
            }

            p, li {
                font-weight: 400;
            }

            ul {
                right: -2px;
                left: auto;
            }

            &:hover p {
                opacity: 1;
            }
        }
    }

    > div {
        position: relative;

        > .mc-title {
            margin: 15px 15px 0 15px;

            > i {
                position: absolute;
                right: 12px;
                font-size: 14px;
                line-height: 20px;
                width: 20px;
                height: 20px;
                cursor: pointer;
                text-align: center;

                &:hover {
                    color: $color-blue;
                }
            }

            & + .mc-list-items {
                margin: 5px 0 -3px 0;
            }
        }

        .mc-list-items:last-child > p {
            margin-bottom: 5px;
        }
    }

    .mc-split {
        margin: 0 15px;
        display: flex;
        justify-content: space-between;

        > div {
            width: 50%;

            & + div {
                border-left: 1px solid $border-color;
                padding-left: 15px;
            }

            span {
                display: block;
                margin-top: 5px;
                font-size: 13px;
            }
        }
    }

    .mc-list-names {
        margin: -5px 0;

        > div {
            padding: 5px 15px;
            display: flex;
            justify-content: space-between;
            overflow: hidden;

            > span {
                white-space: nowrap;
                font-size: 13px;
                font-weight: 500;
            }

            > span:last-child {
                opacity: .6;
                font-weight: 400;
            }

            &.mc-expired > span:last-child {
                opacity: 1;
                color: $color-red;
            }
        }
    }

    .mc-list-links {
        > a {
            padding: 0 15px;
            cursor: pointer;
            display: block;
            text-decoration: none;
            color: $color-black;
            transition: $transition;

            > span span {
                display: inline-block;
            }

            > span:last-child {
                color: #7e8c98;
            }

            i {
                position: absolute;
                right: 0;
                width: 27px;
                height: 25px;
                line-height: 27px;
                text-align: left;
                font-size: 13px;
                opacity: .5;
                z-index: 2;

                &.mc-icon-file {
                    font-size: 16px;
                    line-height: 23px;

                    &:hover {
                        color: $color-blue;
                    }
                }

                &:hover {
                    opacity: 1;
                    color: $color-red;
                }

                &.mc-loading {
                    opacity: 1;
                    right: 6px;
                    top: 4px;

                    &:before {
                        width: 22px;
                        height: 22px;
                        line-height: 22px;
                        font-size: 13px;
                    }
                }
            }

            &:hover {
                color: $color-blue;
            }
        }

        > p {
            font-size: 13px;
            padding: 0 0 0 15px;
            margin: 0;
            opacity: .5;
        }
    }

    .mc-list-icon > a {
        padding-right: 40px;
    }

    .mc-accordion {

        > div {
            position: relative;
            overflow: hidden;


            > span {
                padding: 0 15px;
                cursor: pointer;
                white-space: nowrap;
                display: block;
                overflow: hidden;
                text-overflow: ellipsis;
                transition: $transition;

                > span:first-child {
                    opacity: .6;
                }

                > span + span {
                    margin-left: 15px;
                }

                > a {
                    position: absolute;
                    right: 15px;
                    line-height: 30px;
                    text-decoration: none;
                    font-weight: normal;
                    font-style: normal;
                    color: $color-black;
                    transition: $transition;

                    &:hover {
                        color: $color-blue;
                    }
                }

                &:hover {
                    color: $color-blue;
                }
            }

            > div {
                height: 0;
                overflow: hidden;

                > a {
                    display: block;
                    text-decoration: none;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    color: $color-black;
                    transition: $transition;

                    span:first-child {
                        opacity: .6;
                    }

                    span:last-child {
                        opacity: 1;
                    }

                    &:hover {
                        color: $color-blue;
                    }
                }
            }

            &.mc-active > div {
                padding: 15px;
                height: auto;
            }

            .mc-multiline {
                line-height: 20px;
                padding: 5px 0;
            }
        }
    }

    > i {
        position: absolute;
        right: 12px;
        top: 22px;
        font-size: 13px;
        cursor: pointer;
        opacity: .7;
        transition: $transition;

        &:hover {
            opacity: 1;
            color: $color-blue;
        }

        &.mc-loading {
            position: static;
            height: 60px;
            display: block;
            opacity: 1;
        }

        & + .mc-list-names {
            padding-top: 15px;
        }
    }

    > .mc-title {
        padding: 15px 15px 0 15px;
        display: block;

        .mc-btn-text {
            margin-left: 15px;
            float: right;
            text-transform: none;

            i {
                margin-right: 5px;
            }
        }
    }

    .mc-btn {
        background-color: $white;
        color: $color-gray;
        border: 1px solid #ccd2d5;
        box-shadow: 0 1px 1px rgba(0,0,0,0.12);
        padding: 0 10px;
        white-space: nowrap;
        border-radius: 4px;
        height: 33px;
        line-height: 33px;

        &:hover {
            color: $white;
            border-color: $color-blue;
            background-color: $color-blue;
        }

        &.mc-loading {
            width: 15px;
            background-color: $white !important;
            border: 1px solid $border-color !important;

            &:before {
                color: $color-blue;
                cursor: default;
            }
        }
    }

    > .mc-btn:first-child {
        margin: 15px 15px 0 15px;
    }

    &.mc-collapse > .mc-collapse-btn {
        margin-left: 15px;
    }

    &:empty {
        display: none;
    }
}

.mc-panel-attachments {
    overflow: visible;
}

.mc-info-card {
    position: fixed;
    bottom: 10px;
    right: 10px;
    left: 10px;
    border-radius: 4px;
    padding: 10px 30px;
    background: $color-green;
    color: $white;
    text-align: center;
    box-shadow: $box-shadow;
    cursor: pointer;
    display: none;
    z-index: 9999995;

    &.mc-active {
        display: block;
        animation: mc-fade-bottom-animation .5s;
    }

    h3 {
        margin: 0;
        padding: 0;
        font-size: 15px;
        line-height: 25px;
        white-space: nowrap;
        letter-spacing: 0.5px;
        color: $white;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    p {
        margin: 5px 0 5px 0;
        padding: 0;
        font-size: 12px;
        line-height: 20px;
        letter-spacing: 0.3px;

        &:empty {
            display: none;
        }
    }

    a {
        color: $white;
    }

    .mc-icon-link {
        text-decoration: none;
        font-size: 13px;
        margin-left: 5px;
        display: inline-block;
        transform: translateY(2px);
    }

    span {
        text-decoration: underline;

        &:hover {
            text-decoration: none;
        }
    }

    &.mc-info-card-error {
        background: $color-red;
    }

    &.mc-info-card-info {
        background: $color-gray;

        h3 {
            font-weight: 400;
            font-size: 13px;
        }
    }
}

.mc-panel-notes > div {
    > div {
        margin: 0 15px;
        background: #ffeab0;
        padding: 15px;
        border-radius: 4px;
        word-wrap: break-word;
        overflow-wrap: break-word;
        overflow: hidden;

        > span:first-child {
            display: block;
            font-weight: 500;
            position: relative;
            margin-bottom: 10px;

            i {
                position: absolute;
                right: -5px;
                font-size: 10px;
                cursor: pointer;
                opacity: 0;
                z-index: 9;
                text-align: center;
                width: 20px;
                height: 20px;
                line-height: 23px;
                margin-top: -5px;
                transition: all .5s;

                &:hover {
                    color: $color-red;
                }

                &.mc-edit-note {
                    right: 17px;
                    font-size: 16px;
                    line-height: 26px;
                    background: #ffeab0;

                    &:hover {
                        color: $color-blue;
                    }
                }
            }

            > span {
                font-weight: 400;
                font-size: 12px;
                float: right;
                opacity: .7;
                transition-delay: 1s;
            }

            &.mc-note-hide-info {
                margin-bottom: 0;

                & + span {
                    padding-right: 35px;
                }
            }
        }

        > span:last-child {
            font-size: 13px;
            line-height: 21px;
            display: block;
            opacity: .9;
        }

        &:hover {
            > span i {
                opacity: 1;
            }

            > span > span {
                opacity: 0;
                transition: none;
            }
        }

        & + div {
            margin-top: 10px;
        }
    }

    &:empty {
        margin-bottom: -15px;
    }
}

.mc-note-text a {
    color: $color-black;
    text-decoration: underline;
}

.mc-panel-tags > div {
    > div {
    }

    &:empty {
        margin-bottom: -15px;
    }
}

.mc-tags-box .mc-main {
    float: left;
    width: calc(100% - 40px);
}

.mc-tags-cnt, .mc-panel-tags > div {
    margin: -5px;

    > span {
        padding: 5px 10px;
        margin: 5px;
        letter-spacing: .3px;
        font-size: 13px;
        line-height: 15px;
        border-radius: 4px;
        cursor: pointer;
        display: block;
        float: left;
        -webkit-user-select: none;
        user-select: none;
        transition: $transition;
    }
}

.mc-tags-cnt {
    > span {
        border: 1px dashed transparent;

        &:not(.mc-active):not(:hover) {
            background: none;
            color: $color-black;
            border-color: $color-gray;
        }
    }



    [data-color=""] {
        &.mc-active, &:hover {
            background-color: $color-blue;
            color: $white !important;
        }
    }
}

.mc-panel-tags > div {
    margin: 10px;

    span {
        cursor: default;
        letter-spacing: 0.3px;
        font-size: 12px;
    }

    [data-color=""] {
        background-color: $color-blue;
        color: #FFF;
    }
}

#mc-add-tag {
    margin: 5px;
    height: 25px;
    width: 25px;
    display: block;
    float: left;

    &:before {
        font-size: 15px;
        line-height: 25px;
    }
}

.mc-popup {
    bottom: 70px;

    .mc-scroll-area {
        height: calc(100% - 65px);

        > p, ul > p {
            padding: 0 30px 15px 15px;
        }

        ul.mc-loading {
            height: 100%;
            text-indent: unset;

            &:not(:empty) {
                height: auto;

                &:before {
                    top: auto;
                    bottom: 0;
                }
            }
        }
    }

    .mc-popup-close {
        position: fixed;
        right: -25px;
        top: -25px;
        cursor: pointer;
        display: none;
        transition: $transition;

        &:hover {
            color: $color-red;
        }
    }

    &.mc-popup-lightbox {
        left: 50% !important;
        top: 50%;
        transform: translateY(-50%) translateX(-50%);

        .mc-popup-close {
            display: block;
        }

        &:after {
            display: none;
        }
    }
}

.mc-popup.mc-replies .mc-replies-list ul {

    li {
        display: flex;
        white-space: nowrap;
        font-size: 13px;
        height: 30px;
        line-height: 33px;
        padding-left: 15px;
        padding-right: 15px;
        margin-right: 15px;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
        overflow: hidden;

        div {
            transition: $transition;
        }

        div:first-child {
            margin-right: 15px;
            font-weight: 500;
            letter-spacing: .2px;
            position: relative;
            padding-left: 15px;
            color: $color-gray;

            &:before {
                content: "#";
                position: absolute;
                left: 0;
                font-size: 12px;
                line-height: 37px;
                opacity: 0.5;
            }

            &:empty {
                display: none;
            }
        }

        div:last-child {
            text-overflow: ellipsis;
            overflow: hidden;
        }

        &:hover {
            cursor: pointer;

            > div {
                color: $color-blue;
                opacity: 1;
            }
        }
    }
}

.mc-tooltip {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999995;
    font-family: "Masi Chat Font", "Helvetica Neue", "Apple Color Emoji", Helvetica, Arial, sans-serif;
    display: none;

    &:before {
        content: "";
        background: url(../media/triangle.svg) no-repeat center center;
        background-size: contain;
        position: absolute;
        width: 20px;
        height: 15px;
        top: -11px;
        left: 50%;
        transform: translateX(-50%) rotate(180deg);
        margin-left: 1px;
    }

    &.n:before {
        top: auto;
        bottom: -11px;
        transform: translateX(-50%);
    }

    > div {
        font-size: 13px;
        line-height: 20px;
        padding: 8px 12px;
        max-width: 250px;
        text-align: center;
        letter-spacing: 0.3px;
        white-space: nowrap;
        color: $color-gray;
    }
}

.mc-collapse {

    > div, > ul {
        overflow: hidden;
        position: relative;
    }

    > .mc-collapse-btn {
        margin: 15px 0 -5px 0;
        text-transform: uppercase;
        font-weight: 500;
        font-size: 11px;
        letter-spacing: .3px;
        padding-right: 20px;

        &:after {
            content: "\61";
            font-family: "Masi Chat Icons";
            position: absolute;
            right: 1px;
            font-size: 9px;
            font-style: normal;
            font-weight: normal;
            line-height: 16px;
            text-transform: none;
        }

        &.mc-active:after {
            transform: rotate(180deg);
        }
    }

    .mc-btn + .mc-collapse-btn {
        transform: translateY(10px);
    }
}

.mc-filter-btn {
    i {
        z-index: 0;
    }

    > div {
        display: none;
        position: absolute;
        right: 0;
        left: 0;
        top: 0;
        background: #FFF;
        z-index: 4;
        padding: 15px;
        padding-right: 60px
    }

    .mc-select {
        display: block;
        margin: 0 !important;

        ul {
            width: 100%;
        }
    }

    &.mc-active {
        position: static;

        i {
            z-index: 5;
            top: 25px;
            right: 15px;
        }

        > div {
            display: block;
            border-bottom: 1px solid $border-color;
        }

        &:after {
            display: none;
        }
    }

    &[data-badge]:after {
        content: attr(data-badge);
        position: absolute;
        right: -17px;
        top: 5px;
        background: $color-red;
        color: $white;
        font-size: 10px;
        line-height: 15px;
        height: 15px;
        width: 15px;
        text-align: center;
        border-radius: 50%;
        font-weight: 500;
    }

    &[data-badge="0"]:after {
        display: none;
    }
}

.mc-filter-star {
    cursor: pointer;
    font-size: 20px;
    height: 20px;
    transition: $transition;

    &:not(:hover):not(.mc-active) {
        color: $color-black;
    }
}

.mc-admin {
    > .mc-header {
        position: fixed;
        background: $background-gray;
        border-right: 1px solid $border-color;
        width: 65px;
        top: 0;
        left: 0;
        bottom: 0;
        z-index: 9;

        > .mc-admin-nav {
            > img {
                height: 35px;
                margin: 18px 15px;
            }

            > div {
                padding: 5px 0;

                > a {
                    height: 50px;
                    display: block;
                    cursor: pointer;
                    position: relative;
                    transition: $transition;
                    color: $color-black;

                    &:before {
                        content: "";
                        position: absolute;
                        left: 20px;
                        font-size: 22px;
                        line-height: 50px;
                        opacity: 0.6;
                        background-size: contain;
                        background-repeat: no-repeat;
                        font-family: "Masi Chat Icons";
                        font-style: normal !important;
                        font-weight: normal !important;
                        font-variant: normal !important;
                        text-transform: none !important;
                    }

                    &:hover,
                    &.mc-active {
                        color: $color-blue;
                    }

                    &:hover span {
                        transition-delay: 1s;
                        visibility: visible;
                        opacity: 1;
                        left: 65px;
                    }
                }
            }
        }

        > .mc-admin-nav > div > a > span,
        > .mc-admin-nav-right .mc-account > div {
            margin: 8px 0px;
            font-size: 15px;
            line-height: 20px;
            font-weight: 600;
            padding: 8px 12px;
            text-align: center;
            letter-spacing: 0.5px;
            color: rgb(40, 40, 40);
            position: absolute;
            left: 75px;
            z-index: 9999995;
            opacity: 0;
            visibility: hidden;
            transition: $transition;

            &:before {
                content: "";
                background: url(../media/triangle.svg) no-repeat center center;
                background-size: contain;
                position: absolute;
                width: 20px;
                height: 15px;
                top: 10px;
                left: -13px;
                transform: rotate(90deg);
            }

            &:after {
                content: "";
                position: absolute;
                width: 10px;
                top: 10px;
                bottom: 10px;
                left: 0px;
                background-color: $white;
            }
        }

        > .mc-admin-nav-right {
            align-items: center;
            padding: 15px 0;
            width: 65px;
            bottom: 0;
            left: 0;
            position: fixed;
            text-align: center;

            > div a {
                font-size: 13px;
                line-height: 20px;
                cursor: pointer;
                text-decoration: none;
                display: block;
                transition: $transition;
                color: $color-gray;

                &:hover {
                    color: $color-blue;
                }

                &.mc-profile {
                    padding-left: 35px;

                    img {
                        width: 25px;
                        height: 25px;
                    }
                }

                > i {
                    font-size: 16px;
                    opacity: 0.8;

                    &:hover {
                        opacity: 1;
                    }
                }
            }

            .mc-account {
                padding: 5px 0;
                cursor: pointer;
                text-align: center;

                > img {
                    width: 35px;
                    height: 35px;
                    border-radius: 50%;
                    display: block;
                    margin: 0 auto;
                }

                > div {
                    position: absolute;
                    bottom: 30px;
                    text-align: left;
                    min-width: 200px;
                    padding: 0;
                    transition-delay: 0.5s;

                    &:before {
                        top: auto;
                        bottom: 45px;
                    }
                }

                .mc-profile {
                    padding: 20px 20px 20px 65px;
                    border-bottom: 1px solid rgb(215, 215, 215);
                    background: $white;
                    text-decoration: none;
                    z-index: 9;

                    img {
                        left: 20px;
                    }

                    span {
                        white-space: nowrap;
                    }
                }

                &:hover > div {
                    visibility: visible;
                    opacity: 1;
                    left: 65px;
                }

                .mc-menu {
                    position: relative;
                    box-shadow: none;
                    padding: 20px;
                    margin: 0;
                    z-index: 9;

                    li {
                        padding: 6px 0;
                        position: relative;

                        &.mc-active {
                            color: $color-black;
                        }

                        &:hover,
                        &.mc-active:hover {
                            background: none;
                            color: $color-blue;
                        }
                    }

                    a {
                        text-decoration: none;
                        color: $color-black;

                        &:hover {
                            color: $color-blue;
                        }
                    }
                }
            }

            > div > a + a,
            > div > div + a {
                margin-top: 10px;
            }
        }
    }

    #mc-conversations:before {
        content: "\64";
    }

    #mc-users:before {
        content: "\6e";
    }

    #mc-settings:before {
        content: "\6d";
    }

    #mc-automations:before {
        content: "\e915";
    }

    #mc-reports:before {
        content: "\59";
    }

    #mc-articles:before {
        content: "\e908";
    }

    #mc-chatbot:before {
        content: "\e90a";
    }
}

@media(min-width: 465px) and (max-height: 475px) {
    .mc-admin > .mc-header > .mc-admin-nav > div > a {
        height: 35px;

        &:before {
            line-height: 35px;
        }
    }
}

.mc-enlarger {
    &:not(.mc-active) {
        cursor: pointer;
        position: relative;

        > div {
            display: none !important;
        }

        &:before {
            content: "\4d";
            font-family: "Masi Chat Icons";
            position: absolute;
            left: 1px;
            font-size: 9px;
            font-style: normal;
            font-weight: normal;
            line-height: 16px;
            text-transform: none;
        }
    }
}

.mc-admin {
    height: 100%;
    width: calc(100% - 65px);
    padding-left: 65px;
    position: fixed;
    font-size: 14px;
    line-height: 17px;
    background: $background-gray;
    top: 0;
    z-index: 9;

    &.mc-pwa {
        border-top: 1px solid $border-color;
    }

    &.mc-admin-start {
        width: auto;
        padding: 30px;
        position: static;
    }

    > main {
        padding: 0;
        background: $white;
        height: 100%;
        overflow: hidden;

        > div:not(.mc-active) {
            display: none;
        }

        > div {
            height: 100%;

            &.mc-active {
                animation: mc-fade-animation 0.4s;
            }

            > .mc-top-bar {
                height: 40px;
                margin: 0;
                padding: 15px 20px;
                align-items: center;
                display: flex;
                justify-content: space-between;
                border-bottom: 1px solid $border-color;

                h2 {
                    margin: 0;
                    font-size: 22px;
                    line-height: 35px;
                    font-weight: 600;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                > div {
                    display: flex;
                    align-items: center;

                    h2 {
                        margin-right: 60px;
                    }

                    h3 + .mc-setting {
                        margin-left: 15px;
                    }

                    &:first-child {
                        overflow: hidden;
                        padding-right: 30px;

                        > ul li {
                            flex-shrink: 0;
                        }
                    }

                    &:last-child {
                        .mc-search-btn {
                            margin-right: 30px;
                        }

                        .mc-btn + .mc-btn, .mc-btn-icon + .mc-btn, .mc-btn + .mc-btn-icon {
                            margin-left: 15px;
                        }
                    }
                }
            }

            > .mc-tab {
                height: calc(100% - 70px);
            }
        }
    }
}

.mc-search-dropdown {
    min-width: 391px;
    position: relative;
    text-align: right;

    .mc-search-btn {
        margin-left: auto;

        input {
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
            box-shadow: 0 0px 5px rgba(39, 156, 255, 0.5);
        }
    }

    .mc-search-dropdown-items {
        position: absolute;
        right: 30px;
        left: -1px;
        top: 40px;
        padding: 15px;
        background: $white;
        border-radius: 4px;
        border-top-right-radius: 0;
        border-top-left-radius: 0;
        border: 1px solid $color-blue;
        box-shadow: 0 0px 5px rgba(39, 156, 255, 0.5);
        clip-path: inset(0px -10px -10px -10px);
        border-top: none;
        text-align: left;
        display: none;
        z-index: 4;

        div {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            line-height: 25px;
            cursor: pointer;
            font-size: 13px;
            letter-spacing: 0.3px;
            transition: $transition;

            &:hover {
                color: $color-blue;
            }
        }

        &:empty {
            padding: 2px;
            margin-top: -2px;
        }
    }

    .mc-active + .mc-search-dropdown-items {
        display: block;
    }
}

/*
 *
 * -----------------------------------------------------------
 * CONVERSATIONS AREA
 * -----------------------------------------------------------
 *
*/

.mc-board {
    display: flex;
    justify-content: space-between;
    height: 100%;

    > div > .mc-top {
        border-bottom: 1px solid $border-color;
        padding: 15px 20px;
        height: 70px;
        min-height: 70px;
        box-sizing: border-box;

        .mc-flex {
            justify-content: flex-end;
        }
    }

    > .mc-admin-list {
        max-width: 400px;
        min-width: 400px;
        border-right: 1px solid $border-color;
        position: relative;


        > .mc-top {
            display: flex;
            align-items: center;
            justify-content: space-between;

            > .mc-select > p span {
                padding-left: 5px;
            }
        }

        .mc-scroll-bar {
            opacity: 0.2 !important;
        }

        > .mc-loading {
            position: absolute;
            bottom: 10px;
            left: 50%;
            z-index: 9;
        }

        > .mc-scroll-area {
            height: calc(100% - 90px);
            padding: 10px 0;

            li {
                position: relative;
                padding: 18px 20px 15px 20px;
                max-height: 85px;
                opacity: 0.8;
                cursor: pointer;
                margin: 0 10px;
                border-radius: 6px;
                z-index: 3;
                background: transparent;
                transition: $transition;

                > .mc-notification-counter {
                    position: absolute;
                    right: 19px;
                    bottom: 17px;
                    height: 21px;
                    width: 21px;
                    line-height: 21px;
                    font-size: 12px;
                    border-radius: 50%;
                    text-align: center;
                    background: $color-blue;
                    color: $white;
                    z-index: 9;
                    letter-spacing: 0.3px;
                    animation: mc-fade-animation .3s;
                }

                &:hover {
                    background-color: $background-gray !important;

                    &:after {
                        opacity: 0;
                    }
                }

                &.mc-active {
                    opacity: 1;
                    background-color: #f0f4f9 !important;

                    .mc-name {
                        color: rgb(0, 43, 73);
                    }

                    &:before {
                        background-color: $color-blue;
                    }

                    &:after {
                        opacity: 0;
                    }
                }

                &:before {
                    content: "";
                    position: absolute;
                    left: 0;
                    bottom: 10px;
                    top: 10px;
                    width: 2px;
                    background-color: transparent;
                    border-radius: 3px;
                    transition: $transition;
                    z-index: 2;
                }

                &:after {
                    content: "";
                    position: absolute;
                    left: 20px;
                    right: 20px;
                    bottom: -1px;
                    height: 1px;
                    background-color: $border-color;
                    transition: $transition;
                    z-index: 2;
                }

                &:last-child:after {
                    display: none;
                }

                .mc-profile {
                    align-items: flex-start;
                    padding-left: 58px;

                    img {
                        width: 45px;
                        height: 45px;
                        transform: none;
                        top: 0;
                    }

                    .mc-name {
                        height: 23px;
                        line-height: 23px;
                        padding-right: 10px;
                        font-size: 15px;
                        font-weight: 400;
                        white-space: nowrap;
                        letter-spacing: 0.3px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    .mc-time {
                        margin-left: auto;
                        font-size: 13px;
                        line-height: 13px;
                        font-weight: 400;
                        white-space: nowrap;
                        opacity: 0.8;
                    }
                }

                p {
                    font-size: 13px;
                    height: 22px;
                    line-height: 22px;
                    opacity: 0.8;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    letter-spacing: 0.3px;
                    padding-left: 58px;
                    margin: 0;
                    color: $color-black;
                }

                b {
                    font-weight: 400;
                }

                .mc-notification-counter + div + p {
                    margin-right: 30px;
                }

                &[data-conversation-status="2"] {
                    opacity: 1;
                    font-weight: 500;

                    &:not(.mc-active) {
                        background-color: transparent;
                    }

                    .mc-name {
                        font-size: 16px;
                        font-weight: 600;
                    }

                    .mc-time {
                        font-weight: 500;
                        color: $color-blue;
                    }
                }

                &[data-conversation-status="-1"] {
                    .mc-name {
                        font-weight: 600;
                    }
                }
            }

            > ul > p {
                margin: 20px;
            }

            > ul > li + p {
                display: none;
            }

            &.mc-loading {
                position: relative;
                left: auto;
                bottom: auto;
                z-index: 0;
                text-indent: initial;
                overflow: hidden;
                overflow-y: scroll;

                > ul {
                    opacity: 0;
                }
            }
        }

        &.mc-departments-show li.mc-active:before {
            background-color: $color-blue !important;
        }
    }

    .mc-conversation {
        width: 100%;
        min-width: 0;
        position: relative;
        display: flex;
        flex-direction: column;

        > .mc-top {
            width: auto;
            display: flex;
            align-items: center;
            flex-grow: 0;
            justify-content: flex-start;

            .mc-menu-mobile {
                position: absolute;
                right: 15px;
            }

            > .mc-btn-back {
                display: none;
            }

            > a {
                font-weight: 600;
                position: relative;
                padding: 0 15px 0 0;
                margin: 0;
                cursor: pointer;
                text-decoration: none;
                line-height: 40px;
                color: $color-black;
                font-size: 20px;
                transition: $transition;
                position: relative;
                white-space: nowrap;

                &:hover {
                    color: $color-blue;
                }
            }

            i {
                color: $color-gray;
                transition: color .5s
            }

            a:hover i {
                color: $color-blue;
            }
        }

        .mc-list {
            height: 100%;
            padding: 10px 0 5px 0;
            overflow-y: scroll;

            > div {
                max-width: calc(100% - 275px);

                .mc-menu {
                    width: 170px;
                    right: -183px;
                    top: 0;
                    display: none;
                    margin-bottom: 15px;
                    z-index: 9;

                    li {
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    &.mc-active {
                        display: block;
                    }

                    &:empty {
                        display: none;
                    }
                }

                .mc-menu-btn {
                    display: block;
                    height: 15px;
                    width: 30px;
                    position: absolute;
                    right: -24px;
                    text-align: right;
                    top: 11px;
                    color: $color-gray;
                    cursor: pointer;
                    transition: $transition;
                    opacity: 0;
                    z-index: 9;

                    &:hover {
                        color: $color-blue;
                    }
                }

                &[data-type="slider"], &[data-type="card"] {
                    max-width: 380px;
                }

                &:hover .mc-menu-btn {
                    opacity: 1;
                }

                &.mc-right {
                    .mc-menu-btn {
                        right: auto;
                        left: -24px;
                        text-align: left;
                    }

                    .mc-menu {
                        right: auto;
                        left: -181px;

                        &:empty {
                            display: none;
                        }
                    }
                }

                img {
                    max-width: 600px;
                }

                .mc-message img {
                    max-width: 100%;
                }

                .mc-image {
                    max-width: 500px;
                }

                &:last-child {
                    animation: none;
                }
            }

            .mc-highlight:after {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: #0000002b;
                border-radius: 6px;
                opacity: 0;
                animation: mc-fade-in-out 3s linear .5s;
            }

            .mc-rich-message:not(.mc-rich-list) {
                position: relative;
                z-index: 1;

                &:before {
                    content: "";
                    position: absolute;
                    bottom: -15px;
                    left: -15px;
                    right: -15px;
                    top: -15px;
                    z-index: 995;
                    transition: $transition;
                }

                &:hover > div {
                    opacity: .5;
                    transition: $transition;
                }

                .mc-card-img + div + .mc-card-extra {
                    left: 0;
                }

                .mc-slider .mc-card-img + div + .mc-card-extra {
                    left: 15px;
                    top: 15px;
                }

                .mc-input-btn > div, .mc-btn, .mc-select p {
                    transition: none;
                }

                input, textarea, select {
                    color: #E6F2FC;
                    background-color: #E6F2FC;
                }

                .mc-input > span.mc-active {
                    background: transparent !important;
                    top: 0;
                    left: 0;
                    bottom: 0;
                    padding: 0 10px;
                    border-radius: 5px;
                    font-size: 13px;
                    line-height: 44px;
                    color: $color-gray;
                }
            }

            .mc-delivery-failed {
                font-size: 14px;
                transform: translateY(2px);
                margin-right: 5px;
                display: inline-block;
                cursor: help;
                position: relative;
                z-index: 2;
            }

            iframe:not([height]) {
                height: 165px;
            }

            &.mc-touchmove {
                overflow: hidden;
            }
        }

        .mc-editor {
            flex-shrink: 0;
            margin: 1px 15px 15px 15px;
            border: 1px solid rgba(0, 0, 0, 0.2);
            border-radius: 4px;
            box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
            transition: box-shadow linear 40ms, border-color linear 0.2s;
            background-color: $white;

            .mc-textarea {
                border: none;
                padding: 0 !important;
            }

            .mc-bar {
                margin-top: 10px;
                position: relative;

                .mc-btn-attachment {
                    display: block !important;
                }

                .mc-btn {
                    background-color: $color-blue;
                    color: $white;
                    border-color: $color-blue;
                    font-size: 12px;
                    line-height: 27px;
                    height: 25px;
                    transition: $transition;

                    &:hover {
                        background-color: $color-dark-blue;
                        border-color: $color-dark-blue;
                    }
                }
            }

            .mc-attachments:not(:empty) {
                padding-top: 15px;
            }

            .mc-suggestions {
                display: flex;
                flex-wrap: wrap;
                margin: 10px -5px 0 -5px;

                &:empty {
                    display: none;
                }
            }

            .mc-suggestions span {
                display: block;
                max-width: calc(50% - 50px);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                padding: 7px 15px;
                background: #eef1f6;
                border-radius: 15px;
                font-size: 13px;
                letter-spacing: 0.3px;
                margin: 5px;
                cursor: pointer;
                transition: background-color .5s, color .5s;

                &:hover {
                    color: $white;
                    background-color: $color-blue;
                }

                &.mc-suggestion-full {
                    max-width: 100%;
                    white-space: normal;
                }
            }

            &.mc-audio-message-active {
                .mc-textarea, .mc-bar-icons, .mc-suggestions, .mc-attachments {
                    display: none;
                }

                .mc-bar {
                    margin-top: 0;
                    justify-content: right;
                }
            }
        }

        &[data-conversation-status="4"] [data-value="delete"], [data-value="inbox"], [data-value="empty-trash"], &[data-conversation-status="3"] [data-value="archive"], [data-value="read"] {
            display: none;
        }

        &[data-conversation-status="3"] [data-value="inbox"], &[data-conversation-status="4"] [data-value="inbox"], &[data-conversation-status="4"] [data-value="empty-trash"], [data-value="read"].mc-active {
            display: block;
        }

        [data-value="archive"] {
            color: $color-blue;
            margin-right: 5px;

            i {
                color: $color-green !important;
                font-size: 20px;
            }

            &:hover {
                border-color: $color-green;
                background-color: #d8ece3;
            }
        }

        [data-value="delete"]:hover i {
            color: $color-red !important;
        }
    }

    .mc-user-details {
        min-width: 400px;
        width: 400px;
        border-left: 1px solid $border-color;
        display: flex;
        flex-direction: column;
        height: 100%;
        overflow: hidden;
        position: relative;
        transition: width .3s;

        .mc-top {
            line-height: 42px;
            font-size: 18px;
            font-weight: 600;
            display: flex;
        }

        h3 {
            margin: 15px;
            font-size: 15px;
            line-height: 25px;
            font-weight: 600;
        }

        > div > h3 {
            margin: 0;
            padding: 15px;
            border-top: 1px solid $border-color;
        }

        .mc-profile {
            margin-left: -10px;
            cursor: pointer;
            line-height: 17px;
            transition: opacity .3s;

            &:hover {
                opacity: .7;
            }
        }

        .mc-profile-list {
            padding: 15px;

            ul {
                margin-top: 0;
            }

            [data-id="telegram_bcid"], [data-id="token"], [data-id="woocommerce_session_key"], [data-id="perfex-id"], [data-id="whmcs-id"], [data-id="aecommerce-id"], [data-id="facebook-id"], [data-id="viber-id"], [data-id="zalo-id"], [data-id="telegram-id"], [data-id="wechat-id"], [data-id="martfury-session"], [data-id="line-id"], [data-id="opencart_id"], [data-id="shopify_id"] {
                display: none;
            }

            [data-id="cc"] {
                margin-left: 15px;
            }

            [data-id="cc"], [data-em] {
                transition: $transition;

                label {
                    cursor: pointer;
                }

                &:hover {
                    color: $color-blue;
                }
            }
        }

        .mc-user-details-close {
            position: absolute;
            right: 15px;
            top: 15px;

            i {
                line-height: 35px;

                &:before {
                    font-size: 11px;
                    line-height: 11px;
                }
            }
        }

        &:not(.mc-active) {
            width: 0;
            min-width: 0;
            overflow: hidden;
        }

        .mc-select {
            padding: 0 15px 15px 15px;
        }

        .mc-scroll-bar {
            opacity: 0.2 !important;
        }

        .mc-scroll-area {
            height: calc(100% - 70px);
        }

        .mc-user-conversations {
            margin: 0 0 15px 15px;

            li {
                border-left: 1px solid $border-color;

                > div {
                    padding-left: 45px;

                    .mc-message {
                        font-weight: 400;
                    }
                }

                &:hover, &.mc-active {
                    background-color: rgb(238, 241, 246);
                }

                &:first-child {
                    border-top: 1px solid $border-color;
                    border-top-left-radius: 6px;
                }

                &:last-child {
                    border-bottom-left-radius: 6px;
                }
            }

            img {
                top: 6px;
                width: 30px;
                height: 30px;
            }

            span {
                line-height: 21px;
            }

            .mc-message {
                height: 20px;
                line-height: 20px;
            }
        }

        .mc-inline {
            display: flex;
            align-content: center;
            align-items: center;
            padding: 15px;
            border-top: 1px solid $border-color;

            h3 {
                margin-right: 15px;
                line-height: 55px;
            }

            > * {
                margin: 0;
                padding: 0;
            }

            > .mc-select {
                width: 100%;
                height: 55px;

                > p {
                    font-weight: 400;
                    font-size: 13px !important;
                    line-height: 55px !important;
                    border-radius: 0;

                    &:after {
                        line-height: 55px;
                        right: 15px;
                    }
                }

                > ul {
                    width: 100%;
                }
            }

            &.mc-inline-departments, &.mc-inline-agents {
                padding: 0 0 0 15px;
            }
        }
    }

    .mc-no-conversation-message {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        justify-content: center;
        align-items: center;
        text-align: center;
        background: $white;
        padding: 15px;
        display: none;
        z-index: 9;

        > div {
            max-width: 650px;
        }

        label {
            font-weight: 600;
            font-size: 20px;
            line-height: 30px;
        }

        p {
            margin: 15px 0;
            font-size: 15px;
            line-height: 25px;
            color: $color-gray;
        }
    }

    .mc-labels {
        padding-left: 10px;
        display: flex;
        align-items: center;

        span {
            font-size: 14px;
            line-height: 30px;
            padding: 1px 10px 0 10px;
            border-radius: 3px;
            margin: 0 5px;
            display: block;
            font-weight: 600;
            white-space: nowrap;
            cursor: default;
            position: relative;
        }

        .mc-status-online {
            background: rgba(19, 202, 126, 0.21);
            color: rgb(0, 147, 65);
        }

        .mc-status-warning {
            background: $background-color-yellow;
            color: rgb(98, 87, 5);
        }

        .mc-status-typing {
            background-color: rgb(231, 242, 252);
            color: $color-blue;
            padding-right: 32px;

            &:after {
                content: "...";
                position: absolute;
                width: 15px;
                left: calc(100% - 27px);
                bottom: 0;
                font-weight: 600;
                letter-spacing: 1px;
                overflow: hidden;
                animation: mc-typing 1s infinite;
                text-indent: 0;
            }
        }
    }

    .mc-editor .mc-labels {
        position: absolute;
        left: -15px;
        top: -45px;
        z-index: 95;

        > span {
            animation: mc-fade-bottom-animation .5s;
        }
    }

    .mc-list {

        > div {
            &:not(.mc-right) {
                .mc-thumb {
                    .mc-tooltip {
                        top: 40px;
                        left: -10px;

                        &:before {
                            top: -11px;
                            left: 21px;
                        }
                    }

                    &:hover .mc-tooltip {
                        display: block;
                        opacity: 0;
                        animation: mc-fade-bottom-animation .5s;
                        animation-fill-mode: forwards;
                        animation-delay: 1s;
                    }
                }
            }
        }
    }

    &.mc-no-conversation .mc-no-conversation-message {
        display: flex;
    }

    .mc-tags-area {
        display: flex;
        align-items: center;
        padding-right: 10px;

        > i {
            line-height: 8px;
            font-weight: 400;
            background: none;
        }
    }
}

.mc-area-conversations > .mc-btn-collapse {
    display: none;
    position: fixed;
    left: 70px;
    top: 20px;
    transition: color 0.4s;
    cursor: pointer;
    width: 30px;
    height: 30px;
    line-height: 35px;
    font-size: 15px;
    text-align: center;
    z-index: 3;

    &.mc-left:not(.mc-active) {
        transform: rotate(180deg);
        left: 67px;
    }

    &.mc-right:not(.mc-active) {
        transform: rotate(180deg);
        right: 2px;
    }

    &.mc-right {
        left: auto;
        right: 10px;
    }
}

#mc-audio-clip {
    bottom: 0;
    right: auto;
    left: 0;
    top: 0;
    border-radius: 6px;
    background: $white;

    .mc-icon:before {
        line-height: 50px;
    }
}

.mc-notes-box .mc-setting textarea {
    min-height: 200px;
}

#note-ai-scraping {
    margin-right: 15px;
    max-width: 200px;

    select {
        min-width: auto;
    }
}

.mc-list .mc-select-phone > div > p {
    display: none;
}

.mc-cc-box .mc-setting .repeater-item + .repeater-item {
    border-top: none;
    padding-top: 0;
    margin-top: 10px;

    i {
        margin-top: -6px;
    }
}

.mc-shopify-orders {
    > div > span {
        display: flex !important;

        > span + span + span {
            margin-right: 35px;
            margin-left: auto !important;
        }
    }

    [data-status] {
        padding: 5px 8px;
        background: #ffeb78;
        color: #4f4700;
        font-size: 11px;
        line-height: 13px;
        border-radius: 4px;
    }

    [data-status="Fulfilled"] {
        color: #014b40;
        background: #affebf;
    }

    [data-product-id] {
        cursor: pointer;

        & + .mc-title {
            margin-top: 5px;
        }
    }
}

.mc-shopify-cart > a > span:first-child, .mc-shopify-orders [data-product-id] > span:first-child {
    padding: 3px 5px;
    background: #00000012;
    font-size: 11px;
    line-height: 13px;
    margin-right: 10px;
    border-radius: 4px;
}

.mc-shopify-cart > a > span:last-child {
    float: right;
}

.mc-editor .mc-bar-icons > .mc-btn-shopify:before {
    content: "\e900";
    font-size: 25px;
    left: 5px;
}


/*
 *
 * -----------------------------------------------------------
 * USERS AREA
 * -----------------------------------------------------------
 *
*/

.mc-area-users {
    height: 100%;

    .mc-scroll-area {
        overflow: hidden;
        overflow-y: scroll;
        margin: 15px 0 0 15px;
        padding-right: 15px;
        height: calc(100% - 85px);

        &:hover + .mc-scroll-bar {
            opacity: 0.1 !important;
        }
    }

    > .mc-loading-pagination {
        position: absolute;
        bottom: 10px;
        left: 50%;
    }

    .mc-filter-btn {
        > div {
            left: auto;
            right: 0;
            top: 56px;
            width: auto;
            padding: 15px;
            z-index: 5;

            .mc-select:last-child {
                margin-right: 0;
            }
        }

        &.mc-active {
            position: relative;

            i {
                top: 10px;
                right: 0;
            }
        }
    }

    .mc-filter-star {
        display: none;
    }

    .mc-top-bar > div:first-child {
        overflow: visible !important;
    }
}

.mc-table-users {
    .mc-profile {
        font-weight: 600;
        font-size: 15px;
        text-transform: capitalize;
    }

    th:first-child {
        max-width: 15px;
        width: 15px;
        border-bottom-color: $border-color !important;
    }

    th {
        cursor: pointer;
        transition: $transition;
        position: relative;

        &.mc-active,
        &:hover {
            border-bottom: 1px solid rgb(131, 131, 131);
            color: rgb(0, 0, 0);
        }

        &.mc-active:after {
            content: "\61";
            font-family: "Masi Chat Icons";
            font-style: normal;
            font-weight: normal;
            font-variant: normal;
            text-transform: none;
            line-height: 25px;
            font-size: 12px;
            position: absolute;
            right: 15px;
        }

        &.mc-order-asc:after {
            transform: rotate(180deg);
        }
    }

    td:not(:first-child), th:not(:first-child) {
        overflow: hidden;
        text-overflow: ellipsis;
    }

    &.mc-loading {
        tbody, thead {
            display: none;
        }
    }
}

.mc-profile-box {
    .mc-profile-list {
        padding-right: 30px;
    }

    .mc-user-conversations > p {
        padding: 10px 15px 8px 15px;
    }

    .mc-top-bar {

        .mc-profile {
            font-weight: 600;

            img {
                width: 45px;
                height: 45px;
            }

            span {
                margin-left: 20px;
                font-size: 20px;
                line-height: 45px;
                white-space: nowrap;
            }
        }

        > div > [data-value] {

            & + .mc-btn {
                margin-left: 15px;
            }

            &:not(.mc-active) {
                display: none;
            }
        }
    }

    .mc-user-conversations {
        max-width: 600px;
        border: 1px solid $border-color;
        border-radius: 4px;

        > li {
            padding: 15px;
            border-left: none;

            &:first-child {
                border-top: none;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }

            &:last-child {
                border-bottom: none;
                border-bottom-left-radius: 4px;
                border-bottom-right-radius: 4px;
            }
        }

        .mc-profile {
            padding: 0 0 5px 0;

            .mc-name {
                font-size: 14px;
            }

            img {
                display: none;
            }

            .mc-time {
                float: right;
            }
        }
    }

    .mc-agent-area {
        padding-top: 30px;

        .mc-title {
            margin-bottom: 30px;
        }

        .mc-rating-area {
            min-height: 30px;

            > div {
                display: flex;
                height: 35px;
                align-items: center;

                > div:first-child {
                    font-weight: 500;
                    padding-right: 10px;
                    font-size: 13px;
                    width: 80px;
                }

                > span {
                    height: 3px;
                    background-color: $color-green;
                    margin-right: 10px;

                    &[data-count="0"] {
                        display: none;
                    }
                }

                & + div > span {
                    background-color: $color-red;
                }

                > div:last-child {
                    font-size: 13px;
                }
            }

            .mc-rating-count {
                font-size: 12px;
                line-height: 40px;
                color: $color-gray;
                opacity: 0.7;
            }

            &:before {
                left: 10px;
            }
        }
    }

    &.mc-type-agent .mc-start-conversation, &.mc-type-admin .mc-start-conversation, .mc-agent-area, .mc-hide + .mc-user-conversations {
        display: none;
    }

    &.mc-type-agent .mc-agent-area, &.mc-type-admin .mc-agent-area {
        display: block;
    }
}

.mc-profile-edit-box {
    .mc-top-bar .mc-profile {
        font-weight: 600;
        padding-left: 65px;
        padding-right: 15px;

        img {
            width: 45px;
            height: 45px;
        }

        span {
            font-size: 20px;
            line-height: 45px;
            white-space: nowrap;
        }
    }

    .mc-main {
        justify-content: flex-start;

        > div {
            width: 50%;

            & + div {
                margin-left: 30px;
            }
        }
    }

    &.mc-user-new {
        .mc-top-bar .mc-profile {
            padding-left: 0;

            img {
                display: none;
            }
        }

        .mc-delete {
            display: none;
        }
    }

    .mc-delete {
        margin-top: 60px;
        font-size: 14px;
        display: none;
    }

    #user_type,
    #department {
        display: none;
    }

    &.mc-agent-admin.mc-type-admin #user_type,
    &.mc-agent-admin.mc-type-admin #department,
    &.mc-agent-admin.mc-type-agent #user_type,
    &.mc-agent-admin.mc-type-agent #department,
    &.mc-agent-admin.mc-user-new #user_type {
        display: flex;
    }

    &.mc-agent-admin .mc-delete {
        display: inline-block;
    }
}

.mc-direct-message-subject:not(.mc-active) {
    display: none;
}

/*
 *
 * -----------------------------------------------------------
 * SETTINGS AREA
 * -----------------------------------------------------------
 *
*/

.mc-area-settings {

    .mc-input {
        align-items: flex-start;
        margin-bottom: 30px;
    }



    > .mc-tab {

        > .mc-content {
            padding-left: 30px;

            > div {
                max-width: 1000px;

                > .mc-setting + .mc-setting {
                    margin-top: 15px;
                    padding-top: 15px;
                    border-top: 1px solid rgb(230, 230, 230);
                }
            }

            .mc-inner-tab {
                > .mc-nav {
                    border: none;
                    padding: 0;
                }

                > .mc-content {
                    margin-top: -37px;
                    padding: 0;
                }
            }
        }
    }
}

#registration-fields .input > div:nth-child(2n) {
    margin-top: -10px;
}

#user-additional-fields .repeater-item > div:nth-child(2) {
    display: none !important;
}

#messenger-path-btn, #messenger-key, #open-ai-assistant-id {
    &:not(.mc-active) {
        display: none !important
    }
}

#tags {
    [data-id="tag-name"] {
        min-width: 230px;
        margin-right: 50px;
    }

    .repeater-item div + div {
        position: absolute;
        bottom: 0;
        right: 0;
        margin: 0;

        label {
            display: none;
        }
    }
}

.mc-type-multi-input > .input > div:not(.multi-input-textarea), .mc-setting .mc-language-switcher-cnt, .mc-select-checkbox > div, #open-ai-prompt {
    display: flex;
    align-items: center;

    > label {
        margin: 0 15px 0 0;
    }
}

#open-ai-prompt {
    align-items: baseline;
}

.mc-type-multi-input > .input > div {
    input, select {
        min-width: 285px;
    }
}

@media (max-width: 1490px) {
    .mc-type-multi-input > .input > div:not(.multi-input-textarea), .mc-setting .mc-language-switcher-cnt, .mc-select-checkbox > div {
        display: block;
    }
}

.mc-type-select-checkbox > .input, .multi-input-select-checkbox {
    position: relative;
}

.mc-select-checkbox-input {
    opacity: 1;
    cursor: pointer;

    &.mc-active + div {
        display: block;
    }
}

.mc-select-checkbox {
    position: absolute;
    z-index: 2;
    background: $white;
    right: 0;
    left: 0;
    top: 40px;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid $border-color;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.12);
    display: none;

    > div {
        margin: 5px 0;
    }

    label {
        margin: 0 0 0 15px !important;
    }
}

.mc-select.mc-select-colors {
    > p {
        border-radius: 4px;
        transition: none;
        background: none !important;

        span {
            width: 10px;
            height: 10px;
            display: inline-block;
            border-radius: 50%;
            margin-left: 10px;
            opacity: 1;
        }

        &:after {
            right: 15px;
        }

        &:not(:hover):after {
            color: $color-gray;
        }

        &:not([data-value]), &[data-value=""], &[data-value="-1"] {
            background-color: $white
        }
    }

    > ul {
        padding: 0 !important;
    }

    &:not(.mc-disabled) > p:hover span {
        background-color: $color-blue;
    }

    &.mc-loading {
        > ul {
            display: none;
        }

        &:before {
            line-height: 30px;
        }
    }

    &:hover:before {
        color: $white;
    }
}

.mc-setting .mc-language-switcher-cnt {
    margin-top: 10px;
}

.mc-setting p, .mc-text-gray {
    font-size: 13px;
    line-height: 22px;
    letter-spacing: 0.3px;
    margin: 5px 0 0 0;
    color: rgb(120, 134, 146);
}

.mc-setting {
    display: flex;
    justify-content: flex-start;

    > .mc-setting-content {
        max-width: 420px;
        width: 420px;
        padding-right: 60px;
        flex-shrink: 0;
    }

    > .input {
        width: 100%;
    }

    h2 {
        display: inline-block;
        min-width: 150px;
        flex-grow: 1;
        font-weight: 600;
        font-size: 15px;
        letter-spacing: 0.3px;
        margin: 0;
        color: $color-gray;
    }

    .mc-icon-help {
        transform: translateY(1px);
        margin: 0 0 0 5px;
        color: #889aaa;
        text-decoration: none;
        display: inline-block;

        &:hover {
            color: $color-blue;
        }
    }

    input, select, textarea {
        border-radius: 4px;
        border: 1px solid $border-color;
        outline: none;
        font-size: 13px;
        line-height: 35px;
        height: 35px;
        padding: 0 10px;
        transition: $transition;
        width: 100%;
        min-width: 300px;
        box-sizing: border-box;
        color: rgb(36, 39, 42);
        background-color: $white;

        &:focus {
            border: 1px solid $color-blue;
            box-shadow: 0 0 5px rgba(39, 156, 255, 0.2);
            color: rgb(36, 39, 42);
            outline: none;
        }

        &.mc-error {
            border: 1px solid $color-red;
            box-shadow: 0 0 5px rgba(202, 52, 52, 0.25);
        }
    }

    select {
        padding: 0 5px;
    }

    textarea {
        box-sizing: content-box;
        width: calc(100% - 22px);
        min-width: 280px;
        line-height: 22px;
        padding: 6px 10px;
        min-height: 86px;
    }

    input[type="checkbox"] {
        background: $white;
        clear: none;
        cursor: pointer;
        display: inline-block;
        line-height: 0;
        height: 35px;
        min-height: 35px;
        outline: 0;
        padding: 0;
        margin: 0;
        text-align: center;
        vertical-align: middle;
        width: 35px;
        min-width: 35px !important;
        outline: none;
        box-shadow: none;
        -webkit-appearance: none;

        &:checked:before {
            content: "\77" !important;
            font-family: "Masi Chat Icons" !important;
            font-style: normal !important;
            font-weight: normal !important;
            font-variant: normal !important;
            text-transform: none !important;
            line-height: 35px;
            font-size: 15px;
            color: $color-blue;
            margin: 0;
            width: 100%;
            height: 100%;
        }
    }

    label {
        color: $color-gray;
        font-weight: 600;
        font-size: 14px;
        line-height: 23px;
        margin: 0 30px 15px 0;
        display: block;
        min-width: 220px;
        letter-spacing: 0.3px;

        &:empty {
            display: none !important;
        }
    }

    input[type="number"] {
        padding-right: 0;
    }

    .input > .mc-loading:not(.mc-btn):not(.image) {
        width: 30px;
        height: 30px;
    }

    .multi-input-upload-image[data-type="upload-image"] .image {
        min-width: 50px;
        width: 100%;
    }

    &.mc-type-multi-input .input > div {
        margin-bottom: 15px;
    }

    &.mc-type-input-button .input {
        display: flex;
        width: 100%;

        input {
            min-width: 0;
            flex-shrink: 10;
        }

        a {
            margin-left: 15px;
            min-width: 0;
        }
    }

    &.mc-type-upload-image, [data-type="upload-image"] {
        .image {
            border-radius: 4px;
            border: 1px solid $border-color;
            height: 150px;
            position: relative;
            cursor: pointer;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            transition: $transition;

            &:before {
                content: "\70";
                font-family: "Masi Chat Icons" !important;
                position: absolute;
                left: 50%;
                top: 50%;
                font-size: 9px;
                color: $color-gray;
                width: 30px;
                height: 30px;
                line-height: 30px;
                margin: -15px 0 0 -15px;
                text-align: center;
                transform: rotate(45deg);
            }

            &:hover {
                border: 1px solid $color-blue;
                box-shadow: 0 0 5px rgba(39, 156, 255, 0.2);

                &:before {
                    color: $color-blue;
                }
            }

            > i {
                position: absolute;
                z-index: 9;
                right: 10px;
                top: 10px;
                height: 21px;
                width: 21px;
                font-size: 7px;
                text-align: center;
                line-height: 23px;
                cursor: pointer;
                background: $color-gray;
                color: $white;
                border-radius: 50%;
                display: none;
            }

            &[data-value] {
                &:before {
                    display: none;
                }

                > i {
                    display: block;

                    &:hover {
                        background-color: $color-red;
                    }
                }
            }

            &[data-value=""] {
                > i {
                    display: none;
                }

                &:before {
                    display: block;
                }
            }
        }
    }

    .multi-input-upload-image .image {
        height: 100px;
        background-size: auto 64px;
    }

    &.mc-type-color {
        .input {
            position: relative;

            i {
                position: absolute;
                right: 12px;
                font-size: 10px;
                top: 11px;
                cursor: pointer;
                opacity: 0.5;
                z-index: 2;

                &:hover {
                    opacity: 1;
                }
            }

            &:after {
                content: "";
                position: absolute;
                background: $white;
                right: 1px;
                top: 1px;
                width: 32px;
                height: 33px;
                border-top-right-radius: 3px;
                border-bottom-right-radius: 3px;
                z-index: 0;
            }
        }
    }

    &.mc-type-double-select .input {
        > div {
            display: flex;
            align-items: center;
            margin: 0 0 8px 0;
        }

        label {
            margin: 0 30px 0 0;
            font-weight: 400;
            font-size: 13px;
            color: $color-black;
        }

        select {
            min-width: 100px;
        }
    }

    &.mc-type-repeater > .input {
        width: 100%;
    }

    .mc-repeater {
        margin-bottom: 15px;
        display: block;
    }

    .repeater-item {
        position: relative;

        .mc-repeater-add {
            padding: 0;
            margin-top: -5px;
            width: 30px;
        }

        & + .repeater-item {
            border-top: 1px solid rgb(226, 226, 226);
            padding-top: 20px;
            margin-top: 20px;
        }

        > div {
            display: flex;

            & + div {
                margin-top: 10px;
            }

            &:empty {
                display: none;
            }

            [readonly] {
                min-width: 0 !important;
                border: none;
                padding: 0;
                text-align: center;
                font-weight: 500;
                opacity: .5;
                width: 20px;

                &:hover {
                    opacity: 1;
                }
            }

            label {
                margin: 0 15px 0 0;

                & + div {
                    flex-grow: 1;
                }
            }
        }

        input, select {
            min-width: 285px;
        }

        textarea {
            min-width: 260px;
        }

        .mc-enlarger {
            &:before {
                font-size: 13px;
                top: 4px;
                transition: $transition;
            }

            &:hover:before {
                color: $color-blue;
            }
        }

        > i {
            position: absolute;
            right: -30px;
            top: 50%;
            margin-top: 4px;
            width: 14px;
            font-size: 9px;
            height: 14px;
            line-height: 16px;
            text-align: center;
            cursor: pointer;

            &:hover {
                color: $color-red;
            }
        }

        &:first-child > i {
            margin-top: -6px;
        }
    }

    .mc-repeater-add + .mc-btn-icon {
        transform: translate(11px, 13px);
        margin-top: -35px;

        &.mc-icon-clip:before {
            font-size: 16px;
        }
    }

    input + input {
        margin-top: 8px;
    }

    &.mc-type-select-images .input {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
        grid-gap: 27px;

        > div {
            width: 80px;
            height: 80px;
            background-size: contain;
            background-position: center center;
            background-repeat: no-repeat;
            border-radius: 4px;
            border: 1px solid $border-color;
            position: relative;
            cursor: pointer;
            opacity: .6;
            transition: all 0.4s;

            &.mc-icon-close {
                text-align: center;
                line-height: 82px;
                color: $border-color;
                border: none !important;
                box-shadow: none !important;
                font-size: 20px;
                opacity: 1;
            }

            &:hover, &.mc-active:not(.mc-icon-close) {
                color: $color-blue;
                border-color: $color-blue;
                box-shadow: 0 0 5px rgba(39, 156, 255, 0.2);
                opacity: 1;
            }
        }
    }

    [data-type="upload-file"] {
        width: 100%;
        overflow: hidden;

        input {
            min-width: 100px;
        }

        .mc-btn {
            margin-left: 5px;
            flex: 0 0 auto;
        }
    }

    #push-notifications-onesignal-app-id, #push-notifications-onesignal-api-key, #push-notifications-sw-path {
        &:not(.mc-active) {
            display: none !important;
        }
    }
}

.repeater-item {

    .mc-enlarger {
        label {
            min-width: 160px;
        }
    }

    .repeater-item {
        > div {

            input, select, textarea {
                &:last-child {
                    margin-bottom: -1px;
                    border-bottom-right-radius: 0;
                    border-bottom-left-radius: 0;
                }
            }


            & + div {
                margin-top: 0;

                input, select, textarea {
                    &:first-child {
                        border-top-right-radius: 0;
                        border-top-left-radius: 0;
                    }
                }
            }

            &:last-of-type {
                input, select, textarea {
                    border-bottom-right-radius: 4px;
                    border-bottom-left-radius: 4px;
                }
            }
        }

        input:focus, select:focus, textarea:focus {
            z-index: 2;
        }

        .mc-sub-repeater-close {
            right: 10px;
            background: $white;
            border-radius: 50%;
            color: $color-gray;
            z-index: 9;
        }

        & + .repeater-item {
            border-top: none;
            padding-top: 0;
            margin-top: 10px;

            > i {
                margin-top: -6px;
            }
        }
    }
}

.mc-translations, .mc-automations-area {
    .mc-setting {
        display: block;
        margin-bottom: 15px;
    }
}

.mc-type-select.mc-loading select {
    visibility: hidden;
    transition: none;
}

.mc-area-settings .mc-tab .mc-btn, .mc-btn-white, a.mc-btn-white, .mc-lightbox .mc-btn-white {
    background-color: $white;
    color: rgb(86, 96, 105);
    border: 1px solid rgb(204, 210, 213);
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.12);
    padding: 0 10px;
    white-space: nowrap;
    border-radius: 4px;
    height: 33px;
    line-height: 33px;

    &.mc-icon {
        padding-left: 30px;

        > i {
            left: 10px;
            font-size: 10px;
            line-height: 33px;

            &:before {
                line-height: 33px;
            }
        }
    }

    &:hover {
        color: $white;
        border-color: $color-blue;
        background-color: $color-blue;
    }

    &.mc-loading {
        width: 15px;
        background: $white;
        color: $color-blue;
        border-color: #ccd2d5;
        cursor: default;

        &:before, &:hover:before {
            color: rgb(38, 67, 92);
        }
    }
}

.mc-translations {
    width: 100%;

    h2 {
        font-size: 15px;
        line-height: 27px;
        margin: 2px 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }


    .mc-content {
        padding: 0;

        > div:first-child {
            display: flex;
            justify-content: space-between;
        }

        &.mc-loading:before {
            top: 15px;
        }
    }

    .mc-translations-list {
        padding-top: 30px;

        > div:not(.mc-active) {
            display: none;
        }
    }

    .mc-nav {
        padding: 0;
        border: none;

        li {
            padding-left: 30px !important;
            position: relative;

            img {
                position: absolute;
                left: 0;
                width: 17px;
                top: 13px;
                border-radius: 2px;
                box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3);
            }
        }
    }

    .mc-new-translation {
        margin-bottom: 25px;
    }
}

.mc-automations-area {
    > .mc-tab > .mc-content {
        margin-top: -35px;
    }

    .mc-nav-only {
        margin-right: 30px;
    }
}

.mc-automation-values {
    [data-id="profile_image"], [data-id="icon"], [data-id="brand"] {
        width: 70px;
        height: 70px;
        max-height: 70px;
    }
}

[data-automation-type="more"] .mc-automation-values > h2 + div + h2, [data-automation-type="more"] .mc-automation-values > h2 + div + h2 + div {
    display: none;
}

.mc-conditions, .mc-flow-conditions {
    > div {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        grid-gap: 15px;
    }

    select, input {
        min-width: 50px;
    }
}

.mc-timetable {
    > div > div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;

        span {
            padding: 0 10.5px;
            font-size: 12px;
        }

        & > div {
            font-size: 13px;
            line-height: 30px;
            height: 30px;
            min-width: 88px;
            padding: 0 0 0 7px;
            position: relative;
            border-radius: 4px;
            border: 1px solid $border-color;
            transition: $transition;

            &:after {
                content: "\61";
                font-family: "Masi Chat Icons";
                position: absolute;
                right: 8px;
                font-size: 9px;
                font-style: normal;
                font-weight: normal;
                line-height: 30px;
            }

            &:hover {
                cursor: pointer;
                border: 1px solid rgb(2, 139, 229);
                box-shadow: 0 0 5px rgba(39, 156, 255, 0.2);
            }
        }
    }

    > div + div {
        margin-top: 12px;
    }

    label {
        min-width: 95px;
        margin: 0 0 10px 0;
    }

    .mc-custom-select {
        position: absolute;
        top: 31px;
        left: -1px;
        width: 95px;
        margin: 0;
        background: $white;
        border-radius: 4px;
        border: 1px solid $border-color;
        z-index: 9;
        margin: 2px 0 0 0;
        font-size: 14px;
        line-height: 25px;
        height: 250px;
        overflow-y: scroll;

        span {
            display: block;
            cursor: pointer;
            padding: 5px;
            white-space: nowrap;
            min-height: 22px;

            &:hover {
                background-color: $color-blue;
                color: $white;
            }

            &:empty {
                position: relative;

                &:before {
                    content: "--";
                    position: absolute;
                    left: 5px;
                }
            }
        }
    }

    > .mc-custom-select {
        display: none;
    }
}

.mc-color-palette {
    background: none !important;

    span {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        cursor: pointer;
        display: block;
        transition: $transition;
        position: relative;

        &:hover {
            opacity: 0.7;
        }

        &.mc-active + ul {
            display: block;
        }
    }

    ul {
        padding: 5px 10px;
        position: absolute;
        margin: 15px 0 0 -10px;
        display: none;

        li {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            margin: 5px 0;
            transition: $transition;

            &:hover {
                opacity: 0.7;
            }

            &[data-value=""] {
                box-shadow: inset 0px 0px 1px rgb(145, 145, 145);
                margin-bottom: 6px;
            }
        }
    }
}

.mc-color-palette:not([data-value]) span,
.mc-color-palette[data-value=""] span {
    background-color: #f2f5f5;
}

[data-color="red"],
[data-value="red"],
[data-value="red"] > span {
    background-color: #e22424;
    color: $white;
}

[data-color="yellow"],
[data-value="yellow"],
[data-value="yellow"] > span {
    background-color: #f5a212;
    color: $white;
}

[data-color="green"],
[data-value="green"],
[data-value="green"] > span {
    background-color: #1fa839;
    color: $white;
}

[data-color="pink"],
[data-value="pink"],
[data-value="pink"] > span {
    background-color: #5b31f1;
    color: $white;
}

[data-color="gray"],
[data-value="gray"],
[data-value="gray"] > span {
    background-color: #282828;
    color: $white;
}

[data-color="blue"], [data-value="blue"], [data-value="blue"] > span {
    background-color: #008db5;
    color: $white;
}

[data-color-text="red"] {
    color: #e22424;
}

[data-color-text="yellow"] {
    color: #f5a212;
}

[data-color-text="green"] {
    color: #1fa839;
}

[data-color-text="pink"] {
    color: #5b31f1;
}

[data-color-text="gray"] {
    color: #282828;
}

[data-color-text="blue"] {
    color: #008db5;
}

.mc-select-colors {
    & > [data-value="red"] {
        color: #e22424;
    }

    & > [data-value="yellow"] {
        color: #f5a212;
    }

    & > [data-value="green"] {
        color: #1fa839;
    }

    & > [data-value="pink"] {
        color: #5b31f1;
    }

    & > [data-value="gray"] {
        color: #282828;
    }

    & > [data-value="blue"] {
        color: #008db5;
    }
}

.mc-board > .mc-admin-list .mc-scroll-area {
    li[data-color="red"]:before {
        background-color: #e22424;
    }

    li[data-color="yellow"]:before {
        background-color: #f5a212;
    }

    li[data-color="green"]:before {
        background-color: #1fa839;
    }

    li[data-color="pink"]:before {
        background-color: #5b31f1;
    }

    li[data-color="gray"]:before {
        background-color: #282828;
    }

    li[data-color="blue"]:before {
        background-color: #008db5;
    }
}

#departments {
    > .input {
        width: 100%;
    }

    .repeater-item {
        display: flex;
        border: none;
        padding-top: 0;

        > div {
            margin: 0;
        }

        > div:first-child {
            min-width: calc(100% - 128px);
        }

        input:not([readonly]) {
            min-width: 100px !important;
        }

        > div + div {
            padding-left: 15px;

            label {
                display: none;
            }
        }

        > i {
            top: 50%;
            margin-top: -7px;
        }

        label {
            margin-bottom: 0;
            line-height: 35px;
            min-width: 80px;
        }

        .image {
            width: 33px;
            height: 33px;
            border-radius: 50%;

            &:before {
                font-size: 7px;
                color: rgb(182, 182, 182);
                margin: -15px 0 0 -16px;
            }

            &:hover:before {
                color: $color-blue;
            }
        }
    }
}

#chat-mc-icons .input > div:not(.mc-icon-close) {
    background-color: $border-color;
    border-radius: 50%;
}

#chat-mc-icons .input > .mc-active:not(.mc-icon-close), #chat-mc-icons .input > div:not(.mc-icon-close):hover {
    background-color: $color-blue !important;
}

.mc-open-ai-faq-box {
    top: 30px !important;
    bottom: 30px;
    margin-top: 0 !important;
    max-height: none !important;

    textarea {
        min-height: 66px;
    }
}

/*
 *
 * -----------------------------------------------------------
 * REPORTS AREA
 * -----------------------------------------------------------
 *
*/

.mc-area-reports {
    > .mc-tab {
        > .mc-content {
            display: flex;
            padding-left: 0;

            > .mc-reports-chart {
                width: 80%;
                padding-left: 15px;
            }

            > .mc-reports-sidebar {
                flex: 1 1 auto;
                max-width: 280px;
                padding: 5px 0 0 15px;

                .mc-collapse {
                    padding-bottom: 30px;
                }
            }

            &.mc-no-results-active {
                overflow-y: hidden;
                justify-content: center;
                align-items: center;
            }

            > .mc-no-results {
                display: none;
            }
        }

        .mc-nav > ul li:not(.mc-tab-nav-title) {
            font-weight: 400;
            font-size: 14px;
            line-height: 30px;
        }
    }

    p {
        font-size: 13px;
        line-height: 22px;
        letter-spacing: 0.3px;
        margin: 5px 0 15px 0;
        color: rgb(120, 134, 146);
    }

    .mc-table th, .mc-table td {
        padding: 9px 0;
    }

    table td {
        font-size: 13px;
    }

    .mc-reports-text {
        margin-top: 15px;
    }

    .mc-report-export {
        margin-left: 5px;

        &:not(:hover) {
            border: 1px solid $border-color;
            opacity: 1;
        }
    }

    td {
        .mc-icon-check, .mc-icon-close, .mc-icon-like, .mc-icon-dislike {
            color: $color-green;
            font-size: 13px;
            transform: translateY(1px);
            display: inline-block;
        }

        .mc-icon-close, .mc-icon-dislike {
            color: $color-red;
        }

        .mc-icon-close {
            font-size: 11px;
        }

        div {
            margin: 5px 0 0 0;
        }

        img {
            border-radius: 3px;
            width: 20px;
            margin: 0 10px 0 0;
            transform: translateY(2px);

            &.mc-flag {
                height: 13px;
            }
        }

        &:first-child > div {
            margin: 0 15px 0 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: normal;
        }
    }
}

.chart-cnt {
    padding-bottom: 30px;
}

#mc-date-picker {
    text-align: center;
    font-weight: 500;
    min-width: 255px;
}

.mc-report-agents-ratings, .mc-report-articles-ratings {
    td .mc-icon-check, td .mc-icon-like {
        margin: 0 10px 0 0;
    }

    td .mc-icon-close, td .mc-icon-dislike {
        margin: 0 10px 0 15px;
    }
}

/*
 *
 * -----------------------------------------------------------
 * CHATBOT AREA
 * -----------------------------------------------------------
 *
*/
.mc-qea-repeater-answer {
    > label {
        position: relative;

        i {
            position: absolute;
            right: 0;
            top: 0;

            &:before {
                font-size: 17px;
                line-height: 35px;
            }

            &:not(.mc-active) {
                display: none;
            }
        }
    }
}

.mc-area-chatbot {
    .mc-repeater {
        margin-right: 30px;

        .mc-repeater {
            margin-right: 0;
        }
    }

    > [data-id]:not(.mc-active) {
        display: none;
    }
}

#mc-table-chatbot-files, #mc-table-chatbot-website {
    letter-spacing: .3px;

    td {
        &:first-child, &:last-child {
            max-width: 25px;
            width: 25px;
        }

        &:last-child {
            padding-right: 0;

            i {
                right: 5px;
                top: 12px;
            }
        }
    }

    td {
        position: relative;
        transition: background-color .4s;

        &:nth-child(3) {
            width: 100px;
            max-width: 100px;
        }
    }

    label {
        letter-spacing: 0.3px;
        font-size: 12px;
        line-height: 12px;
        color: $color-yellow;
        font-weight: 500;
        margin: 0 15px;
    }

    &:empty + div .mc-btn-red {
        display: none;
    }
}

.mc-enlarger-function-calling.mc-active > div, .mc-enlarger-rest-api.mc-active > div {
    display: flex;

    &:not(:last-child) {
        margin-bottom: 10px;
    }
}

[data-id="open-ai-faq-set-data"] {
    .mc-setting {
        width: 50%;

        & + .mc-setting {
            margin-left: 10px;
        }
    }
}

#mc-chatbot-info {
    min-height: calc(100% - 2px);

    p {
        line-height: 30px;
        font-size: 13px;
    }
}

[data-id="playground"] {
    height: 100%;
}

[data-id="playground"] > div {
    height: calc(100% - 70px);
}

[data-id="playground"] > div > div {
    height: calc(100% - 35px);
    padding: 15px 20px 20px 20px;
    position: relative;
}

.mc-playground {
    flex-grow: 1;

    .mc-scroll-area {
        height: calc(100% - 110px);
        letter-spacing: .3px;

        > div {
            margin: 0 15px 0 0;
            min-width: 70px;
            animation: none !important;

            > div:first-child {
                font-weight: 500;
                font-size: 13px;
                text-transform: uppercase;
                position: relative;
                margin-bottom: 5px;

                div {
                    font-weight: 400;
                    position: absolute;
                    right: 0;
                    top: 0;
                    opacity: 0;
                    transition: $transition;
                }

                i {
                    width: 17px;
                    height: 17px;

                    &:before {
                        font-size: 10px;
                        line-height: 18px;
                    }
                }
            }

            > div:last-child, > div a:not(.mc-btn) {
                color: $color-gray;
                font-size: 13px;
                line-height: 21px;
            }


            &:hover div div {
                opacity: 1;
            }

            & + div {
                margin-top: 15px;
            }
        }

        .mc-menu-btn, .mc-menu, .mc-time {
            display: none !important;
        }

        &:empty {
            height: 0;

            + .mc-no-results {
                display: block;
            }
        }

        .mc-rich-message {
            .mc-slider {
                margin: 0;
            }

            .mc-card .mc-card-img + div + .mc-card-extra {
                top: 45px;
                left: 25px;
            }
        }
    }

    .mc-no-results {
        display: none;
        min-height: 50px;
    }

    .mc-rich-cnt[data-type="chips"] .mc-text {
        padding: 0;
    }
}

#mc-playground-query-panel {
    max-width: 800px;

    pre {
        text-align: left;
        margin: 0;
        line-height: 20px;
        font-size: 15px;
    }
}

#mc-playground-embeddings-panel p {
    text-align: left;
    font-size: 13px;
    line-height: 20px;
    font-weight: 400;

    b {
        font-weight: 500;
    }

    a {
        margin: 0;
        color: $color-black;
    }

    span {
        display: block;

        & + span {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid $border-color;
        }
    }

    span span {
        line-height: 20px;
        margin-top: 10px;
    }
}

.mc-playground-info {
    min-width: 250px;
    border-left: 1px solid $border-color;
    line-height: 25px;
    font-size: 13px;
    letter-spacing: .3px;

    b {
        font-weight: 500;
    }

    .mc-btn-text {
        text-decoration: underline;
        margin-top: 15px;

        & + .mc-btn-text {
            margin-left: 15px;
        }
    }
}

.mc-playground-editor {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;

    > .mc-flex {
        justify-content: space-between;
        margin: 15px 0 0 0;

        [data-value=add]:not(:hover) {
            opacity: .6;
        }
    }

    textarea {
        min-height: 25px;
    }

    [data-value="clear"]:before {
        font-size: 10px;
    }
}

[data-id="flows"] {

    > .mc-content {
        overflow: hidden;
        overflow-x: scroll;
        position: relative;
        display: flex;
        padding-top: 0;
        padding-bottom: 0;

        > div {
            width: 200px;
            min-width: 200px;
            margin-right: 50px;
            display: flex !important;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            position: relative;


            > div:not(.mc-flow-add-step) {
                overflow-y: scroll;
                -ms-overflow-style: none;
                scrollbar-width: none;
                padding: 20px 0;
                width: 100%;
            }

            &:first-child .mc-flow-add-block, &:last-child .mc-flow-add-step {
                display: none;
            }
        }
    }
}

.mc-flow-add-block {
    height: 40px;
    text-align: center;
    line-height: 45px;
    border-radius: 4px;
    cursor: pointer;
    border: 1px dashed $color-gray;
    position: relative;
    transition: $transition;

    &:hover {
        background: $color-blue;
        border-color: $color-blue;

        &:before {
            color: $white;
        }
    }
}

.mc-flow-add-step {
    position: absolute;
    right: -40px;
    width: 30px;
    height: 30px;
    top: 50%;
    margin-top: -15px;
    text-align: center;
    line-height: 35px;
    border-radius: 50%;
    cursor: default;
    transition: $transition;
}

.mc-flow-block {
    border-radius: 4px;
    border: 1px solid $border-color;
    transition: $transition;
    background: $white;
    cursor: pointer;
    letter-spacing: .3px;

    > div {
        padding: 10px;
        font-size: 13px;
        line-height: 23px;
        user-select: none;
        position: relative;

        &:first-child {
            transition: $transition;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            user-select: none;
        }

        & + div {
            transition: $transition;
            border-top: 1px solid $border-color;

            &:empty {
                display: none;
            }
        }
    }

    & + div {
        margin-top: 10px;
    }

    &:hover {
        border-color: $color-blue;
    }

    &[data-type="video"] > div + div {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    &[data-type="set_data"] > div, &[data-type="action"] > div, &[data-type="rest_api"], &[data-type="condition"] > div {
        > div {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }
}

.mc-flow-connectors {
    padding: 0 !important;

    > div {
        padding: 5px 50px 5px 10px;
        position: relative;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 30px;
        transition: $transition;

        &:hover, &.mc-active {
            color: $color-blue;
        }

        & + div {
            border-top: 1px solid $border-color;
        }
    }

    &:empty {
        display: none;
    }
}

.mc-flow-connectors > div > span, .mc-flow-block-cnt-name {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 35px;
    font-size: 11px;
    letter-spacing: 1px;
    font-weight: 500;
    line-height: 42px;
    text-align: center;
    border-left: 1px solid $border-color;
}

#mc-flows-blocks-nav {
    position: fixed;

    &:not(.mc-active) {
        display: none;
    }
}

.mc-flow-block-cnt {
    background: $background-gray;
    padding: 10px;
    border-radius: 4px;
    position: relative;
    transition: $transition;

    & + .mc-flow-block-cnt {
        margin-top: 15px;
    }

    &.mc-active {
        background: #E6F2FC;

        .mc-flow-block-cnt-name {
            color: $color-blue;
            border-color: $color-blue;
        }
    }
}

.mc-flow-block-cnt-name {
    left: auto;
    right: 0;
    bottom: auto;
    background: $background-gray;
    border-radius: 4px;
    line-height: 25px;
    width: 30px;
    border: 1px solid $border-color;
    transition: $transition;
    z-index: 1;
}

.mc-flows-blocks-nav-box {
    background: none;
    box-shadow: none;
    width: 170px !important;

    .mc-top-bar {
        padding: 0 !important;
        border: none !important;
        margin: 0 0 10px 0 !important;

        .mc-close {
            background-color: $white;
            opacity: 1;
            padding: 0;
            z-index: 0;
        }
    }

    .mc-main {
        padding: 0 !important;
    }

    .mc-menu {
        width: 100%;
        position: relative;
    }
}

#mc-block-delete {
    margin-top: 60px;
    font-size: 15px;
    letter-spacing: .3px;

    &:hover {
        color: $color-red;
    }
}

.mc-flow-block-box {
    height: calc(100% - 50px) !important;

    .mc-setting + .mc-setting {
        margin-top: 30px;
    }

    .mc-setting .repeater-item + .repeater-item, .mc-flow-conditions > div + div {
        border-top: none;
        padding-top: 10px;
        margin-top: 0;

        > i {
            margin-top: -1px;
        }
    }

    .mc-title {
        font-size: 14px;
        font-weight: 500;
        letter-spacing: .3px;

        &.mc-hide + .mc-setting {
            display: none;
        }
    }

    .mc-flow-conditions {
        .mc-setting {
            margin-top: 0;
        }

        &:empty + div {
            margin-top: 0;
        }

        [value="custom_variable"], [value="repeat"], [value="browsing_time"], [value="scroll_position"] {
            display: none !important;
        }
    }

    .mc-main .mc-add-condition {
        margin: 10px 0 30px 0;
        width: auto;
    }
}

.mc-repeater-block-user-details {
    .repeater-item {
        > div {
            display: block;
        }

        & + .repeater-item {
            padding-top: 20px !important;
        }
    }

    .mc-setting {
        label {
            margin: 0 15px 0 0;
            min-width: 0;
            line-height: 25px;
            font-weight: 400;
            font-size: 13px;
        }

        & + .mc-setting {
            margin: 10px 0 0 0;
            align-items: center;
        }
    }

    input[type="checkbox"] {
        height: 25px;
        min-height: 25px;
        width: 25px;
        min-width: 25px !important;

        &:checked:before {
            line-height: 25px;
            font-size: 12px;
        }
    }
}

.mc-repeater-block-data, .mc-repeater-block-actions, .mc-repeater-block-rest-api {
    .mc-setting + .mc-setting {
        margin: 0 0 0 15px;
    }
}

.mc-repeater-block-actions input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    display: none;
}

.mc-flow-scroll {
    position: fixed;
    z-index: 1;
    bottom: 15px;
    right: 15px;
    width: 35px;
    line-height: 40px;
    height: 35px;
    padding: 0;
    border-radius: 50%;
    background: $white;
    text-align: center;
    cursor: pointer;
    transition: $transition;

    &:not(.mc-active) {
        display: none;
    }

    &:before {
        color: $color-gray;
    }

    &:hover {
        background-color: $color-blue;
        border-color: $color-blue;

        &:before {
            color: $white;
        }
    }

    &.mc-icon-arrow-left {
        right: 60px;
    }
}

.mc-flow-start-messages [data-id="message"] {
    min-height: 45px;
}

/*
 *
 * -----------------------------------------------------------
 * ARTICLES AREA
 * -----------------------------------------------------------
 *
*/

.mc-area-articles {

    > .mc-tab > .mc-content {

        .mc-loading:before {
            left: 20px;
            top: 30px;
        }

        .mc-article-content {
            padding-left: 40px;
            max-width: 1326px;
        }
    }

    .mc-add-category, .ul-categories {
        display: none !important;
    }

    #editorjs {
        margin-top: -5px;
    }

    &[data-type="categories"] {
        .mc-nav {

            .mc-add-category {
                display: inline-block !important;
            }

            .ul-categories {
                display: block !important;
            }

            .ul-articles, .mc-add-article, & + .mc-content-articles {
                display: none !important;
            }

            & + div + .mc-content-categories:not(.mc-hide) {
                display: block !important;
            }
        }

        .mc-view-article {
            display: none;
        }
    }

    .mc-content-categories {
        display: none !important;

        .mc-setting {
            display: block;
        }
    }

    #mc-article-id {
        display: block;
        margin-bottom: 0;

        span {
            font-weight: 400;
            padding-left: 15px;
        }
    }

    .mc-setting + h2, .mc-article-categories, #mc-article-id {
        margin-top: 30px;
    }
}

.mc-content-articles:not([data-id]) .mc-language-switcher {
    display: none;
}



.mc-article-categories .mc-setting > div {
    width: 100%;
}

.mc-view-article[href=""] {
    display: none;
}

#category-image {
    max-width: 100px;
    background-size: 60px;
}

.mc-menu-articles .mc-docs, .mc-menu-chatbot .mc-docs {
    text-decoration: none;
    color: $color-gray;
    line-height: 42px;
    opacity: .8;
}

/*
 *
 * -----------------------------------------------------------
 * MISCELLANEOUS
 * -----------------------------------------------------------
 *
*/


#mc-whatsapp-alert-btn {
    transform: translate(10px, 2px);
    display: inline-block;
    transition: $transition;

    &:hover {
        opacity: .6;
    }
}

.mc-btn-app-disable {
    display: none !important;
}

@keyframes mc-lightbox-animation {
    0% {
        transform: translateY(-50px);
        opacity: 0;
    }

    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

[data-provider="twilio"] .mc-whatsapp-box-header, [data-provider="twilio"] .mc-whatsapp-box-header + div {
    display: none;
}

.mc-lightbox {
    position: fixed;
    width: calc(100% - 50px);
    height: auto;
    padding: 0;
    max-width: 700px;
    max-height: 700px;
    left: 50%;
    top: 50%;
    margin-left: 150px;
    margin-top: 150px;
    border-radius: 6px;
    display: none;
    transition: opacity 0.4s, transform 0.4s;

    &.mc-active {
        display: block;
        animation: mc-lightbox-animation 0.5s;
    }

    &.mc-dialogflow-intent-box, &.mc-updates-box {
        height: calc(100% - 100px);
    }

    .mc-top-bar {
        padding: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid $border-color;
        margin-bottom: 30px;

        > div {
            display: flex;
            align-items: center;

            &:first-child {
                font-weight: 600;
                font-size: 20px;
                line-height: 45px;
                white-space: nowrap;
                overflow: hidden;
                margin-right: 15px;

                a {
                    text-decoration: none;

                    i {
                        font-size: 13px;
                        color: #b1c2d1;
                        margin-left: 15px;

                        &:hover {
                            color: $color-blue;
                        }
                    }
                }
            }
        }
    }

    .mc-main {
        display: block;
        padding: 0 20px 30px 20px;
        justify-content: space-between;
        height: calc(100% - 145px);

        > div {
            height: auto;
            width: 100%;
        }

        > .mc-title + *, > div > .mc-title + *, p + .mc-title {
            margin-top: 27px;

            *:first-child {
                margin-top: 0;
            }
        }

        > .mc-setting + .mc-title, > div > .mc-setting + .mc-title {
            margin-top: 30px;
        }

        p {
            font-size: 14px;
            line-height: 24px;
            letter-spacing: 0.3px;
            margin: 0;
            color: $color-gray;

            a:not([class]) {
                color: $color-blue;
                text-decoration: none;
            }

            b {
                font-weight: 500;
            }
        }

        > .mc-bottom {
            padding-top: 30px;
            display: flex;
            align-items: center;
        }
    }

    .mc-info {
        padding: 20px;
        background: $color-red;
        color: $white;
        font-size: 14px;
        line-height: 23px;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
        letter-spacing: 0.3px;
        position: absolute;
        right: 0;
        left: 0;
        z-index: 5;
        height: 45px;
        font-weight: 600;
        cursor: pointer;
        align-items: center;
        display: none;

        a {
            color: #FFF;
            padding-left: 4px;
        }

        &.mc-active {
            display: flex;
            animation: mc-fade-animation 0.4s;
        }
    }

    .mc-repeater {
        width: calc(100% - 25px);
    }

    &.mc-lightbox-media {
        animation: none;
        background: #000;
    }
}

.mc-lightbox-overlay {
    z-index: 995 !important;
    background-color: rgba(255, 255, 255, 0.75) !important;

    &.mc-active {
        display: block;
    }
}

.mc-lightbox-media.mc-active + .mc-lightbox-overlay {
    background-color: rgba(0,0,0,0.6) !important;
}

.mc-profile-edit-box, .mc-profile-box {
    height: calc(100% - 50px);
    max-width: 1000px;

    .mc-main {
        display: flex;
        padding: 0 20px;

        > div {
            height: 100%;
            width: 50%;
        }
    }
}

.mc-direct-message-box, #mc-whatsapp-send-template-box {
    height: calc(100% - 50px);
    max-width: 1000px;

    .mc-bottom {
        div + a {
            margin-left: 15px;
        }

        > div {
            font-weight: 500;
            margin: 0 15px;
            color: $color-blue;

            &:empty {
                display: none;
            }
        }
    }
}

.mc-updates-box, .mc-requirements-box {
    .mc-input {
        justify-content: flex-start;

        > span {
            max-width: 200px;
            min-width: 200px;
        }

        > div {
            font-size: 13px;
            letter-spacing: 0.3px;
            background: rgba(255, 196, 88, 0.15);
            color: rgb(155, 116, 36);
            width: 100%;
            line-height: 20px;
            padding: 13px;
            border-radius: 4px;

            &.mc-green {
                background-color: rgba(26, 146, 96, 0.07);
                color: rgb(21, 116, 77);
            }
        }
    }
}

.mc-requirements-box {
    .mc-input > div {
        display: inline-block;
        padding: 5px 10px;
        width: auto;
    }

    .mc-main > p {
        margin-top: 30px;
    }
}

.mc-dialog-box {
    max-width: 500px;
    height: auto;
    min-height: 75px;
    text-align: center;
    padding: 30px;
    z-index: 999998;

    p {
        font-weight: 600;
        font-size: 16px;
        line-height: 30px;
        margin: 0 0 20px 0;
    }

    a {
        margin: 0 7px;
    }

    .mc-link-text {
        margin: 0;
        color: $color-black;
    }

    &[data-type="choice"] .mc-close,
    &[data-type="alert"] .mc-close,
    &[data-type="info"] .mc-cancel,
    &[data-type="info"] .mc-confirm,
    &[data-type="map"] .mc-cancel,
    &[data-type="map"] .mc-confirm {
        display: none;
    }

    &[data-type="alert"] {
        padding: 60px 30px;
    }

    iframe {
        width: 100%;
        height: 400px;
        border: none;
        margin: 0;
        display: block;
    }

    pre {
        font-family: monospace;
        margin: 10px 0 0 0;
        overflow-x: scroll;
        padding: 5px 5px 7px 5px;
        border-radius: 3px;
        border: 1px solid $border-color;
        font-weight: 400;
    }

    &#error pre {
        white-space: normal;
    }

    .mc-title {
        font-size: 21px;
        line-height: 50px;
        margin-top: -10px;
        color: $color-blue;

        &:empty {
            display: none;
        }
    }

    &[data-type="map"] {
        max-width: 1000px;
        padding: 0;

        p {
            margin: 0;
        }

        > div {
            position: absolute;
            z-index: 9;
            top: 10px;
            right: 0;
        }
    }
}

.mc-dialogflow-intent-box {
    .mc-intent-add, .mc-bot-response {
        position: relative;

        i {
            position: absolute;
            margin-left: 15px;
            top: -10px;

            &:before {
                font-size: 16px;
            }

            &.mc-loading:before {
                line-height: 30px;
                font-size: 20px;
            }
        }
    }

    .mc-intent-add {
        margin-top: 10px;

        i {
            position: absolute;
            margin-left: 15px;
            top: -10px;

            & + i {
                margin-left: 50px;
            }

            & + i + i {
                margin-left: 85px;
            }
        }
    }

    .mc-type-text + .mc-type-text {
        margin-top: 5px;
    }

    .mc-title.mc-title-search .mc-search-btn {
        right: 36px;
    }
}

.mc-bot-response.mc-disabled {
    position: relative;

    textarea {
        opacity: 0.5;
    }

    i {
        display: none !important;
    }

    &:before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }
}

#mc-intent-preview, #mc-qea-preview {
    position: absolute;
    top: -1px;
    right: 0;
    font-size: 18px;
    cursor: pointer;
    transition: $transition;

    &:hover {
        color: $color-blue;
    }
}

#mc-envato-box, .mc-bold-list {
    p {
        font-weight: 400;
        text-align: left;
    }

    b {
        display: inline-block;
        width: 150px;
        font-weight: 500;
    }
}

#opencart-order-details {
    padding: 20px 30px;

    .mc-title {
        text-align: left;
    }

    .mc-panel-details {
        border: none;
    }

    .mc-title-opencart-products {
        color: $color-black;
        margin-top: -30px;
        font-weight: 500;
        font-size: 16px;
    }

    .mc-list-links {
        text-align: left;

        > a {
            padding: 0;
            margin: 0;
            font-weight: 400;
            font-size: 16px;
            line-height: 30px;

            span:last-child {
                float: right;
            }
        }
    }

    .mc-panel-details {
        display: inline-block;
        width: calc(100% - 150px);
        padding: 0;
    }

    .oc-b-products {
        transform: translateY(-10px);
    }
}

#intent-preview-box, #qea-preview-box {
    max-width: 350px;
    padding: 30px;
    text-align: left;

    > p {
        font-weight: 400;
        font-size: 13px;
        text-align: left;

        span {
            display: block;
            line-height: 20px;
            overflow: hidden;

            & + span {
                margin-top: 8px;
            }
        }
    }

    .mc-btn {
        margin: 0;
    }
}

#mc-errors-list-box {
    max-width: 600px;

    pre {
        white-space: normal;
        text-align: left;
        padding: 7px 10px;
    }
}

.mc-loading-global {
    height: 36px;
    width: 36px;
    max-width: 36px;
    max-height: 36px;
    margin: -18px 0 0 -18px;
    color: $color-blue;
    border-radius: 3px;
}

.mc-admin-box {
    max-width: 500px;
    margin: 30px auto;
    padding: 48px 40px 36px;
    border-radius: 6px;
    border: 1px solid $border-color;
    background: $white;
    position: relative;

    .mc-top-bar {
        padding-bottom: 30px;

        img {
            max-width: 300px;
        }

        .mc-title {
            font-size: 20px;
            line-height: 20px;
            font-weight: 600;
            margin: 30px 0px 15px 0px;
        }
    }

    .mc-text {
        font-size: 15px;
        line-height: 25px;
        color: $color-gray;
    }

    .mc-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 30px;
    }

    .mc-info {
        font-size: 13px;
        line-height: 20px;
        color: $white;
        background-color: $color-red;
        right: 0;
        left: 0;
        top: 0;
        margin: 0 0 40px 0;
        padding: 15px 20px;
        border-radius: 4px;
        display: none;

        &.mc-active {
            display: block;
            animation: mc-fade-animation 0.4s;
        }
    }

    .mc-text + .mc-input, .mc-input + .mc-text {
        margin-top: 30px;
    }
}

.mc-rich-login {
    max-width: 500px;

    .mc-forgot {
        font-size: 12px;
        margin-top: 15px;
        text-align: right;
        color: $color-gray;
    }

    .mc-register {
        font-weight: 600;
        font-size: 15px;
    }

    .mc-forgot,
    .mc-register {
        cursor: pointer;
        transition: $transition;
        text-decoration: none;

        &:hover {
            color: $color-blue;
        }
    }
}

#mc-error-check {
    display: none !important
}

.mc-language-switcher {
    display: flex;
    align-items: center;

    img, > i {
        display: block;
        opacity: .5;
        transition: $transition;
        cursor: pointer;
    }

    img {
        border-radius: 2px;
        margin: 0 5px;
        width: 20px;
        height: 13px;
        position: relative;
        z-index: 2;
    }

    span {
        position: relative;
        transition: $transition;

        i {
            position: absolute;
            top: 0;
            left: 5px;
            line-height: 18px;
            cursor: pointer;
            font-size: 9px;
            opacity: 0;
            z-index: 0;
            transition: $transition;

            &:hover {
                color: $color-red;
            }
        }

        &:hover {
            padding-left: 15px;
            transition-delay: 1s;

            i {
                opacity: 1;
                transition-delay: 1s;

                &:hover {
                    transition-delay: 0s;
                }
            }
        }
    }

    > i {
        font-size: 12px;
        line-height: 12px;
        height: 12px;
        margin-left: 10px;
    }

    .mc-active img, span:hover i, span:hover img {
        opacity: 1;
    }

    & > i:hover {
        opacity: 1;
        color: $color-blue;
    }
}

.mc-languages-box .mc-main {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    grid-gap: 20px;
    max-height: 300px;
    margin-bottom: 30px;
    padding-bottom: 0;

    > div {
        display: flex;
        align-items: center;
        font-size: 14px;
        line-height: 14px;
        cursor: pointer;
        transition: $transition;

        > img {
            margin-right: 15px;
            border-radius: 3px;
            width: 26px;
            height: 15px;
        }

        &:hover {
            color: $color-blue;
        }
    }
}

.mc-apps {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-gap: 20px;

    > div {
        min-height: 0;
        min-width: 0;
        position: relative;
        overflow: hidden;
        padding: 30px 30px 30px 130px;
        border-radius: 6px;
        border: 1px solid rgb(205, 210, 215);
        cursor: pointer;
        transition: $transition;

        img {
            max-width: 60px;
            position: absolute;
            left: 30px;
            top: 30px;
        }

        h2 {
            font-size: 19px;
            padding: 0;
            margin: 0;
            color: $color-gray;
        }

        p {
            font-size: 14px;
            line-height: 24px;
            letter-spacing: 0.3px;
            margin: 5px 0 0 0;
            color: rgb(120, 134, 146);
        }

        i {
            position: absolute;
            right: 30px;
            top: 30px;
            color: $color-green;
        }

        &:hover {
            border-color: $color-blue;
            box-shadow: 0 0 5px rgba(39, 156, 255, 0.2);
        }

        &.mc-disabled {
            opacity: 0.5;
        }
    }
}

.mc-panel-details .mc-whmcs-link {
    margin: 15px 15px 0 15px;
}

.mc-panel-whmcs .mc-list-items {
    margin: 0 15px 0 15px;
}

.mc-panel-aecommerce, .mc-panel-martfury, .mc-panel-opencart {
    .mc-list-items > a > span:first-child {
        opacity: .6;
        margin-right: 5px;
    }

    .mc-list-links > a > span:last-child {
        float: right;
    }
}

.mc-panel-zendesk {
    .mc-list-items > a {
        margin-bottom: 5px;

        > span:first-child {
            display: block;
            line-height: 15px;
            padding-left: 22px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;

            i {
                right: auto;
                left: 15px;
                font-style: normal;
                height: 15px;
                width: 15px;
                line-height: 15px;
                text-align: center;
                border-radius: 3px;
                font-size: 9px;
                opacity: 1;
                font-weight: 500;
                color: $white;
            }
        }
    }

    .mc-btn {
        margin-left: 15px;
    }

    #mc-zendesk-update-ticket {
        top: 10px;

        &:hover {
            color: $color-blue;
        }
    }
}

.mc-zendesk-status-n {
    background-color: rgb(255, 176, 87);
    color: rgb(112, 56, 21) !important;
}

.mc-zendesk-status-o {
    background-color: rgb(227, 79, 50);
}

.mc-zendesk-status-c {
    background-color: rgb(19, 202, 126);
}

.mc-zendesk-status-p {
    background-color: rgb(48, 145, 236);
    color: rgb(255, 255, 255);
}

.mc-zendesk-status-s {
    background-color: rgb(135, 146, 157);
}

.mc-zendesk-sync:before {
    content: "";
    position: absolute;
    width: 2px;
    background: $color-blue;
    height: calc(100% - 7px);
    left: 0;
    top: 0;
}

.mc-aecommerce-orders > a > span:last-child {
    margin-left: 15px;
}

.mc-panel-details .mc-woocommerce-cart > a {
    padding-right: 35px;
}

.mc-btn-app-setting:not(.mc-active) {
    display: none;

    & + .mc-btn {
        margin-left: 0;
    }
}

#mc-embeddings-box {
    min-height: 25px;

    p {
        margin: 0;

        span {
            display: block;
            font-weight: 400;
            font-size: 14px;
            margin: 10px 0 0 0;
        }

        & + div {
            display: none !important;
        }
    }
}

/*
 *
 * -----------------------------------------------------------
 * 3TH PARTY SCRIPTS
 * -----------------------------------------------------------
 *
*/

.daterangepicker {
    position: absolute;
    color: inherit;
    background-color: #fff;
    border-radius: 4px;
    border: 1px solid #ddd;
    width: 278px;
    max-width: none;
    padding: 0;
    margin-top: 7px;
    top: 100px;
    right: 20px !important;
    left: 20px;
    z-index: 3001;
    display: none;
    font-size: 15px;
    line-height: 1em;
}

.daterangepicker.single .daterangepicker .ranges, .daterangepicker.single .drp-calendar {
    float: none;
}

.daterangepicker.single .drp-selected {
    display: none;
}

.daterangepicker.show-calendar .drp-calendar {
    display: block;
}

.daterangepicker.show-calendar .drp-buttons {
    display: block;
}

.daterangepicker.auto-apply .drp-buttons {
    display: none;
}

.daterangepicker .drp-calendar {
    display: none;
    max-width: 270px;
}

.daterangepicker .drp-calendar.left {
    padding: 8px 0 8px 8px;
}

.daterangepicker .drp-calendar.right {
    padding: 8px;
}

.daterangepicker .drp-calendar.single .calendar-table {
    border: none;
}

.daterangepicker .calendar-table .next span, .daterangepicker .calendar-table .prev span {
    color: #fff;
    border: solid black;
    border-width: 0 2px 2px 0;
    border-radius: 0;
    display: inline-block;
    padding: 3px;
}

.daterangepicker .calendar-table .next span {
    transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
}

.daterangepicker .calendar-table .prev span {
    transform: rotate(135deg);
    -webkit-transform: rotate(135deg);
}

.daterangepicker .calendar-table th, .daterangepicker .calendar-table td {
    white-space: nowrap;
    text-align: center;
    vertical-align: middle;
    min-width: 32px;
    width: 32px;
    height: 24px;
    line-height: 24px;
    font-size: 12px;
    border-radius: 4px;
    border: 1px solid transparent;
    white-space: nowrap;
    cursor: pointer;
}

.daterangepicker .calendar-table th {
    font-weight: 500;
    font-size: 13px;
    letter-spacing: .3px;
}

.daterangepicker .calendar-table {
    border: 1px solid #fff;
    border-radius: 4px;
    background-color: #fff;
}

.daterangepicker .calendar-table table {
    width: 100%;
    margin: 0;
    border-spacing: 0;
    border-collapse: collapse;
}

.daterangepicker td.available:hover, .daterangepicker th.available:hover {
    background-color: #eee;
    border-color: transparent;
    color: inherit;
}

.daterangepicker td.week, .daterangepicker th.week {
    font-size: 80%;
    color: #ccc;
}

.daterangepicker td.off, .daterangepicker td.off.in-range, .daterangepicker td.off.start-date, .daterangepicker td.off.end-date {
    background-color: #fff;
    border-color: transparent;
    color: #999;
}

.daterangepicker td.in-range {
    background-color: #ebf4f8;
    border-color: transparent;
    color: #000;
    border-radius: 0;
}

.daterangepicker td.start-date {
    border-radius: 4px 0 0 4px;
}

.daterangepicker td.end-date {
    border-radius: 0 4px 4px 0;
}

.daterangepicker td.start-date.end-date {
    border-radius: 4px;
}

.daterangepicker td.active, .daterangepicker td.active:hover {
    background-color: $color-blue;
    border-color: transparent;
    color: #fff;
}

.daterangepicker th.month {
    width: auto;
    font-weight: 500;
    font-size: 13px;
    letter-spacing: .3px;
}

.daterangepicker td.disabled, .daterangepicker option.disabled {
    color: #999;
    cursor: not-allowed;
    text-decoration: line-through;
}

.daterangepicker select.monthselect, .daterangepicker select.yearselect {
    font-size: 12px;
    padding: 1px;
    height: auto;
    margin: 0;
    cursor: default;
}

.daterangepicker select.monthselect {
    margin-right: 2%;
    width: 56%;
}

.daterangepicker select.yearselect {
    width: 40%;
}

.daterangepicker select.hourselect, .daterangepicker select.minuteselect, .daterangepicker select.secondselect, .daterangepicker select.ampmselect {
    width: 50px;
    margin: 0 auto;
    background: #eee;
    border: 1px solid #eee;
    padding: 2px;
    outline: 0;
    font-size: 12px;
}

.daterangepicker .calendar-time {
    text-align: center;
    margin: 4px auto 0 auto;
    line-height: 30px;
    position: relative;
}

.daterangepicker .calendar-time select.disabled {
    color: #ccc;
    cursor: not-allowed;
}

.daterangepicker .drp-buttons {
    clear: both;
    text-align: right;
    padding: 8px;
    border-top: 1px solid #ddd;
    display: none;
    line-height: 12px;
    vertical-align: middle;
}

.daterangepicker .drp-selected {
    display: inline-block;
    font-size: 12px;
    padding-right: 8px;
}

.daterangepicker .drp-buttons .btn {
    margin-left: 8px;
    font-size: 12px;
    font-weight: bold;
    padding: 4px 8px;
}

.daterangepicker.show-ranges.single.rtl .drp-calendar.left {
    border-right: 1px solid #ddd;
}

.daterangepicker.show-ranges.single.ltr .drp-calendar.left {
    border-left: 1px solid #ddd;
}

.daterangepicker.show-ranges.rtl .drp-calendar.right {
    border-right: 1px solid #ddd;
}

.daterangepicker.show-ranges.ltr .drp-calendar.left {
    border-left: 1px solid #ddd;
}

.daterangepicker .ranges {
    float: none;
    text-align: left;
    margin: 0;
}

.daterangepicker.show-calendar .ranges {
    margin-top: 8px;
}

.daterangepicker .ranges ul {
    list-style: none;
    margin: 0 auto;
    padding: 0;
    width: 100%;
}

.daterangepicker .ranges li {
    font-size: 12px;
    padding: 8px 12px;
    cursor: pointer;
}

.daterangepicker .ranges li:hover {
    background-color: #eee;
}

.daterangepicker .ranges li.active {
    background-color: $color-blue;
    color: #fff;
}

@media (min-width: 564px) {
    .daterangepicker {
        width: auto;
    }

    .daterangepicker .ranges ul {
        width: 140px;
    }

    .daterangepicker.single .ranges ul {
        width: 100%;
    }

    .daterangepicker.single .drp-calendar.left {
        clear: none;
    }

    .daterangepicker.single .ranges, .daterangepicker.single .drp-calendar {
        float: left;
    }

    .daterangepicker {
        direction: ltr;
        text-align: left;
        box-shadow: 0 0 10px #0000001f;
    }

    .daterangepicker .drp-calendar.left {
        clear: left;
        margin-right: 0;
    }

    .daterangepicker .drp-calendar.left .calendar-table {
        border-right: none;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
    }

    .daterangepicker .drp-calendar.right {
        margin-left: 0;
    }

    .daterangepicker .drp-calendar.right .calendar-table {
        border-left: none;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
    }

    .daterangepicker .drp-calendar.left .calendar-table {
        padding-right: 8px;
    }

    .daterangepicker .ranges, .daterangepicker .drp-calendar {
        float: left;
    }
}

@media (min-width: 730px) {
    .daterangepicker .ranges {
        width: auto;
    }

    .daterangepicker .ranges {
        float: left;
    }

    .daterangepicker.rtl .ranges {
        float: right;
    }

    .daterangepicker .drp-calendar.left {
        clear: none !important;
    }
}

.codex-editor:after {
    content: "\4d";
    font-family: "Masi Chat Icons";
    position: absolute;
    left: 0;
    font-size: 9px;
    font-style: normal;
    font-weight: normal;
    bottom: 25px;
    opacity: .5;
    z-index: -1;
}

.ce-block a {
    color: $color-black;
}

.ce-header {
    font-size: 16px !important;
    color: $color-black !important;
}

.ce-toolbar__actions {
    padding-right: 15px;
}

.ce-block__content, .ce-toolbar__content {
    max-width: 100% !important;
    margin: 0;
}

.cdx-input {
    width: auto;
}

.ce-rawtool__textarea {
    min-height: 0 !important;
    background: $background-gray !important;
}

.cdx-search-field__icon {
    height: 35px !important;
}

.ct__content {
    color: $white
}

.cdx-block {
    max-width: 100% !important;
}

.image-tool__image-picture {
    border-radius: 3px;
}

/*
 *
 * -----------------------------------------------------------
 * CLOUD VERSION
 * -----------------------------------------------------------
 *
*/

#whatsapp-cloud-sync-btn, #whatsapp-cloud-reconnect-btn {
    display: none;
}

.mc-cloud-admin {
    [id="user_type"], [id="first_name"], [id="last_name"], [id="password"], [id="email"], .mc-delete {
        display: none !important;
    }
}

.mc-cloud {
    #whatsapp-cloud-sync-btn, #whatsapp-cloud-reconnect-btn {
        display: flex;
    }

    #admin-title, #login-icon, #login-message, #envato-purchase-code, #envato-validation, #email-piping-disable-cron,
    #auto-updates, #pusher, #cookie-domain, #system-requirements, #mc-path, #tab-aecommerce, #admin-icon, #push-notifications-provider, #push-notifications-id, #push-notifications-key,
    #tab-whmcs, #tab-perfex, #tab-ump, #tab-armember, #tab-martfury, #logs, .mc-version, #amazon-s3, #whatsapp-twilio-btn.mc-active + #whatsapp-cloud-sync-btn,
    #whatsapp-twilio-btn.mc-active + #whatsapp-cloud-sync-btn + #whatsapp-cloud-reconnect-btn {
        display: none !important;
    }

    #google-client-id, #google-client-secret, #google-refresh-token, #open-ai-key {
        &:not(.mc-active) {
            display: none !important;
        }
    }

    #whatsapp-cloud-key, #whatsapp-twilio-btn {
        &:not(.mc-active) {
            display: none !important;
        }
    }

    .mc-app-box {
        .mc-setting, .mc-main .mc-title, .mc-btn-app-puchase {
            display: none !important;
        }
    }

    .mc-active-app {
        .mc-btn-app-disable {
            display: block !important;
        }

        .mc-activate {
            display: none;
        }
    }
}

.mc-main:not(.mc-cloud) {
    #google-sync-mode, #open-ai-sync-mode, #whatsapp-cloud-sync-mode, #open-ai-training-cron-job {
        display: none !important;
    }
}

.mc-credits-panel-box p b a {
    display: none !important;
}

.mc-onboarding-box {
    height: calc(100% - 50px);

    .mc-setting {
        align-items: center;

        > div:first-child {
            padding-right: 30px;
            width: 100%;
        }

        > div:last-child {
            flex: 1;
        }

        h2 {
            font-size: 16px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;

            img, i {
                font-size: 18px;
                line-height: 20px;
                margin-left: 15px;
                height: 18px;
                display: block;
                transform: none;
            }

            a {
                text-decoration: none;
            }
        }

        .mc-btn {
            width: 70px;

            &.mc-active {
                background-color: $color-green;
                color: $color-green;
                cursor: default;

                &:after {
                    content: "\77";
                    font-family: "Masi Chat Icons";
                    position: absolute;
                    left: 0;
                    right: 0;
                    color: $white;
                }
            }
        }

        & + div {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgb(230, 230, 230);
        }
    }

    .mc-main > p {
        margin-bottom: 30px;
    }
}

.mc-menu [data-value="switch"] {
    position: static !important;

    > div {
        position: absolute;
        left: calc(100% - 1px);
        top: -61px;
        height: calc(100% + 45px);
        padding: 8px 15px;
        background: #FFF;
        box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(0, 0, 0, 0.05);
        border-radius: 4px;
        opacity: 0;
        transition: all 0.4s linear .2s;

        a {
            display: block;
            padding: 6px 0;
        }
    }

    &:hover > div {
        opacity: 1;
    }
}

/*
 *
 * -----------------------------------------------------------
 * RESPONSIVE
 * -----------------------------------------------------------
 *
*/

@media (max-width: 1600px) {
    .mc-admin > main > .mc-area-users > .mc-top-bar h2 {
        max-width: 200px;
        margin-right: 30px;
    }
}

@media (max-width: 1300px) {
    .mc-admin > main > .mc-area-users > .mc-top-bar h2 {
        max-width: 100px;
    }

    .mc-area-users .mc-menu-wide ul li {
        margin-right: 10px;
    }

    .mc-setting {
        .repeater-item > div label {
            margin: 0 0 15px 0;
        }

        .repeater-item > div, .repeater-item > div, .input > div:not([data-type="textarea"]):not(.mc-repeater-add) {
            display: block;
        }

        .input > div:not([data-type="textarea"]):not(.mc-repeater-add) > label {
            margin: 0 30px 10px 0;
        }

        > .mc-setting-content {
            flex-shrink: 1;
        }

        input, select, textarea, input, select, textarea {
            min-width: 0;
        }
    }

    [data-id="open-ai-faq-set-data"] .repeater-item > div {
        display: flex;
    }

    #mc-chatbot-qea .mc-enlarger {
        min-height: 30px;
    }

    .mc-type-timetable {
        > .input {
            overflow: hidden;
        }

        .mc-timetable > div > div {
            flex-wrap: wrap;

            [data-day] {
                margin-bottom: 5px;
            }
        }
    }

    .mc-area-reports {
        > .mc-tab > .mc-content {
            display: block;

            .mc-reports-chart {
                width: auto;
            }

            .mc-reports-sidebar {
                max-width: 100%;
            }
        }
    }

    .mc-repeater-block-data .mc-setting + .mc-setting, .mc-repeater-block-actions .mc-setting + .mc-setting, .mc-repeater-block-rest-api .mc-setting + .mc-setting {
        margin: 5px 0 0 0;
    }
}

@media (min-width: 465px) {
    .mc-menu-mobile > ul {
        box-shadow: none;
    }
}

@media (min-width: 465px) and (max-width: 1200px) {
    .mc-board > .mc-admin-list {
        min-width: 300px;
    }

    .mc-board .mc-labels .mc-status-online {
        text-indent: 999px;
        width: 10px;
        height: 10px;
        padding: 0;
        overflow: hidden;
        border-radius: 50%;
        background: $color-green;
    }

    .mc-board .mc-labels .mc-status-typing {
        padding: 0;
        text-indent: 999px;
        width: 30px;
        height: 25px;
        overflow: hidden;
        background: none;
    }

    .mc-board .mc-conversation .mc-list > div {
        max-width: calc(100% - 100px);
    }
}

@media (min-width: 465px) and (max-width: 1366px) {
    .mc-board > .mc-admin-list, .mc-board .mc-user-details {
        max-width: 330px;
        min-width: 330px;
    }

    .mc-admin-list .mc-search-btn > input {
        min-width: 225px;
    }
}

@media (min-width: 465px) and (max-width: 1140px) {
    .mc-admin > main > div > .mc-top-bar > div:first-child > ul {
        overflow-x: scroll;
    }
}

@media (min-width: 465px) and (max-width: 1024px) {
    .mc-board > .mc-admin-list, .mc-board .mc-user-details {
        max-width: 250px;
        min-width: 250px;
    }

    .mc-admin-list .mc-search-btn > input {
        min-width: 145px;
        max-width: 145px;
    }

    .mc-setting, .mc-setting {
        display: block;
    }

    .mc-setting > .mc-setting-content, .mc-setting > .mc-setting-content {
        max-width: 100%;
        width: auto;
        padding-right: 0;
        padding-bottom: 15px;
    }

    .mc-area-settings > .mc-tab > .mc-nav > ul, .mc-area-reports > .mc-tab > .mc-nav > ul {
        min-width: 180px;
    }

    .mc-area-users .mc-top-bar h2 {
        display: none;
    }

    .mc-menu-wide ul li, .mc-tab > .mc-nav > ul li {
        margin: 0 10px 0 0;
    }
}

@media (min-width: 465px) and (max-width: 912px) {
    .mc-board {

        > .mc-admin-list, .mc-user-details {
            position: fixed;
            z-index: 2;
            background: $white;
            left: 65px;
            top: 0;
            bottom: 0;
            max-width: 330px;
            min-width: 330px;
            display: none;
        }

        > .mc-admin-list.mc-active, .mc-user-details.mc-active {
            display: block;
        }

        > .mc-admin-list .mc-top {
            padding-left: 45px;
        }

        .mc-user-details {
            left: auto;
            right: 0;
        }

        .mc-conversation > .mc-top {
            padding-left: 45px;
        }

        .mc-conversation > .mc-top .mc-menu-mobile {
            right: 35px;
        }
    }

    .mc-admin-list {
        .mc-search-btn > input {
            min-width: 200px;
            max-width: 200px;
        }

        .mc-filter-btn.mc-active > div {
            min-width: 265px;
            max-width: 265px;
            left: auto;
        }
    }

    .mc-area-conversations > .mc-btn-collapse {
        display: block;
    }
}
