﻿
/*
 * ==========================================================
 * MAIN SCRIPT
 * ==========================================================
 *
 * Main JavaScript file. This file is shared by frond-end and back-end. © 2017-2025 masichat.com. All rights reserved.
 * 
 */

'use strict';

(function ($) {

    var version = '3.8.2';
    var main;
    var global;
    var upload_target;
    var admin = false;
    var tickets = false;
    var timeout = false;
    var timeout_typing = false;

    var interval = false;
    var timeout_debounce = [];
    var previous_search;
    var mc_current_user = false;
    var chat;
    var chat_editor;
    var chat_textarea;
    var chat_header;
    var chat_status;
    var chat_emoji;
    var chat_scroll_area;
    var chat_overlay_panel;
    var label_date_items = false;
    var label_date_history = [9999999, ''];
    var label_date_timeout = [false, false];
    var document_title = document.title;
    var CHAT_SETTINGS = {};
    var mobile = $(window).width() < 465;
    var bot_id;
    var force_action = '';
    var dialogflow_human_takeover;
    var agents_online = false;
    var ND = 'undefined';
    var cookies_supported = true;
    var utc_offset_user = (new Date()).getTimezoneOffset() * 60000;
    var cloud_data = false;
    var articles_page = false;
    var ajax_calls;
    var audio_mp3;
    var audio_recorder_dom;
    var audio_recorder_dom_time;
    var audio_recorder;
    var audio_recorder_chunks = [];
    var audio_recorder_time = [0, false];
    var audio_recorder_time_player = [0, false];
    var audio_recorder_stream;
    var prevent_focusout = false;
    var init_push_notifications = false;
    var conversation_update_last_check = 0;
    var is_shopify = typeof Shopify !== ND;

    /*
    * ----------------------------------------------------------
    * EXTERNAL SCRIPTS
    * ----------------------------------------------------------
    */

    // Auto Expand Scroll Area | Schiocco
    $.fn.extend({ manualExpandTextarea: function () { var t = this[0]; t.style.height = "auto", t.style.maxHeight = "25px"; window.getComputedStyle(t); t.style.height = (t.scrollHeight > 350 ? 350 : t.scrollHeight) + "px", t.style.maxHeight = "", $(t).trigger("textareaChanged") }, autoExpandTextarea: function () { var t = this[0]; t.addEventListener("input", function (e) { $(t).manualExpandTextarea() }, !1) } });

    // Autolink-js
    (function () { var t = [].slice; String.prototype.autoLink = function () { var n, a, r, i, c, e, l; return e = /(^||[\s\n]|<[A-Za-z]*\/?>)((?:https?|ftp):\/\/[\-A-Z0-9+\u0026\u2019@#\/%?=()~_|!:,.;]*[\-A-Z0-9+\u0026@#\/%=~_|])/gi, 0 < (c = 1 <= arguments.length ? t.call(arguments, 0) : []).length ? (i = c[0], n = i.callback, r = function () { var t; for (a in t = [], i) l = i[a], "callback" !== a && t.push(" " + a + "='" + l + "'"); return t }().join(""), this.replace(e, function (t, a, i) { return "" + a + (("function" == typeof n ? n(i) : void 0) || "<a href='" + i + "'" + r + ">" + i + "</a>") })) : this.replace(e, "$1<a href='$2'>$2</a>") } }).call(this);

    /*
    * ----------------------------------------------------------
    * FUNCTIONS
    * ----------------------------------------------------------
    */

    var MCF = {
        visibility_status: 'visible',
        loop_prevention: false,

        // Main Ajax function
        ajax: function (data, onSuccess = false) {
            if (ajax_calls) {
                ajax_calls[0].push(data);
                ajax_calls[1].push(onSuccess);
            } else {
                ajax_calls = [[data], [onSuccess]];
                setTimeout(() => {
                    let onSuccessCalls = ajax_calls[1];
                    let data_auto = { 'login-cookie': MCF.loginCookie() }
                    if (activeUser()) {
                        data_auto.user_id = activeUser().id;
                    }
                    if (typeof MC_LANG != ND) {
                        data_auto.language = MC_LANG;
                    }
                    if (cloud_data) {
                        data_auto.cloud = cloud_data;
                    }
                    if (location.search.includes('debug')) {
                        data_auto.debug = true;
                    }
                    $.ajax({
                        method: 'POST',
                        url: MC_AJAX_URL,
                        data: $.extend({ function: 'ajax_calls', calls: ajax_calls[0] }, data_auto)
                    }).done((response) => {
                        let result;
                        if (Array.isArray(response)) {
                            result = response;
                        } else if (response === 'invalid-session') {
                            setTimeout(() => { MCF.reset() }, 1000);
                        } else if (admin && MC_ADMIN_SETTINGS.cloud && response.includes('no-credits') && !response.includes('"value":"no-credits')) {
                            return MCCloud.creditsAlertQuota();
                        } else {
                            try {
                                result = typeof response === 'string' || response instanceof String ? JSON.parse(response) : response;
                            } catch (e) {
                                this.ajax_error(response, data);
                                return;
                            }
                        }
                        for (var i = 0; i < result.length; i++) {
                            let result_sub = result[i];
                            if (!Array.isArray(result_sub)) {
                                result_sub = result;
                            }
                            if (result_sub[0] == 'success') {
                                onSuccess = onSuccessCalls[i];
                                if (onSuccess) {
                                    onSuccess(result_sub[1]);
                                }
                            } else if (MCF.errorValidation(result_sub)) {
                                if (onSuccess) {
                                    onSuccess(result_sub);
                                }
                            } else {
                                if (admin) {
                                    if (result_sub[1] == 'security-error') {
                                        setTimeout(() => { MCF.reset() }, 1000);
                                    }
                                    MCAdmin.conversations.busy = false;
                                }
                                MCChat.is_busy_update = false;
                                MCChat.busy(false);
                                if (result_sub[1] == 'login-data-error' && !this.loop_prevention) {
                                    return;
                                }
                                let result_sub_arr = JSON.parse(result_sub);
                                if (Array.isArray(result_sub_arr) && result_sub_arr[0] == 'error' && result_sub_arr[2] && result_sub_arr[3]) {
                                    MCF.error(result_sub_arr[3], result_sub_arr[2]);
                                } else {
                                    MCF.error(JSON.stringify(result_sub).replace(/\\/g, "").replace(/\"/g, "").replace(/\[/g, "").replace(/\]/g, "").replace('error,', ''), data.function);
                                }
                            }
                        }
                    }).fail((jqXHR, textStatus, error) => {
                        if (error) {
                            this.ajax_error('HTTP CURL ERROR');
                            console.log(error);
                        }
                    });
                    ajax_calls = false;
                }, 100);
            }
        },

        ajax_error: function (response, data = false) {
            if (admin) {
                MCAdmin.conversations.busy = false;
                MCApps.dialogflow.smart_reply_busy = false;
            }
            if (MCApps.dialogflow.busy) {
                MCApps.dialogflow.busy = false;
                if (!admin && MCChat.conversation) {
                    MCF.ajax({ function: 'open-ai-send-fallback-message', conversation_id: MCChat.conversation.id });
                    MCChat.typing(-1, 'stop');
                }
            }
            MCChat.is_busy_update = false;
            MCChat.busy(false);
            console.log(response);
            MCF.error(response.length > 500 ? response.substr(0, 500) + '... Check the console for more details.' : response, `MCF.ajax.${data ? data.function : ''}`);
        },

        // Cors function
        cors: function (method = 'GET', url, onSuccess) {
            let xhr = new XMLHttpRequest();
            if ('withCredentials' in xhr) {
                xhr.open(method, url, true);
            } else if (typeof XDomainRequest != ND) {
                xhr = new XDomainRequest();
                xhr.open(method, url);
            } else {
                return false;
            }
            xhr.onload = function () {
                onSuccess(xhr.responseText);
            };
            xhr.onerror = function () {
                return false;
            };
            xhr.send();
        },

        // Uploads
        upload: function (form, onSuccess) {
            if (cloud_data) form.append('cloud', cloud_data);
            jQuery.ajax({
                url: MC_URL + '/include/upload.php',
                cache: false,
                contentType: false,
                processData: false,
                data: form,
                type: 'POST',
                success: function (response) {
                    onSuccess(response);
                }
            });
        },

        // Get file type
        getFileType: function (url_or_name) {
            if (/.jpg|.jpeg|.png|.gif|.webp/.test(url_or_name)) {
                return 'image';
            }
            if (/.mp3|.ogg|.wav|.aac/.test(url_or_name)) {
                return 'audio';
            }
            if (/.mp4|.mkv|.vob|.3gp|.webm/.test(url_or_name)) {
                return 'video';
            }
            return 'file';
        },

        // UTC Time
        UTC: function (datetime) {
            return new Date(datetime).getTime() - utc_offset_user;
        },

        // Check if a variable is null or empty
        null: function (obj) { if (typeof (obj) !== ND && obj !== null && obj !== 'null' && obj !== false && (obj.length > 0 || typeof (obj) == 'number' || typeof (obj.length) == ND) && obj !== ND) return false; else return true; },

        // Deactivate and hide the elements
        deactivateAll: function () {
            global.find('.mc-popup, .mc-tooltip, .mc-list .mc-menu, .mc-select ul').mcActive(false);
        },

        // Deselect the content of the target
        deselectAll: function () {
            if (window.getSelection) {
                window.getSelection().removeAllRanges();
            } else if (document.selection) {
                document.selection.empty();
            }
        },

        // Get URL parameters
        getURL: function (name = false, url = false) {
            if (!url) {
                url = location.search;
            }
            if (name == false) {
                var c = url.split('?').pop().split('&');
                var p = {};
                for (var i = 0; i < c.length; i++) {
                    var d = c[i].split('=');
                    p[d[0]] = MCF.escape(d[1]);
                }
                return p;
            }
            if (url.indexOf('?') > 0) {
                url = url.substr(0, url.indexOf('?'));
            }
            return MCF.escape(decodeURIComponent((new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(url) || [, ""])[1].replace(/\+/g, '%20') || ""));
        },

        URL: function () {
            return window.location.href.substr(0, window.location.href.indexOf('?'));
        },

        // Convert a string to slug and inverse
        stringToSlug: function (string) {
            let map = {
                'ก': 'k', 'ข': 'kh', 'ฃ': 'kh', 'ค': 'kh', 'ฅ': 'kh', 'ฆ': 'kh', 'ง': 'ng',
                'จ': 'ch', 'ฉ': 'ch', 'ช': 'ch', 'ซ': 's', 'ฌ': 'ch', 'ญ': 'y', 'ฎ': 'd',
                'ฏ': 't', 'ฐ': 'th', 'ฑ': 'th', 'ฒ': 'th', 'ณ': 'n', 'ด': 'd', 'ต': 't',
                'ถ': 'th', 'ท': 'th', 'ธ': 'th', 'น': 'n', 'บ': 'b', 'ป': 'p', 'ผ': 'ph',
                'ฝ': 'f', 'พ': 'ph', 'ฟ': 'f', 'ภ': 'ph', 'ม': 'm', 'ย': 'y', 'ร': 'r',
                'ล': 'l', 'ว': 'w', 'ศ': 's', 'ษ': 's', 'ส': 's', 'ห': 'h', 'ฬ': 'l',
                'อ': 'o', 'ฮ': 'h', 'ะ': 'a', 'ั': 'a', 'า': 'a', 'ำ': 'am', 'ิ': 'i',
                'ี': 'i', 'ึ': 'ue', 'ื': 'ue', 'ุ': 'u', 'ู': 'u', 'เ': 'e', 'แ': 'ae',
                'โ': 'o', 'ใ': 'ai', 'ไ': 'ai', 'ا': 'a', 'ب': 'b', 'ت': 't', 'ث': 'th', 'ج': 'j', 'ح': 'h', 'خ': 'kh',
                'د': 'd', 'ذ': 'dh', 'ر': 'r', 'ز': 'z', 'س': 's', 'ش': 'sh', 'ص': 's',
                'ض': 'd', 'ط': 't', 'ظ': 'z', 'ع': 'a', 'غ': 'gh', 'ف': 'f', 'ق': 'q',
                'ك': 'k', 'ل': 'l', 'م': 'm', 'ن': 'n', 'ه': 'h', 'و': 'w', 'ي': 'y',
                '你': 'ni', '好': 'hao', '世': 'shi', '界': 'jie', '我': 'wo', '是': 'shi',
                '中': 'zhong', '国': 'guo', '人': 'ren', '谢': 'xie', '再': 'zai', '见': 'jian'
            };
            let from = "åàáãäâèéëêìíïîòóöôùúüûñç·/_,:;";
            let to = "aaaaaaeeeeiiiioooouuuunc------";
            string = string.trim().toLowerCase().split('').map(char => map[char] || char).join('');
            for (var i = 0, l = from.length; i < l; i++) {
                string = string.replace(new RegExp(from.charAt(i), 'g'), to.charAt(i));
            }
            return string.replace(/[^a-z0-9 -]/g, '').replace(/\s+/g, '-').replace(/-+/g, '-').replace(/^-+/, '').replace(/-+$/, '').replace(/ /g, '');
        },

        slugToString: function (string) {
            string = string.replace(/_/g, ' ').replace(/-/g, ' ');
            return string.charAt(0).toUpperCase() + string.slice(1);
        },

        // Random string
        random: function () {
            let chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
            let result = '';
            for (var i = 5; i > 0; --i) result += chars[Math.floor(Math.random() * 62)];
            return result;
        },

        // Check if a user type is an agent
        isAgent: function (user_type) {
            return user_type == 'agent' || user_type == 'admin' || user_type == 'bot';
        },

        // Get cors elapsed of a given date from now
        beautifyTime: function (datetime, extended = false, future = false) {
            let date;
            if (datetime == '0000-00-00 00:00:00') {
                return '';
            }
            if (datetime.indexOf('-') > 0) {
                let arr = datetime.split(/[- :]/);
                date = new Date(arr[0], arr[1] - 1, arr[2], arr[3], arr[4], arr[5]);
            } else {
                let arr = datetime.split(/[. :]/);
                date = new Date(arr[2], arr[1] - 1, arr[0], arr[3], arr[4], arr[5]);
            }
            let now = new Date();
            let date_string = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds()));
            let diff_days = ((now - date_string) / 86400000) * (future ? -1 : 1);
            let days = [mc_('Sunday'), mc_('Monday'), mc_('Tuesday'), mc_('Wednesday'), mc_('Thursday'), mc_('Friday'), mc_('Saturday')];
            let time = date_string.toLocaleTimeString(navigator.language, { hour: '2-digit', minute: '2-digit' });
            if (time.charAt(0) === '0' && (time.includes('PM') || time.includes('AM'))) {
                time = time.substring(1);
            }
            if (diff_days < 1 && now.getDate() == date_string.getDate()) {
                return extended ? `<span>${mc_('Today')}</span> <span>${time}</span>` : `<span data-today>${time}</span>`;
            } else if (diff_days < 6) {
                return `<span>${days[date_string.getDay()]}</span>${extended ? ` <span>${time}</span>` : ''}`;
            } else {
                return `<span>${date_string.toLocaleDateString(undefined, { year: '2-digit', month: '2-digit', day: '2-digit' })}</span>${extended ? ` <span>${time}</span>` : ''}`;
            }
        },

        // Get the unix timestamp value of a date string with format yyyy-mm-dd hh:mm:ss
        unix: function (datetime) {
            let arr = datetime.split(/[- :]/);
            return Date.UTC(arr[0], arr[1] - 1, arr[2], arr[3], arr[4], arr[5]);
        },

        // Generate a string containing the agent location and time
        getLocationTimeString: function (details, onSuccess) {
            if (details.timezone) {
                let location = {};
                location.timezone = details.timezone.value;
                location.country = details.country ? details.country.value : location.timezone.split('/')[0].replace(/_/g, ' ');
                location.city = details.city ? details.city.value : location.timezone.split('/')[1].replace(/_/g, ' ');
                onSuccess(`${new Intl.DateTimeFormat(undefined, { timeZone: location.timezone, hour: '2-digit', minute: '2-digit' }).format(new Date())} ${mc_('in')} ${location.city ? location.city : ''}${location.country ? ', ' + location.country : ''}`);
            }
        },

        // Date string
        dateDB: function (date) {
            if (date == 'now') {
                date = (new Date).toISOString().replace('T', ' ');
                if (date.indexOf('.') > 0) {
                    date = date.substr(0, date.indexOf('.'));
                }
                return date;
            } else {
                return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes()}:${date.getSeconds()}`;
            }
        },

        // Set and get users last activity
        updateUsersActivity: function (user_id, return_user_id, onSuccess) {
            if (MCPusher.active) {
                onSuccess((admin && MC_ADMIN_SETTINGS.bot_id == return_user_id) || (!admin && CHAT_SETTINGS.bot_id == return_user_id) ? 'online' : (MCPusher.online_ids.includes(return_user_id) ? 'online' : 'offline'));
            } else {
                MCF.ajax({
                    function: 'update-users-last-activity',
                    user_id: user_id,
                    return_user_id: return_user_id,
                    check_slack: !admin && CHAT_SETTINGS.slack_active
                }, (response) => {
                    if (response === 'online') {
                        onSuccess('online');
                    } else {
                        onSuccess('offline');
                    }
                });
            }
        },

        // Search functions
        search: function (search, searchFunction) {
            search = search.toLowerCase();
            if (search == previous_search) {
                global.find('.mc-search-btn i').mcLoading(false);
                return;
            }
            clearTimeout(timeout);
            timeout = setTimeout(function () {
                previous_search = search;
                searchFunction();
            }, 1000);
        },

        searchClear: function (icon, onSuccess) {
            let search = $(icon).next().val();
            if (search) {
                $(icon).next().val('');
                onSuccess();
            }
        },

        // Masi Chat error JS reporting
        error: function (message, function_name) {
            let is_full_error = message.includes(function_name);
            if (admin && MCAdmin.is_logout) {
                return;
            }
            if (message instanceof Error) {
                message = message.message;
            }
            if (message[message.length - 1] == '.') {
                message = message.slice(0, -1);
            }
            if (admin) {
                if (message && !function_name.includes('update-users-last-activity') && !function_name.startsWith('security-error')) {
                    MCAdmin.infoPanel(`<pre>${is_full_error ? `` : `[Error] [${function_name}] `}${message}. Check the console for more details.</pre>`, 'info', false, 'error');
                }
                MCApps.dialogflow.smart_reply_busy = false;
            }
            global.find('.mc-loading').mcLoading(false);
            MCChat.busy(false);
            MCF.event('MCError', { message: message, function_name: function_name });
            throw new Error(is_full_error ? message : `Masi Chat Error [${function_name}]: ${message}.`);
        },

        errorValidation: function (response, code = true) {
            return Array.isArray(response) && response[0] === 'validation-error' && (code === true || response[1] == code);
        },

        // Login
        loginForm: function (button, area = false, onSuccess = false, isRecursion = true) {
            button = $(button);
            if (!button.mcLoading()) {
                if (area === false) {
                    area = button.closest('.mc-rich-login');
                } else {
                    area = $(area);
                }
                let email = $.trim(area.find('#email input').val());
                let password = $.trim(area.find('#password input').val());
                if (!email || !password) {
                    area.find('.mc-info').html(mc_('Please insert email and password.')).mcActive(true);
                    MCChat.scrollBottom();
                } else {
                    MCF.ajax({
                        function: 'login',
                        email: email,
                        password: password
                    }, (response) => {
                        if (response && Array.isArray(response)) {
                            if (!admin && this.isAgent(response[0].user_type)) {
                                MCForm.showErrorMessage(area, 'You cannot sign in as an agent.');
                                MCChat.scrollBottom();
                            } else {
                                let user = new MCUser(response[0]);
                                user.set('conversation_id', MCChat.conversation ? MCChat.conversation.id : false);
                                this.loginCookie(response[1]);
                                this.event('MCLoginForm', user);
                                if (onSuccess) {
                                    onSuccess(response);
                                }
                            }
                            if (MCF.setting('wp-users-system') == 'wp') {
                                MCApps.wordpress.ajax('wp-login', { user: email, password: password });
                            }
                        } else if (admin && MCApps.is('wordpress')) {
                            return MCApps.wordpress.ajax('wp-login-admin', { user: email, password: password }, () => {
                                button.mcLoading(false);
                                if (isRecursion) {
                                    this.loginForm(button, area, onSuccess, false);
                                } else {
                                    area.find('.mc-info').html(mc_(response === 'ip-ban' ? 'Too many login attempts. Please retry again in a few hours.' : 'Invalid email or password.')).mcActive(true);
                                }
                            });
                        } else {
                            area.find('.mc-info').html(mc_(response === 'ip-ban' ? 'Too many login attempts. Please retry again in a few hours.' : 'Invalid email or password.')).mcActive(true);
                            if (!admin) {
                                MCChat.scrollBottom();
                            }
                        }
                        button.mcLoading(false);
                    });
                    area.find('.mc-info').html('').mcActive(false);
                    button.mcLoading(true);
                }
            }
        },

        // Set the login cookie
        loginCookie: function (value = false) {
            if (value === false) {
                return this.cookie('mc-login') ? this.cookie('mc-login') : storage('login');
            }
            if (CHAT_SETTINGS.cloud) {
                storage('login', value);
            } else {
                this.cookie('mc-login', value, 3650, 'set');
            }
        },

        // Login
        login: function (email = '', password = '', user_id = '', token = '', onSuccess = false) {
            MCF.ajax({
                function: 'login',
                email: email,
                password: password,
                user_id: user_id,
                token: token
            }, (response) => {
                if (response != false && Array.isArray(response)) {
                    this.loginCookie(response[1]);
                    if (onSuccess) {
                        onSuccess(response);
                    }
                    return true;
                } else {
                    return false;
                }
            });
        },

        // Logout
        logout: function (reload = true) {
            MCChat.stopRealTime();
            this.cookie('mc-login', '', '', false);
            this.cookie('mc-cloud', '', '', false);
            storage('open-conversation', '');
            storage('login', '');
            MCChat.conversations = false;
            activeUser(false);
            if (typeof mc_beams_client !== ND) {
                mc_beams_client.stop();
            }
            if (typeof MC_AJAX_URL !== ND) {
                MCF.ajax({
                    function: 'logout'
                }, () => {
                    MCF.event('MCLogout');
                    if (reload) {
                        setTimeout(() => { location.reload() }, 500);
                    }
                });
            }
        },

        // Return the active user
        activeUser: function () {
            return activeUser();
        },

        // Get the active user
        getActiveUser: function (database = false, onSuccess) {
            let app_login = MCApps.login();
            if (!app_login && (storage('wp-login') || storage('whmcs-login') || storage('perfex-login') || storage('aecommerce-login'))) {
                this.cookie('mc-login', '', '', 'delete');
                activeUser(false);
                storage('login', '');
                storage('wp-login', '');
                storage('whmcs-login', '');
                storage('perfex-login', '');
                storage('aecommerce-login', '');
            }
            MCF.ajax({
                function: 'get-active-user',
                db: database,
                login_app: JSON.stringify(app_login),
                user_token: MCF.getURL('token')
            }, (response) => {
                if (!response) {
                    onSuccess();
                    return false;
                } else {
                    if (response.cookie) {
                        MCF.loginCookie(response.cookie);
                    }
                    if (response.user_type) {
                        if (!admin && MCF.isAgent(response.user_type)) {
                            let message = 'You are logged in as both agent and user. Logout or use another browser, Incognito or Private mode, to login as user. Force a logout by running the function MCF.reset() in the console.';
                            if (!storage('double-login-alert')) {
                                storage('double-login-alert', true);
                                alert(message);
                            }
                            console.warn('Masi Chat: ' + message);
                            MCF.event('MCDoubleLoginError');
                        } else {
                            activeUser(new MCUser(response, response.phone ? { phone: response.phone } : {}));
                            MCPusher.start();
                            if (app_login) {
                                storage(app_login[1] + '-login', true);
                            }
                            onSuccess();
                            MCF.event('MCActiveUserLoaded', response);
                        }
                    }
                }
            });
        },

        // Clean
        reset: function () {
            let cookies = ['mc-login', 'mc-cloud', 'mc-dialogflow-disabled'];
            for (var i = 0; i < cookies.length; i++) {
                this.cookie(cookies[i], '', 0, false);
            }
            try { localStorage.removeItem('masi-chat') } catch (e) { }
            this.logout();
        },

        // Lightbox
        lightbox: function (content) {
            let lightbox = $(admin ? global : main).find('.mc-lightbox-media');
            lightbox.mcActive(true).find(' > div').html(content);
            if (admin) {
                MCAdmin.open_popup = lightbox;
            }
        },

        // Manage the local storage
        storage: function (key, value = ND) {
            try { if (typeof localStorage == ND) return false } catch (e) { return false }
            let settings = localStorage.getItem('masi-chat');
            if (settings === null) {
                settings = {};
            } else {
                settings = JSON.parse(settings);
            }
            if (value === ND) {
                return key in settings ? settings[key] : false;
            } else {
                if (!value) {
                    delete settings[key];
                } else {
                    settings[key] = value;
                }
                localStorage.setItem('masi-chat', JSON.stringify(settings));
            }
        },

        // Save the current time or check if the saved time is older than the given hours
        storageTime: function (key, hours = false) {
            let today = new Date();
            if (hours === false) {
                storage(key, today.getTime());
            } else {
                if (storage(key) == false) {
                    return true;
                }
                if ((today.getTime() - storage(key)) > (3600000 * hours)) {
                    storage(key, false);
                    return true;
                }
                return false;
            }
        },

        // Set or get a cookie
        cookie: function (name, value = false, expiration_days = false, action = 'get', seconds = false) {
            let cookie_https = location.protocol == 'https:' ? 'SameSite=None;Secure;' : '';
            let settings = window[admin ? 'MC_ADMIN_SETTINGS' : 'CHAT_SETTINGS'];
            let domain = settings && settings.cookie_domain ? 'domain=' + settings.cookie_domain + ';' : '';
            if (action == 'get') {
                if (!cookies_supported) {
                    return this.storage(name);
                }
                let cookies = document.cookie.split(';');
                for (var i = 0; i < cookies.length; i++) {
                    var cookie = cookies[i];
                    while (cookie.charAt(0) == ' ') {
                        cookie = cookie.substring(1);
                    }
                    if (cookie.indexOf(name) == 0) {
                        let value = cookie.substring(name.length + 1, cookie.length);
                        return this.null(value) ? false : value;
                    }
                }
                return false;
            } else if (action == 'set') {
                if (!cookies_supported) {
                    this.storage(name, value);
                } else {
                    let date = new Date();
                    date.setTime(date.getTime() + (expiration_days * (seconds ? 1 : 86400) * 1000));
                    document.cookie = name + "=" + value + ";expires=" + date.toUTCString() + ";path=/;" + cookie_https + domain;
                }
            } else if (this.cookie(name)) {
                if (!cookies_supported) {
                    this.storage(name, '');
                } else {
                    document.cookie = name + "=" + value + ";expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/;" + cookie_https + domain;
                }
            }
        },

        // Return a front setting or set it
        setting: function (key, value = -1) {
            if (value !== -1) {
                if (typeof CHAT_SETTINGS !== ND) CHAT_SETTINGS[key] = value;
            } else return typeof CHAT_SETTINGS !== ND && key in CHAT_SETTINGS ? CHAT_SETTINGS[key] : false;
        },

        // Return the shortcode array
        shortcode: function (shortcode) {
            return MCRichMessages.shortcode(shortcode);
        },

        // Events and webhooks
        event: function (name, parameters) {
            $(document).trigger(name, parameters);
            let webhooks = admin ? (typeof MC_ADMIN_SETTINGS === ND ? false : MC_ADMIN_SETTINGS.webhooks) : CHAT_SETTINGS.webhooks;
            let webhooks_list = { MCGetUser: 'get-user', MCSMSSent: 'sms-sent', MCLoginForm: 'login', MCRegistrationForm: 'registration', MCUserDeleted: 'user-deleted', MCNewMessagesReceived: 'new-messages', MCNewConversationReceived: 'new-conversation', MCSlackMessageSent: 'slack-message-sent', MCMessageDeleted: 'message-deleted', MCRichMessageSubmit: 'rich-message', MCNewEmailAddress: 'new-email-address' };
            if (webhooks && name in webhooks_list) {
                if (webhooks !== true) {
                    if (!Array.isArray(webhooks)) {
                        webhooks = webhooks.replace(/ /g, '').split(',');
                    }
                    if (!webhooks.includes(webhooks_list[name])) {
                        return;
                    }
                }
                MCF.ajax({
                    function: 'webhooks',
                    function_name: name,
                    parameters: parameters
                });
            }
        },

        // Translate a string
        translate: function (string) {
            if ((!admin && MCF.null(CHAT_SETTINGS)) || (admin && typeof MC_TRANSLATIONS === ND)) {
                return string;
            }
            let translations = admin ? MC_TRANSLATIONS : CHAT_SETTINGS.translations;
            if (translations && translations[string]) {
                return translations[string] ? translations[string] : string;
            } else {
                return string;
            }
        },

        // Escape a string
        escape: function (string) {
            return string ? string.replace(/</ig, '&lt;').replace(/javascript:|onclick|onerror|ontoggle|onmouseover|onload|oncontextmenu|ondblclick|onmousedown|onmouseenter|onmouseleave|onmousemove|onmouseout|onmouseup/ig, '') : '';
        },

        // Remove the Masi Chat syntax from a string
        strip: function (message) {
            message = message.replace('```', '');
            let patterns = [/\*([^\**]+)\*/, /\__([^\____]+)\__/, /\~([^\~~]+)\~/, /\`([^\``]+)\`/];
            for (var i = 0; i < 2; i++) {
                patterns.forEach(pattern => {
                    message = message.replace(pattern, (match) => match.replace(/[\*\_\~\`]/g, ''));
                });
            }
            return message.replace(/\\,/g, ',').replace(/\\:/g, ':');;
        },

        // Visibility change function
        visibilityChange: function (visibility = '') {
            this.visibility_status = visibility;
            let is_admin = admin && typeof MCAdmin !== ND;
            if (visibility == 'hidden') {
                if (!admin) {
                    MCChat.stopRealTime();
                }
                MCChat.tab_active = false;
                this.visibility_was_hidden = true;
            } else {
                if (activeUser() && !admin) {
                    MCChat.startRealTime();
                }
                MCChat.tab_active = true;
                clearInterval(interval);
                clearInterval(MCChat.audio_interval);
                if (MCChat.conversation) {
                    MCChat.conversation.updateMessagesStatus();
                    if (MCChat.chat_open || admin) {
                        MCChat.updateNotifications(MCChat.conversation.id);
                    }
                    if (is_admin) {
                        setTimeout(() => {
                            MCAdmin.conversations.notificationsCounterReset(MCChat.conversation.id);
                        }, 2000);
                    }
                }
                if (is_admin) {
                    if ((Date.now() - (mobile ? 60000 : 180000)) > conversation_update_last_check) {
                        MCAdmin.conversations.update();
                        conversation_update_last_check = Date.now();
                    }
                    if (mobile) {
                        MCChat.update();
                    }
                }
                document.title = document_title;
                this.serviceWorker.closeNotifications();
            }
        },

        // Convert a settings string to an Array
        settingsStringToArray: function (string) {
            if (this.null(string)) {
                return [];
            }
            let result = [];
            string = string.split(',');
            for (var i = 0; i < string.length; i++) {
                let values = string[i].split(':');
                result[values[0]] = values[1] == 'false' ? false : values[1] == 'true' ? true : values[1];
            }
            return result;
        },

        // Open a browser window
        openWindow: function (link, width = 550, height = 350) {
            let left = (screen.width / 2) - (width / 2);
            let top = (screen.height / 2) - (height / 2);
            window.open(link, 'targetWindow', 'toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=' + width + ',height=' + height + ', top=' + top + ', left=' + left);
            return false;
        },

        // Convert a date to local time
        convertUTCDateToLocalDate: function (datetime, utc_offset = 0) {
            datetime = new Date(datetime); // Y/m/d H:i:s
            datetime = new Date(datetime.getTime() + utc_offset * 3600000);
            return new Date(datetime.getTime() + utc_offset_user * -1);
        },

        // Load a JS or CSS file
        loadResource: function (src, js = false, onLoad = false, content = false) {
            let resource = document.createElement(js ? 'script' : 'link');
            if (src) {
                if (js) resource.src = src; else resource.href = src;
                resource.type = js ? 'text/javascript' : 'text/css';
            } else {
                resource.innerHTML = content;
            }
            if (onLoad) {
                resource.onload = function () {
                    onLoad();
                }
            }
            if (!js) {
                resource.rel = 'stylesheet';
            }
            document.head.appendChild(resource);
        },

        // Debounce
        debounce: function (bounceFunction, id, interval = 500) {
            if (!(id in timeout_debounce)) {
                timeout_debounce[id] = true;
                bounceFunction();
                setTimeout(() => {
                    delete timeout_debounce[id];
                }, interval);
            }
        },

        // Push Notifications
        serviceWorker: {
            sw: false,
            timeout: false,

            init: function () {
                if (navigator.serviceWorker) {
                    navigator.serviceWorker.register(admin || typeof MC_CLOUD_SW != 'undefined' ? MC_URL.replace('/script', '') + '/sw.js?v=' + version : CHAT_SETTINGS.push_notifications_url + '?v=' + version).then((registration) => {
                        registration.update();
                        this.sw = registration;
                    }).catch(function (error) {
                        console.warn(error);
                    });
                }
            },

            initPushNotifications: function () {
                if ((admin && (MC_ADMIN_SETTINGS.push_notifications_provider == 'pusher' || MC_ADMIN_SETTINGS.push_notifications_provider != 'onesignal')) || (!admin && (CHAT_SETTINGS.push_notifications_provider == 'pusher' || CHAT_SETTINGS.push_notifications_provider != 'pushalert'))) { // Deprecated: remove || CHAT_SETTINGS.push_notifications_provider != '' and || MC_ADMIN_SETTINGS.push_notifications_provider != 'pushalert'
                    MCPusher.initPushNotifications();
                } else {
                    $.getScript('https://cdn.onesignal.com/sdks/web/v16/OneSignalSDK.page.js', () => {
                        window.OneSignalDeferred = window.OneSignalDeferred || [];
                        OneSignalDeferred.push((OneSignal) => {
                            OneSignal.init({
                                appId: MC_ADMIN_SETTINGS.push_notifications_id
                            });
                        });
                        OneSignalDeferred.push((OneSignal) => {
                            OneSignal.User.PushSubscription.addEventListener('change', (event) => {
                                if (event.current.optedIn) {
                                    let external_id = (admin && MC_ADMIN_SETTINGS.cloud ? MC_ADMIN_SETTINGS.cloud.cloud_user_id + '-' : (!admin && CHAT_SETTINGS.cloud ? CHAT_SETTINGS.cloud.cloud_user_id + '-' : '')) + (admin ? MC_ACTIVE_AGENT.id : activeUser().id);
                                    if (external_id == 1) {
                                        external_id = 'MC-1';
                                    }
                                    OneSignal.User.addTag('user_type', admin ? 'agents' : 'users');
                                    OneSignal.login(external_id);
                                }
                                MCF.event('MCPushNotificationSubscription', event.current);
                            });
                        });
                        init_push_notifications = false;
                    });
                }
            },

            closeNotifications: function (index = 0) {
                if (this.sw) {
                    this.sw.getNotifications().then((notifications) => {
                        if (notifications.length) {
                            for (let i = 0; i < notifications.length; i += 1) {
                                notifications[i].close();
                            }
                        } else if (index < 300 && MCF.visibility_status == 'visible') {
                            setTimeout(() => {
                                this.closeNotifications(index + 1);
                            }, 10);
                        }
                    });
                }
            },

            pushNotification: function (message, interests = false) {
                let icon = admin ? MC_ACTIVE_AGENT.profile_image : activeUser().image;
                MCF.ajax({
                    function: 'push-notification',
                    title: admin ? MC_ACTIVE_AGENT.full_name : activeUser().name,
                    message: MCF.strip(message),
                    icon: icon.indexOf('user.svg') > 0 ? CHAT_SETTINGS.notifications_icon : icon,
                    interests: interests ? interests : MCChat.getRecipientUserID(),
                    conversation_id: MCChat.conversation ? MCChat.conversation.id : false
                }, (response) => {
                    return response
                });
            }
        },

        beautifyAttachmentName: function (name) {
            let index = name.indexOf('_');
            return index !== -1 ? name.substring(index + 1) : name;
        }
    }

    /*
    * ----------------------------------------------------------
    * PUSHER
    * ----------------------------------------------------------
    */

    var MCPusher = {
        channels: {},
        channels_presence: [],
        active: false,
        pusher: false,
        started: false,
        pusher_beams: false,
        initialized: false,
        online_ids: [],
        beams_loaded: false,

        // Initialize Pusher
        init: function (onSuccess = false) {
            if (MCPusher.active) {
                if (this.pusher) {
                    return onSuccess ? onSuccess() : true;
                } else if (onSuccess) {
                    $(window).one('MCPusherInit', () => {
                        onSuccess();
                    });
                } else {
                    return;
                }
                this.initialized = true;
                if (typeof Pusher === ND) {
                    $.getScript('https://js.pusher.com/8.2.0/pusher.min.js', () => {
                        window.Pusher = Pusher;
                        this.init_2();
                    }, true);
                } else {
                    this.init_2();
                }
            }
        },

        init_2: function () {
            this.pusher = new Pusher(admin ? MC_ADMIN_SETTINGS.pusher_key : CHAT_SETTINGS.pusher_key, {
                cluster: admin ? MC_ADMIN_SETTINGS.pusher_cluster : CHAT_SETTINGS.pusher_cluster,
                channelAuthorization: {
                    endpoint: MC_URL + '/include/pusher.php',
                    params: {
                        login: MCF.loginCookie(),
                        cloud_user_id: CHAT_SETTINGS.cloud ? CHAT_SETTINGS.cloud.cloud_user_id : false
                    }
                }
            });
            MCF.event('MCPusherInit');
        },

        // Initialize Push notifications
        initPushNotifications: function () {
            if (activeUser() || admin) {
                if (this.beams_loaded) {
                    this.initPushNotifications_2();
                } else {
                    $.getScript('https://js.pusher.com/beams/2.0.0-beta.0/push-notifications-cdn.js', () => {
                        this.initPushNotifications_2();
                    }, true);
                }
            }
        },

        initPushNotifications_2: function () {
            window.navigator.serviceWorker.ready.then((serviceWorkerRegistration) => {
                this.pusher_beams = new PusherPushNotifications.Client({
                    instanceId: admin ? MC_ADMIN_SETTINGS.push_notifications_id : CHAT_SETTINGS.push_notifications_id,
                    serviceWorkerRegistration: serviceWorkerRegistration,
                });
                MCF.serviceWorker.closeNotifications();
                this.pusher_beams.start().then(() => this.pusher_beams.setDeviceInterests(admin ? [MC_ACTIVE_AGENT.id, 'agents'] : [activeUser().id, 'users'])).catch(console.error);
                init_push_notifications = false;
            });
        },

        // Start Pusher and Push notifications
        start: function () {
            if (!admin && !this.started && activeUser()) {
                if (this.active) {
                    this.init(() => {
                        this.event('client-typing', (response) => {
                            if (response.user_id == MCChat.agent_id && MCChat.conversation && response.conversation_id == MCChat.conversation.id) {
                                MCChat.typing(-1, 'start');
                                clearTimeout(timeout_typing);
                                timeout_typing = setTimeout(() => { MCChat.typing(-1, 'stop') }, 1000);
                            }
                        });
                        this.event('new-message', (response) => {
                            if (response && activeUser() && response.conversation_id && (!activeUser().getConversationByID(response.conversation_id) || !MCChat.conversation || MCChat.conversation.id != response.conversation_id)) {
                                MCChat.updateConversations();
                            } else {
                                MCChat.update();
                            }
                        });
                        this.presence(1, () => {
                            this.started = true;
                            MCChat.automations.runAll();
                        });
                    });
                }
                if (CHAT_SETTINGS.push_notifications_users) {
                    if (CHAT_SETTINGS.push_notifications_provider == 'pusher' || CHAT_SETTINGS.push_notifications_provider != 'onesignal') { // Deprecated: remove || CHAT_SETTINGS.push_notifications != ''
                        if (typeof Notification != ND && Notification.permission == 'granted') {
                            this.initPushNotifications();
                        } else {
                            init_push_notifications = true;
                        }
                    }
                }
            }
        },

        // Subscribe to a channel
        subscribe: function (channel_name, onSuccess = false) {
            if (!this.pusher) {
                return this.init(() => { this.subscribe(channel_name, onSuccess) });
            }
            channel_name = this.cloudChannelRename(channel_name);
            let channel = this.pusher.subscribe(channel_name);
            channel.bind('pusher:subscription_error', (error) => {
                return console.log(error);
            });
            channel.bind('pusher:subscription_succeeded', () => {
                this.channels[channel_name] = channel;
                if (onSuccess) onSuccess();
            })
        },

        // Add event listener for a channel
        event: function (event, callback, channel = 'private-user-' + activeUser().id) {
            if (!this.pusher) {
                return this.init(() => { this.event(event, callback, channel) });
            }
            let channel_original = channel;
            channel = this.cloudChannelRename(channel);
            if (channel in this.channels) {
                this.channels[channel].unbind(event);
                this.channels[channel].bind(event, (data) => {
                    callback(data);
                });
            } else {
                this.subscribe(channel_original, () => { this.event(event, callback, channel_original) });
            }
        },

        // Trigger an event
        trigger: function (event, data = {}, channel = 'private-user-' + activeUser().id) {
            if (event.indexOf('client-') == 0) {
                return this.channels[this.cloudChannelRename(channel)].trigger(event, data);
            } else {
                MCF.ajax({
                    function: 'pusher-trigger',
                    channel: channel,
                    event: event,
                    data: data
                }, (response) => {
                    return response
                });
            }
        },

        // Presence  
        presence: function (index = 1, onSuccess) {
            if (!this.pusher) {
                return this.init(() => { this.presence() });
            }
            let channel = this.pusher.subscribe(this.cloudChannelRename('presence-' + index));
            channel.bind('pusher:subscription_succeeded', (members) => {
                if (members.count > 98) {
                    return this.subscribe(index + 1);
                }
                members.each((member) => {
                    if (this.presenceCheck(member)) {
                        this.online_ids.push(member.id);
                    }
                });
                MCChat.updateUsersActivity();
                if (onSuccess) {
                    onSuccess();
                }
            })
            channel.bind('pusher:subscription_error', (error) => {
                return console.log(error);
            });
            channel.bind('pusher:member_added', (member) => {
                if (this.presenceCheck(member)) {
                    this.presenceAdd(member.id);
                }
                if (admin && MCF.storageTime('online-user-notification-' + member.id, 24)) {
                    MCAdmin.users.onlineUserNotification(member);
                    MCF.storageTime('online-user-notification-' + member.id);
                }
            });
            channel.bind('pusher:member_removed', (member) => {
                this.presenceRemove(member.id);
            });
            this.channels_presence.push(channel);
            if (!admin && CHAT_SETTINGS.slack_active) {
                this.event('add-user-presence', (response) => {
                    this.presenceAdd(response.agent_id)
                });
                MCF.ajax({
                    function: 'slack-presence',
                    list: true
                }, (response) => {
                    for (var i = 0; i < response.length; i++) {
                        this.presenceAdd(response[i]);
                    }
                    MCChat.updateUsersActivity();
                });
            }
        },

        presenceCheck: function (member) {
            let agent = MCF.isAgent(member.info.user_type);
            return ((admin && !agent) || (!admin && agent)) && !this.online_ids.includes(member.id);
        },

        presenceAdd: function (user_id) {
            if (typeof user_id != ND && !this.online_ids.includes(user_id)) {
                this.online_ids.push(user_id);
                this.presenceUpdateAdmin(user_id);
                MCChat.updateUsersActivity();
            }
        },

        presenceRemove: function (user_id) {
            if (typeof user_id == ND) {
                return;
            }
            let index = this.online_ids.indexOf(user_id);
            if (index !== -1) {
                this.online_ids.splice(index, 1);
                this.presenceUpdateAdmin(user_id);
                MCChat.updateUsersActivity();
            } else if (admin) {
                global.find(`.mc-conversation-busy[data-agent="${user_id}"]`).remove();
            }
        },

        presenceUnsubscribe: function () {
            for (var i = 0; i < this.channels_presence.length; i++) {
                this.channels_presence[i].unsubscribe(this.cloudChannelRename('presence-' + (i + 1)));
            }
        },

        presenceUpdateAdmin: function (user_id) {
            if (admin) {
                if (global.find('.mc-area-users.mc-active').length) {
                    MCAdmin.users.update();
                }
                if (activeUser() && activeUser().id == user_id) {
                    MCAdmin.users.updateUsersActivity();
                }
            }
        },

        cloudChannelRename: function (channel) {
            return (CHAT_SETTINGS.cloud || (admin && MC_ADMIN_SETTINGS.cloud)) ? channel + '-' + (admin ? MC_ADMIN_SETTINGS.cloud.cloud_user_id : CHAT_SETTINGS.cloud.cloud_user_id) : channel;
        }
    }

    /*
    * ----------------------------------------------------------
    * GLOBAL FUNCTIONS
    * ----------------------------------------------------------
    */

    window.MCF = MCF;
    window.MCPusher = MCPusher;
    window.mc_current_user = mc_current_user;

    /*
    * ----------------------------------------------------------
    * JQUERY FUNCTIONS
    * ----------------------------------------------------------
    */

    $.fn.mcActive = function (show = -1) {
        if (show === -1) {
            return $(this).hasClass('mc-active');
        }
        $(this).setClass('mc-active', show);
        return this;
    };

    $.fn.mcLoading = function (value = 'check') {
        if (value == 'check') {
            return $(this).hasClass('mc-loading');
        } else {
            $(this).setClass('mc-loading', value);
        }
        return this;
    }

    $.fn.mcTogglePopup = function (button = false) {
        let showed = true;
        if (admin) MCAdmin.open_popup = false;
        if ($(this).mcActive()) {
            $(this).mcActive(false);
            global.removeClass('mc-popup-active');
            showed = false;
        } else {
            global.addClass('mc-popup-active');
            global.find('.mc-popup').mcActive(false);
            if (button) $(this).css('left', $(button).offset().left + 15).mcActive(true);
            if (admin) setTimeout(() => { MCAdmin.open_popup = this }, 500);
            MCF.deselectAll();
        }
        return showed;
    };

    $.fn.mcUploadFiles = function (onSuccess, index = false) {
        let files = $(this).prop('files');
        for (var i = (index === false ? 0 : index); i < (index === false ? files.length : index + 1); i++) {
            let file = files[i];
            let size_mb = file.size / (1024 ** 2);
            let max_size = admin ? MC_ADMIN_SETTINGS.max_file_size : CHAT_SETTINGS.max_file_size;
            if (size_mb > max_size) {
                let message = mc_('Maximum upload size is {R}MB. File size: {R2}MB.').replace('{R}', max_size).replace('{R2}', size_mb.toFixed(2));
                if (admin) {
                    MCAdmin.infoPanel(message, 'info');
                } else {
                    alert(message);
                }
            }
            let form = new FormData();
            form.append('file', file);
            MCF.upload(form, onSuccess);
        }
        $(this).value = '';
    }

    $.fn.setProfile = function (name = false, profile_image = false) {
        if (MCF.null(name)) {
            name = activeUser() ? activeUser().name : '';
        }
        if (MCF.null(profile_image)) {
            profile_image = activeUser() ? activeUser().image : MC_URL + '/media/user.svg';
        }
        if (name) {
            $(this).removeClass('mc-profile-empty');
        }
        $(this).find('img').attr('src', profile_image);
        $(this).find('.mc-name').html(name);
        return this;
    }

    $.fn.setClass = function (class_name, add = true) {
        if (add) {
            $(this).addClass(class_name);
        } else {
            $(this).removeClass(class_name);
        }
        return this;
    }

    /*
    * ----------------------------------------------------------
    * FUNCTIONS
    * ----------------------------------------------------------
    */

    function mcDelta(e) {
        let delta = e.originalEvent.wheelDelta;
        if (typeof delta == ND) {
            delta = e.originalEvent.deltaY;
        }
        if (typeof delta == ND) {
            delta = e.originalEvent.detail * -1;
        }
        return delta;
    }

    function loading(element) {
        if ($(element).mcLoading()) {
            return true;
        } else {
            $(element).mcLoading(true);
        }
        return false;
    }

    function storage(key, value = ND) {
        return MCF.storage(key, value);
    }

    function mc_(string) {
        return MCF.translate(string);
    }

    function activeUser(value = -1) {
        if (value === -1) {
            return window.mc_current_user;
        } else {
            window.mc_current_user = value;
        }
    }

    function setAudio() {
        let volume = admin ? MC_ADMIN_SETTINGS.sound.volume : CHAT_SETTINGS.sound.volume;
        if (MCChat.audio && volume) {
            MCChat.audio.volume = volume;
        }
    }

    function getMinutesSeconds(seconds) {
        let minutes = Math.floor(seconds / 60);
        seconds = seconds - minutes * 60;
        return (minutes ? minutes : '0') + ':' + (seconds < 10 ? '0' + seconds : seconds);
    }

    /* 
    * ----------------------------------------------------------
    * USER
    * ----------------------------------------------------------
    */

    class MCUser {
        constructor(details = {}, extra = {}) {
            this.details = details;
            this.extra = extra;
            this.conversations = [];
            this.processArray(details);
        }

        get id() {
            return this.get('id') ? this.get('id') : this.get('user_id');
        }

        get type() {
            return this.get('user_type');
        }

        get email() {
            return this.get('email');
        }

        get name() {
            return this.details.first_name ? this.details.first_name + (this.details.last_name ? ' ' + this.details.last_name : '') : '';
        }

        get nameBeautified() {
            let default_name = admin ? MC_ADMIN_SETTINGS.visitor_default_name : CHAT_SETTINGS.visitor_default_name;
            return !default_name || (this.details.last_name && this.details.last_name.charAt(0) != '#') ? this.name : default_name;
        }

        get image() {
            return this.get('profile_image');
        }

        get language() {
            let language = this.getExtra('language');
            if (!language) {
                language = this.getExtra('browser_language');
            }
            return language ? language.value.toLowerCase() : '';
        }

        get(id) {
            if (id in this.details && !MCF.null(this.details[id])) {
                return this.details[id];
            }
            return '';
        }

        getExtra(id) {
            if (id in this.extra && !MCF.null(this.extra[id])) {
                return this.extra[id];
            }
            return '';
        }

        set(id, value) {
            this.details[id] = value;
        }

        setExtra(id, value) {
            this.extra[id] = value;
        }

        // Initialization
        processArray(details) {
            if (details && details.details) {
                for (var i = 0; i < details.details.length; i++) {
                    this.setExtra(details.details[i].slug, details.details[i]);
                }
                delete details.details;
                this.details = details;
            }
        }

        // Get user details and extra details
        update(onSuccess) {
            if (this.id) {
                MCF.ajax({
                    function: 'get-user',
                    user_id: this.id,
                    extra: true
                }, (response) => {
                    this.processArray(response);
                    onSuccess();
                    MCF.event('MCGetUser', this);
                });
            } else {
                MCF.error('Missing user ID', 'MCUser.update');
            }
        }

        // Get user conversations
        getConversations(onSuccess = false, exclude_id) {
            if (this.id) {
                MCF.ajax({
                    function: 'get-user-conversations',
                    user_id: this.id,
                    exclude_id: exclude_id,
                    agent: MCF.isAgent(this.type)
                }, (response) => {
                    if (!MCF.errorValidation(response)) {
                        let conversations = [];
                        for (var i = 0; i < response.length; i++) {
                            if (MCChat.isConversationAllowed(response[i].source, response[i].conversation_status_code)) {
                                conversations.push(new MCConversation([new MCMessage(response[i])], response[i]));
                            }
                        }
                        this.conversations = conversations;
                        if (onSuccess) {
                            onSuccess(conversations);
                        }
                    }
                });
            } else {
                MCF.error('Missing user ID', 'MCUser.getConversations');
            }
        }

        // Get conversations code
        getConversationsCode(conversations = false) {
            let code = '';
            let active_conversation_id = MCChat.conversation ? MCChat.conversation.id : -1;
            if (!conversations) {
                conversations = this.conversations;
            }
            for (var i = 0; i < conversations.length; i++) {
                if (conversations[i] instanceof MCConversation) {
                    if (!admin && !MCChat.isConversationAllowed(conversations[i].get('source'), conversations[i].status_code)) {
                        continue;
                    }
                    let red_notifications = 0;
                    let is_active_conversation = active_conversation_id == conversations[i].id;
                    if (!admin && !is_active_conversation) {
                        for (var j = 0; j < MCChat.notifications.length; j++) {
                            if (MCChat.notifications[j][0] == conversations[i].id) {
                                red_notifications++;
                            }
                        }
                    }
                    code += `<li ${is_active_conversation ? 'class="mc-active" ' : ''}data-conversation-status="${is_active_conversation ? 0 : conversations[i].status_code}" data-conversation-id="${conversations[i].id}" data-department="${conversations[i].get('department')}">${conversations[i].getCode()}${red_notifications ? '<span data-count="' + red_notifications + '">' + red_notifications + '</span>' : ''}</li>`;
                } else {
                    MCF.error('Conversation not of type MCConversation', 'MCUser.getConversationsCode');
                }
            }
            return code;
        }

        // Get single conversation
        getFullConversation(conversation_id = false, onSuccess = false) {
            if (conversation_id !== false) {
                MCF.ajax({
                    function: 'get-conversation',
                    conversation_id: conversation_id
                }, (response) => {
                    let messages = [];
                    if (response) {
                        if (response === 'agent-not-authorized') {
                            window.location.href = MCF.URL();
                            return;
                        }
                        for (var i = 0; i < response.messages.length; i++) {
                            messages.push(new MCMessage(response.messages[i]));
                        }
                    }
                    if (onSuccess) {
                        onSuccess(new MCConversation(messages, response ? response.details : false));
                    }
                });
            } else {
                MCF.error('Missing conversation ID', 'MCUser.getFullConversation');
            }
        }

        getConversationByID(conversation_id, index = false) {
            for (var i = 0; i < this.conversations.length; i++) {
                if (this.conversations[i].id == conversation_id) {
                    return index ? i : this.conversations[i];
                }
            }
            return false;
        }

        // Add a new conversation
        addConversation(conversation) {
            if (conversation instanceof MCConversation) {
                let conversation_id = conversation.id;
                let is_new = true;
                for (var i = 0; i < this.conversations.length; i++) {
                    if (this.conversations[i].id == conversation_id) {
                        this.conversations[i] = conversation;
                        is_new = false;
                        break;
                    }
                }
                if (is_new) {
                    this.conversations.unshift(conversation);
                }
                return is_new;
            } else {
                MCF.error('Conversation not of type MCConversation', 'MCUser.addConversation');
            }
        }

        // Remove a conversation
        removeConversation(conversation_id) {
            let index = this.getConversationByID(conversation_id, true);
            if (index !== false) {
                this.conversations.splice(index, 1);
            }
        }

        // Get the last conversation
        getLastConversation() {
            return this.isConversationsEmpty() ? false : this.conversations[this.conversations.length - 1];
        }

        // Check if the conversation array is empty
        isConversationsEmpty() {
            return this.conversations.length == 0;
        }

        // Check if the extra array is empty
        isExtraEmpty() {
            return Object.keys(this.extra).length === 0 && this.extra.constructor === Object;
        }

        // Delete the user
        delete(onSuccess) {
            if (this.id) {
                MCF.ajax({
                    function: 'delete-user',
                    user_id: this.id
                }, () => {
                    MCF.event('MCUserDeleted', this.id);
                    onSuccess();
                    return true;
                });
            } else {
                MCF.error('Missing user ID', 'MCUser.delete');
            }
        }
    }
    window.MCUser = MCUser;

    /* 
    * ----------------------------------------------------------
    * MESSAGE
    * ----------------------------------------------------------
    */

    class MCMessage {
        constructor(details = {}) {
            this.details = Object.assign({}, details);
            let keys = ['message_status_code', 'message_id', 'message_profile_image', 'message_first_name', 'message_last_name', 'message_user_id', 'message_user_type'];
            let keys_delete = ['source', 'extra', 'title', 'tags', 'agent_id', 'department', 'last_update_time', 'conversation_creation_time', 'conversation_id', 'conversation_status_code', 'conversation_user_id'];
            for (var i = 0; i < keys.length; i++) {
                if (details[keys[i]]) {
                    this.details[keys[i].replace('message_', '')] = details[keys[i]];
                }
                delete this.details[keys[i]];
            }
            if (this.details.first_name) {
                this.details.full_name = this.details.first_name + (this.details.last_name ? ' ' + this.details.last_name : '');
            }
            if (details.last_update_time) {
                this.details.creation_time = details.last_update_time;
            }
            for (var i = 0; i < keys_delete.length; i++) {
                delete this.details[keys_delete[i]];
            }
            let payload = this.get('payload');
            if (payload) {
                try {
                    var json = JSON.parse(this.get('payload').replace("\\'", "'"));
                    if (json && typeof json === 'object') {
                        payload = json;
                    } else {
                        payload = {};
                    }
                } catch (e) {
                    payload = {};
                }
            } else {
                payload = {};
            }
            this.set('payload', payload);
        }

        get id() {
            return this.get('id');
        }

        get attachments() {
            return !MCF.null(this.details.attachments) ? JSON.parse(this.details.attachments) : [];
        }

        get message() {
            return admin ? (this.payload('translation') && this.payload('translation-language') == MC_ADMIN_SETTINGS.active_agent_language ? this.payload('translation') : (this.payload('original-message-language') == MC_ADMIN_SETTINGS.active_agent_language ? this.payload('original-message') : this.get('message'))) : this.get('message');
        }

        get(id) {
            if (id in this.details && !MCF.null(this.details[id])) {
                return this.details[id];
            }
            return '';
        }

        set(id, value) {
            this.details[id] = value;
        }

        payload(key = false, value = false) {
            let payload = this.get('payload');
            if (key !== false && value !== false) {
                payload[key] = value;
                this.set('payload', payload);
            } else if (key !== false) {
                return key in payload ? payload[key] : (payload.id && payload.id == key ? payload : false);
            }
            return ['boolean', 'string'].includes(typeof payload) ? [] : payload;
        }

        getCode() {
            let agent = MCF.isAgent(this.details.user_type);
            let message = this.message;
            let attachments = this.attachments;
            let reply = this.payload('reply');
            let admin_menu = admin ? MCAdmin.conversations.messageMenu(agent, message, !reply) : '';
            let attachments_code = '';
            let media_code = '';
            let thumb = (admin && MC_ADMIN_SETTINGS.show_profile_images) || (!admin && ((agent && !CHAT_SETTINGS.hide_agents_thumb) || (!agent && CHAT_SETTINGS.display_users_thumb))) ? `<div class="mc-thumb"><img loading="lazy" src="${this.details['profile_image']}"><div class="mc-tooltip"><div>${this.details['full_name']}</div></div></div>` : '';
            let css = ((admin && agent) || (!admin && !agent) ? 'mc-right' : '') + (thumb ? ' mc-thumb-active' : '');
            let type = '';
            let name = (!admin && agent && CHAT_SETTINGS.sender_name) || (admin && MC_ADMIN_SETTINGS.sender_name == 'chat-admin') ? `<span class="mc-agent-name">${this.get('full_name')}</span>` : '';
            let delivery_failed = admin ? this.payload().delivery_failed : false;

            if (!message && !attachments.length) {
                return '';
            }
            if (reply && MCChat.conversation && MCChat.conversation.getMessage(reply)) {
                reply = MCChat.conversation.getMessage(reply);
                let is_agent = MCF.isAgent(reply.get('user_type'));
                let text = reply.message;
                if (!text) {
                    text = '<div class="mc-message-attachments">';
                    attachments.forEach((attachment) => {
                        text += `<a>${attachment[0]}</a>`;
                    });
                    text += '</div>';
                }
                reply = `<div class="mc-reply-message${is_agent ? ' mc-reply-agent' : ''}"><span>${(is_agent && admin) || (!is_agent && !admin) ? mc_('You') : reply.get('full_name')}</span> ${text}</div>`;
            } else {
                reply = '';
            }

            // Rich Messages
            if (agent) {
                message = message.replace(/\n/g, '<br>');
                message = message.replace(/`([\s\S]*?)`/g, (match) => {
                    return match.replace(/\[/g, '&#91;');
                });
                let shortcodes = message.match(/\[([^\[\]]*(?:\[[^\[\]]*\][^\[\]]*)*)\]/g) || [];
                let is_rich_message = false;
                let count = shortcodes.length;
                for (var i = 0; i < count; i++) {
                    let settings = MCRichMessages.shortcode(shortcodes[i]);
                    if (settings[0]) {
                        if (settings[0] == 'action') {
                            message = message.replace(shortcodes[i], '');
                        } else {
                            let rich_message = MCRichMessages.generate(settings[1], settings[0]);
                            if (rich_message) {
                                message = message.replace(shortcodes[i], rich_message);
                                is_rich_message = true;
                                type = `data-type="${settings[0]}"`;
                            }
                        }
                    }
                }
                if (is_rich_message) {
                    css += ' mc-rich-cnt';
                    if (count > 1) {
                        type = 'data-type="multiple"';
                    }
                }
            } else if (message.includes('[rating ')) {
                let settings = MCRichMessages.shortcode(message);
                message = MCRichMessages.generate(settings[1], settings[0]);
            }
            let matches = message.includes('data-success') ? [...message.matchAll(/data-success="([^"]*)"/g)].map(match => match[1]) : [];
            for (var i = 0; i < matches.length; i++) {
                message = message.replace(matches[i], '{R' + i + '}');
            }
            message = this.render(message);
            for (var i = 0; i < matches.length; i++) {
                message = message.replace('{R' + i + '}', matches[i]);
            }

            // Attachments
            if (attachments.length) {
                attachments_code = '<div class="mc-message-attachments">';
                for (var i = 0; i < attachments.length; i++) {
                    let url = attachments[i][1];
                    let url_and_name = url + attachments[i][0];
                    if (MCF.getFileType(url_and_name) == 'image') {
                        let size = '';
                        if (attachments[i].length > 2) {
                            size = attachments[i][2].split('|');
                            size = `width="${size[0]}" style="aspect-ratio: ${size[0]} / ${size[1]}"`;
                        }
                        media_code += `<div class="mc-image${url_and_name.includes('.png') ? ' mc-image-png' : (url.includes('sticker_') ? ' mc-image-sticker' : '')}"><img loading="lazy" src="${url}" ${size}/></div>`;
                    } else if (MCF.getFileType(url_and_name) == 'audio' || url.includes('voice_message') || url.includes('audioclip')) {
                        if ((admin && !MC_ADMIN_SETTINGS.speech_recognition) || (!admin && !CHAT_SETTINGS.speech_recognition)) {
                            message = '';
                        }
                        attachments_code += `<div class="mc-player"><div class="mc-player-btn mc-icon-play"></div><div class="mc-player-speed"><div class="mc-player-speed-number">1</div><div class="mc-icon-close"></div></div><div class="mc-player-download mc-icon-arrow-down"></div><audio><source src="${url}" type="audio/mpeg"></audio></div>`;
                    } else {
                        attachments_code += `<a rel="noopener" target="_blank" href="${url}">${MCF.beautifyAttachmentName(attachments[i][0])}</a>`;
                    }
                }
                attachments_code += '</div>';
            }

            // Message creation
            return `<div data-id="${this.details.id}" class="${css}" ${type}>${thumb}${reply}<div class="mc-cnt"><div class="mc-message${media_code && !message ? ' mc-message-media' : ''}"${delivery_failed ? ' style="opacity:.7"' : ''}>${delivery_failed ? MCAdmin.conversations.getDeliveryFailedMessage(delivery_failed) : ''}${(name + message + media_code).trim()}</div>${attachments_code}<div class="mc-time">${MCF.beautifyTime(this.details.creation_time, true)}${admin && agent && this.details.status_code == 2 ? '<i class="mc-icon-check"></i>' : ''}</div></div>${admin_menu}</div>`;
        }

        render(message = false) {
            if (message === false) {
                message = '' + this.details.message;
            }
            let len = message.length;

            // Code block
            let codes = message.match(/```([\s\S]*?)```/g) || [];
            for (var i = 0; i < codes.length; i++) {
                message = message.replace(codes[i], '[code-' + i + ']');
            }

            // Breakline
            message = message.replace(/(?:\r\n|\r|\n)/g, '<br>');

            // Bold
            message = message.replace(/\*([^\**]+)\*/g, "<b>$1</b>");

            // Italic
            message = message.replace(/__(.+?)__/g, "<i>$1</i>");

            // Strikethrough
            message = message.replace(/\~([^\~~]+)\~/g, "<del>$1</del>");

            // Code
            message = message.replace(/\`([^\``]+)\`/g, "<code>$1</code>");

            // Single emoji
            if (((len == 6 || len == 5) && message.startsWith('&#x')) || len < 3 && message.match(/(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])/)) {
                message = `<span class="emoji-large">${message}</span>`;
            }

            // Links
            if (message.includes('](http')) {
                let temp = message.split('[');
                message = '';
                for (var i = 0; i < temp.length; i++) {
                    if (temp[i].includes('](http')) {
                        temp[i] = temp[i].substring(temp[i].indexOf('](') + 2, temp[i].length - 1);
                    }
                    message += temp[i];
                }
            }
            if (message.includes('www.')) {
                message = message.replaceAll('www.', 'https://www.').replaceAll('https://https:', 'https:').replaceAll('http://https:', 'http:');
            }
            let replace = [['href="http', '[L1]'], ['src="http', '[L2]'], ['url("http', '[L3]'], ['url(\'http', '[L4]'], ['extra="http', '[L5]'], ['data-link="http', '[L6]'], ['data-value="http', '[L7]']];
            for (var i = 0; i < replace.length; i++) {
                message = message.replaceAll(replace[i][0], replace[i][1]);
            }
            if (message.includes('http')) {
                message = message.autoLink({
                    target: '_blank',
                    callback: function (url) {
                        return url.includes('#mc-') ? `<a href="${url.split('#mc-')[0]}" target="_blank">${url.split('#mc-')[1].replaceAll('--', ' ')}</a>` : null;
                    }
                });
            }
            for (var i = 0; i < replace.length; i++) {
                message = message.replaceAll(replace[i][1], replace[i][0]);
            }

            // Code block restore
            for (var i = 0; i < codes.length; i++) {
                message = message.replace('[code-' + i + ']', '<pre>' + $.trim($.trim(codes[i].replace(/```<br>/g, '```').replace(/<br>```/g, '```')).replace(/```/g, '').replace(/(?:\r\n|\r|\n)/g, '<br>')) + '</pre>');
            }

            return message.replace(/&amp;lt;/g, '&lt;');
        }

        strip(message = false) {
            return MCF.strip(message === false ? '' + this.details.message : message);
        }
    }
    window.MCMessage = MCMessage;

    /* 
    * ----------------------------------------------------------
    * CONVERSATION
    * ----------------------------------------------------------
    */

    class MCConversation {
        constructor(messages, details) {
            this.details = MCF.null(details) ? {} : details;
            if (Array.isArray(messages)) {
                this.messages = [];
                if (messages.length) {
                    if (messages[0] instanceof MCMessage) {
                        this.messages = messages;
                    } else {
                        MCF.error('Messages not of type MCMessage', 'MCConversation.constructor');
                    }
                }
            } else {
                MCF.error('Message array not of type Array', 'MCConversation.constructor');
            }
            let keys = ['conversation_id', 'conversation_user_id', 'conversation_first_name', 'conversation_last_name', 'conversation_profile_image', 'conversation_user_type', 'conversation_creation_time', 'conversation_status_code'];
            let keys_delete = ['payload'];
            for (var i = 0; i < keys.length; i++) {
                if (details[keys[i]]) {
                    this.details[keys[i].replace('conversation_', '')] = details[keys[i]];
                }
                delete this.details[keys[i]];
            }
            for (var i = 0; i < keys_delete.length; i++) {
                delete this.details[keys_delete[i]];
            }
            if (details) {
                this.details.tags = 'tags' in details ? (typeof details.tags === 'string' ? details.tags.split(',') : details.tags) : [];
            }
        }

        get id() {
            return this.get('id');
        }

        get status_code() {
            return this.get('status_code');
        }

        get(id) {
            if (id in this.details && !MCF.null(this.details[id])) {
                return this.details[id];
            }
            if (id == 'title') {
                if (this.details.title) {
                    return this.details.title;
                } else if (this.details.first_name) {
                    return this.details.first_name + ' ' + this.details.last_name;
                } else if (this.messages.length) {
                    return this.messages[0].get('full_name');
                }
            }
            return '';
        }

        set(id, value) {
            this.details[id] = value;
        }

        getMessage(id) {
            for (var i = 0; i < this.messages.length; i++) {
                if (this.messages[i].id == id) {
                    this.messages[i].set('index', i);
                    return this.messages[i];
                }
            }
            return false;
        }

        getLastMessage() {
            let index = this.messages.length - 1;
            for (var i = index; i > -1; i--) {
                if (this.messages[i].message || this.messages[i].attachments.length || this.messages[i].payload('preview')) {
                    return this.messages[i];
                }
            }
            return false;
        }

        getLastUserMessage(index = false, agent = false) {
            if (index === false) {
                index = this.messages.length - 1;
            }
            for (var i = index; i > -1; i--) {
                let message = this.messages[i];
                let user_type = message.get('user_type');
                if ((message.message || message.attachments.length) && ((!agent && !MCF.isAgent(user_type)) || (agent === true && (user_type == 'agent' || user_type == 'admin')) || (agent == 'bot' && user_type == 'bot') || (agent == 'no-bot' && user_type != 'bot') || (agent == 'all' && MCF.isAgent(user_type)))) {
                    this.messages[i].set('index', i - 1);
                    return this.messages[i];
                }
            }
            return false;
        }

        getNextMessage(message_id, user_type = false) {
            let count = this.messages.length;
            for (var i = 0; i < count; i++) {
                if (this.messages[i].id == message_id && i < (count - 1)) {
                    for (var j = i + 1; j < count; j++) {
                        let message = this.messages[j];
                        let message_user_type = message.get('user_type');
                        let next_message = this.messages[i + 1];
                        if (!user_type || (user_type == 'agent' && MCF.isAgent(message_user_type)) || (user_type == 'user' && !MCF.isAgent(message_user_type))) {
                            return next_message;
                        }
                    }
                    break;
                }
            }
            return false;
        }

        getUserMessages(user_type = 'user') {
            let results = [];
            let checks = user_type == 'user' ? ['visitor', 'lead', 'user'] : (user_type == 'agents' ? ['agent', 'admin'] : ['bot']);
            for (var i = 0; i < this.messages.length; i++) {
                if (checks.includes(this.messages[i].get('user_type'))) {
                    this.messages[i].set('index', i);
                    results.push(this.messages[i]);
                }
            }
            return results;
        }

        updateMessage(id, message) {
            if (message instanceof MCMessage) {
                for (var i = 0; i < this.messages.length; i++) {
                    if (this.messages[i].id == id) {
                        this.messages[i] = message;
                        return true;
                    }
                }
            } else {
                MCF.error('Message not of type MCMessage', 'MCConversation.updateMessage');
            }
            return false;
        }

        addMessages(messages) {
            if (Array.isArray(messages)) {
                for (var i = 0; i < messages.length; i++) {
                    if (messages[i] instanceof MCMessage) {
                        this.messages.push(messages[i]);
                    }
                }
            } else {
                if (messages instanceof MCMessage) {
                    this.messages.push(messages);
                } else {
                    MCF.error('Messages not of type MCMessage', 'MCConversation.addMessages()');
                }
            }
            return this;
        }

        getCode(text_only = false) {
            let message = this.getLastMessage();
            if (message) {
                let text = message.message;
                if (!text && message.payload('preview')) {
                    text = message.payload('preview');
                }
                if (admin) {
                    text = message.payload().preview ? message.payload().preview : text;
                }
                text = text.replace(/(\r\n|\n|\r)/gm, ' ');
                if (text.indexOf('[') !== false) {
                    let shortcodes = text.match(/\[.+?\]/g) || [];
                    if (shortcodes.length) {
                        let shortcode = MCRichMessages.shortcode(shortcodes[0]);
                        if (shortcode[0]) {
                            text = text.replace(shortcodes[0], shortcode[0] == 'action' ? '' : mc_(shortcode[1].message ? shortcode[1].message : (shortcode[1].title ? shortcode[1].title : (shortcode[1].name && shortcode[1].name != 'false' && shortcode[1].name != 'true' ? shortcode[1].name : (shortcode[1].link ? shortcode[1].link : (shortcode[1].values ? shortcode[1].values.replaceAll(',', ', ').replaceAll('  ', ' ') : (shortcode[1].options ? shortcode[1].options.replaceAll(',', ', ').replaceAll('  ', ' ') : mc_(MCF.slugToString(shortcode[0])))))))));
                        }
                    }
                }
                if (!text && message.attachments.length) {
                    for (var i = 0; i < message.attachments.length; i++) {
                        text += message.attachments[i][0] + ' ';
                    }
                }
                text = MCF.strip(text);
                if (text.length > 114) {
                    text = text.substr(0, 114) + ' ...';
                }
                if (text_only) {
                    return text;
                }
                let title = this.get('title');
                if (!title || (activeUser() && activeUser().name == title) || (tickets && CHAT_SETTINGS.tickets_conversations_title_user)) {
                    title = activeUser() && message.get('user_id') == activeUser().id ? mc_('You') : message.get('full_name');
                }
                return `<div class="mc-conversation-item" data-user-id="${this.get('user_id')}"><img loading="lazy" src="${message.get('profile_image')}"><div><span class="mc-name">${title}</span><span class="mc-time">${MCF.beautifyTime(message.get('creation_time'))}</span></div><div class="mc-message">${text}</div></div>`;
            }
            return '';
        }

        deleteMessage(id) {
            for (var i = 0; i < this.messages.length; i++) {
                if (this.messages[i].id == id) {
                    this.messages.splice(i, 1);
                    return true;
                }
            }
            return false;
        }

        searchMessages(search, exact_match = false) {
            let results = [];
            for (var i = 0; i < this.messages.length; i++) {
                let message = this.messages[i].message;
                if ((exact_match && message == search) || (!exact_match && message.includes(search))) {
                    this.messages[i].set('index', i);
                    results.push(this.messages[i]);
                }
            }
            return results;
        }

        getAttachments() {
            let list = [];
            for (var i = 0; i < this.messages.length; i++) {
                let attachments = this.messages[i].attachments;
                for (var j = 0; j < attachments.length; j++) {
                    let link = attachments[j][1];
                    list.push([attachments[j][0], link, link.substr(link.lastIndexOf('.') + 1), this.messages[i].id]);
                }
            }
            return list;
        }

        updateMessagesStatus(ids = false) {
            if (ids) {
                for (var i = 0; i < this.messages.length; i++) {
                    let id = this.messages[i].id;
                    if (ids.includes(id)) {
                        let div = chat.find(`[data-id="${id}"] .mc-time`);
                        if (!div.find('i').length) {
                            div.append('<i class="mc-icon-check"></i>');
                        }
                    }
                }
            } else if (!admin && MCF.visibility_status == 'visible') {
                ids = [];
                for (var i = 0; i < this.messages.length; i++) {
                    let message = this.messages[i];
                    if (MCF.isAgent(message.get('user_type')) && message.get('status_code') != 2) {
                        ids.push(message.id);
                        message.set('status_code', 2);
                    }
                }
                if (ids.length) {
                    MCF.ajax({ function: 'update-messages-status', message_ids: ids });
                }
            }
        }
    }
    window.MCConversation = MCConversation;

    /* 
    * ----------------------------------------------------------
    * CHAT
    * ----------------------------------------------------------
    */

    var MCChat = {
        emoji_options: { range: 0, range_limit: 47, list: [], list_now: [], touch: false },
        initialized: false,
        editor_listening: false,
        conversation: false,
        is_busy: false,
        is_busy_update: false,
        is_busy_populate: false,
        chat_open: false,
        real_time: false,
        agent_id: -1,
        agent_online: false,
        user_online: false,
        expanded: false,
        main_header: true,
        start_header: false,
        desktop_notifications: false,
        flash_notifications: false,
        id_last_message: 0,
        id_last_message_conversation: 0,
        datetime_last_message_conversation: '2000-01-01 00:00:00',
        audio: false,
        audio_interval: false,
        tab_active: true,
        notifications: storage('notifications') ? storage('notifications') : [],
        typing_settings: { typing: false, sent: false, timeout: false },
        email_sent: false,
        dashboard: false,
        articles: false,
        articles_allowed_ids: false,
        articles_category: false,
        slack_channel: [-1, -1],
        skip: false,
        queue_interval: false,
        departments: false,
        default_department: null,
        default_agent: null,
        default_tags: null,
        offline_message_set: false,
        label_date: false,
        label_date_show: false,

        // Send a message
        sendMessage: function (user_id = -1, message = '', attachments = [], onSuccess = false, payload = false, conversation_status_code = false) {
            let is_dialogflow_human_takeover = dialogflow_human_takeover && MCApps.dialogflow.active();
            let is_return = false;
            let conversation = this.conversation;
            let reply_id = chat_editor.find('> [data-reply]').attr('data-reply');

            // Check settings and contents
            if (!activeUser() && !admin) {
                this.addUserAndLogin(() => {
                    return this.sendMessage(user_id, message, attachments, onSuccess, payload);
                }, true);
                this.busy(true);
                return;
            }
            if (!conversation) {
                let last_conversation = admin ? false : activeUser().getLastConversation();
                if (last_conversation && force_action != 'new-conversation' && (!MCChat.default_department || MCChat.default_department == last_conversation.get('department')) && (!MCChat.default_agent || MCChat.default_agent == last_conversation.get('agent_id'))) {
                    this.openConversation(last_conversation.id);
                    this.setConversation(last_conversation);
                    force_action = false;
                } else {
                    this.newConversation(conversation_status_code, user_id, '', [], (admin && MC_ACTIVE_AGENT.department ? MC_ACTIVE_AGENT.department : null), null, () => {
                        return this.sendMessage(user_id, message, attachments, onSuccess, payload);
                    });
                    this.busy(true);
                    return;
                }
            }
            this.calculateLabelDateFirst();
            if (user_id == -1) {
                user_id = admin ? MC_ACTIVE_AGENT.id : activeUser().id;
            }
            let is_user = user_id != bot_id;
            if (!message && !attachments.length) {
                message = chat_textarea.val().trim();
                chat_editor.find('.mc-attachments > div').each(function () {
                    let attachment = [$(this).attr('data-name'), $(this).attr('data-value')];
                    if ($(this).attr('data-size')) {
                        attachment.push($(this).attr('data-size'));
                    }
                    attachments.push(attachment);
                });
                if (admin && MCAdmin.must_translate && message) {
                    MCApps.dialogflow.translate([message], activeUser().language, (response) => {
                        if (response.length) {
                            let language = admin ? MC_ADMIN_SETTINGS.active_agent_language : activeUser().language;
                            if (payload) {
                                payload['original-message'] = message;
                                payload['original-message-language'] = language;
                            } else {
                                payload = { 'original-message': message, 'original-message-language': language };
                            }
                            if (response[0]) {
                                message = response[0];
                            }
                        }
                        this.sendMessage(user_id, message, attachments, onSuccess, payload, conversation_status_code);
                    });
                    is_return = true;
                }
            }
            this.busy(true);
            if (is_user) {
                chat_textarea.val('').css('height', '');
                chat_editor.find('.mc-attachments').html('');
            }
            chat_editor.mcActive(false);
            if (is_return) {
                return;
            }
            if (conversation_status_code === false && user_id == bot_id) {
                conversation_status_code = 'skip';
            }
            if (!admin && is_user && !is_dialogflow_human_takeover) {
                conversation_status_code = 2;
            }
            if (reply_id) {
                if (payload) {
                    payload.reply = message;
                } else {
                    payload = { reply: reply_id };
                }
            }

            // Send message
            if (message || attachments.length || payload) {
                let message_response = { user_id: user_id, user: activeUser(), conversation_id: conversation.id, conversation: conversation, conversation_status_code: conversation_status_code, attachments: attachments };
                MCF.ajax({
                    function: 'send-message',
                    user_id: user_id,
                    conversation_id: conversation.id,
                    message: message,
                    attachments: attachments,
                    conversation_status_code: conversation_status_code,
                    queue: !admin && CHAT_SETTINGS.queue && is_user,
                    payload: payload,
                    recipient_id: admin ? activeUser().id : false
                }, (response) => {
                    let send_slack = admin || !is_dialogflow_human_takeover || response.human_takeover_active

                    // Update the dashboard conversations area
                    if (!admin && user_id == bot_id) {
                        if (this.dashboard) {
                            this.updateConversations();
                        } else if (!this.chat_open) {
                            this.updateNotifications(conversation.id, response.id);
                        }
                    }

                    // Update the chat current conversation
                    if ((admin && !this.user_online) || (!admin && !this.agent_online)) {
                        this.update();
                    }

                    // Follow up and offline messages
                    if (!admin && is_user && !dialogflow_human_takeover) {
                        this.followUp();
                        this.offlineMessage();
                    }

                    // Dialogflow
                    if (!admin && is_user && (!payload || (payload.id != 'mc-human-takeover' && MCF.null(payload['skip-dialogflow'])))) {
                        MCApps.dialogflow.message(message, attachments);
                    }

                    // Slack and visitor to lead
                    if (!admin && is_user && activeUser().type == 'visitor') {
                        MCF.ajax({ function: 'update-user-to-lead', user_id: user_id }, () => {
                            activeUser().set('user_type', 'lead');
                            if (CHAT_SETTINGS.slack_active && send_slack) {
                                this.slackMessage(user_id, activeUser().name, activeUser().image, message, attachments);
                            }
                        });
                    } else if (send_slack && !this.skip) {
                        if (admin && MC_ADMIN_SETTINGS.slack_active) {
                            this.slackMessage(activeUser().id, MC_ACTIVE_AGENT.full_name, MC_ACTIVE_AGENT.profile_image, message, attachments);
                        } else if (CHAT_SETTINGS.slack_active) {
                            this.slackMessage(activeUser().id, (is_user ? activeUser().name : CHAT_SETTINGS.bot_name), (is_user ? activeUser().image : CHAT_SETTINGS.bot_image), message, attachments);
                        }
                    }

                    // Language detection
                    if (is_user && CHAT_SETTINGS.language_detection && conversation && message.split(' ').length && message.length > 3 && !MCF.storage('language-detection-completed')) {
                        MCF.ajax({ function: 'google-language-detection-update-user', user_id: user_id, string: message, token: MCApps.dialogflow.token }, (response) => {
                            if (response) {
                                CHAT_SETTINGS.translations = response;
                            }
                        });
                        MCF.storage('language-detection-completed', true);
                    }

                    // Articles
                    if (this.articles && !admin && CHAT_SETTINGS.articles && !CHAT_SETTINGS.office_hours && !this.isInitDashboard()) {
                        setTimeout(() => {
                            if (this.conversation && conversation.id == this.conversation.id) {
                                this.sendMessage(bot_id, '[articles]');
                                this.scrollBottom();
                                this.articles = false;
                            }
                        }, 5000);
                    }

                    // Queue
                    if (response.queue) {
                        this.queue(this.conversation.id);
                    }

                    // Events
                    message_response.message = response.message;
                    message_response.message_id = response.id;
                    MCF.event('MCMessageSent', message_response);
                    if (tickets) {
                        MCTickets.onMessageSent();
                    }
                    if (onSuccess) {
                        onSuccess(message_response);
                    }
                    if (response.notifications.length) {
                        MCF.event('MCNotificationsSent', response.notifications);
                    }

                    // Miscellaneous
                    if (this.skip) {
                        this.skip = false;
                    }
                    this.busy(false);
                });

                // Display the message as sending in progress
                if (is_user) {
                    message = MCF.escape(message);
                    chat.append((new MCMessage({ id: 'sending', profile_image: (admin ? MC_ACTIVE_AGENT.profile_image : activeUser().image), full_name: admin ? MC_ACTIVE_AGENT.full_name : activeUser().name, creation_time: '0000-00-00 00:00:00', message: message.replaceAll('<', '&lt;'), user_type: (admin ? 'agent' : 'user') })).getCode().replace('<div class="mc-time"></div>', `<div class="mc-time">${mc_('Sending')}<i></i></div>`));
                }
                if (!this.dashboard && (is_user || this.isBottom())) {
                    this.scrollBottom();
                }
            } else {
                this.busy(false);
            }
        },

        // Update message
        updateMessage: function (message_id, message = '') {
            MCF.ajax({
                function: 'update-message',
                message_id: message_id,
                message: message
            });
        },

        // Email notifications
        sendEmail: function (message, attachments, send_to_active_user = false, onSuccess = false) {
            let recipient_id = send_to_active_user ? (send_to_active_user === true ? activeUser().id : send_to_active_user) : this.getRecipientUserID();
            if (!admin && !isNaN(recipient_id) && this.agent_online) {
                return false;
            }
            MCF.ajax({
                function: 'create-email',
                recipient_id: recipient_id,
                sender_name: admin ? (send_to_active_user ? MC_ACTIVE_AGENT.full_name : activeUser().name) : (send_to_active_user ? CHAT_SETTINGS.bot_name : activeUser().name),
                sender_profile_image: admin ? (send_to_active_user ? MC_ACTIVE_AGENT.profile_image : activeUser().name) : (send_to_active_user ? CHAT_SETTINGS.bot_image : activeUser().image),
                message: message,
                attachments: attachments,
                department: this.conversation ? this.conversation.get('department') : false,
                conversation_id: this.conversation ? this.conversation.id : false
            }, (response) => {
                if (onSuccess) onSuccess(response);
            });
        },

        // SMS notifications
        sendSMS: function (message) {
            let recipient_id = this.getRecipientUserID();
            if (!admin && !isNaN(recipient_id) && this.agent_online) return false;
            MCF.ajax({
                function: 'send-sms',
                to: recipient_id,
                message: message,
                conversation_id: this.conversation ? this.conversation.id : false
            }, (response) => {
                if (response.status == 'sent' || response.status == 'queued') {
                    MCF.event('MCSMSSent', { recipient_id: this.getRecipientUserID(), message: message, response: response });
                } else if (response.message) {
                    MCF.error(response.message, 'MCChat.sendSMS');
                }
            });
        },

        // Desktop notifications
        desktopNotification: function (title, message, icon, conversation_id = false, user_id = false) {
            if (Notification.permission !== 'granted') {
                Notification.requestPermission();
            } else {
                let notify = MCF.serviceWorker.sw.showNotification(title, {
                    body: MCF.strip(message),
                    icon: icon.indexOf('user.svg') > 0 ? CHAT_SETTINGS.notifications_icon : icon
                });
                notify.onclick = () => {
                    if (admin) {
                        if (conversation_id) {
                            MCAdmin.conversations.openConversation(conversation_id, user_id == false ? activeUser().id : user_id);
                            MCAdmin.conversations.update();
                        } else if (user_id) {
                            MCAdmin.profile.show(user_id);
                        }
                    } else {
                        this.start();
                    }
                    window.focus();
                }
            }
        },

        // Returns the recipient user ID 
        getRecipientUserID: function () {
            return admin ? activeUser().id : (this.lastAgent(false) ? this.lastAgent(false).user_id : (MCF.null(this.conversation.get('agent_id')) ? (MCF.null(this.conversation.get('department')) ? 'agents' : 'department-' + this.conversation.get('department')) : this.conversation.get('agent_id')));
        },

        // Editor submit message
        submit: function () {
            if (!this.is_busy) {
                if (audio_recorder_dom && audio_recorder_dom.mcActive()) {
                    let button = audio_recorder_dom.find('.mc-btn-mic');
                    audio_recorder_dom.mcActive(false);
                    if (button.hasClass('mc-icon-pause')) {
                        button.click();
                    }
                    setTimeout(() => {
                        let form = new FormData();
                        let source = this.conversation ? this.conversation.get('source') : false;
                        if (source == 'wa' || audio_recorder_chunks.length) {
                            form.append('file', new File([source == 'wa' ? MCAudioRecorder.blob() : new Blob(audio_recorder_chunks, { type: source == 'ig' ? 'audio/wav' : 'audio/mp3' })], 'voice_message.' + (source == 'ig' ? 'wav' : 'mp3')));
                            MCF.upload(form, (response) => {
                                MCChat.uploadResponse(response);
                                this.submit();
                            });
                        } else {
                            this.submit();
                        }
                        audio_recorder_dom.find('.mc-icon-close').click();
                    }, 100);
                    return;
                }
                this.sendMessage();
                if (CHAT_SETTINGS.cron_email_piping_active) {
                    setTimeout(() => {
                        MCF.ajax({ function: 'email-piping' });
                        CHAT_SETTINGS.cron_email_piping_active = true;
                    }, 60000);
                    CHAT_SETTINGS.cron_email_piping_active = false;
                }
                if (init_push_notifications) {
                    MCF.serviceWorker.initPushNotifications();
                }
                if (admin) {
                    MCAdmin.conversations.setStatus(1, false, true);
                }
                MCChat.cancelReply();
            }
        },

        // Initialize the chat
        initChat: function () {
            if (admin) return;
            MCF.getActiveUser(true, () => {
                let active = activeUser() !== false;
                let user_type = active ? activeUser().type : false;
                if (!tickets && CHAT_SETTINGS.popup && !storage('popup') && (!mobile || !CHAT_SETTINGS.popup_mobile_hidden)) {
                    this.popup();
                }
                MCChat.automations.runAll();
                if (!tickets && CHAT_SETTINGS.privacy && !CHAT_SETTINGS.registration_required && !storage('privacy-approved')) {
                    this.privacy();
                    return;
                }
                if (typeof Notification !== ND && !CHAT_SETTINGS.push_notifications_users && (['all', 'users'].includes(CHAT_SETTINGS.desktop_notifications) || (admin && CHAT_SETTINGS.desktop_notifications == 'agents'))) {
                    this.desktop_notifications = true;
                }
                if (['all', 'users'].includes(CHAT_SETTINGS.flash_notifications) || (admin && CHAT_SETTINGS.flash_notifications == 'agents')) {
                    this.flash_notifications = true;
                }
                if (this.registration(true) && !tickets) {
                    this.registration();
                    if (!active && CHAT_SETTINGS.visitors_registration) {
                        this.addUserAndLogin();
                    }
                    return;
                }
                if (!active && (typeof MC_WP_WAITING_LIST !== ND || CHAT_SETTINGS.visitors_registration || CHAT_SETTINGS.welcome || tickets || CHAT_SETTINGS.flow_on_load) && (!tickets || !CHAT_SETTINGS.tickets_registration_required)) {
                    this.addUserAndLogin(() => {
                        this.welcome();
                        MCApps.dialogflow.flowOnLoad();
                        MCApps.woocommerce.waitingList();
                        this.finalizeInit();
                    });
                } else if (!this.conversation && active) {
                    this.populateConversations();
                } else {
                    this.finalizeInit();
                }
                if (CHAT_SETTINGS.header_name && active && user_type == 'user' && !tickets) {
                    chat_header.find('.mc-title').html(`${mc_('Hello')} ${activeUser().nameBeautified}!`);
                }
                this.welcome();
                if (!MCPusher.active) {
                    setInterval(() => {
                        this.updateConversations();
                        this.updateUsersActivity();
                    }, 10200);
                }
                MCApps.dialogflow.flowOnLoad();
                MCApps.woocommerce.waitingList();
                this.scrollBottom(true);
            });
        },

        finalizeInit: function () {
            if (!this.initialized) {
                main.attr('style', '');
                if (!admin && !tickets) {
                    if (this.isInitDashboard()) {
                        this.showDashboard();
                    }
                    if (!mobile && window.innerHeight < 760) {
                        main.find(' > .mc-body').css('max-height', (window.innerHeight - 130) + 'px');
                    }
                }
                this.initialized = true;
                if (!admin) {
                    if (activeUser() && !this.registration(true)) {
                        if (storage('open-conversation')) {
                            this.openConversation(storage('open-conversation'));
                        }
                        if (MCF.getURL('conversation')) {
                            this.openConversation(MCF.getURL('conversation'));
                        }
                    }
                    if (!this.chat_open && ((!mobile && storage('chat-open')) || MCF.getURL('chat') == 'open') || MCF.getURL('conversation')) {
                        setTimeout(() => { this.start(); }, 500);
                    }
                    if (CHAT_SETTINGS.woocommerce_returning_visitor) {
                        if (storage('returning-visitor') === false) {
                            MCF.storageTime('returning-visitor');
                        } else if (MCF.storageTime('returning-visitor', 24) && !storage('returning-visitor-processed')) {
                            setTimeout(() => {
                                MCF.ajax({
                                    function: 'woocommerce-returning-visitor'
                                }, () => {
                                    storage('returning-visitor-processed', true);
                                });
                            }, 15000);
                        }
                    }
                    if (CHAT_SETTINGS.timetable_type) {
                        MCChat.offlineMessage();
                    }
                    if (CHAT_SETTINGS.queue_human_takeover && MCApps.dialogflow.humanTakeoverActive()) {
                        CHAT_SETTINGS.queue = true;
                    }
                    $(window).on('resize', function () {
                        if (!mobile && window.innerHeight < 760) {
                            main.find(' > .mc-body').css('max-height', (window.innerHeight - 130) + 'px');
                        }
                    });
                    MCApps.dialogflow.flowOnLoad();
                }
                if (tickets) {
                    MCTickets.init();
                }
                MCF.event('MCInit');
            }
        },

        // Initialize the chat settings and open the chat
        start: function () {
            if (this.initialized) {
                this.populate();
                this.headerAgent();
                this.updateUsersActivity();
                this.startRealTime();
                this.popup(true);
                if (this.conversation) {
                    this.updateNotifications(this.conversation.id);
                }
                main.mcActive(true);
                $('body').addClass('mc-chat-open');
                this.chat_open = true;
                if (CHAT_SETTINGS.welcome_trigger == 'open' && !this.registration(true)) {
                    this.welcome();
                }
                MCApps.martfury.privateChat();
                this.calculateLabelDates();
            }
        },

        // Open or close the chat
        open: function (open = true) {
            if (open && !this.chat_open) {
                this.start();
                this.chat_open = true;
                this.startRealTime();
                main.mcActive(true);
                $('body').addClass('mc-chat-open');
                storage('chat-open', true);
                if (this.conversation) {
                    storage('last-open-message', this.conversation.getLastMessage().id);
                }
                if (mobile) {
                    history.pushState({ 'chat-open': true }, '', '');
                }
                MCF.event('MCChatOpen');
            } else if (!open && this.chat_open) {
                main.mcActive(false);
                this.stopRealTime();
                this.chat_open = false;
                storage('chat-open', false);
                $('body').removeClass('mc-chat-open');
                MCF.event('MCChatClose');
            }
        },

        // Get a full conversation and display it in the chat
        openConversation: function (conversation_id) {
            activeUser().getFullConversation(conversation_id, (response) => {
                if (!response.id || !MCChat.isConversationAllowed(response.get('source'), response.status_code)) {
                    storage('open-conversation', '');
                    return false;
                }
                this.setConversation(response);
                this.hideDashboard();
                this.populate();
                this.main_header = false;
                if (storage('chat-open')) {
                    MCChat.open();
                }
                if (storage('queue') == conversation_id) {
                    this.queue(conversation_id);
                }
                if (this.chat_open || tickets) {
                    this.updateNotifications(conversation_id);
                }
                if (tickets) {
                    MCTickets.activateConversation(response);
                }
                storage('open-conversation', conversation_id);
                MCF.event('MCConversationOpen', response);
            });
        },

        // Update the active conversation with the latest messages
        update: function () {
            if (this.conversation) {
                if (this.is_busy_update) return;
                let last_message = this.conversation.getLastMessage();
                let is_update = false;
                MCF.ajax({
                    function: 'get-new-messages',
                    conversation_id: this.conversation.id,
                    datetime: this.datetime_last_message_conversation,
                    last_id: this.id_last_message_conversation
                }, (response) => {
                    let count = response.length;
                    this.is_busy_update = false;
                    if (this.conversation) {
                        if (Array.isArray(response) && count > 0 && (!last_message || last_message.id != response[count - 1].id || last_message.message != response[count - 1].message || last_message.payload != response[count - 1].payload || last_message.attachments != response[count - 1].attachments)) {
                            let code = '';
                            let messages = [];
                            let id_check = [];
                            let dialogflow_activation = false;

                            // Generate and add the new messages
                            this.calculateLabelDateFirst();
                            for (var i = 0; i < count; i++) {
                                if (!id_check.includes(response[i].id) && (!admin || this.conversation.id == response[i].conversation_id)) {
                                    let message = new MCMessage(response[i]);
                                    let payload = message.payload();
                                    this.id_last_message_conversation = message.id;
                                    this.datetime_last_message_conversation = message.get('creation_time');

                                    // Payload
                                    if (payload.event) {
                                        let event = payload.event;
                                        if ((event == 'delete-message' && this.conversation.getMessage(message.id) !== false) || (!admin && !message.message && !message.attachments.length && !payload)) {
                                            this.deleteMessage(message.id);
                                        }
                                        if (event == 'woocommerce-update-cart' && !admin) {
                                            MCApps.woocommerce.updateCart(payload.action, payload.id);
                                        }
                                        if (event == 'woocommerce-checkout' && !admin) {
                                            MCApps.wordpress.ajax('url', { url_name: 'checkout' }, (response) => {
                                                setTimeout(() => document.location = response, 500);
                                            });
                                        }
                                        if (!MCApps.dialogflow.active() && (event == 'conversation-status-update-3' || event == 'conversation-status-update-4' || event == 'activate-bot')) {
                                            MCApps.dialogflow.active('activate');
                                            dialogflow_activation = true;
                                        }
                                        if (event == 'conversation-status-update-3') {
                                            this.conversationArchived();
                                        }
                                    }
                                    if (payload['human-takeover'] && CHAT_SETTINGS.queue_human_takeover) {
                                        CHAT_SETTINGS.queue = true;
                                        MCChat.queue(MCChat.conversation.id);
                                    }
                                    if (payload['human-takeover-fallback']) {
                                        MCApps.dialogflow.typing_enabled = false;
                                    }

                                    // Message
                                    if (this.conversation.getMessage(response[i].id)) {
                                        this.conversation.updateMessage(message.id, message);
                                        chat.find(`[data-id="${message.id}"]`).replaceWith(message.getCode());
                                        is_update = true;
                                    } else {
                                        if (message.message || message.attachments.length) {
                                            chat.find(`[data-id="sending"]`).remove();
                                        }
                                        if (this.conversation.id == response[i].conversation_id) {
                                            this.conversation.addMessages(message);
                                            code += message.getCode();
                                            is_update = false;
                                        }
                                    }
                                    this.conversation.updateMessagesStatus();
                                    messages.push(message);
                                    id_check.push(message.id);
                                    if (this.chat_open) {
                                        storage('last-open-message', message.id);
                                    }
                                    if (!admin && ((this.dashboard || !this.chat_open || !this.tab_active) && (message.get('user_id') != activeUser().id) && (message.message || message.attachments.length))) {
                                        this.updateNotifications(this.conversation.id, message.id);
                                    }
                                }
                            }
                            chat.append(code);

                            // Update status code
                            let last_message = this.conversation.getLastMessage();
                            let user_type = last_message ? last_message.get('user_type') : false;
                            let is_agent = MCF.isAgent(user_type);
                            let is_agent_human = is_agent && user_type != 'bot';
                            if (!admin && is_agent_human) {
                                if (this.chat_open) {
                                    if (last_message && !last_message.message.includes('mc-rich-success') && (!response[0].payload || !response[0].payload.includes('conversation-status-update'))) {
                                        this.setConversationStatus(0);
                                    }
                                    if (CHAT_SETTINGS.follow) {
                                        clearTimeout(timeout);
                                    }
                                }
                                if (!dialogflow_activation) {
                                    MCApps.dialogflow.active(false);
                                }
                            }

                            // Queue
                            if (storage('queue') == this.conversation.id && is_agent_human) {
                                this.queue('clear');
                            }

                            // Flash notifications
                            if (messages.length && (!(MCF.null(messages[0].message) && MCF.null(messages[0].attachments)) || count != 1)) {
                                if (!admin && !this.tab_active) {
                                    this.flashNotification();
                                }

                                // Sound notifications
                                if (this.audio && ((!admin && is_agent) || (admin && !is_agent))) {
                                    this.playSound();
                                }
                            }

                            // Miscellaneous
                            this.headerAgent();
                            if (!is_update && !this.dashboard) {
                                this.scrollBottom();
                                setTimeout(() => { this.scrollBottom() }, 300);
                            }
                            if (CHAT_SETTINGS.auto_open && (this.dashboard || !this.chat_open)) {
                                this.open();
                            }
                            if (is_agent_human) {
                                this.typing(-1, 'stop');
                            }
                            this.busy(false);
                            MCF.event('MCNewMessagesReceived', { messages: messages, conversation_id: this.conversation.id });
                            if (tickets) {
                                MCTickets.onNewMessageReceived(messages[0], this.conversation.id);
                            }
                        }
                    }
                });
                this.is_busy_update = true;
                setTimeout(() => { this.is_busy_update = false }, 5000);
            } else {
                this.updateConversations();
            }
        },

        // Update the user conversations list with the latest conversations and messages
        updateConversations: function () {
            if (activeUser()) {
                MCF.ajax({
                    function: 'get-new-user-conversations',
                    datetime: this.id_last_message
                }, (response) => {
                    if (response.length) {
                        this.id_last_message = response[0].message_id;
                        if (this.chat_open) {
                            storage('last-open-message', this.id_last_message);
                        }
                        for (var i = 0; i < response.length; i++) {
                            let status_code = response[i].conversation_status_code;
                            if (!MCChat.isConversationAllowed(response[i].source, status_code)) {
                                continue;
                            }
                            let conversation_id = response[i].conversation_id;
                            let message = new MCMessage(response[i]);
                            let conversation = new MCConversation([message], response[i]);
                            let is_new = activeUser().addConversation(conversation);

                            // Red notifications
                            if (response[i].message_user_id != activeUser().id && (this.conversation.id != conversation_id || !this.chat_open) && (message.message || message.attachments.length)) {
                                this.updateNotifications(conversation_id, message.id);
                                if (CHAT_SETTINGS.auto_open) {
                                    this.open();
                                }
                            }

                            // Payload
                            let payload = message.payload();
                            if (typeof payload !== 'boolean' && payload.event) {
                                let event = payload.event;
                                if (event == 'open-chat') {
                                    if (this.conversation.id != conversation_id || this.dashboard) {
                                        this.openConversation(conversation_id);
                                    }
                                    if (!mobile) {
                                        setTimeout(() => { this.open() }, 500);
                                    }
                                }
                                if (!message.message && !message.attachments.length) {
                                    continue;
                                }
                            }

                            if (!this.tab_active) {

                                // Desktop notifications
                                if (this.desktop_notifications) {
                                    MCChat.desktopNotification(message.get('full_name'), message.message, message.get('profile_image'));
                                }

                                // Flash notifications
                                if (this.flash_notifications) {
                                    this.flashNotification();
                                }

                                // Sound notifications
                                if (!admin && this.audio && CHAT_SETTINGS.sound && (!this.chat_open || this.dashboard || this.conversation.id != conversation_id) && !(MCF.null(message.message) && MCF.null(message.attachments))) {
                                    this.playSound();
                                }
                            }
                            if (is_new) {
                                MCF.event('MCNewConversationReceived', conversation);
                            }
                            if (tickets) {
                                MCTickets.onConversationReceived(conversation);
                            }
                            if (!this.conversation && is_new) {
                                this.openConversation(conversation.id);
                            }
                        }
                        if (this.conversation) {
                            this.conversation.updateMessagesStatus();
                        }
                        main.find('.mc-user-conversations').html(activeUser().getConversationsCode());
                        main.find('.mc-dashboard-conversations').setClass('mc-conversations-hidden', main.find('.mc-user-conversations > li').length > 3);
                    }
                });
            }
        },

        // Generate the conversation code and display it
        populate: function () {
            if (this.conversation) {
                let code = '';
                let notify = chat.find(' > .mc-notify-message');
                let last_date = false;
                let conversation_id = this.conversation.id;
                for (var i = 0; i < this.conversation.messages.length; i++) {
                    let message = this.conversation.messages[i];
                    let current_date = MCF.beautifyTime(message.get('creation_time'));
                    if (current_date.includes('today')) {
                        current_date = `<span>${mc_('Today')}</span>`;
                    }
                    if (current_date != last_date && (message.message || message.attachments.length)) {
                        code += `<div class="mc-label-date">${current_date}</div>`;
                        last_date = current_date;
                    }
                    code += message.getCode();
                }
                chat.html((notify.length ? notify[0].outerHTML : '') + code);
                if (!this.dashboard) {
                    this.scrollBottom();
                    this.calculateLabelDates();
                    if ((admin && MC_ADMIN_SETTINGS.notify_email_cron) || (!admin && CHAT_SETTINGS.notify_email_cron)) {
                        let last_message = this.conversation.getLastUserMessage(false, !admin);
                        if (last_message && last_message.id != storage('email-cron-' + conversation_id)) {
                            MCF.ajax({ function: 'remove-email-cron', conversation_id: conversation_id });
                            storage('email-cron-' + conversation_id, last_message.id);
                        }
                    }
                }
            } else if (activeUser() && !activeUser().isConversationsEmpty()) {
                if (CHAT_SETTINGS.disable_dashboard) {
                    this.openConversation(activeUser().conversations[0].id);
                } else {
                    this.showDashboard();
                }
            }
        },

        // Populate the dashboard with all conversations
        populateConversations: function (onSuccess = false) {
            if (!this.is_busy_populate && activeUser()) {
                this.is_busy_populate = true;
                setTimeout(() => { this.is_busy_populate = false }, 5000);
                activeUser().getConversations((response) => {
                    let count = response.length;
                    let converstion_ids = [];
                    if (count) {
                        let now = Date.now();
                        let last_message = response[0].messages[0];
                        this.id_last_message = last_message.id;
                        for (var i = 0; i < count; i++) {
                            converstion_ids.push(response[i].id);
                            if (!tickets && (response[i].status_code == 1 && storage('last-open-message') < last_message.id) && (!this.conversation || this.conversation.id != response[i].id)) {
                                this.updateNotifications(response[i].id, last_message.id);
                            }
                            if (!mobile && (now - MCF.UTC(response[i].messages[0].get('creation_time'))) < 6000) {
                                this.open();
                            }
                        }
                        main.find('.mc-user-conversations').html(activeUser().getConversationsCode());
                        main.find('.mc-dashboard-conversations').setClass('mc-conversations-hidden', main.find('.mc-user-conversations > li').length > 3);
                    }
                    main.setClass('mc-no-conversations', !count);
                    if ((!this.initialized || force_action == 'open-conversation') && count == 1 && !this.isInitDashboard() && !storage('open-conversation')) {
                        this.openConversation(activeUser().getLastConversation().id);
                        if (force_action == 'open-conversation') {
                            force_action = '';
                        }
                    }
                    for (var i = 0; i < MCChat.notifications.length; i++) {
                        this.updateNotifications(MCChat.notifications[i][0], converstion_ids.includes(MCChat.notifications[i][0]) ? MCChat.notifications[i][1] : false);
                    }
                    if (onSuccess) {
                        onSuccess(response);
                    }
                    this.finalizeInit();
                    MCF.event('MCPopulateConversations', { conversations: response });
                });
            }
        },

        // Create a new conversation and optionally send the first message
        newConversation: function (status_code, user_id = - 1, message = '', attachments = [], department = null, agent_id = null, onSuccess = false) {
            if (activeUser()) {
                MCF.ajax({
                    function: 'new-conversation',
                    status_code: status_code,
                    title: tickets ? main.find('.mc-ticket-title input').val() : null,
                    department: MCF.null(department) ? this.default_department : department,
                    agent_id: MCF.null(agent_id) ? this.default_agent : agent_id,
                    tags: this.default_tags,
                    source: tickets ? 'tk' : ''
                }, (response) => {
                    if (MCF.errorValidation(response, 'user-not-found')) {
                        this.addUserAndLogin(() => {
                            this.newConversation(status_code, user_id, message, attachments, department, agent_id, onSuccess);
                        });
                        return;
                    }
                    let conversation = new MCConversation([], response.details);
                    this.setConversation(conversation);
                    if (message || attachments.length) {
                        this.sendMessage(user_id, message, attachments);
                    }
                    if (user_id != bot_id) {
                        setTimeout(() => { this.queue(conversation.id) }, 1000);
                    }
                    if (onSuccess) {
                        onSuccess(conversation);
                    }
                });

            } else {
                MCF.error('activeUser() not setted', 'MCChat.newConversation');
            }
        },

        // Set an existing conversation as active conversation
        setConversation: function (conversation) {
            if (conversation instanceof MCConversation) {
                let conversations = activeUser().conversations;
                let is_new = true;
                this.conversation = conversation;
                this.id_last_message_conversation = !this.conversation.getLastMessage() ? 0 : this.conversation.getLastMessage().id;
                this.datetime_last_message_conversation = this.conversation.getLastMessage() == false ? '2000-01-01 00:00:00' : this.conversation.getLastMessage().get('creation_time');
                if (conversation.id != this.conversation.id) {
                    this.queue(conversation.id);
                }
                for (var i = 0; i < conversations.length; i++) {
                    if (conversations[i].id == conversation.id) {
                        conversations[i] = conversation;
                        is_new = false;
                        break;
                    }
                }
                if (is_new) {
                    conversations.push(conversation);
                }
                storage('open-conversation', conversation.id);
                MCApps.dialogflow.typing_enabled = true;
                this.headerAgent();
                MCF.event('MCActiveConversationChanged', conversation);
            } else {
                MCF.error('Value not of type MCConversation', 'MCChat.setConversation');
            }
        },

        // Manage all the queue functionalities
        queue: function (conversation_id) {
            if (conversation_id == 'clear') {
                main.removeClass('mc-notify-active mc-queue-active');
                chat.find(' > .mc-notify-message').remove();
                clearInterval(this.queue_interval);
                this.queue_interval = false;
                storage('queue', '');
                if (CHAT_SETTINGS.queue_sound && !MCChat.tab_active) {
                    MCChat.playSound(999);
                }
                return;
            }
            if (!admin && CHAT_SETTINGS.queue) {
                MCF.ajax({
                    function: 'queue',
                    conversation_id: conversation_id,
                    department: this.conversation.get('department')
                }, (response) => {
                    chat.find(' > .mc-notify-message').remove();
                    let position = response[0];
                    if (position == 0) {
                        this.queue('clear');
                    } else {
                        let time = (!CHAT_SETTINGS.queue_response_time ? 5 : parseInt(CHAT_SETTINGS.queue_response_time)) * position;
                        let text = mc_(!CHAT_SETTINGS.queue_message ? 'Please wait for an agent. You are number {position} in the queue. Your waiting time is approximately {minutes} minutes.' : CHAT_SETTINGS.queue_message).replace('{position}', '<b>' + position + '</b>').replace('{minutes}', '<b>' + time + '</b>');
                        if (response[1]) {
                            chat.prepend(`<div class="mc-notify-message mc-rich-cnt"><div class="mc-cnt"><div class="mc-message">${text}</div></div></div>`);
                        }
                        if (this.queue_interval === false) {
                            this.queue_interval = setInterval(() => { this.queue(conversation_id) }, 10100);
                            if (response[1]) {
                                main.addClass('mc-notify-active mc-queue-active');
                            }
                            storage('queue', conversation_id);
                        }
                    }
                    MCF.event('MCQueueUpdate', position);
                });
            }
        },

        // Get the departments details and generate the department code
        getDepartmentCode(department_id, onSuccess) {
            if (this.departments) {
                if (department_id == 'all') {
                    let code = '';
                    for (var key in this.departments) {
                        this.getDepartmentCode(this.departments[key].id, (response) => { code += response; });
                    };
                    onSuccess(code);
                } else {
                    onSuccess(`<div data-color="${this.departments[department_id].color}">${this.departments[department_id].image ? `<img loading="lazy" src="${this.departments[department_id].image}" />` : ''}<div>${this.departments[department_id].name}<div></div>`);
                }
            } else {
                MCF.ajax({
                    function: 'get-departments'
                }, (response) => {
                    if (response) {
                        this.departments = response;
                        this.getDepartmentCode(department_id, onSuccess);
                    }
                });
            }
        },

        // Start and stop the real time check of new messages
        startRealTime: function () {
            if (MCPusher.active) return;
            this.stopRealTime();
            this.real_time = setInterval(() => {
                this.update();
                this.typing(admin ? (activeUser() ? activeUser().id : -1) : this.agent_id, 'check');
            }, 1000);
        },

        stopRealTime: function () {
            clearInterval(this.real_time);
        },

        // Check if the agent is online and set the online status of the active user
        updateUsersActivity: function () {
            if (activeUser()) {
                MCF.updateUsersActivity(activeUser().id, this.agent_id, (response) => {
                    if (!this.typing_settings.typing) {
                        if (response == 'online' || this.agent_id == bot_id) {
                            $(chat_status).addClass('mc-status-online').html(mc_('Online'));
                            this.agent_online = this.agent_id != bot_id;
                        } else {
                            $(chat_status).removeClass('mc-status-online').html(mc_('Away'));
                            this.agent_online = false;
                        }
                    }
                });
            }
        },

        // Show the loading icon and put the chat in busy mode
        busy: function (value) {
            if (chat_editor) {
                chat_editor.find('.mc-loader').mcActive(value);
            }
            this.is_busy = value;
            MCF.event('MCBusy', value);
        },

        // Manage the agent header
        headerAgent: function (is_default_chatbot = false) {
            if (!admin && !tickets && !this.dashboard && this.conversation && (this.agent_id == -1 || (this.conversation.getLastMessage() && MCF.isAgent(this.conversation.getLastMessage().get('user_type')) && this.conversation.getLastMessage().get('user_id') != this.agent_id))) {
                let agent = this.lastAgent(false);
                agent = agent && MCApps.dialogflow.humanTakeoverActive() && MCPusher.active && !MCPusher.presenceCheck({ info: { user_type: 'agent' }, id: agent.user_id }) ? agent : this.lastAgent();
                if (!agent && is_default_chatbot) {
                    agent = { user_id: CHAT_SETTINGS.bot_id, full_name: CHAT_SETTINGS.bot_name, profile_image: CHAT_SETTINGS.bot_image };
                }
                this.headerReset();
                if (agent) {
                    this.agent_id = agent.user_id;
                    chat_header.addClass('mc-header-agent').attr('data-agent-id', this.agent_id).html(`<div class="mc-dashboard-btn mc-icon-arrow-left"></div><div class="mc-profile"><img loading="lazy" src="${agent['profile_image']}" /><div><span class="mc-name">${agent['full_name']}</span><span class="mc-status">${mc_('Away')}</span></div><i class="mc-icon mc-icon-close ${CHAT_SETTINGS.close_chat ? 'mc-close-chat' : 'mc-responsive-close-btn'}"></i></div><div class="mc-label-date-top"></div>`);
                    chat_status = chat_header.find('.mc-status');
                    this.updateUsersActivity();
                    this.label_date = chat_header.find('.mc-label-date-top');
                    if (MCF.storageTime('header-animation', 1)) {
                        this.headerAnimation();
                    }
                } else {
                    chat_header.html(this.start_header[0]).addClass(this.start_header[1]);
                }
            }
        },

        headerReset: function () {
            if (this.start_header == false) {
                this.start_header = [chat_header.html(), chat_header.attr('class')];
            }
            chat_header.removeClass('mc-header-main mc-header-brand mc-header-agent mc-header-minimal');
            this.main_header = false;
        },

        headerAnimation: function () {
            chat_header.addClass('mc-header-animation');
            setTimeout(() => { chat_header.removeClass('mc-header-animation') }, 8000);
            MCF.storageTime('header-animation');
        },

        // Return the last agent of the active conversation
        lastAgent: function (bot = true) {
            let agent = false;
            if (this.conversation) {
                let message = this.conversation.getLastUserMessage(false, bot ? 'all' : true);
                if (message) {
                    agent = { user_id: message.get('user_id'), full_name: message.get('full_name'), profile_image: message.get('profile_image') };
                }
            }
            return agent;
        },

        // Scroll the chat to the bottom
        scrollBottom: function (top = false) {
            setTimeout(() => {
                chat_scroll_area.scrollTop(top ? 0 : chat_scroll_area[0].scrollHeight);
                this.scrollHeader();
            }, 20);
        },

        // Check if the chat is at bottom
        isBottom: function () {
            return chat_scroll_area[0].scrollTop === (chat_scroll_area[0].scrollHeight - chat_scroll_area[0].offsetHeight);
        },

        // Dashboard header animation
        scrollHeader: function () {
            if (this.main_header && this.dashboard) {
                let scroll = chat_scroll_area.scrollTop();
                if (scroll > -1 && scroll < 1000) {
                    chat_header.find('.mc-content').css({ 'opacity': (1 - (scroll / 500)), 'top': (scroll / 10 * -1) + 'px' });
                };
            }
        },

        // Display the dashboard area 
        showDashboard: function () {
            if (!admin && !tickets) {
                main.addClass('mc-dashboard-active');
                chat_header.removeClass('mc-header-agent');
                this.hidePanel()
                if (this.start_header) {
                    chat_header.html(this.start_header[0]).addClass(this.start_header[1]);
                }
                chat_scroll_area.find(' > div').mcActive(false);
                main.find('.mc-dashboard').mcActive(true);
                this.populateConversations();
                this.conversation = false;
                this.agent_id = -1;
                this.stopRealTime();
                this.dashboard = true;
                this.main_header = true;
                this.scrollBottom(true);
                MCF.event('MCDashboard');
            }
        },

        // Hide the dashboard area
        hideDashboard: function () {
            if (!admin && !tickets) {
                chat.mcActive(true);
                main.removeClass('mc-dashboard-active').find('.mc-dashboard').mcActive(false);
                this.dashboard = false;
                this.headerAgent();
                this.scrollHeader(0);
                if (this.chat_open) {
                    this.startRealTime();
                }
                MCF.event('MCDashboardClosed');
            }
        },

        // Show a chat panel
        showPanel: function (name, title) {
            if (tickets) {
                return MCTickets.showPanel(name, title);
            }
            let panel = chat_scroll_area.find(' > .mc-panel-' + name);
            if (panel.length) {
                chat_scroll_area.find(' > div').mcActive(false);
                panel.mcActive(true);
                if (!this.start_header) {
                    this.start_header = [chat_header.html(), chat_header.attr('class')];
                }
                chat_header.attr('class', 'mc-header mc-header-panel').html(`<span>${mc_(title)}</span><div class="mc-dashboard-btn mc-icon-close"></div>`);
                main.addClass('mc-panel-active');
                this.dashboard = true;
            }
            MCF.event('MCPanelActive', name);
        },

        hidePanel: function () {
            main.removeClass('mc-panel-active');
            chat_header.removeClass('mc-header-panel');
        },

        // Clear the conversation area and the active conversation
        clear: function () {
            this.conversation = false;
            chat.html('');
        },

        // Update the red notification counter of the chat
        updateNotifications: function (conversation_id, message_id = false) {
            let check = false;
            if (message_id) {
                for (var i = 0; i < this.notifications.length; i++) {
                    if (this.notifications[i][0] == conversation_id && message_id == this.notifications[i][1]) {
                        check = true;
                    }
                }
                if (!check) {
                    this.notifications.push([conversation_id, message_id]);
                    if (!this.dashboard && this.conversation && this.conversation.id != conversation_id) {
                        this.headerAnimation();
                    }
                }
            } else {
                let active_notifications = [];
                for (var i = 0; i < this.notifications.length; i++) {
                    if (this.notifications[i][0] == conversation_id) {
                        check = true;
                    } else {
                        active_notifications.push(this.notifications[i]);
                    }
                }
                if (!admin && check && ['0', 0].includes(this.conversation.status_code)) {
                    this.setConversationStatus(1);
                }
                this.notifications = active_notifications;
            }
            let count = this.notifications.length;
            storage('notifications', this.notifications);
            main.find('.mc-chat-btn span').attr('data-count', count).html(count > -1 ? count : 0);
            MCF.event('MCNotificationsUpdate', { conversation_id: conversation_id, message_id: message_id });
        },

        // Set the active conversation status
        setConversationStatus: function (status_code) {
            if (this.conversation) {
                MCF.ajax({
                    function: 'update-conversation-status',
                    conversation_id: this.conversation.id,
                    status_code: status_code
                }, () => {
                    this.conversation.set('status_code', status_code);
                    MCF.event('MCActiveConversationStatusUpdated', { conversation_id: this.conversation.id, status_code: status_code });
                });
                return true;
            }
            return false;
        },

        // Typing status
        typing: function (user_id = -1, action = 'check') {
            if (this.conversation) {
                let valid = this.agent_online || (admin && this.user_online);
                if (action == 'check' && !MCPusher.active && user_id != -1 && user_id != bot_id && valid) {
                    MCF.ajax({
                        function: 'is-typing',
                        user_id: user_id,
                        conversation_id: this.conversation.id
                    }, (response) => {
                        if (response && !this.typing_settings.typing) {
                            this.typing(-1, 'start');
                        } else if (!response && this.typing_settings.typing) {
                            this.typing(-1, 'stop');
                        }
                    });
                } else if (action == 'set' && valid) {
                    let source = this.conversation.get('source');
                    clearTimeout(timeout_typing);
                    if (source) {
                        source = source == 'fb' ? [source, activeUser().getExtra('facebook-id').value, this.conversation.get('extra')] : (source == 'tw' ? [source, activeUser().getExtra('twitter-id').value] : false);
                    }
                    if (MCPusher.active) {
                        MCF.debounce(() => {
                            MCPusher.trigger('client-typing', { user_id: admin ? MC_ACTIVE_AGENT.id : activeUser().id, conversation_id: this.conversation.id });
                            if (source) {
                                MCF.ajax({ function: 'set-typing', source: source });
                            }
                        }, '#2');
                    } else {
                        if (!this.typing_settings.sent) {
                            this.typing_settings.sent = true;
                            MCF.ajax({
                                function: 'set-typing',
                                user_id: user_id,
                                conversation_id: this.conversation.id,
                                source: source
                            });
                            this.typing(user_id, 'set');
                        } else {
                            clearTimeout(this.typing_settings.timeout);
                            this.typing_settings.timeout = setTimeout(() => {
                                MCF.ajax({
                                    function: 'set-typing',
                                    user_id: user_id,
                                    conversation_id: -1
                                }, () => {
                                    this.typing_settings.sent = false;
                                });
                            }, 2000);
                        }
                    }
                } else if (action == 'start' || action == 'stop') {
                    let start = action == 'start';
                    if (!admin && chat_status) {
                        if (start) {
                            $(chat_status).addClass('mc-status-typing').html(mc_('Typing'));
                        } else {
                            let online = this.agent_online || this.agent_id == bot_id;
                            clearTimeout(timeout_typing);
                            $(chat_status).removeClass('mc-status-typing').html(mc_(online ? 'Online' : 'Away'));
                            if (online) {
                                $(chat_status).addClass('mc-status-online');
                            }
                        }
                    }
                    this.typing_settings.typing = start;
                    MCF.event('MCTyping', start);
                }
            }
        },

        // Articles
        showArticles: function (id = false, is_category = false) {
            let panel = tickets ? main.find('.mc-panel-main .mc-panel') : chat_scroll_area.find(' > .mc-panel-articles');
            panel.html('').mcLoading(true);
            this.showPanel('articles', CHAT_SETTINGS.articles_title ? CHAT_SETTINGS.articles_title : 'Help Center');
            if (!id && CHAT_SETTINGS.articles_categories) {
                this.getArticleCategories((categories) => {
                    this.showArticles_(categories, panel, { categories: categories });
                }, 'parent');
            } else {
                this.getArticles(is_category ? false : id, (articles) => {
                    if (id && !is_category) {
                        panel.html(this.getArticleCode(articles[0]));
                        panel.mcLoading(false);
                        MCF.event('MCArticles', { id: id, articles: articles });
                    } else {
                        this.showArticles_(articles, panel, { id: id, articles: articles });
                    }
                }, is_category ? id : false);
            }
        },

        showArticles_: function (items, panel, payload) {
            let code = '';
            let language = typeof MC_LANG != ND ? MC_LANG[0] : false;
            for (var i = 0; i < items.length; i++) {
                let is_article = 'content' in items[i];
                code += `<div data-id="${items[i].id}"${is_article ? '' : ' data-is-category="true"'}><div>${is_article ? items[i].title : (language && items[i].languages && items[i].languages[language] ? items[i].languages[language].title : items[i].title)}</div><span>${is_article ? items[i].content : (language && items[i].languages && items[i].languages[language] ? items[i].languages[language].description : (items[i].description ? items[i].description : ''))}</span></div>`;
            }
            panel.html(`<div class="mc-articles">${code}</div>`);
            panel.mcLoading(false);
            MCF.event('MCArticles', payload);
        },

        getArticles: function (id = false, onSuccess = false, category = false, count = false) {
            MCF.ajax({
                function: 'get-articles',
                categories: this.articles_category ? this.articles_category : category,
                id: id ? id : this.articles_allowed_ids,
                count: count,
                return_categories: this.articles_category ? true : false,
                full: id,
                skip_language: true
            }, (response) => {
                onSuccess(response);
            });
        },

        getArticleCategories: function (onSuccess = false, category_type = false) {
            MCF.ajax({
                function: 'get-articles-categories',
                category_type: category_type
            }, (response) => {
                onSuccess(response);
            });
        },

        searchArticles: function (search, button, target) {
            if (search) {
                $(button).mcLoading(true);
                MCF.ajax({
                    function: 'search-articles',
                    search: search
                }, (articles) => {
                    let code = '';
                    let count = articles.length;
                    if (count == 0) {
                        code += `<p class="mc-no-results">${mc_('No articles found.')}</p>`;
                    } else {
                        for (var i = 0; i < articles.length; i++) {
                            code += `<div data-id="${articles[i].id}"><div>${articles[i].title}</div><span>${articles[i].content}</span></div>`;
                        }
                    }
                    $(target).html(code);
                    $(button).mcLoading(false);
                });
            }
        },

        setArticleRating: function (article_id, rating, onSuccess = false) {
            MCF.ajax({
                function: 'article-ratings',
                article_id: article_id,
                rating: rating
            }, (response) => {
                if (onSuccess) onSuccess(response);
            });
        },

        articleRatingOnClick: function (button) {
            let article = $(button).closest('.mc-article');
            if (!article[0].hasAttribute('data-user-rating')) {
                $(button).parent().mcLoading();
                let rating = $(button).attr('data-rating') == 'positive' ? 1 : -1;
                let article_id = $(button).closest('.mc-article').attr('data-id');
                MCChat.setArticleRating(article_id, rating, () => {
                    MCF.storage('article-rating-' + article_id, rating);
                    article.attr('data-user-rating', rating);
                    $(button).parent().mcLoading(false);
                });
            }
        },

        getArticleCode: function (article) {
            let user_rating = MCF.storage('article-rating-' + article.id);
            let code = '';
            if (this.articles_categories && !MCF.null(article.categories)) {
                for (var i = 0; i < this.articles_categories.length; i++) {
                    let category_id = this.articles_categories[i].id;
                    if (article.categories.includes(category_id) || article.parent_category == category_id) {
                        code += `<span data-id="${category_id}">${mc_(this.articles_categories[i].title)}</span>`;
                    }
                }
            }
            return `<div data-id="${article.id}"${user_rating ? ` data-user-rating="${user_rating}"` : ''} class="mc-article"><div class="mc-title">${article.title}<div class="mc-close mc-icon-close"></div></div><div class="mc-content">${article.content.replace(/(?:\r\n|\r|\n)/g, '<br>')}</div>${article.link ? `<a href="${article.link}" target="_blank" class="mc-btn-text"><i class="mc-icon-plane"></i>${mc_('Read more')}</a>` : ''}${code ? `<div class="mc-article-category-links">${code}</div>` : ''}<div class="mc-rating"><span>${mc_('Rate and review')}</span><div><i data-rating="positive" class="mc-submit mc-icon-like"><span>${mc_('Helpful')}</span></i><i data-rating="negative" class="mc-submit mc-icon-dislike"><span>${mc_('Not helpful')}</span></i></div></div></div>`;
        },

        initArticlesPage: function () {
            let query_article_id = MCF.getURL('article_id');
            let query_category = MCF.getURL('category');
            let query_search = MCF.getURL('search');
            if (CHAT_SETTINGS.articles_url_rewrite) {
                let url_parts = location.href.replace(CHAT_SETTINGS.articles_url_rewrite, '').split('/');
                if (url_parts[url_parts.length - 2] == 'category') {
                    query_category = url_parts[url_parts.length - 1];
                }
                if ((url_parts.length == 2 && !url_parts[0] && url_parts[1]) || (url_parts.length == 1 && url_parts[0])) {
                    query_article_id = url_parts[url_parts.length - 1];
                }
                CHAT_SETTINGS.articles_page_url
            }
            articles_page = $('body').find('#mc-articles');
            if (!articles_page.length) {
                articles_page = $('body');
            }
            if (articles_page.mcLoading()) {
                let url = MC_URL + '/include/articles.php' + (query_category ? '?category=' + query_category : (query_article_id ? '?article_id=' + query_article_id : (query_search ? '?search=' + query_search : '')));
                if (cloud_data) {
                    url += (url.includes('?') ? '&' : '?') + 'cloud=' + cloud_data;
                }
                MCF.loadResource(MC_URL + '/css/articles.css');
                MCF.cors('GET', url, (html) => {
                    articles_page.html(html);
                    articles_page.mcLoading(false);
                });
            }
            articles_page.on('keydown', '.mc-panel-side input', function (e) {
                if (e.which == 13) {
                    $(this).next().click();
                }
            });

            articles_page.on('click', '.mc-articles > [data-id]', function () {
                cache = panel_main.html();
                panel_main.mcLoading(true);
                MCChat.getArticles($(this).attr('data-id'), (article) => {
                    panel_main.removeClass('mc-articles').html(MCChat.getArticleCode(article));
                    panel_main.mcLoading(false);
                });
            });

            articles_page.on('click', '.mc-article [data-rating]', function () {
                MCChat.articleRatingOnClick(this);
            });

            articles_page.on('click', '.mc-article .mc-title .mc-close', function () {
                articles_page.find('.mc-panel-main').addClass('mc-articles').html(cache);
            });

            articles_page.on('click', '.mc-submit-articles', function () {
                let search = $(this).parent().find('input').val();
                if (search) {
                    cache = panel_main.html();
                    panel_main.html('');
                    MCChat.searchArticles(search, this, panel_main);
                } else {
                    panel_main.html(cache);
                }
                panel_main.addClass('mc-articles');
            });

            articles_page.on('click', '.mc-article-categories [data-id]', function () {
                if (loading(panel_main)) return;
                let code = '';
                cache = panel_main.html();
                articles_page.find('.mc-article-categories [data-id]').mcActive(false);
                $(this).mcActive(true);
                MCChat.getArticles(-1, (articles) => {
                    for (var i = 0; i < articles.length; i++) {
                        code += `<div data-id="${articles[i].id}"><div>${articles[i].title}</div><span>${articles[i].content}</span></div>`;
                    }
                    panel_main.addClass('mc-articles').html(code ? code : `<p class="mc-no-results">${mc_('No articles found.')}</p>`);
                    panel_main.mcLoading(false);
                }, $(this).attr('data-id'));
            });
        },

        // Emoji
        categoryEmoji: function (category) {
            let list = this.emoji_options.list;
            if (category == 'all') {
                this.emoji_options.list_now = list;
            } else {
                this.emoji_options.list_now = [];
                for (var i = 0; i < list.length; i++) {
                    if (list[i].category.startsWith(category)) {
                        this.emoji_options.list_now.push(list[i]);
                    }
                }
            }
            this.emoji_options.range = 0;
            this.populateEmoji(0);
            this.populateEmojiBar();
        },

        mouseWheelEmoji: function (e) {
            let range = this.emoji_options.range;
            if (mcDelta(e) > 0 || (mobile && typeof e.originalEvent.changedTouches !== ND && this.emoji_options.touch < e.originalEvent.changedTouches[0].clientY)) {
                range -= (range < 1 ? 0 : 1);
            } else {
                range += (range > this.emoji_options.range_limit ? 0 : 1);
            }
            chat_emoji.find('.mc-emoji-bar > div').mcActive(false).eq(range).mcActive(true);
            this.emoji_options.range = range;
            this.populateEmoji(range);
            e.preventDefault();
        },

        insertEmoji: function (emoji) {
            if (emoji.indexOf('.svg') > 0) {
                emoji = $.parseHTML(emoji)[0].alt;
            }
            this.insertText(emoji);
            chat_emoji.mcTogglePopup();
        },

        showEmoji: function (button) {
            if (chat_emoji.mcTogglePopup(button)) {
                if (!admin) {
                    chat_emoji.css({ left: chat_editor.offset().left + (tickets ? 68 : 20), top: chat_editor.offset().top - window.scrollY - (tickets ? chat_editor.height() - 330 : 304) });
                }
                if (!chat_emoji.find('.mc-emoji-list > ul').html()) {
                    jQuery.ajax({
                        method: 'POST',
                        url: MC_AJAX_URL,
                        data: {
                            function: 'emoji',
                            'login-cookie': MCF.loginCookie()
                        }
                    }).done((response) => {
                        this.emoji_options.list = JSON.parse(response);
                        this.emoji_options.list_now = this.emoji_options.list;
                        this.populateEmoji(0);
                        this.populateEmojiBar();
                    });
                }
                MCF.deselectAll();
            }
        },

        populateEmoji: function (range) {
            let code = '';
            let per_page = mobile ? 42 : 48;
            let limit = range * per_page + per_page;
            let list_now = this.emoji_options.list_now;
            if (limit > list_now.length) limit = list_now.length;
            this.emoji_options.range_limit = list_now.length / per_page - 1;
            this.emoji_options.range = range;
            for (var i = (range * per_page); i < limit; i++) {
                code += `<li>${list_now[i].char}</li>`;
            }
            chat_emoji.find('.mc-emoji-list').html(`<ul>${code}</ul>`);
        },

        populateEmojiBar: function () {
            let code = '<div class="mc-active"></div>';
            let per_page = mobile ? 42 : 49;
            for (var i = 0; i < this.emoji_options.list_now.length / per_page - 1; i++) {
                code += '<div></div>';
            }
            this.emoji_options.range = 0;
            chat_emoji.find('.mc-emoji-bar').html(code);
        },

        clickEmojiBar: function (item) {
            let range = $(item).index();
            this.populateEmoji(range);
            this.emoji_options.range = range;
            chat_emoji.find('.mc-emoji-bar > div').mcActive(false).eq(range).mcActive(true);
        },

        searchEmoji: function (search) {
            MCF.search(search, () => {
                if (search.length > 1) {
                    let list = this.emoji_options.list;
                    let list_now = [];
                    for (var i = 0; i < list.length; i++) {
                        if (list[i].category.toLowerCase().includes(search) || list[i].name.toLowerCase().includes(search)) {
                            list_now.push(list[i]);
                        }
                    }
                    this.emoji_options.list_now = list_now;
                } else {
                    this.emoji_options.list_now = this.emoji_options.list;
                }
                this.emoji_options.range = 0;
                this.populateEmoji(0);
                this.populateEmojiBar();
            });
        },

        // Editor methods
        textareaChange: function (textarea) {
            let value = $(textarea).val();

            // Saved replies
            if (admin) {
                MCAdmin.conversations.savedReplies(textarea, value);
                MCAdmin.apps.openAI.rewriteButton(value);
            }

            // Typing
            if (value) {
                this.typing((admin && !MCPusher.active ? MC_ACTIVE_AGENT.id : activeUser().id), 'set');
            }
            chat_editor.mcActive(value);
        },

        insertText: function (text) {
            let textarea = $(chat_textarea.get(0));
            let index = 0;
            if (this.dashboard) return false;
            if (textarea.get(0).selectionStart) {
                index = textarea.get(0).selectionStart;
            } else if (document.selection) {
                textarea.focus();
                let selection = document.selection.createRange();
                var selection_length = document.selection.createRange().text.length;
                selection.moveStart('character', -textarea.value.length);
                index = selection.text.length - selection_length;
            }
            textarea.val(textarea.val().substr(0, index) + text + textarea.val().substr(index));
            textarea.focus();
            textarea.manualExpandTextarea();
            chat_editor.mcActive(true);
        },

        enabledAutoExpand: function () {
            if (chat_textarea.length) {
                chat_textarea.autoExpandTextarea();
            }
        },

        cancelReply: function () {
            chat_editor.find('[data-reply]').remove();
            chat.removeClass('mc-reply-active');
        },

        // Privacy message
        privacy: function () {
            MCF.ajax({
                function: 'get-block-setting',
                value: 'privacy'
            }, (response) => {
                chat_scroll_area.append(`<div class="mc-privacy mc-init-form" data-decline="${response.decline}"><div class="mc-title">${response.title}</div><div class="mc-text">${response.message.replace(/\n/g, '<br>')}</div>` + (response.link ? `<a target="_blank" href="${response.link}">${response['link-name']}</a>` : '') + `<div class="mc-buttons"><a class="mc-btn mc-approve">${response['btn-approve']}</a><a class="mc-btn mc-decline">${response['btn-decline']}</a></div></div>`);
                this.finalizeInit();
                MCF.event('MCPrivacy');
            });
            if (!this.dashboard) {
                this.showDashboard();
            }
            this.dashboard = true;
            main.addClass('mc-init-form-active');
        },

        // Popup message 
        popup: function (close = false, content = false) {
            if (close) {
                let popup = main.find('.mc-popup-message');
                let id = popup.attr('data-id');
                storage('popup' + (MCF.null(id) ? '' : id), true);
                popup.remove();
                return;
            }
            setTimeout(() => {
                if (!this.chat_open) {
                    if (content == false) {
                        content = CHAT_SETTINGS.popup;
                    }
                    main.find('.mc-popup-message').remove();
                    main.append(`<div data-id="${content.id ? content.id : ''}" class="mc-popup-message">` + (content.image ? `<img loading="lazy" src="${content.image}" />` : '') + (content.title ? `<div class="mc-top">${content.title}</div>` : '') + `<div class="mc-text">${content.message}</div><div class="mc-icon-close"></div></div>`);
                    MCF.event('MCPopup', content);
                }
            }, 1000);
        },

        // Follow up message
        followUp: function () {
            if (this.followUpCheck()) {
                timeout = setTimeout(() => {
                    if (this.followUpCheck()) {
                        MCF.ajax({
                            function: 'execute-bot-message',
                            conversation_id: this.conversation.id,
                            name: 'follow_up',
                            check: false
                        }, (response) => {
                            if (response.settings.sound && this.audio) {
                                this.audio.play();
                            }
                            this.skip = true;
                            MCF.storageTime('email');
                            MCF.event('MCFollowUp');
                        });
                    }
                }, CHAT_SETTINGS.follow === true ? (CHAT_SETTINGS.office_hours || agents_online ? 15000 : 5000) : parseInt(CHAT_SETTINGS.follow));
            }
        },

        followUpCheck: function () {
            return !admin && this.conversation && CHAT_SETTINGS.follow && activeUser() && !activeUser().email && MCF.storageTime('email', 24);
        },

        // Welcome message
        welcome: function () {
            if (!tickets && (CHAT_SETTINGS.welcome_trigger != 'open' || this.chat_open) && (CHAT_SETTINGS.office_hours || !CHAT_SETTINGS.welcome_disable_office_hours) && CHAT_SETTINGS.welcome && !storage('welcome') && activeUser()) {
                MCF.ajax({
                    function: 'get-block-setting',
                    value: 'welcome'
                }, (response) => {
                    setTimeout(() => {
                        if (CHAT_SETTINGS.dialogflow_welcome) {
                            if (this.conversation === false) {
                                this.newConversation(3, -1, '', [], null, null, function () {
                                    MCApps.dialogflow.welcome(response.open, response.sound)
                                });
                            } else {
                                MCApps.dialogflow.welcome(response.open, response.sound);
                            }
                        } else {
                            this.sendMessage(bot_id, response.message, [], false, false, 3);
                            if (response.open && !mobile) {
                                this.start();
                            }
                            if (response.sound) {
                                this.audio.play();
                            }
                        }
                        this.skip = true;
                        MCF.event('MCWelcomeMessage');
                    }, parseInt(tickets ? 0 : CHAT_SETTINGS.welcome_delay));
                    storage('welcome', true);
                });
            }
        },

        // Offline timetable message
        offlineMessage: function () {
            if (!admin && CHAT_SETTINGS.timetable && (!CHAT_SETTINGS.office_hours || (!agents_online && !CHAT_SETTINGS.timetable_disable_agents))) {
                let message = CHAT_SETTINGS.timetable_message;
                switch (CHAT_SETTINGS.timetable_type) {
                    case 'header':
                        if (!this.offline_message_set) {
                            if (message[0]) chat_header.find('.mc-title').html(message[0]);
                            chat_header.find('.mc-text').html(message[1]);
                            this.offline_message_set = true;
                        }
                        break;
                    case 'info':
                        if (!this.offline_message_set) {
                            chat.prepend(`<div class="mc-notify-message mc-rich-cnt"><div class="mc-cnt"><div class="mc-message">${message[0] ? `<b>${message[0]}</b> ` : ''}${message[1]}</div></div></div>`);
                            main.addClass('mc-notify-active');
                            this.offline_message_set = true;
                        }
                        break;
                    default:
                        setTimeout(() => {
                            if (this.conversation) {
                                let offline_message = CHAT_SETTINGS.timetable_hide ? `${message[0] ? `*${message[0]}*\n` : ''}${message[1]}` : '[timetable]';
                                let offline_message_sent = this.conversation.searchMessages(offline_message, true);
                                offline_message_sent = offline_message_sent.length ? offline_message_sent : false;
                                if (offline_message_sent) {
                                    let last_agent_message = this.conversation.getLastUserMessage(false, true);
                                    offline_message_sent = !last_agent_message || (last_agent_message.get('index') < offline_message_sent[offline_message_sent.length - 1].get('index') && Date.now() - 3600000) < MCF.unix(offline_message_sent[0].get('creation_time'));
                                }
                                if (!offline_message_sent) {
                                    this.sendMessage(bot_id, offline_message);
                                }
                            }
                        }, 5000);
                }
            }
        },

        // Send Slack message
        slackMessage: function (user_id, full_name, profile_image, message, attachments = []) {
            if (!this.conversation || (!message && !attachments.length)) return false;
            let conversation_id = this.conversation.id;
            MCF.ajax({
                function: 'send-slack-message',
                user_id: user_id,
                full_name: full_name,
                profile_image: profile_image,
                conversation_id: conversation_id,
                message: message,
                attachments: attachments,
                channel: this.slack_channel[0] == activeUser().id ? this.slack_channel[1] : false
            }, (response) => {
                this.slack_channel = [activeUser().id, response[1]];
                MCF.event('MCSlackMessageSent', { message: message, conversation_id: conversation_id, slack_channel: response[1] });
            });
        },

        // Delete message
        deleteMessage: function (message_id) {
            MCF.ajax({
                function: 'delete-message',
                message_id: message_id
            }, () => {
                if (this.conversation) this.conversation.deleteMessage(message_id);
                chat.find(`[data-id="${message_id}"]`).remove();
                MCF.event('MCMessageDeleted', message_id);
            });
        },

        // Registration form
        registration: function (check = false, type = CHAT_SETTINGS.registration_required) {
            if (check) {
                return CHAT_SETTINGS.registration_required && (!CHAT_SETTINGS.registration_offline || !agents_online) && (typeof MC_DEFAULT_USER == ND || !MC_DEFAULT_USER.email) && (!CHAT_SETTINGS.registration_timetable || !CHAT_SETTINGS.office_hours) && (activeUser() === false || ['visitor', 'lead'].includes(activeUser().type));
            }
            chat_scroll_area.append(MCRichMessages.generate({}, CHAT_SETTINGS.registration_link || (CHAT_SETTINGS.registration_required == 'registration-login') ? 'login' : type, 'mc-init-form'));
            if (!this.dashboard) {
                this.showDashboard();
            }
            this.dashboard = true;
            this.finalizeInit();
            main.addClass('mc-init-form-active');
        },

        // Shortcut for add user and login function
        addUserAndLogin: function (onSuccess = false, lead = false) {
            let settings = typeof MC_DEFAULT_USER != ND && MC_DEFAULT_USER ? MC_DEFAULT_USER : {};
            settings.user_type = lead ? 'lead' : 'visitor';
            MCF.ajax({
                function: 'add-user-and-login',
                settings: settings,
                settings_extra: settings.extra
            }, (response) => {
                if (MCF.errorValidation(response)) {
                    if (response[1] == 'duplicate-email' || response[1] == 'duplicate-phone') {
                        delete MC_DEFAULT_USER.email;
                        delete MC_DEFAULT_USER.extra.phone;
                        return this.addUserAndLogin(onSuccess, lead);
                    }
                } else {
                    MCF.loginCookie(response[1]);
                    activeUser(new MCUser(response[0]));
                    MCPusher.start();
                    if (!MCPusher.active) {
                        MCChat.automations.runAll();
                    }
                    if (onSuccess) {
                        onSuccess(response);
                    }
                }
            });
        },

        // Check if the dashboard must be showed
        isInitDashboard: function () {
            return CHAT_SETTINGS.init_dashboard || (activeUser() && activeUser().conversations.length > 1);
        },

        // Upload response
        uploadResponse: function (response) {
            response = JSON.parse(response);
            if (response[0] == 'success') {
                if (response[1] == 'extension_error') {
                    let message = 'The file you are trying to upload has an extension that is not allowed.';
                    if (admin) {
                        MCAdmin.infoPanel(message, 'info');
                    } else {
                        alert(message);
                    }
                } else if ($(upload_target).hasClass('mc-input-image')) {
                    let image = $(upload_target).find('.image');
                    let image_url = image.attr('data-value');
                    if (image_url && !image_url.includes('media/user.svg')) {
                        MCF.ajax({ function: 'delete-file', path: image_url });
                    }
                    image.attr('data-value', '').css('background-image', '');
                    setTimeout(() => {
                        image.attr('data-value', response[1]).css('background-image', `url("${response[1]}?v=${MCF.random()}")`).append('<i class="mc-icon-close"></i>');
                        upload_target = false;
                    }, 500);
                } else {
                    let name = MCF.beautifyAttachmentName(response[1].substr(response[1].lastIndexOf('/') + 1));
                    chat_editor.find('.mc-attachments').append(`<div data-name="${name}" data-value="${response[1]}"${response.length > 2 ? ' data-size="' + response[2][0] + '|' + response[2][1] + '"' : ''}>${name}<i class="mc-icon-close"></i></div>`);
                    chat_editor.mcActive(true);
                }
            } else {
                MCF.error(response[1], 'mc-upload-files.change');
            }
            this.busy(false);
        },

        // Archive a conversation and close it
        closeChat: function (update_conversation_status = true) {
            let id = this.conversation.id;
            MCChat.clear();
            if (update_conversation_status) {
                MCF.ajax({
                    function: 'update-conversation-status',
                    conversation_id: id,
                    status_code: 3
                }, () => {
                    this.closeChat_(id);
                });
            } else {
                this.closeChat_(id);
            }

        },

        closeChat_(id) {
            main.find(`li[data-conversation-id="${id}"]`).remove();
            force_action = 'new-conversation';
            MCChat.clear();
            storage('open-conversation', '');
            storage('welcome', '');
            storage('flow_on_load', '');
            activeUser().removeConversation(id);
            if (!CHAT_SETTINGS.disable_dashboard) {
                MCChat.showDashboard();
            }
        },

        conversationArchived: function () {
            let is_close_chat = (!tickets && CHAT_SETTINGS.close_chat) || (tickets && CHAT_SETTINGS.tickets_close);
            if (is_close_chat && !CHAT_SETTINGS.rating) {
                return this.closeChat(false);
            }
            if (CHAT_SETTINGS.rating) {
                MCRichMessages.rating(is_close_chat);
            }
        },

        // Automations
        automations: {
            history: [],
            busy: [],
            scroll_position_intervals: {},
            timeout_queue: [],

            runAll: function () {
                let automations = CHAT_SETTINGS.automations;
                for (var i = 0; i < automations.length; i++) {
                    let automation = automations[i];
                    let conditions = automation.conditions;
                    let count = conditions.length;
                    let valid = count == 0;
                    let browsing_time = false;
                    let scroll_position = false;
                    let server_conditions = false;
                    for (var j = 0; j < conditions.length; j++) {
                        let criteria = conditions[j][1];
                        valid = false;
                        switch (conditions[j][0]) {
                            case 'browsing_time':
                                valid = true;
                                browsing_time = criteria;
                                break;
                            case 'scroll_position':
                                valid = true;
                                scroll_position = criteria;
                                break;
                            case 'referring':
                            case 'url':
                                let url = conditions[j][0] == 'referring' ? document.referrer : window.location.href;
                                let checks = conditions[j][2].replace(/https?:\/\/|www\./g, '').split(',');
                                url = url.replace(/https?:\/\/|www\./g, '');
                                for (var y = 0; y < checks.length; y++) {
                                    if (url.includes(checks[y])) {
                                        valid = criteria == 'contains';
                                        break;
                                    }
                                }
                                break;
                            case 'include_urls': // Deprecated whole block
                            case 'exclude_urls': // Deprecated whole block
                                let url2 = conditions[j][0] == 'referring' ? document.referrer : window.location.href;
                                let checks2 = conditions[j][2].replace(/https?:\/\/|www\./g, '').split(',');
                                let include = conditions[j][0] != 'exclude_urls';
                                if (!include) valid = true;
                                url2 = url2.replace(/https?:\/\/|www\./g, '');
                                for (var y = 0; y < checks2.length; y++) {
                                    checks2[y] = $.trim(checks2[y].replace('https://', '').replace('http://', '').replace('www.', ''));
                                    if ((criteria == 'contains' && url2.indexOf(checks2[y]) != -1) || (criteria == 'does-not-contain' && url2.indexOf(checks2[y]) == -1) || (criteria == 'is-exactly' && checks2[y] == url2) || (criteria == 'is-not' && checks2[y] != url2)) {
                                        valid = include;
                                        break;
                                    }
                                }
                                break;
                            case 'custom_variable':
                                let variable = criteria.split('=');
                                if (variable[0] in window && window[variable[0]] == variable[1]) {
                                    valid = true;
                                }
                                break;
                            case 'returning_visitor':
                            case 'user_type':
                            case 'cities':
                            case 'languages':
                            case 'countries':
                            case 'postal_code':
                            case 'website':
                            case 'company':
                            case 'creation_time':
                            case 'last_activity':
                                valid = activeUser();
                                server_conditions = true;
                                break;
                            case 'phone':
                                valid = activeUser() && activeUser().getExtra('phone');
                                break;
                            case 'email':
                                valid = activeUser() && activeUser().email;
                                break;
                            default:
                                valid = activeUser() && activeUser().getExtra(conditions[j][0]);
                        }
                        if (!valid) {
                            break;
                        }
                    }
                    if (['messages', 'emails', 'sms'].includes(automation.type) && !activeUser()) {
                        valid = false;
                    }
                    if (valid) {
                        if (server_conditions) {
                            if (!(automation.id in this.busy)) {
                                MCF.ajax({
                                    function: 'automations-validate',
                                    automation: automation
                                }, (response) => {
                                    if (response !== false) {
                                        this.runAll_final(automation, scroll_position, browsing_time);
                                    }
                                    delete this.busy[automation.id];
                                });
                                this.busy[automation.id] = true;
                            }
                        } else if (automation.type != 'messages' || !MCChat.registration(true)) {
                            this.runAll_final(automation, scroll_position, browsing_time);
                        }
                    }
                }
            },

            runAll_final: function (automation, scroll_position, browsing_time) {
                if (scroll_position) {
                    this.scroll_position_intervals[automation.id] = setInterval(() => {
                        if ($(window).scrollTop() > parseInt(scroll_position)) {
                            if (browsing_time) {
                                setTimeout(() => { this.run(automation) }, parseInt(browsing_time) * 1000);
                            } else {
                                this.run(automation);
                            }
                            clearInterval(this.scroll_position_intervals[automation.id]);
                        }
                    }, 1000);
                } else if (browsing_time) {
                    if (!this.timeout_queue.includes(automation.id)) {
                        setTimeout(() => { this.run(automation) }, parseInt(browsing_time) * 1000);
                        this.timeout_queue.push(automation.id);
                    }
                } else this.run(automation);
            },

            run: function (automation) {
                if (this.history.includes(automation.id)) return;
                switch (automation.type) {
                    case 'messages':
                    case 'emails':
                    case 'sms':
                        if ((!MCPusher.active || MCPusher.started) && !(automation.id in this.busy)) {
                            if (automation.type == 'messages' && MCChat.chat_open) {
                                let last_message = MCChat.conversation ? MCChat.conversation.getLastUserMessage(false, 'no-bot') : false;
                                if (last_message && ((Date.now() - 600000) < MCF.unix(last_message.get('creation_time')))) {
                                    return;
                                }
                            }
                            MCF.ajax({
                                function: 'automations-run',
                                automation: automation
                            }, (response) => {
                                if (response !== false) {
                                    this.history.push(automation.id);
                                    if (automation.type == 'messages' && !MCPusher.active) MCChat.updateConversations();
                                }
                                delete this.busy[automation.id];
                            });
                            this.busy[automation.id] = true;
                        }
                        break;
                    case 'popups':
                        if (!storage('popup' + automation.id)) {
                            setTimeout(() => {
                                if (!MCChat.chat_open) {
                                    MCChat.popup(false, { id: automation.id, image: automation.profile_image, title: automation.title, message: automation.message });
                                    this.history.push(automation.id);
                                } else if (automation.fallback) {
                                    let last_message = MCChat.conversation ? MCChat.conversation.getLastUserMessage(false, 'no-bot') : false;
                                    if (!last_message || ((Date.now() - 600000) > MCF.unix(last_message.get('creation_time')))) {
                                        MCChat.sendMessage(bot_id, (MCF.null(automation.title) ? '' : `*${automation.title}*\n`) + automation.message, [], false, false, 0);
                                        storage('popup' + automation.id, true);
                                        this.history.push(automation.id);
                                    }
                                }
                            }, 1000);
                        }
                        break;
                    case 'design':
                        if (automation.background) {
                            chat_header.css('background-image', `url("${automation.background}")`);
                        }
                        if (automation.brand) {
                            chat_header.find('.mc-brand img').attr('src', automation.brand);
                        }
                        if (automation.title) {
                            chat_header.find('.mc-title').html(automation.title);
                        }
                        if (automation.message) {
                            chat_header.find('.mc-text').html(automation.message);
                        }
                        if (automation.icon) {
                            main.find('.mc-chat-btn .mc-icon').attr('src', automation.icon);
                        }
                        if (automation.color_1 || automation.color_2 || automation.color_3) {
                            MCF.ajax({ function: 'chat-css', color_1: automation.color_1, color_2: automation.color_2, color_3: automation.color_3 }, (response) => {
                                global.append(`<style>${response}</style>`);
                            });
                        }
                        this.history.push(automation.id);
                        break;
                    case 'more':
                        let parameters = {};
                        if (automation.department) {
                            MCChat.default_department = automation.department;
                            parameters = { function: 'update-conversation-department', department: automation.department };
                        }
                        if (automation.agent) {
                            MCChat.default_agent = automation.agent;
                            parameters = { function: 'update-conversation-agent', agent_id: automation.agent };
                        }
                        if (automation.tags) {
                            automation.tags = automation.tags.split(',');
                            MCChat.default_tags = automation.tags;
                            parameters = { function: 'update-tags', tags: automation.tags, add: true };
                        }
                        if (MCChat.conversation.id && (automation.tags || automation.agent || automation.department)) {
                            parameters.conversation_id = MCChat.conversation.id
                            MCF.ajax(parameters);
                        }
                        if (automation.articles || automation.articles_category) {
                            let articles_dashboard = main.find('.mc-dashboard-articles > .mc-articles');
                            MCChat.articles_allowed_ids = automation.articles;
                            MCChat.articles_category = automation.articles_category;
                            if (articles_dashboard) {
                                MCChat.getArticles(automation.articles, (response) => {
                                    let code = '';
                                    for (var i = 0; i < 2; i++) {
                                        code += `<div data-id="${response[i].id}"><div>${response[i].title}</div><span>${response[i].content}</span></div>`;
                                    }
                                    articles_dashboard.html(code);
                                }, automation.articles_category, 2);
                            }
                        }
                        this.history.push(automation.id);
                        break;
                }
            }
        },

        // More
        flashNotification: function () {
            clearInterval(interval);
            interval = setInterval(function () {
                if (MCChat.notifications.length) {
                    document.title = document.title == document_title ? '(' + MCChat.notifications.length + ') ' + mc_('New message' + (MCChat.notifications.length > 1 ? 's' : '')) : document_title;
                }
            }, 2000);
        },

        calculateLabelDates: function () {
            if (admin || this.chat_open) {
                label_date_items = chat.find('.mc-label-date');
            }
        },

        calculateLabelDateFirst: function () {
            if (!this.conversation.messages.length) {
                chat.append(`<div class="mc-label-date"><span>${mc_('Today')}</span></div>`);
            }
        },

        playSound: function (repeat = false) {
            this.audio.play();
            let index = repeat ? repeat : (admin ? MC_ADMIN_SETTINGS.sound.repeat : CHAT_SETTINGS.sound.repeat);
            if (index && !this.tab_active) {
                clearInterval(this.audio_interval);
                this.audio_interval = setInterval(() => {
                    this.audio.play();
                    index--;
                    if (!index) {
                        clearInterval(this.audio_interval);
                    }
                }, this.audio.duration * 1000 + 1500);
            }
        },

        isConversationAllowed: function (source, status_code) {
            return (!CHAT_SETTINGS.tickets_hide || (tickets && source == 'tk') || (!tickets && source != 'tk')) && (![3, 4, '3', '4'].includes(status_code) || ((!tickets && !CHAT_SETTINGS.close_chat) || (tickets && !CHAT_SETTINGS.tickets_close)));
        }
    }
    window.MCChat = MCChat;

    /* 
    * ----------------------------------------------------------
    * RICH MESSAGES
    * ----------------------------------------------------------
    */

    var MCRichMessages = {
        rich_messsages: {
            email: '',
            button: '',
            video: '',
            image: '',
            woocommerce_button: '',
            rating: '',
            chips: '<div class="mc-buttons">[options]</div>',
            buttons: '<div class="mc-buttons">[options]</div>',
            select: '<div class="mc-select"><p></p><ul>[options]</ul></div>',
            list: '<div class="mc-text-list">[values]</div>',
            'list-image': '<div class="mc-image-list">[values]</div>',
            table: '<table><tbody>[header][values]</tbody></table>',
            inputs: '<div class="mc-form">[values]</div>',
            card: '<div class="mc-card">[settings]</div>',
            share: '<div class="mc-social-buttons">[settings]</div>',
            slider: '<div class="mc-slider"><div>[items]</div></div><div class="mc-slider-arrow mc-icon-arrow-left[class]"></div><div class="mc-slider-arrow mc-icon-arrow-right mc-active[class]"></div>',
            'slider-images': '<div class="mc-slider mc-slider-images"><div>[items]</div></div><div class="mc-slider-arrow mc-icon-arrow-left[class]"></div><div class="mc-slider-arrow mc-icon-arrow-right mc-active[class]"></div>'
        },
        cache: {},
        duplicated_email: false,

        // Generate a rich message
        generate: function (settings, name, css = '') {
            let content;
            let next = true;
            let id = settings.id ? settings.id : MCF.random();
            let render = new MCMessage({});

            // Check if the rich message exist
            if (name in this.rich_messsages) {
                content = this.rich_messsages[name];
            } else if (name in this.cache) {
                content = this.cache[name];
            } else if (this.isShortcode(name)) {
                if (!settings.id) {
                    id = name;
                }
                content = '<div class="mc-rich-loading mc-loading"></div>';
                MCF.ajax({
                    function: 'get-rich-message',
                    name: name,
                    settings: settings
                }, (response) => {
                    response = this.initInputs(response);
                    if (name == 'timetable') {
                        response = this.timetable(response);
                    }
                    $(admin && MCAdmin.active_admin_area == 'chatbot' ? '.mc-playground' : main).find(`.mc-rich-message[id="${id}"]`).html(`<div class="mc-content">${response}</div>`);
                    this.cache[name] = response;
                    MCChat.scrollBottom(MCChat.dashboard);
                    MCF.event('MCRichMessageShown', { name: name, settings: settings, response: response });
                });
                next = false;
            } else {
                return false;
            }

            // Generate the rich message
            let disabled = settings.disabled;
            if (next) {
                let options;
                let code = '';
                switch (name) {
                    case 'email':
                        let inputs = [];
                        let email = activeUser().email;
                        let default_name = activeUser().get('last_name').charAt(0) == '#';
                        if (settings['name'] == 'true') {
                            inputs.push(['first_name', settings['last-name'] == 'true' ? 'First name' : 'Name', default_name ? '' : (settings['last-name'] == 'true' ? activeUser().get('first_name') : activeUser().name), 'text', true]);
                        }
                        if (settings['last-name'] == 'true') {
                            inputs.push(['last_name', 'Last name', default_name ? '' : activeUser().get('last_name'), 'text', true]);
                        }
                        for (var i = 0; i < inputs.length; i++) {
                            content += `<div id="${inputs[i][0]}" data-type="text" class="mc-input mc-input-text"><span class="${inputs[i][2] ? 'mc-active mc-filled' : ''}">${mc_(inputs[i][1])}</span><input value="${inputs[i][2]}" autocomplete="false" type="${inputs[i][3]}" ${inputs[i][4] ? 'required' : ''}></div>`;
                        }
                        if (settings['phone'] == 'true') {
                            let phone = activeUser().getExtra('phone');
                            if (!this.cache.phone) {
                                this.cache.phone = true;
                                MCF.ajax({
                                    function: 'get-select-phone'
                                }, (response) => {
                                    this.cache.phone = response;
                                    chat.find('#phone .mc-select-phone').html(response);
                                });
                            }
                            content += `<div id="phone" data-type="select-input" class="mc-input mc-input-select-input"><span class="${phone ? 'mc-active mc-filled' : ''}">${mc_('Phone')}</span><div class="mc-select-phone">${this.cache.phone ? this.cache.phone : ''}</div><input autocomplete="false" type="text" data-phone="true"${settings['phone-required'] != 'false' ? ' required' : ''}></div>`;
                        }
                        content += `<div id="email" data-type="email" class="mc-input mc-input-btn"><span class="${email ? 'mc-active mc-filled' : ''}">${mc_(MCF.null(settings.placeholder) ? 'Email' : settings.placeholder)}</span><input value="${email}" autocomplete="off" type="email" required><div class="mc-submit mc-icon-arrow-right"></div></div>`;
                        break;
                    case 'image':
                        content = `<div class="mc-image"><img loading="lazy" src="${settings.url}"></div>`;
                        break;
                    case 'video':
                        content = `<iframe loading="lazy"${settings.height ? ` height="${settings.height}"` : ''} src="https://${settings.type == 'youtube' ? 'www.youtube.com/embed/' : 'player.vimeo.com/video/'}${settings.id}" allowfullscreen></iframe>`;
                        break;
                    case 'select':
                        options = settings && settings.options ? settings.options.replace(/\\,/g, '{R}').split(',') : [];
                        for (var i = 0; i < options.length; i++) {
                            let item = options[i].replace(/{R}/g, ',');
                            code += `<li data-value="${MCF.stringToSlug(item)}">${mc_(item)}</li>`;
                        }
                        content = content.replace('[options]', code);
                        break;
                    case 'chips':
                    case 'buttons':
                        options = settings && settings.options ? settings.options.replace(/\\,/g, '{R}').split(',') : [];
                        for (var i = 0; i < options.length; i++) {
                            code += `<div class="mc-btn mc-submit">${mc_(options[i].replace(/{R}/g, ','))}</div>`;
                        }
                        content = content.replace('[options]', code);
                        break;
                    case 'button':
                        if (settings && settings.link) {
                            let action_link = settings.link.includes('calendly.com');
                            content = `<a ${action_link ? 'data-action="calendly" data-extra="' + settings.link + '|' + (settings.success ? settings.success.replaceAll('"', '\'') : '') + '" ' : ''}href="${action_link ? '#' : settings.link.replace(/<i>/g, '_').replace(/<\/i>/g, '_')}"${settings.target && !action_link ? ' target="_blank"' : ''} class="mc-rich-btn mc-btn${settings.style == 'link' ? '-text' : ''}">${mc_(settings.name)}</a>`;
                        }
                        break;
                    case 'list':
                        if (settings.values) {
                            options = settings.values.replace(/\\,/g, '{R}').replace(/\\:/g, '{R2}').replace(/:\/\//g, '{R3}').split(',');
                            let list = name == 'list';
                            let list_double = list && options.length && options[0].indexOf(':') > 0;
                            if (list && !list_double) {
                                content = content.replace('mc-text-list', 'mc-text-list mc-text-list-single');
                            }
                            if (settings.numeric) {
                                content = content.replace('mc-text-list', 'mc-text-list mc-text-list-numeric');
                            }
                            for (var i = 0; i < options.length; i++) {
                                let item = options[i].replace(/{R}/g, ',');
                                let is_inner = item.substr(0, 1) === '-';
                                code += list_double && item.includes(':') ? `<div><div>${mc_(item.split(':')[0].replace(/{R2}/g, ':').replace(/{R3}/g, '://'))}</div><div>${mc_(item.split(':')[1].replace(/{R2}/g, ':').replace(/{R3}/g, '://'))}</div></div>` : `<div${is_inner ? ' data-inner="true"' : ''}>${$.trim(mc_((is_inner ? item.substr(1) : item).replace(/{R2}/g, ':').replace(/{R3}/g, '://')))}</div>`;
                            }
                            content = content.replace('[values]', code);
                        }
                        break;
                    case 'list-image':
                        if (settings.values) {
                            options = settings.values.split(',');
                            for (var i = 0; i < options.length; i++) {
                                let item = options[i].replace('://', '///').split(':');
                                code += `<div><div class="mc-thumb" style="background-image:url('${item[0].replace('///', '://')}')"></div><div class="mc-list-title">${item[1]}</div><div>${item[2]}</div></div>`;
                            }
                            content = content.replace('[values]', code);
                        }
                        break;
                    case 'table':
                        if (settings.values) {
                            options = settings.header.split(',');
                            code += '<tr>';
                            for (var i = 0; i < options.length; i++) {
                                code += `<th>${options[i]}</th>`;
                            }
                            code += '</tr>';
                            content = content.replace('[header]', code);
                            code = '';
                            options = settings.values.split(',');
                            for (var i = 0; i < options.length; i++) {
                                let tds = options[i].split(':');
                                code += '<tr>';
                                for (var j = 0; j < tds.length; j++) {
                                    code += `<td>${tds[j]}</td>`;
                                }
                                code += '</tr>';
                            }
                            content = content.replace('[values]', code);
                        }
                        break;
                    case 'inputs':
                        if (settings.values) {
                            options = settings.values.split(',');
                            for (var i = 0; i < options.length; i++) {
                                if (disabled && !options[i]) continue;
                                code += `<div id="${MCF.stringToSlug(options[i])}" data-type="text" class="mc-input mc-input-text"><span>${mc_(options[i])}</span><input autocomplete="false" type="text" required></div>`;
                            }
                            code += '<div class="mc-btn mc-submit">' + mc_(settings.button ? settings.button : 'Send now') + '</div>';
                            content = content.replace('[values]', code);
                        }
                        break;
                    case 'card':
                        code = `${settings.image ? `<div class="mc-card-img" style="background-image:url('${settings.image}')"></div>` : ''}<div class="mc-card-header">${settings.header}</div>${settings.extra ? `<div class="mc-card-extra">${settings.extra}</div>` : ''}${settings.description ? `<div class="mc-card-description">${settings.description}</div>` : ''}${settings.link ? `<a class="mc-card-btn" href="${settings.link}"${settings.target ? ' target="_blank"' : ''}>${mc_(settings['link-text'])}</a>` : ''}`;
                        content = content.replace('[settings]', code);
                        break;
                    case 'share':
                        let channels = settings.channels ? settings.channels.replace(/ /g, '').split(',') : ['fb', 'tw', 'li', 'wa', 'pi'];
                        let link = '';
                        for (var i = 0; i < channels.length; i++) {
                            switch (channels[i]) {
                                case 'fb':
                                    link = 'www.facebook.com/sharer.php?u=';
                                    break;
                                case 'tw':
                                    link = 'twitter.com/intent/tweet?url=';
                                    break;
                                case 'li':
                                    link = 'www.linkedin.com/sharing/share-offsite/?url=';
                                    break;
                                case 'wa':
                                    link = 'web.whatsapp.com/send?text=';
                                    break;
                                case 'pi':
                                    link = 'www.pinterest.com/pin/create/button/?url=';
                                    break;
                            }
                            code += `<div class="mc-${channels[i]} mc-icon-social-${channels[i]}" data-link="https://${link}${encodeURIComponent(settings[channels[i]])}"></div>`;
                        }
                        content = content.replace('[settings]', code);
                        break;
                    case 'slider':
                        let count = 0;
                        for (var i = 1; i < 16; i++) {
                            if (('header-' + i) in settings) {
                                code += `<div>${('image-' + i) in settings ? `<div class="mc-card-img" style="background-image:url('${settings['image-' + i]}')"></div>` : ''}<div class="mc-card-header">${settings['header-' + i]}</div>${('extra-' + i) in settings ? `<div class="mc-card-extra">${settings['extra-' + i]}</div>` : ''}${('description-' + i) in settings ? `<div class="mc-card-description">${settings['description-' + i]}</div>` : ''}${('link-' + i) in settings ? `<a class="mc-card-btn" href="${settings['link-' + i]}"${settings.target ? ' target="_blank"' : ''}>${mc_(settings['link-text-' + i])}</a>` : ''}</div>`;
                                count++;
                            } else {
                                break;
                            }
                        }
                        content = content.replace('[items]', code).replace(/\[class\]/g, count == 1 ? ' mc-hide' : '');
                        break;
                    case 'slider-images':
                        if (settings.images) {
                            let images = settings.images.split(',');
                            for (var i = 0; i < images.length; i++) {
                                code += `<div class="mc-card-img" data-value="${images[i]}" style="background-image:url('${images[i]}')"></div>`;
                            }
                            content = content.replace(/\[class\]/g, images.length == 1 ? ' mc-hide' : '');
                        }
                        content = content.replace('[items]', code);
                        break;
                    case 'woocommerce_button':
                        settings.settings = `checkout:${settings.checkout},coupon:${settings.coupon}`;
                        content = `<a href="#" data-ids="${settings.ids}" class="mc-rich-btn mc-btn">${settings.name}</a>`;
                        break;
                    case 'rating':
                        content = `<div class="mc-rating-message mc-rating-${settings.value == 1 ? 'positive' : 'negative'}"><div><i class="mc-icon-${settings.value == 1 ? 'like' : 'dislike'}"></i> ${mc_(settings.value == 1 ? 'Helpful' : 'Not helpful')}</div>${settings.message ? '<div>' + settings.message + '</div>' : ''}</div>`;
                        settings.message = false;
                        break;
                }
            }
            return `<div id="${id}" data-type="${name}"${disabled ? 'disabled="true"' : ''}${settings.settings ? ` data-settings="${settings.settings}"` : ''}class="mc-rich-message mc-rich-${name} ${css}">` + (settings.title ? `<div class="mc-top">${render.render(mc_(settings.title))}</div>` : '') + (settings.message ? `<div class="mc-text">${render.render(mc_(settings.message))}</div>` : '') + `<div class="mc-content">${content}</div>${name == 'email' ? `<div data-success="${settings.success ? settings.success.replace(/"/g, '') : ''}" class="mc-info"></div>` : ''}</div>`;
        },

        // Function of built-in rich messages
        submit: function (area, type, element) {
            if (!admin && !loading(element) && !this.is_busy) {
                let error = '';
                let shortcode = '';
                let parameters = {};
                let success = $(area).find('[data-success]').length ? $(area).find('[data-success]').attr('data-success') : '';
                let rich_message_id = $(area).closest('.mc-rich-message').attr('id');
                let message_id = $(area).closest('[data-id]').attr('data-id');
                let message = '';
                let payload = { 'rich-messages': {} };
                let user_settings = activeUser() == false ? { profile_image: '', first_name: '', last_name: '', email: '', password: '', user_type: '' } : { profile_image: activeUser().image, first_name: activeUser().get('first_name'), last_name: activeUser().get('last_name'), email: activeUser().email, password: '', user_type: '' };
                let settings = {};
                let input = $(element);
                let dialogflow_response = '';
                let dialogflow_parameters = false;
                let active_conversation = MCChat.conversation !== false;
                let settings_extra = {};
                let payload_settings = {};
                let otp = area.find('#otp');
                if (MCF.null(message_id)) {
                    message_id = -1;
                } else {
                    let item = MCChat.conversation.getMessage(message_id);
                    message = item.message;
                    payload = item.payload();
                    if (!payload['rich-messages']) {
                        payload['rich-messages'] = {};
                    }
                }
                if (!$(element).hasClass('mc-btn') && !$(element).hasClass('mc-select') && !$(element).hasClass('mc-submit')) {
                    input = $(element).closest('.mc-btn,.mc-select');
                }
                $(area).find('.mc-info').html('').mcActive(false);
                switch (type) {
                    case 'email':
                        settings = MCForm.getAll(area);
                        $.each(settings, function (key, value) {
                            settings[key] = value[0];
                        });
                        if (settings.first_name) {
                            user_settings.user_type = 'user';
                            if (!settings.last_name) {
                                user_settings.last_name = '';
                            }
                        }
                        if (settings.phone) {
                            settings_extra = { phone: [settings.phone, 'Phone'] };
                        }
                        $.extend(user_settings, settings);
                        error = 'Please fill in all required fields and make sure the email is valid.';
                        if (success) {
                            success = mc_(success).replace('{user_email}', user_settings.email).replace('{user_name_}', user_settings.first_name + (settings.last_name ? (' ' + user_settings.last_name) : ''));
                        }
                        if (otp.mcActive()) {
                            let otp_string = otp.attr('data-otp');
                            settings.otp = otp_string ? [otp_string, area.find('#otp input').val()] : false;
                        }
                        payload['rich-messages'][rich_message_id] = { type: type, result: settings };
                        payload['event'] = 'update-user';
                        parameters = { function: 'update-user-and-message', settings: user_settings, settings_extra: settings_extra, payload: payload, skip_otp: true };
                        dialogflow_parameters = { settings: user_settings, settings_extra: settings_extra };
                        break;
                    case 'registration':
                        settings = MCForm.getAll(area.find('.mc-form-main'));
                        settings_extra = MCForm.getAll(area.find('.mc-form-extra'));
                        $.each(settings, function (key, value) {
                            settings[key] = value[0];
                        });
                        $.extend(user_settings, settings);
                        payload_settings = $.extend({}, user_settings);
                        if (success) {
                            success = mc_(success);
                        }
                        if (CHAT_SETTINGS.registration_details) {
                            success += '[list values="';
                            for (var key in user_settings) {
                                let value = user_settings[key].replace(/:|,/g, '');
                                if (value) {
                                    if (key == 'profile_image') {
                                        value = value.substr(value.lastIndexOf('/') + 1);
                                    }
                                    if (['password', 'password-check', 'envato-purchase-code', 'otp'].includes(key)) {
                                        value = '********';
                                        payload_settings[key] = '********';
                                    } else {
                                        success += user_settings[key] ? `${mc_(MCF.slugToString(key.replace('first_name', 'name')))}:${value},` : '';
                                    }
                                }
                            }
                            for (var key in settings_extra) {
                                if (settings_extra[key][0]) {
                                    success += `${mc_(settings_extra[key][1].replace(/:|,/g, ''))}:${settings_extra[key][0].replace(/:|,/g, '')},`;
                                }
                            }
                            success = success.slice(0, -1) + '"]';
                        }
                        if (otp.mcActive()) {
                            let otp_string = otp.attr('data-otp');
                            user_settings.otp = otp_string ? [otp_string, area.find('#otp input').val()] : false;
                        }
                        user_settings.user_type = 'user';
                        payload['rich-messages'][rich_message_id] = { type: type, result: { user: payload_settings, extra: settings_extra } };
                        payload['event'] = 'update-user';
                        parameters = CHAT_SETTINGS.registration_otp && user_settings.email && !user_settings.otp ? { function: 'otp', email: user_settings.email } : { function: activeUser() ? 'update-user-and-message' : 'add-user-and-login', settings: user_settings, settings_extra: settings_extra, payload: payload };
                        error = MCForm.getRegistrationErrorMessage(area);
                        dialogflow_parameters = { settings: user_settings, settings_extra: settings_extra };
                        break;
                    case 'chips':
                    case 'select':
                    case 'buttons':
                        settings = MCF.escape($(element).html());
                        if (success) {
                            success = mc_(success) + ` *${settings}*`;
                        }
                        payload['rich-messages'][rich_message_id] = { type: type, result: settings };
                        parameters = { function: 'update-message', payload: payload };
                        dialogflow_response = settings;
                        if (type == 'chips') {
                            MCChat.sendMessage(activeUser().id, settings, [], false, { id: rich_message_id, event: 'chips-click', result: settings }, rich_message_id == 'mc-human-takeover' && input.index() == 0 ? 2 : false);
                            if (rich_message_id == 'mc-human-takeover' && $(element).index() == 0) {
                                MCApps.dialogflow.humanTakeover();
                            }
                            $(element).closest('.mc-content').remove();
                        }
                        break;
                    case 'inputs':
                        settings = MCForm.getAll(area);
                        error = 'All fields are required.';
                        if (success) {
                            success = mc_(success) + ' [list values="';
                            for (var key in settings) {
                                success += `${mc_(settings[key][1].replace(/:|,/g, ''))}:${settings[key][0].replace(/:|,/g, '')},`;
                            }
                            success = success.slice(0, -1) + '"]';
                        }
                        payload['rich-messages'][rich_message_id] = { type: type, result: settings };
                        parameters = { function: 'update-message', payload: payload };
                        dialogflow_parameters = { settings: settings };
                        break;
                }
                shortcode = message.substr(message.indexOf('[' + type))
                shortcode = shortcode.substr(0, shortcode.indexOf(']') + 1);
                if (error && MCForm.errors(area)) {
                    MCForm.showErrorMessage(area, error);
                    input.mcLoading(false);
                    if (MCChat.dashboard || (active_conversation && MCChat.conversation.getLastMessage().id == message_id)) {
                        MCChat.scrollBottom();
                    }
                    return false;
                }
                if (!success && type != 'registration') {
                    let shortcode_settings = this.shortcode(shortcode);
                    let id = shortcode_settings[1].id ? `id="${shortcode_settings[1].id}"` : '';
                    let title = shortcode_settings[1].title ? `title="${shortcode_settings[1].title}"` : '';
                    let message = shortcode_settings[1].message ? `message="${shortcode_settings[1].message}"` : '';
                    let value = '';
                    if (['inputs', 'email'].includes(type)) {
                        for (var key in settings) {
                            value += settings[key] + ',';
                        }
                        value = `values="${value.slice(0, -1)}"`
                    } else {
                        value = `options="${settings}"`;
                    }
                    success = `[${type == 'email' ? 'inputs' : type} ${id} ${title} ${message} ${value} disabled="true"]`;
                }
                if (message_id != -1) {
                    success = success.replace(/<br>/g, '\n');
                    $.extend(parameters, {
                        message_id: message_id,
                        message: message ? (type == 'chips' ? message.replace(']', ' disabled="true"]') : message.replace(shortcode, success)) : success,
                        payload: payload
                    });
                }
                MCF.ajax(parameters, (response) => {
                    if (response && !MCF.errorValidation(response)) {
                        switch (type) {
                            case 'email':
                                for (var key in user_settings) {
                                    activeUser().set(key, user_settings[key]);
                                }
                                for (var key in settings_extra) {
                                    activeUser().setExtra(key, settings_extra[key][0]);
                                }
                                MCF.loginCookie(response[1]);
                                if (rich_message_id == 'mc-follow-up') {
                                    MCF.ajax({
                                        function: 'subscribe-email',
                                        email: activeUser().email
                                    });
                                }
                                MCChat.automations.runAll();
                                MCF.event('MCNewEmailAddress', { id: rich_message_id, name: activeUser().name, email: activeUser().email });
                                break;
                            case 'registration':
                                let otp = area.find('#otp');
                                if (CHAT_SETTINGS.registration_otp && !otp.mcActive()) {
                                    otp.attr('data-otp', response).mcActive(true);
                                    otp.find('input').attr('required', true).addClass('mc-error');
                                    MCForm.showErrorMessage(area, mc_('Please check your email for the one-time code.'));
                                    MCChat.scrollBottom();
                                    input.mcLoading(false);
                                    return;
                                }
                                MCF.loginCookie(response[1]);
                                user_settings.id = response[0].id;
                                if (!activeUser()) {
                                    activeUser(new MCUser(response[0]));
                                    for (var key in settings_extra) {
                                        activeUser().setExtra(key, settings_extra[key][0]);
                                    }
                                    if (this.duplicated_email && !CHAT_SETTINGS.init_dashboard) {
                                        force_action = 'open-conversation';
                                    }
                                    MCPusher.start();
                                    MCChat.initChat();
                                    if (!this.duplicated_email && (!CHAT_SETTINGS.init_dashboard || !main.find('.mc-departments-list').length) && success) {
                                        MCChat.sendMessage(bot_id, success, [], false, false, 3);
                                    }
                                } else {
                                    for (var key in user_settings) {
                                        activeUser().set(key, user_settings[key]);
                                    }
                                    for (var key in settings_extra) {
                                        activeUser().setExtra(key, settings_extra[key][0]);
                                    }
                                    MCChat.automations.runAll();
                                    MCChat.welcome();
                                }
                                if (MCChat.dashboard) {
                                    main.removeClass('mc-init-form-active');
                                    $(area).remove();
                                    if (!MCChat.isInitDashboard() && (!CHAT_SETTINGS.init_dashboard || !this.duplicated_email)) {
                                        MCChat.hideDashboard();
                                    }
                                }
                                if (CHAT_SETTINGS.wp_registration && user_settings.email && user_settings.password) {
                                    MCApps.wordpress.ajax('wp-registration', { user_id: response[0].id, first_name: response[0].first_name, last_name: response[0].last_name, password: user_settings.password, email: user_settings.email });
                                } else if (CHAT_SETTINGS.wp_users_system == 'wp') {
                                    MCApps.wordpress.ajax('wp-login', { user: user_settings.email, password: user_settings.password });
                                }
                                delete this.cache.registration;
                                setTimeout(() => {
                                    MCF.event('MCRegistrationForm', { id: rich_message_id, conversation_id: MCChat.conversation ? MCChat.conversation.id : false, user: user_settings, extra: payload['rich-messages'][rich_message_id]['result']['extra'] });
                                }, 5000);
                                break;
                            case 'buttons':
                                MCChat.scrollBottom();
                                break;
                        }
                        if (message_id == -1) {
                            $(element).closest('.mc-rich-message').html(success);
                        } else {
                            input.mcLoading(false);
                            if ((!payload.type || payload.type != 'close-message') && !dialogflow_human_takeover) {
                                MCChat.setConversationStatus(2);
                            }
                        }
                        if (!['login', 'chips'].includes(type) && (CHAT_SETTINGS.dialogflow_send_user_details || !['email', 'registration'].includes(type))) {
                            MCApps.dialogflow.message(`${rich_message_id}${dialogflow_response ? ('|' + dialogflow_response) : ''}`, [], false, dialogflow_parameters);
                        }
                        if (CHAT_SETTINGS.slack_active && (!dialogflow_human_takeover || MCApps.dialogflow.humanTakeoverActive())) {
                            MCChat.slackMessage(activeUser().id, activeUser().name, activeUser().image, success);
                        }
                        if (MCPusher.active) {
                            MCChat.update();
                        }
                        if (type != 'registration' && type != 'email') {
                            MCF.event('MCRichMessageSubmit', { result: response, data: payload['rich-messages'][rich_message_id], id: rich_message_id });
                        }
                    } else {
                        if (type == 'registration' && MCF.errorValidation(response, 'duplicate-email')) {
                            MCF.ajax({
                                function: 'otp',
                                email: user_settings.email
                            }, (response) => {
                                let otp = area.find('#otp');
                                otp.attr('data-otp', response).mcActive(true);
                                otp.find('input').attr('required', true).addClass('mc-error');
                                MCForm.showErrorMessage(area, mc_('This email is already in use. Please check your email for the one-time code.'));
                                area.find('.mc-submit').html(mc_('Sign in'));
                                MCChat.scrollBottom();
                            });
                            this.duplicated_email = true;
                        } else {
                            this.duplicated_email = false;
                            MCForm.showErrorMessage(area, MCForm.getRegistrationErrorMessage(response, 'response'));
                        }
                        if (MCChat.dashboard) {
                            MCChat.scrollBottom();
                        }
                        input.mcLoading(false);
                    }
                });
            }
        },

        // Return the shortcode name and the shortcode settings
        shortcode: function (shortcode) {
            let result = {};
            let shortcode_name = shortcode.includes(' ') ? shortcode.substr(1, shortcode.indexOf(' ') - 1) : shortcode.slice(1, -1);
            if (/\?|"|'|`|\*/gi.test(shortcode_name)) {
                return [false, false];
            }
            shortcode = shortcode.slice(1, -1).substr(shortcode_name.length + 1);
            let settings = shortcode.split('" ');
            for (var i = 0; i < settings.length; i++) {
                if (settings[i].includes('=')) {
                    let item = [settings[i].substr(0, settings[i].indexOf('=')), settings[i].substr(settings[i].indexOf('=') + 2)];
                    result[$.trim(item[0])] = item[1].replace(/"/g, '');
                }
            }
            return [shortcode_name, result];
        },

        // Init the rich message inputs
        initInputs: function (code) {
            code = $($.parseHTML('<div>' + code + '</div>'));
            code.find('.mc-input input').each(function () {
                if ($(this).val()) {
                    $(this).siblings().addClass('mc-active mc-filled');
                }
            });
            return code.html();
        },

        // Timetable shortcode
        timetable: function (code) {
            let table = $($.parseHTML(`<div>${code}</div>`));
            let utc_offset = table.find('[data-offset]').attr('data-offset');
            utc_offset = MCF.null(utc_offset) ? 0 : parseFloat(utc_offset);
            table.find('[data-time]').each(function () {
                let times = $(this).attr('data-time').split('|');
                code = ''
                for (var i = 0; i < times.length; i++) {
                    if (times[i] == 'closed') {
                        code += mc_('Closed');
                        break;
                    } else if (times[i]) {
                        let hm = times[i].split(':');
                        let time = MCF.convertUTCDateToLocalDate(`01/01/2000 ${hm[0]}:${hm[1]}`, utc_offset);
                        code += time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) + (i == 0 || i == 2 ? `<span>${mc_('to')}</span>` : i == 1 && times[i + 1] ? `<br />` : '');
                    }
                }
                table.find(' > div > span').html(`<i class="mc-icon-clock"></i> ${mc_('Time zone')} ${Intl.DateTimeFormat().resolvedOptions().timeZone}`);
                $(this).html(code);
            });
            return table.html();
        },

        // Slider
        sliderChange: function (id, direction = 'left') {
            let slider = chat.find(`#${id}`);
            if (slider.length && !slider.hasClass('mc-moving')) {
                let items = slider.find('.mc-slider > div > div');
                let item = items.eq(0);
                let width = Math.ceil(item.closest('.mc-slider').width());
                let negative = (direction == 'right' ? -1 : 1);
                let margin = parseFloat(parseFloat(parseFloat(item.css('margin-left')) + (width * negative)));
                let check = width * (items.length - 1) * -1;
                if (margin < 1 && margin >= check) {
                    item.css('margin-left', margin + 'px');
                    slider.addClass('mc-moving');
                    setTimeout(() => { slider.removeClass('mc-moving'); }, 1200);
                }
                slider.find('.mc-icon-arrow-right').mcActive(!(check > (margin - 15) && check < (margin + 15)));
                slider.find('.mc-icon-arrow-left').mcActive(margin < -10);
            }
        },

        calendly: {
            script_loaded: false,

            load: function (url, title, message_id) {
                url = url.split('|');
                if (this.script_loaded) {
                    this.load_(url[0], title);
                } else {
                    $.getScript('https://assets.calendly.com/assets/external/widget.js', () => {
                        this.script_loaded = true;
                        window.addEventListener('message', function (e) {
                            if (e.origin === 'https://calendly.com' && e.data.event == 'calendly.event_scheduled') {
                                MCChat.updateMessage(message_id, mc_(url[1] ? url[1] : 'Booking completed.'));
                                chat_overlay_panel.mcActive(false);
                            }
                        });
                        this.load_(url[0], title);
                    }, true);
                }
            },

            load_: function (url, title) {
                Calendly.initInlineWidget({
                    url: (url.includes('http') ? '' : 'https://') + url + '?hide_landing_page_details=1&hide_event_type_details=1&hide_gdpr_banner=1',
                    parentElement: chat_overlay_panel.find('> div').eq(1),
                    prefil: activeUser().type == 'user' ? { name: activeUser().name, email: activeUser().email } : {}
                });
                chat_overlay_panel.find('> div:first-child > div').html(mc_(title));
                chat_overlay_panel.mcActive(true).attr('data-id', 'calendly');
            }
        },

        rating: function (is_close_chat = false) {
            chat_overlay_panel.find('> div:first-child > div').html(mc_('Rate your experience'));
            chat_overlay_panel.find('> div:last-child').html(`${CHAT_SETTINGS.rating_message ? `<div class="mc-input mc-input-textarea"><textarea placeholder="${mc_('Add a message here...')}"></textarea></div>` : ''}<div class="mc-rating"><div><i data-rating="positive" class="mc-submit mc-icon-like"><span>${mc_('Helpful')}</span></i><i data-rating="negative" class="mc-submit mc-icon-dislike"><span>${mc_('Not helpful')}</span></i></div></div>`);
            chat_overlay_panel.mcActive(true).attr('data-id', 'rating').attr('data-close-chat', is_close_chat ? 'true' : '').css('max-height', (CHAT_SETTINGS.rating_message ? 167 : 80) + 'px');
        },

        isShortcode: function (shortcode_name) {
            return shortcode_name in this.rich_messsages || (admin && MC_ADMIN_SETTINGS.rich_messages.includes(shortcode_name)) || (!admin && CHAT_SETTINGS.rich_messages.includes(shortcode_name));
        }
    }
    window.MCRichMessages = MCRichMessages;

    /* 
    * ----------------------------------------------------------
    * FORM METHODS
    * ----------------------------------------------------------
    */

    var MCForm = {

        // Get all settings
        getAll: function (area) {
            let settings = {};
            $(area).find('.mc-input[id]').each((i, element) => {
                settings[$(element).attr('id')] = this.get(element);
            });
            return settings;
        },

        // Get a single setting
        get: function (input) {
            input = $(input);
            let type = input.data('type');
            let name = mc_(MCF.escape(input.find(' > span').html()));
            switch (type) {
                case 'image':
                    let url = input.find('.image').attr('data-value');
                    return [MCF.null(url) ? '' : url, name];
                case 'select':
                    return [MCF.escape(input.find('select').val()), name];
                case 'select-input':
                    let select = input.find('select,input[disabled]');
                    return [MCF.escape((select.is('select') || select.is('input') ? select.val() : (input.find('.mc-select').length ? input.find('.mc-select > p').attr('data-value') : input.find('> div').html())) + input.find('> input').val()), name];
                default:
                    let target = input.find('input');
                    return [MCF.escape(target.length ? target.val() : input.find('[data-value]').attr('data-value')), name];
            }
        },

        // Set a single setting
        set: function (item, value) {
            item = $(item);
            if (item.length) {
                let type = item.data('type');
                switch (type) {
                    case 'image':
                        if (value) {
                            item.find('.image').attr('data-value', value).css('background-image', `url("${value}")`);
                        } else {
                            item.find('.image').removeAttr('data-value').removeAttr('style');
                        }
                        break;
                    case 'select':
                        item.find('select').val(value);
                        break;
                    default:
                        item.find('input,textarea').val(value);
                        break;
                }
                return true;
            }
            return false;
        },

        // Clear all the input values
        clear: function (area) {
            $(area).find('.mc-input,.mc-setting').each((i, element) => {
                this.set(element, '');
                $(element).find('input, select, textarea').removeClass('mc-error');
            });
            this.set($(area).find('#user_type'), 'user');
        },

        // Check for errors on user input
        errors: function (area) {
            let errors = false;
            let items = $(area).find('input, select, textarea').removeClass('mc-error');
            items.each(function (i) {
                let value = $.trim($(this).val());
                let type = $(this).attr('type');
                let required = $(this).prop('required');
                if ((required && !value) || ((required || value) && ((type == 'password' && (value.length < 8 || (items.length > (i + 1) && items.eq(i + 1).attr('type') == 'password' && items.eq(i + 1).val() != value))) || (type == 'email' && (value.indexOf('@') < 0 || value.indexOf('.') < 0 || /;|:|\/|\\|,|#|"|!|=|\*|{|}|[|]|£|\$|€|~|'|>|<|\^|&/.test(value))) || ($(this).attr('data-phone') && value && ((!$(this).parent().find('select').val() && !$(this).prev().find('> div > p').attr('data-value') && !$(this).parent().find('div').html().trim().startsWith('+')) || isNaN(value) || value.includes('+') || value.length < 5))))) {
                    errors = true;
                    $(this).addClass('mc-error');
                }
            });
            items = $(area).find('[data-required]').removeClass('mc-error');
            items.each(function () {
                if (MCF.null($(this).attr('data-value'))) {
                    $(this).addClass('mc-error');
                    errors = true;
                }
            });
            return errors;
        },

        // Display a error message
        showErrorMessage: function (area, message) {
            $(area).find('.mc-info').html(mc_(message)).mcActive(true);
            clearTimeout(timeout);
            timeout = setTimeout(function () {
                $(area).find('.mc-info').mcActive(false);
            }, 7000);
        },

        // Display a success message
        showSuccessMessage: function (area, message) {
            $(area).find('.mc-info').remove();
            $(area).addClass('mc-success').find('.mc-content').html(`<div class="mc-text">${message}</div>`);
        },

        // Return the registration error message
        getRegistrationErrorMessage(area_or_response, type = 'validation') {
            let error_text = '';
            if (type == 'response') {
                return MCF.errorValidation(area_or_response, 'duplicate-email') ? 'This email is already in use. Please use another email.' : (MCF.errorValidation(area_or_response, 'duplicate-phone') ? 'This phone number is already in use. Please use another number.' : (MCF.errorValidation(area_or_response, 'invalid-otp') ? 'Invalid one-time code.' : 'Error. Please check your information and try again.'))
            }
            $(area_or_response).find('.mc-input:not(#password-check) [required]').each(function () {
                error_text += ', ' + $(this).closest('.mc-input').find('span').html() + ($(this).attr('type') == 'password' ? ' (' + mc_('8 characters minimum') + ')' : '');
            });
            return `${error_text.substring(2)} ${mc_((error_text.includes(',') ? 'are' : 'is') + ' required.')}`;
        }
    }
    window.MCForm = MCForm;

    /* 
    * ----------------------------------------------------------
    * APPS
    * ----------------------------------------------------------
    */

    var MCApps = {

        // Get the login data 
        login: function () {
            if (typeof MC_DEFAULT_USER != ND && MC_DEFAULT_USER) {
                return [MC_DEFAULT_USER, 'default'];
            }
            if (this.is('wp') && typeof MC_WP_ACTIVE_USER != ND && CHAT_SETTINGS.wp_users_system == 'wp') {
                return [[MC_WP_ACTIVE_USER, typeof MC_WP_AVATAR != ND ? MC_WP_AVATAR : ''], 'wp'];
            }
            if (typeof MC_SHOPIFY_ACTIVE_USER != ND) {
                return [MC_SHOPIFY_ACTIVE_USER, 'shopify'];
            }
            if (typeof MC_PERFEX_ACTIVE_USER != ND) {
                return [[MC_PERFEX_ACTIVE_USER, MC_PERFEX_CONTACT_ID], 'perfex'];
            }
            if (typeof MC_WHMCS_ACTIVE_USER != ND) {
                return [MC_WHMCS_ACTIVE_USER, 'whmcs'];
            }
            if (typeof MC_AECOMMERCE_ACTIVE_USER != ND) {
                return [MC_AECOMMERCE_ACTIVE_USER, 'aecommerce'];
            }
            return false;
        },

        // Check if an app is installed and active
        is: function (name) {
            if (admin) return MCAdmin.apps.is(name);
            if (name == 'wordpress' || name == 'wp') return CHAT_SETTINGS.wp;
            return name in CHAT_SETTINGS ? CHAT_SETTINGS[name] : false;
        },

        shopify: {

            startCartSynchronization: function () {
                setInterval(() => {
                    if (activeUser()) {
                        fetch('/cart.js').then((response) => response.json()).then((cart) => {
                            cart = {
                                total: cart.total_price,
                                currency: cart.currency,
                                items: cart.items.map(item => ({
                                    id: item.product_id,
                                    price: item.price,
                                    handle: item.handle,
                                    title: item.title,
                                    quantity: item.quantity
                                }))
                            };
                            let cart_string = JSON.stringify(cart);
                            if (storage('shopify-cart') != cart_string) {
                                storage('shopify-cart', cart_string);
                                MCF.ajax({
                                    function: 'shopify-cart-sync',
                                    cart: cart
                                });
                            }
                        });
                    }
                }, 10000);
            }
        },

        wordpress: {

            ajax: function (action, data, onSuccess = false) {
                if (typeof MC_WP_AJAX_URL == ND) return;
                $.ajax({
                    method: 'POST',
                    url: MC_WP_AJAX_URL,
                    data: $.extend({ action: 'mc_wp_ajax', type: action }, data)
                }).done((response) => {
                    if (onSuccess) {
                        onSuccess(response);
                    }
                });
            }
        },

        woocommerce: {

            // Update the cart
            updateCart: function (action, product_id, onSuccess = false) {
                MCApps.wordpress.ajax(action, { product_id: product_id }, onSuccess);
            },

            // Waiting list
            waitingList: function (action = 'request', product_id = false) {
                if (typeof MC_WP_WAITING_LIST !== ND && (action != 'request' || MC_WP_WAITING_LIST && MCF.storageTime('waiting-list-' + MC_WP_PAGE_ID, 24)) && activeUser()) {
                    MCF.ajax({
                        function: 'woocommerce-waiting-list',
                        product_id: product_id === false ? MC_WP_PAGE_ID : product_id,
                        conversation_id: MCChat.conversation.id,
                        action: action,
                        token: this.token
                    }, (response) => {
                        if (response) {
                            MCF.storageTime('waiting-list-' + MC_WP_PAGE_ID);
                            if (action == 'request' && (!MCChat.chat_open || MCChat.dashboard)) {
                                MCChat.updateConversations();
                            }
                        }
                    });
                }
            }
        },

        dialogflow: {
            token: storage('dialogflow-token'),
            typing_enabled: true,
            project_id: false,
            busy: false,

            message: function (message = '', attachments = [], delay = false, parameters = false, audio = false) {
                if (message.length < 2 && !attachments.length) {
                    return;
                }
                if (!audio) {
                    for (var i = 0; i < attachments.length; i++) {
                        if (attachments[i][0].includes('voice_message')) {
                            audio = attachments[i][1];
                        }
                    }
                }
                if (this.active(true, true, false)) {
                    let human_takeover_active = MCApps.dialogflow.humanTakeoverActive();
                    if (!human_takeover_active || this.typing_enabled) {
                        this.typing();
                    }
                    if (this.chatbotLimit()) {
                        return MCChat.sendMessage(bot_id, this.chatbotLimit());
                    }
                    MCChat.headerAgent(true);
                    setTimeout(() => {
                        let conversation = MCChat.conversation;
                        MCF.ajax({
                            function: 'dialogflow-message',
                            conversation_id: conversation ? conversation.id : false,
                            message: message,
                            attachments: attachments,
                            parameters: parameters,
                            token: this.token,
                            dialogflow_language: storage('dialogflow-language') ? storage('dialogflow-language') : MC_LANG,
                            project_id: this.project_id,
                            session_id: activeUser().id + '-' + conversation.id,
                            audio: audio
                        }, (response) => {
                            MCChat.typing(-1, 'stop');
                            if (response === false) {
                                return;
                            }
                            if (response.response && response.response.error) {
                                return console.error(response.response);
                            }
                            if (response.human_takeover) {
                                MCChat.offlineMessage();
                                MCChat.followUp();
                                return this.active(false);
                            }
                            if (response.language_detection && !storage('dialogflow-language')) {
                                storage('dialogflow-language', [response.language_detection]);
                            }
                            if (response.user_language && !storage('dialogflow-language')) {
                                activeUser().setExtra('language', response.user_language);
                            }
                            if (response.translations) {
                                CHAT_SETTINGS.translations = response.translations;
                            }
                            if (!MCF.errorValidation(response)) {
                                let query_result = response.response.queryResult ? response.response.queryResult : false;
                                let is_unknow = query_result && (query_result.action == 'input.unknown' || (query_result.match && query_result.match.matchType == 'NO_MATCH'));
                                let messages = response.messages && Array.isArray(response.messages) ? response.messages : [];
                                clearTimeout(timeout);
                                if (this.token != response.token) {
                                    this.token = response.token;
                                    storage('dialogflow-token', response.token);
                                }
                                if (is_unknow) {
                                    if (human_takeover_active) {
                                        this.typing_enabled = false;
                                    }
                                } else {
                                    this.typing_enabled = true;
                                }
                                if (query_result) {

                                    // Actions
                                    if (query_result.action) {
                                        let action = query_result.action;
                                        if (action == 'end') {
                                            this.active(false);
                                        }
                                        MCF.event('MCBotAction', action);
                                    }

                                    // Payload
                                    for (var i = 0; i < messages.length; i++) {
                                        if (messages[i].payload) {
                                            let payloads = ['human-takeover', 'redirect', 'woocommerce-update-cart', 'woocommerce-checkout', 'open-article', 'transcript', 'department', 'agent', 'send-email', 'rich-message', 'tags'];
                                            let payload = messages[i].payload;
                                            if (MCF.null(payload)) {
                                                payload = [];
                                            }
                                            if (payloads[0] in payload && payload[payloads[0]] === true) {
                                                this.humanTakeover();
                                            }
                                            if (payloads[1] in payload) {
                                                setTimeout(function () {
                                                    payload['new-window'] ? window.open(payload[payloads[1]]) : document.location = payload[payloads[1]];
                                                }, 500);
                                            }
                                            if (payloads[2] in payload) {
                                                let payload_value = payload[payloads[2]];
                                                MCApps.woocommerce.updateCart(payload_value[0], payload_value[1], (response) => {
                                                    if (response) {
                                                        MCF.event('MCWoocommerceCartUpdated', { 'action': payload_value[0], 'product': payload_value[1] });
                                                    }
                                                });
                                            }
                                            if (payloads[3] in payload && payload[payloads[3]] === true) {
                                                MCApps.wordpress.ajax('url', { url_name: 'checkout' }, (response) => {
                                                    setTimeout(() => document.location = response, 500);
                                                });
                                            }
                                            if (payloads[4] in payload) {
                                                MCChat.showArticles(payload[payloads[4]]);
                                            }
                                            if (payloads[5] in payload && payload[payloads[5]]) {
                                                MCF.ajax({
                                                    function: 'transcript',
                                                    conversation_id: conversation.id,
                                                    type: 'txt'
                                                }, (response) => {
                                                    if (payload[payloads[5]] == 'email' && activeUser().email) {
                                                        MCChat.sendEmail(payload.message ? payload.message : '', [[response, response]], conversation.id);
                                                    } else window.open(response);
                                                });
                                            }
                                            if (payloads[6] in payload) {
                                                MCF.ajax({
                                                    function: 'update-conversation-department',
                                                    conversation_id: conversation.id,
                                                    department: payload[payloads[6]],
                                                    message: conversation.getLastUserMessage().message
                                                });
                                            }
                                            if (payloads[7] in payload) {
                                                MCF.ajax({
                                                    function: 'update-conversation-agent',
                                                    conversation_id: conversation.id,
                                                    agent_id: payload[payloads[7]],
                                                    message: conversation.getLastUserMessage().message
                                                });
                                            }
                                            if (payloads[8] in payload) {
                                                let email = payload[payloads[8]];
                                                MCChat.sendEmail(email.message, email.attachments, email.recipient == 'active_user');
                                            }
                                            if (payloads[9] in payload) {
                                                MCChat.sendMessage(bot_id, payload[payloads[10]]);
                                            }
                                            if (payloads[10] in payload) {
                                                MCF.ajax({
                                                    function: 'update-tags',
                                                    conversation_id: conversation.id,
                                                    tags: payload[payloads[11]]
                                                });
                                            }
                                            MCF.event('MCBotPayload', payload);
                                        }

                                        // More
                                        this.chatbotLimit(false);
                                        if (!storage('dialogflow-language') && query_result.languageCode && (!MC_LANG || query_result.languageCode != MC_LANG[0])) {
                                            storage('dialogflow-language', [query_result.languageCode]);
                                        }
                                    }

                                    // Diagnostic info
                                    if (query_result.diagnosticInfo) {
                                        let info = query_result.diagnosticInfo;

                                        // End conversation
                                        if (info.end_conversation) {
                                            this.active(false);
                                        }
                                    }
                                }

                                // Slack
                                if (CHAT_SETTINGS.slack_active && messages && (!dialogflow_human_takeover || human_takeover_active)) {
                                    for (var i = 0; i < messages.length; i++) {
                                        MCChat.slackMessage(activeUser().id, CHAT_SETTINGS.bot_name, CHAT_SETTINGS.bot_image, messages[i].message, messages[i].attachments);
                                    }
                                }

                                MCF.event('MCBotMessage', { response: response, message: message });
                            }
                        });
                        this.busy = true;
                    }, delay !== false ? delay : (CHAT_SETTINGS.bot_delay == 0 ? 2000 : parseInt(CHAT_SETTINGS.bot_delay)));
                } else if (this.active(true, false, true) && !$('.mc-emoji-list').html().includes('<li>' + message + '</li>')) {
                    this.openAI(message, false, audio, attachments);
                }
            },

            openAI: function (message, onSuccess = false, audio = false, attachments = []) {
                if (CHAT_SETTINGS.open_ai_active) {
                    MCChat.headerAgent(true);
                    if (MCChat.agent_id == bot_id && (!MCApps.dialogflow.humanTakeoverActive() || this.typing_enabled)) {
                        this.typing();
                    }
                    if (this.chatbotLimit()) {
                        return MCChat.sendMessage(bot_id, this.chatbotLimit());
                    }
                    setTimeout(() => {
                        MCF.ajax({
                            function: 'open-ai-message',
                            message: message,
                            conversation_id: MCChat.conversation ? MCChat.conversation.id : false,
                            audio: audio,
                            extra: { token: MCApps.dialogflow.token },
                            attachments: attachments,
                            context: CHAT_SETTINGS.open_ai_context_awareness ? document.title.toUpperCase() + '\n\n\n' + $('meta[name="description"]').attr('content') || '' : false
                        }, (response) => {
                            this.busy = false;
                            MCChat.typing(-1, 'stop');
                            MCF.event('MCOpenAIMessage', { response: response, message: message });
                            if (response && (response[0] || response[5])) {
                                if (CHAT_SETTINGS.chatbot_limit) {
                                    this.chatbot_limit++;
                                }
                                if (CHAT_SETTINGS.slack_active && message && (!dialogflow_human_takeover || MCApps.dialogflow.humanTakeoverActive())) {
                                    MCChat.slackMessage(activeUser().id, CHAT_SETTINGS.bot_name, CHAT_SETTINGS.bot_image, response[1]);
                                }
                                if (response[2] && MCApps.dialogflow.token != response[2]) {
                                    MCApps.dialogflow.token = response.token;
                                    storage('dialogflow-token', response.token);
                                }
                                if (response[3]) {
                                    MCChat.offlineMessage();
                                    MCChat.followUp();
                                }

                                // Payload
                                if (response[5]) {
                                    if (response[5].redirect) {
                                        setTimeout(() => {
                                            document.location = response[5].redirect;
                                        }, 500);
                                    }
                                    if (response[5].open_article) {
                                        MCChat.showArticles(response[5].open_article);
                                    }

                                    if (response[5].event == 'conversation-status-update-3') {
                                        MCChat.conversationArchived();
                                    }
                                }
                                this.chatbotLimit(false);
                            } else if (response[1] !== false) {
                                MCF.error(response[1].error ? response[1].error.message : response[1], 'MCApps.dialogflow.openAI');
                            }
                            if (onSuccess) {
                                onSuccess(response);
                            }
                        });
                        this.busy = true;
                    }, CHAT_SETTINGS.bot_delay == 0 ? 2000 : parseInt(CHAT_SETTINGS.bot_delay));
                }
            },

            flowOnLoad() {
                if (CHAT_SETTINGS.flow_on_load && !storage('flow-on-load') && activeUser()) {
                    if (MCChat.conversation) {
                        MCF.ajax({
                            function: 'run-flow-on-load',
                            message: CHAT_SETTINGS.flow_on_load,
                            conversation_id: MCChat.conversation.id
                        });
                    } else {
                        MCChat.newConversation(3, activeUser().id, '', [], null, null, () => {
                            MCF.ajax({
                                function: 'run-flow-on-load',
                                message: CHAT_SETTINGS.flow_on_load,
                                conversation_id: MCChat.conversation.id
                            });
                        });
                    }
                    storage('flow-on-load', true);
                }
            },

            typing: function () {
                clearTimeout(timeout_typing);
                timeout_typing = setTimeout(() => {
                    MCChat.typing(-1, 'start');
                }, 1000);
            },

            active: function (active = true, check_dialogflow = true, check_open_ai = true) {
                if (active === false) {
                    MCChat.conversation.set('is_human_takeover', true);
                    return false;
                }
                if (active == 'activate') {
                    MCChat.conversation.set('is_human_takeover', false);
                }
                if (!admin && MCApps.dialogflow.humanTakeoverActive() && CHAT_SETTINGS.dialogflow_human_takeover_disable_chatbot) {
                    return false;
                }
                let check = !admin && (!MCChat.conversation || !MCApps.dialogflow.humanTakeoverActive() || !MCChat.agent_online) && (!CHAT_SETTINGS.dialogflow_office_hours || !CHAT_SETTINGS.office_hours);
                if (check_dialogflow && check_open_ai) {
                    return (CHAT_SETTINGS.dialogflow_active || CHAT_SETTINGS.open_ai_active) && check;
                }
                return (!check_dialogflow || CHAT_SETTINGS.dialogflow_active) && (!check_open_ai || CHAT_SETTINGS.open_ai_active) && check;
            },

            welcome: function (open = false, sound = false) {
                MCF.ajax({
                    function: 'dialogflow-message',
                    message: '',
                    conversation_id: MCChat.conversation.id,
                    token: this.token,
                    event: 'Welcome',
                    dialogflow_language: storage('dialogflow-language') ? storage('dialogflow-language') : MC_LANG
                }, () => {
                    if (open) {
                        MCChat.start();
                    }
                    if (sound) {
                        MCChat.audio.play();
                    }
                });
            },

            humanTakeover: function () {
                MCF.ajax({
                    function: 'dialogflow-human-takeover',
                    conversation_id: MCChat.conversation.id
                }, () => {
                    MCChat.offlineMessage();
                    MCChat.followUp();
                    this.active(false);
                    if (CHAT_SETTINGS.queue_human_takeover) {
                        CHAT_SETTINGS.queue = true;
                        MCChat.queue(MCChat.conversation.id);
                    }
                });
            },

            humanTakeoverActive: function () {
                return MCChat.conversation ? MCChat.conversation.get('is_human_takeover') : false;
            },

            translate: function (strings, language_code, onSuccess, message_ids, conversation_id) {
                MCF.ajax({
                    function: 'google-translate',
                    strings: strings,
                    language_code: language_code,
                    token: this.token,
                    message_ids: message_ids,
                    conversation_id: conversation_id
                }, (response) => {
                    this.token = response[1]
                    onSuccess(response[0]);
                });
            },

            chatbotLimit: function (check = true) {
                if (CHAT_SETTINGS.chatbot_limit) {
                    let chatbot_limit = storage('chatbot_limit');
                    let now = (new Date()).getTime() / 1000;
                    if (!chatbot_limit) {
                        chatbot_limit = [];
                    }
                    if (check) {
                        let interval = now - CHAT_SETTINGS.chatbot_limit.interval;
                        let chatbot_limit_new = [];
                        for (var i = 0; i < chatbot_limit.length; i++) {
                            if (chatbot_limit[i] > interval) {
                                chatbot_limit_new.push(chatbot_limit[i]);
                            }
                        }
                        storage('chatbot_limit', chatbot_limit_new);
                        if (chatbot_limit_new.length >= CHAT_SETTINGS.chatbot_limit.quota) {
                            return CHAT_SETTINGS.chatbot_limit.message;
                        }
                    } else {
                        chatbot_limit.push(now);
                        storage('chatbot_limit', chatbot_limit);
                    }
                }
                return false;
            }
        },

        aecommerce: {

            cart: function () {
                if (MCApps.is('aecommerce') && typeof MC_AECOMMERCE_CART != ND && typeof MC_AECOMMERCE_ACTIVE_USER != ND && storage('aecommerce') != JSON.stringify(MC_AECOMMERCE_CART)) {
                    MCF.ajax({
                        function: 'aecommerce-cart',
                        cart: MC_AECOMMERCE_CART
                    }, () => {
                        storage('aecommerce', JSON.stringify(MC_AECOMMERCE_CART));
                    });
                }
            }
        },

        martfury: {

            privateChat: function () {
                let store = $('#tab-vendor > h4,.ps-product__vendor > a');
                if (store.length) {
                    store = store.html().toLowerCase();
                    for (var i = 0; i < CHAT_SETTINGS.martfury.length; i++) {
                        if (store == CHAT_SETTINGS.martfury[i]['martfury-linking-store'].toLowerCase()) {
                            let agent_id = CHAT_SETTINGS.martfury[i]['martfury-linking-agent'];
                            let conversations = MCF.activeUser() ? MCF.activeUser().conversations : [];
                            MCChat.default_agent = agent_id;
                            for (var j = 0; j < conversations.length; j++) {
                                if (conversations[j].get('agent_id') == agent_id) {
                                    MCChat.openConversation(conversations[j].id);
                                    return;
                                }
                            }
                            MCChat.clear();
                            MCChat.hideDashboard();
                        }
                    }
                }
            }
        }
    }
    window.MCApps = MCApps;

    /*
    * ----------------------------------------------------------
    * INIT
    * ----------------------------------------------------------
    */

    $(document).ready(function () {
        main = $('.mc-admin, .mc-admin-start');
        if (main.length) {
            admin = true;
            initialize();
            return;
        }
        let url_full;
        let url;
        let init = false;
        if (typeof MC_INIT_URL != ND) {
            if (MC_INIT_URL.indexOf('.js') < 0) {
                MC_INIT_URL += '/js/main.js?v=' + version;
            }
            url_full = MC_INIT_URL;
        } else {
            let scripts = document.getElementsByTagName('script');
            let checks = ['init.js', 'main.js', 'min/init.min.js', 'min/main.min.js'];
            for (var i = 0; i < scripts.length; i++) {
                let source = scripts[i].src;
                if (scripts[i].id == 'mcinit') {
                    url_full = source;
                    init = init ? init : url_full.includes('init.');
                    break;
                } else {
                    for (var j = 0; j < checks.length; j++) {
                        if (source && source.includes('/masichat/js/' + checks[j])) {
                            url_full = source;
                            init = init ? init : url_full.includes('init.');
                            break;
                        }
                    }
                }
            }
        }
        let parameters = MCF.getURL(false, url_full);
        if (parameters.url) {
            url_full = parameters.url;
        }
        if (typeof MC_DISABLED != ND && MC_DISABLED) {
            return;
        }
        if (init) {
            initialize();
            return;
        }
        if (typeof MC_TICKETS != ND || (parameters.mode == 'tickets')) {
            tickets = true;
            parameters.mode = 'tickets';
        }
        if (parameters.cloud) {
            cloud_data = parameters.cloud;
        }
        let lang_optional = is_shopify && Shopify.locale != 'en' ? Shopify.locale : false;
        let min = url_full.lastIndexOf('main.min.js');
        url = url_full.substr(0, url_full.lastIndexOf('main.js') > 0 ? (url_full.lastIndexOf('main.js') - 4) : (min - 8));
        let url_chat = url + '/include/init.php' + (parameters.lang ? '?lang=' + parameters.lang : '') + (lang_optional ? '?lang_optional=' + lang_optional : '') + (parameters.mode ? '&mode=' + parameters.mode : '') + (cloud_data ? '&cloud=' + cloud_data : '');
        MCF.cors('GET', url_chat.replace('.php&', '.php?'), (response) => {
            let target = 'body';
            if (tickets && $('#mc-tickets').length) {
                target = '#mc-tickets';
            }
            $(target).append(response);
            MCF.loadResource(url + '/css/' + (tickets ? 'tickets' : 'main') + '.css');
            if (tickets) {
                MCF.loadResource(url + '/apps/tickets/tickets' + (min > 0 ? '.min' : '') + '.js?v=' + version, true, () => {
                    initialize();
                });
            } else {
                initialize();
            }
            if (parameters.lang) {
                MC_LANG = [parameters.lang, admin ? 'admin' : 'front'];
            }
        });
    });

    function initialize() {
        main = $('.mc-admin, .mc-chat, .mc-tickets');

        // Initialize the chat and the user
        if (main.length && typeof MC_AJAX_URL != ND) {
            chat = main.find('.mc-list').eq(0);
            chat_editor = main.find('.mc-editor');
            chat_textarea = chat_editor.find('textarea');
            chat_scroll_area = main.find(admin || tickets ? '.mc-list' : '> div > .mc-scroll-area');
            chat_header = chat_scroll_area.find('.mc-header');
            chat_emoji = chat_editor.find('.mc-emoji');
            chat_overlay_panel = main.find('.mc-overlay-panel');
            chat_status = tickets ? main.find('.mc-profile-agent .mc-status') : null;
            MCChat.enabledAutoExpand();
            MCChat.audio = main.find('#mc-audio').get(0);
            MCChat.label_date = main.find('.mc-label-date-top');

            // Check if cookies works
            MCF.cookie('mc-check', 'ok', 1, 'set');
            if (MCF.cookie('mc-check') != 'ok') {
                cookies_supported = false;
                console.warn('Masi Chat: cookies not available.');
            } else {
                MCF.cookie('mc-check', false, false, false);
            }
            if (!admin) {
                MCF.ajax({
                    function: 'get-front-settings',
                    current_url: window.location.href,
                    tickets: tickets,
                    popup: !storage('popup') && !tickets
                }, (response) => {
                    CHAT_SETTINGS = response;
                    if (typeof MC_LOCAL_SETTINGS != ND) {
                        $.extend(CHAT_SETTINGS, MC_LOCAL_SETTINGS);
                    }
                    bot_id = CHAT_SETTINGS.bot_id;
                    dialogflow_human_takeover = CHAT_SETTINGS.dialogflow_human_takeover;
                    agents_online = CHAT_SETTINGS.agents_online;
                    MCPusher.active = CHAT_SETTINGS.pusher;
                    if (CHAT_SETTINGS.language) {
                        MC_LANG = CHAT_SETTINGS.language != 'auto' ? [CHAT_SETTINGS.language, 'front'] : (is_shopify && Shopify.locale != 'en' ? Shopify.locale : false);
                    }
                    if (typeof MC_REGISTRATION_REQUIRED != ND) {
                        CHAT_SETTINGS.registration_required = MC_REGISTRATION_REQUIRED;
                        CHAT_SETTINGS.tickets_registration_required = MC_REGISTRATION_REQUIRED;
                    }
                    if (typeof MC_ARTICLES_PAGE != ND && MC_ARTICLES_PAGE) {
                        MCChat.automations.runAll();
                        MCChat.initArticlesPage();
                    }
                    if ((!tickets || !CHAT_SETTINGS.tickets_manual_init) && ((tickets && !CHAT_SETTINGS.tickets_manual_init) || (!CHAT_SETTINGS.chat_manual_init && (!CHAT_SETTINGS.disable_offline || agents_online) && (!CHAT_SETTINGS.disable_office_hours || CHAT_SETTINGS.office_hours) && (!CHAT_SETTINGS.chat_login_init || MCApps.login())))) {
                        MCChat.initChat();
                    }
                    if (CHAT_SETTINGS.cron) {
                        setTimeout(function () {
                            MCF.ajax({ function: 'cron-jobs' });
                        }, 10000);
                    }
                    if (CHAT_SETTINGS.cron_email_piping) {
                        setTimeout(function () {
                            MCF.ajax({ function: 'email-piping' });
                        }, 8000);
                    }
                    if (CHAT_SETTINGS.push_notifications_users) {
                        MCF.serviceWorker.init();
                    }
                    if (tickets) {
                        if (CHAT_SETTINGS.tickets_default_department) {
                            MCChat.default_department = CHAT_SETTINGS.tickets_default_department;
                        }
                        if (CHAT_SETTINGS.dialogflow_disable_tickets) {
                            CHAT_SETTINGS.dialogflow_active = false;
                            CHAT_SETTINGS.open_ai_active = false;
                        }
                    }
                    if (MCApps.is('martfury')) {
                        let session = false;
                        setInterval(function () {
                            if (activeUser()) {
                                let current = MCF.cookie('XSRF-TOKEN');
                                if (current && current != session) {
                                    MCF.ajax({ function: 'martfury-session' });
                                    session = current;
                                }
                            }
                        }, 3000);
                    }
                    if (is_shopify) {
                        MCApps.shopify.startCartSynchronization();
                        main.addClass('mc-shopify');
                    }
                    setAudio();
                    MCApps.aecommerce.cart();
                    setTimeout(() => {
                        MCF.event('MCReady');
                    }, 500);
                });
            } else {
                setAudio();
                MCF.event('MCReady');
            }
            $(chat_editor).on('keydown', 'textarea', function (e) {
                if (e.which == 13 && (!tickets || CHAT_SETTINGS.tickets_enter_button) && !mobile && !e.ctrlKey && !e.shiftKey) {
                    MCChat.submit();
                    e.preventDefault;
                    return false;
                }
                if (admin && e.which == 13 && e.ctrlKey) {
                    MCChat.insertText('\n');
                }
            });
            $(main).on('keydown', '.mc-dashboard-articles input', function (e) {
                if (e.which == 13) {
                    $(this).next().click();
                }
            });
            if (typeof MC_DEFAULT_DEPARTMENT !== ND) {
                MCChat.default_department = MC_DEFAULT_DEPARTMENT;
            }
            if (typeof MC_DEFAULT_AGENT !== ND) {
                MCChat.default_agent = MC_DEFAULT_AGENT;
            }
            if (typeof MC_DIALOGFLOW_TAGS !== ND) {
                MCChat.default_tags = MC_DEFAULT_TAGS;
            }
            if (typeof MC_DIALOGFLOW_AGENT !== ND) {
                MCApps.dialogflow.project_id = MC_DIALOGFLOW_AGENT;
            }
        } else {
            MCF.event('MCReady');
        }

        // Disable real-time if browser tab not active
        document.addEventListener('visibilitychange', function () {
            MCF.visibilityChange(document.visibilityState);
        }, false);

        $(main).on('click', function () {
            if (!MCChat.tab_active) {
                MCF.visibilityChange();
            }
        });

        // Set the global container for both admin and front
        global = main;
        if (admin) {
            main = main.find('.mc-conversation');
        }

        // Scroll detection
        $(chat_scroll_area).on('scroll', function () {
            let scroll = chat_scroll_area.scrollTop();
            let count = label_date_items.length;
            MCChat.scrollHeader();
            if (!MCChat.label_date_show && count && label_date_items[count - 1].getBoundingClientRect().top > (chat[0].getBoundingClientRect().top + chat.outerHeight())) {
                MCChat.label_date_show = true;
                if (count > 1) {
                    MCChat.label_date.html($(label_date_items[count - 2]).html());
                }
            }
            if (MCChat.label_date_show) {
                MCChat.label_date.mcActive(true);
                clearTimeout(label_date_timeout[0]);
                label_date_timeout[0] = setTimeout(() => { MCChat.label_date.mcActive(false) }, 1500);
            }
            if (count) {
                if (MCChat.isBottom()) {
                    MCChat.label_date.html($(label_date_items[count - 1]).html());
                    MCChat.label_date_show = label_date_items.length && label_date_items[label_date_items.length - 1].getBoundingClientRect().top < 0;
                } else {
                    let label_date_top = MCChat.label_date[0].getBoundingClientRect().top;
                    for (var i = 0; i < count; i++) {
                        let top = label_date_items[i].getBoundingClientRect().top;
                        if ((top - 100) < label_date_top && (top + 100) > label_date_top) {
                            let label = $(label_date_items[label_date_history[0] > scroll && i > 0 ? i - 1 : i]).html();
                            if (label != label_date_history[1]) {
                                MCChat.label_date.html(label);
                                label_date_history[1] = label;
                            }
                            break;
                        }
                    }
                }
            }
            label_date_history[0] = scroll;
        });

        // Show the message menu
        $(chat).on('click', '.mc-menu-btn', function () {
            let menu = $(this).parent().find('.mc-menu');
            let active = $(menu).mcActive();
            MCF.deactivateAll();
            if (!active) {
                $(menu).mcActive(true);
                MCF.deselectAll();
                if (admin) MCAdmin.open_popup = menu;
            }
        });

        // Mobile
        if (mobile) {
            $(chat_editor).on('click', '.mc-textarea', function () {
                main.addClass('mc-header-hidden');
                $(this).find('textarea').get(0).focus();
                if (MCChat.isBottom()) {
                    MCChat.scrollBottom();
                    setTimeout(() => {
                        MCChat.scrollBottom();
                    }, 200);
                }
            });

            $(chat_editor).on('focusout', '.mc-textarea', function () {
                setTimeout(() => {
                    main.removeClass('mc-header-hidden');
                }, 300);
            });

            $(chat_editor).on('click', '.mc-submit', function () {
                chat_textarea.blur();
            });

            window.addEventListener('popstate', function () {
                if (MCChat.chat_open) {
                    MCChat.open(false);
                }
            });
        }

        // Hide the message menu
        $(chat).on('click', '.mc-menu li', function () {
            $(this).parent().mcActive(false);
        });

        // Send a message
        $(chat_editor).on('click', '.mc-submit', function () {
            MCChat.submit();
        });

        // Open the chat
        $('body').on('click', '.mc-chat-btn,.mc-responsive-close-btn, #mc-open-chat, .mc-open-chat', function () {
            MCChat.open(!MCChat.chat_open);
        });

        // Show the dashboard
        $(main).on('click', '.mc-dashboard-btn', function () {
            MCChat.showDashboard();
            if (chat_scroll_area.find(' > .mc-panel-articles > .mc-article').length) {
                MCChat.showArticles();
            }
            storage('open-conversation', 0);
            force_action = false;
        });

        // Open a conversation from the dashboard
        $(main).on('click', '.mc-user-conversations li', function () {
            MCChat.openConversation($(this).attr('data-conversation-id'));
        });

        // Start a new conversation from the dashboard
        $(main).on('click', '.mc-btn-new-conversation, .mc-departments-list > div, .mc-agents-list > div', function () {
            let id = $(this).data('id');
            let existing_conversation = false;
            if (!MCF.null(id)) {
                let is_departments = $(this).parent().hasClass('mc-departments-list');
                if (is_departments) {
                    MCChat.default_department = parseInt(id);
                } else {
                    MCChat.default_agent = parseInt(id);
                }
                if ($(this).parent().data('force-one')) {
                    let converstations = activeUser() ? activeUser().conversations : [];
                    for (var i = 0; i < converstations.length; i++) {
                        if ((is_departments && converstations[i].get('department') == id) || (!is_departments && converstations[i].get('agent_id') == id)) {
                            existing_conversation = true;
                            MCChat.openConversation(converstations[i].id);
                            break;
                        }
                    }
                }
            }
            if (!existing_conversation) {
                force_action = 'new-conversation';
                MCChat.clear();
                MCChat.hideDashboard();
            }
        });

        // Displays all conversations in the dashboard
        $(main).on('click', '.mc-btn-all-conversations', function () {
            main.find('.mc-dashboard-conversations').removeClass('mc-conversations-hidden');
        });

        // Events uploader
        $(chat_editor).on('click', '.mc-btn-attachment', function () {
            if (!MCChat.is_busy) {
                chat_editor.find('.mc-upload-files').val('').click();
            }
        });

        $(chat_editor).on('click', '.mc-attachments > div > i', function (e) {
            $(this).parent().remove();
            if (!chat_textarea.val() && chat_editor.find('.mc-attachments > div').length == 0) {
                chat_editor.mcActive(false);
            }
            e.preventDefault();
            return false;
        });

        $(chat_editor).on('change', '.mc-upload-files', function (data) {
            MCChat.busy(true);
            $(this).mcUploadFiles(function (response) {
                MCChat.uploadResponse(response);
            });
            MCF.event('MCAttachments');
        });

        $(chat_editor).on('dragover', function (e) {
            $(this).addClass('mc-drag');
            clearTimeout(timeout);
            e.preventDefault();
            e.stopPropagation();
        });

        $(chat_editor).on('dragleave', function (e) {
            timeout = setTimeout(() => {
                $(this).removeClass('mc-drag');
            }, 200);
            e.preventDefault();
            e.stopPropagation();
        });

        $(chat_editor).on('drop', function (e) {
            let files = e.originalEvent.dataTransfer.files;
            e.preventDefault();
            e.stopPropagation();
            if (files.length > 0) {
                for (var i = 0; i < files.length; ++i) {
                    let form = new FormData();
                    form.append('file', files[i]);
                    MCF.upload(form, function (response) { MCChat.uploadResponse(response) });
                }
            }
            $(this).removeClass('mc-drag');
            return false;
        });

        // Articles
        $(main).on('click', '.mc-btn-all-articles:not([onclick])', function () {
            MCChat.showArticles();
        });

        $(main).on('click', '.mc-articles > div', function () {
            MCChat.showArticles($(this).attr('data-id'), $(this).attr('data-is-category'));
        });

        $(main).on('click', '.mc-dashboard-articles .mc-input-btn .mc-submit-articles', function () {
            MCChat.searchArticles($(this).parent().find('input').val(), this, $(this).parent().next());
        });

        $(global).on('click', '.mc-article [data-rating]', function () {
            MCChat.articleRatingOnClick(this);
        });

        $(chat).on('click', '.mc-rich-button a', function (e) {
            let link = $(this).attr('href');
            if (link.indexOf('#') === 0) {
                if (link.indexOf('#article-') === 0) {
                    MCChat.showArticles(link.replace('#article-', ''));
                    e.preventDefault();
                    return false;
                }
            }
        });

        // Rating
        $(chat_overlay_panel).on('click', '[data-rating]', function () {
            let area = chat_overlay_panel.find('> div:last-child');
            let rating = $(this).attr('data-rating');
            let conversation = MCChat.conversation ? MCChat.conversation : activeUser().getLastConversation();
            let agent = conversation.getLastUserMessage(false, true);
            loading(area);
            MCF.ajax({
                function: 'set-rating',
                conversation_id: conversation.id,
                agent_id: agent ? agent.get('user_id') : false,
                user_id: activeUser().id,
                message: area.find('textarea').val(),
                rating: rating == 'positive' ? 1 : -1
            }, (response) => {
                if (chat_overlay_panel.attr('data-close-chat')) {
                    MCChat.closeChat();
                }
                MCChat.update();
                chat_overlay_panel.mcActive(false);
                area.mcLoading(false);
            });
        });

        // Lightbox
        $(global).on('click', '.mc-lightbox-media > i', function () {
            global.find('.mc-lightbox-media').mcActive(false);
            if (admin) {
                MCAdmin.open_popup = false;
            }
            return false;
        });

        $(main).on('click', '.mc-image', function () {
            MCF.lightbox($(this).html());
        });

        $(main).on('click', '.mc-slider-images .mc-card-img', function () {
            MCF.lightbox(`<img loading="lazy" src="${$(this).attr('data-value')}" />`);
        });

        // Event: on conversation loaded
        $(document).on('MCConversationLoaded', function () {
            if (storage('queue')) {
                MCChat.queue(storage('queue'));
            }
        });

        // Events emoji
        $(chat_editor).on('click', '.mc-btn-emoji', function () {
            MCChat.showEmoji(this);
        });

        $(chat_emoji).on('click', '.mc-emoji-list li', function (e) {
            MCChat.insertEmoji($(this).html());
            if (mobile) clearTimeout(timeout);
        });

        $(chat_emoji).find('.mc-emoji-list').on('touchend', function (e) {
            timeout = setTimeout(() => {
                MCChat.mouseWheelEmoji(e);
            }, 50);
        });

        $(chat_emoji).find('.mc-emoji-list').on('mousewheel DOMMouseScroll', function (e) {
            MCChat.mouseWheelEmoji(e);
        });

        $(chat_emoji).find('.mc-emoji-list').on('touchstart', function (e) {
            MCChat.emoji_options.touch = e.originalEvent.touches[0].clientY;
        });

        $(chat_emoji).on('click', '.mc-emoji-bar > div', function () {
            MCChat.clickEmojiBar(this);
        });

        $(chat_emoji).on('click', '.mc-select li', function () {
            MCChat.categoryEmoji($(this).data('value'));
        });

        $(chat_emoji).find('.mc-search-btn input').on('change keyup paste', function () {
            MCChat.searchEmoji($(this).val());
        });

        // Textarea
        $(chat_textarea).on('keyup', function () {
            MCChat.textareaChange(this);
        });

        // Privacy message
        $(main).on('click', '.mc-privacy .mc-approve', function () {
            storage('privacy-approved', true);
            $(this).closest('.mc-privacy').remove();
            main.removeClass('mc-init-form-active');
            chat_header.find(' > div').css({ opacity: 1, top: 0 });
            MCChat.initChat();
            if (tickets) {
                MCTickets.showPanel(MCF.setting('tickets_disable_first') ? '' : 'new-ticket');
            } else if (!MCChat.isInitDashboard()) {
                MCChat.hideDashboard();
            }
        });

        $(main).on('click', '.mc-privacy .mc-decline', function () {
            let privacy = $(this).closest('.mc-privacy');
            $(privacy).find('.mc-text').html($(privacy).attr('data-decline'));
            $(privacy).find('.mc-decline').remove();
            MCChat.scrollBottom(true);
        });

        // Popup message
        $(main).on('click', '.mc-popup-message .mc-icon-close', function () {
            MCChat.popup(true);
        });

        // Rich messages and inputs
        $(main).on('click', '.mc-rich-message .mc-submit,.mc-rich-message:not(.mc-rich-registration) .mc-select ul li', function () {
            let message = $(this).closest('.mc-rich-message');
            if (!message[0].hasAttribute('disabled')) {
                MCRichMessages.submit(message, message.attr('data-type'), this);
            };
        });

        $(main).on('click', '.mc-rich-message .mc-input > span', function () {
            $(this).mcActive(true);
            $(this).siblings().focus();
        });

        $(main).on('focus focusout click', '.mc-rich-message .mc-input input,.mc-rich-message .mc-input select', function (e) {
            switch (e.type) {
                case 'focusin':
                case 'focus':
                    $(this).siblings().mcActive(true);
                    break;
                case 'focusout':
                    if ($(this).val()) {
                        $(this).siblings().addClass('mc-filled mc-active');
                    } else {
                        setTimeout(() => {
                            if (!prevent_focusout) {
                                $(this).siblings().mcActive(false);
                            }
                        }, 100);
                    }
                    break;
                case 'click':
                    $(this).siblings().removeClass('mc-filled');
                    break;
            }
        });

        $(main).on('click', '.mc-input-select-input > div', function () {
            prevent_focusout = true;
            setTimeout(() => {
                prevent_focusout = false;
            }, 250);
        });

        $(main).on('click', '.mc-slider-arrow', function () {
            MCRichMessages.sliderChange($(this).closest('[id]').attr('id'), $(this).hasClass('mc-icon-arrow-right') ? 'right' : 'left');
        });

        $(main).on('change', '.mc-rich-message [data-type="select"] select', function () {
            $(this).siblings().mcActive(true);
        });

        $(main).on('click', '[data-type="select-input"] > div', function () {
            $(this).prev().mcActive(true);
            $(this).next().addClass('mc-focus');
        });

        $(main).on('focusout', '[data-type="select-input"] input,[data-type="select-input"] select', function () {
            let cnt = $(this).closest('.mc-input');
            if (cnt.find('> input').val() + cnt.find('select').val() == '') {
                cnt.find('span').mcActive(false);
            }
            cnt.find('.mc-focus').removeClass('mc-focus');
        });

        // Registration and Login
        $(main).on('click', '.mc-rich-registration .mc-login-area', function () {
            let init = main.hasClass('mc-init-form-active');
            $(this).closest('.mc-rich-registration').replaceWith(MCRichMessages.generate({}, 'login', init ? 'mc-init-form' : ''));
            MCChat.scrollBottom(init);
        });

        $(main).on('click', '.mc-rich-login .mc-registration-area', function () {
            if (CHAT_SETTINGS.registration_link) {
                document.location = CHAT_SETTINGS.registration_link;
            } else {
                let init = main.hasClass('mc-init-form-active');
                $(this).closest('.mc-rich-login').replaceWith(MCRichMessages.generate({}, 'registration', init ? 'mc-init-form' : ''));
                MCChat.scrollBottom(init);
            }
        });

        $(main).on('click', '.mc-rich-login .mc-submit-login', function () {
            MCF.loginForm(this, false, (response) => {
                let area = $(this).closest('.mc-rich-login');
                activeUser(new MCUser(response[0]));
                if (area.hasClass('mc-init-form')) {
                    force_action = 'open-conversation';
                    MCChat.initChat();
                    MCPusher.start();
                    if (!MCChat.isInitDashboard()) {
                        MCChat.hideDashboard();
                    }
                    $(document).on('MCPopulateConversations', function () {
                        main.removeClass('mc-init-form-active');
                        area.remove();
                        $(document).off('MCPopulateConversations');
                    });
                } else {
                    area = area.closest('[data-id]');
                    let message = MCChat.conversation.getMessage(area.attr('data-id'));
                    let text = `${mc_('Logged in as')} *${activeUser().name}*`;
                    message.set('message', text);
                    MCChat.updateMessage(message.id, text);
                    area.replaceWith(message.getCode());
                    MCPusher.started = false;
                    MCPusher.start();
                }
            });
        });

        // Social share buttons
        $(chat).on('click', '.mc-social-buttons div', function () {
            MCF.openWindow($(this).attr('data-link'));
        });

        // Archive chat
        $(main).on('click', '.mc-close-chat', function () {
            MCChat.closeChat();
        });

        // WooCommerce
        $(chat).on('click', '.mc-rich-woocommerce_button a, [href="#"].mc-card-btn', function (e) {
            let settings = MCF.settingsStringToArray($(this).closest('.mc-rich-message').attr('data-settings'));
            let checkout = settings['link-type'] == 'checkout' || settings.checkout;
            let product_ids = $(this)[0].hasAttribute('data-ids') ? $(this).attr('data-ids').split(',') : [settings.id.split('|')[$(this).parent().index()]];
            if (product_ids.length) {
                if (loading(this)) return;
                MCApps.wordpress.ajax('button-purchase', { product_ids: product_ids, checkout: checkout, coupon: settings.coupon }, (response) => {
                    if (checkout) {
                        document.location = response;
                    } else {
                        $(this).addClass('mc-icon-check').mcLoading(false);
                    }
                });
            }
            e.preventDefault();
            return false;
        });

        $(chat).on('click', '#mc-waiting-list .mc-submit', function () {
            if ($(this).index() == 0) {
                setTimeout(() => { MCApps.woocommerce.waitingList('submit') }, 1000);
            }
        });

        $(document).on('MCNewEmailAddress', function (e, response) {
            if (response.id == 'mc-waiting-list-email') {
                MCApps.woocommerce.waitingList('submit');
            }
        });

        // Audio player
        $(chat).on('click', '.mc-player-btn', function () {
            let audio = $(this).parent().find('audio').get(0);
            let stopped = $(this).hasClass('mc-icon-play');
            chat.find('audio').each(function () {
                $(this).get(0).pause();
                $(this).unbind('ended');
                $(this).parent().find('.mc-player-btn').removeClass('mc-icon-pause').addClass('mc-icon-play');
            });
            if (stopped) {
                audio.play();
                $(this).removeClass('mc-icon-play');
            } else {
                audio.pause();
            }
            $(audio).unbind('ended');
            $(audio).bind('ended', () => {
                $(this).removeClass('mc-icon-pause').addClass('mc-icon-play');
            });
            $(this).addClass('mc-icon-' + (stopped ? 'pause' : 'play'));
        });

        $(chat).on('click', '.mc-player-speed', function () {
            let parent = $(this).parent();
            if (parent.find('.mc-player-btn').hasClass('mc-icon-pause')) {
                let element = $(this).find('.mc-player-speed-number');
                let speed = parseFloat(element.html());
                speed += 0.5;
                if (speed > 2) {
                    speed = 1;
                }
                parent.find('audio').get(0).playbackRate = speed;
                element.html(speed);
            }
        });

        $(chat).on('click', '.mc-player-download', function () {
            window.open($(this).parent().find('audio source').attr('src'));
        });

        // Audio recording
        $(chat_editor).on('click', '.mc-btn-audio-clip', function () {
            audio_mp3 = MCChat.conversation && MCChat.conversation.get('source') == 'wa';
            if (audio_mp3 && typeof MCAudioRecorder === ND) {
                return $.getScript(MC_URL + '/vendor/lame.min.js', () => { this.click(); }, true);
            }
            navigator.mediaDevices.getUserMedia({ audio: true }).then(stream => {
                if (audio_mp3) {
                    MCAudioRecorder.init(stream);
                } else {
                    if (typeof MCAudioRecorder != ND) {
                        MCAudioRecorder.close();
                    }
                    audio_recorder_chunks = [];
                    audio_recorder = new MediaRecorder(stream);
                    audio_recorder.addEventListener('dataavailable', e => {
                        audio_recorder_chunks.push(e.data);
                    });
                }
                if (!audio_recorder_dom) {
                    audio_recorder_dom = chat_editor.find('#mc-audio-clip');
                    audio_recorder_dom_time = audio_recorder_dom.find('.mc-audio-clip-time');
                    audio_recorder_dom.on('click', '.mc-btn-mic', function () {
                        let is_pause = $(this).hasClass('mc-icon-pause');
                        if (is_pause) {
                            if (audio_mp3) {
                                MCAudioRecorder.pause();
                            } else {
                                audio_recorder.stop();
                            }
                        } else {
                            let button = audio_recorder_dom.find('.mc-btn-clip-player');
                            if (button.hasClass('mc-icon-pause')) {
                                button.click();
                            }
                            if (audio_mp3) {
                                MCAudioRecorder.resume();
                            } else {
                                audio_recorder.start();
                            }
                        }
                        audio_recorder_time[2] = !is_pause;
                        audio_recorder_dom.find('.mc-icon-play').mcActive(is_pause);
                        $(this).removeClass('mc-icon-' + (is_pause ? 'pause' : 'mic')).addClass('mc-icon-' + (is_pause ? 'mic' : 'pause'));
                    });

                    audio_recorder_dom.on('click', '.mc-btn-clip-player', function () {
                        let is_pause = $(this).hasClass('mc-icon-pause');
                        if (is_pause) {
                            audio_recorder_time_player[3].pause();
                            audio_recorder_time_player[2] = false;
                        } else {
                            if (audio_recorder_time_player[3]) {
                                audio_recorder_time_player[3].play();
                                audio_recorder_time_player[2] = true;
                            } else {
                                let audio = new Audio(URL.createObjectURL(audio_mp3 ? MCAudioRecorder.blob(false) : new Blob(audio_recorder_chunks, { type: 'audio/webm' })));
                                audio_recorder_dom_time.html('0:00');
                                audio.play();
                                audio.onended = () => {
                                    audio_recorder_time_player[0] = 0;
                                    audio_recorder_time_player[2] = false;
                                    audio_recorder_time_player[3] = false;
                                    audio_recorder_dom_time.html('0:00');
                                    $(this).removeClass('mc-icon-pause').addClass('mc-icon-play');
                                }
                                audio_recorder_time_player[0] = 0;
                                audio_recorder_time_player[2] = true;
                                audio_recorder_time_player[3] = audio;
                            }
                        }
                        $(this).removeClass('mc-icon-' + (is_pause ? 'pause' : 'play')).addClass('mc-icon-' + (is_pause ? 'play' : 'pause'));
                    });

                    audio_recorder_dom.on('click', '.mc-icon-close', function () {
                        audio_recorder_chunks = [];
                        clearInterval(audio_recorder_time[1]);
                        clearInterval(audio_recorder_time_player[1]);
                        audio_recorder_dom.mcActive(false);
                        chat_editor.mcActive(false).removeClass('mc-audio-message-active');
                        if (audio_mp3) {
                            MCAudioRecorder.pause();
                        } else {
                            audio_recorder.stop();
                        }
                        audio_recorder_stream.getTracks()[0].stop();
                    });
                }
                audio_recorder_dom_time.html('0:00');
                audio_recorder_stream = stream;
                clearInterval(audio_recorder_time[1]);
                clearInterval(audio_recorder_time_player[1]);
                audio_recorder_time = [0, setInterval(() => {
                    if (audio_recorder_time[2]) {
                        audio_recorder_time[0]++;
                        audio_recorder_dom_time.html(getMinutesSeconds(audio_recorder_time[0]));
                    }
                }, 1000), true];
                audio_recorder_time_player = [0, setInterval(() => {
                    if (audio_recorder_time_player[2]) {
                        audio_recorder_time_player[0]++;
                        audio_recorder_dom_time.html(getMinutesSeconds(audio_recorder_time_player[0]));
                    }
                }, 1000), false, false];
                if (!audio_mp3) {
                    audio_recorder.start();
                }
                audio_recorder_dom.find('.mc-btn-clip-player').removeClass('mc-icon-pause').addClass('mc-icon-play').mcActive(false);
                audio_recorder_dom.find('.mc-icon-mic').removeClass('mc-icon-mic').addClass('mc-icon-pause');
                audio_recorder_dom.mcActive(true);
                chat_textarea.val('').css('height', '');
                chat_editor.mcActive(true).addClass('mc-audio-message-active');
            }).catch(error => {
                alert(error);
            });
        });

        // Overlay panel
        $(chat).on('click', '[data-action].mc-rich-btn', function (e) {
            MCRichMessages.calendly.load($(this).attr('data-extra'), $(this).html(), $(this).closest('[data-id]').attr('data-id'));
            e.preventDefault();
            return false;
        });

        $(chat_overlay_panel).on('click', '> div:first-child i', function () {
            main.find('.mc-overlay-panel').mcActive(false);
            if (chat_overlay_panel.attr('data-close-chat')) {
                MCChat.closeChat();
            }
        });

        // Phone 
        $(main).on('change input', '#phone > input', function () {
            let value = $(this).val().trim();
            if (!/^[0-9+]+$/.test(value)) {
                value = value.replace(/[^0-9+]/g, '');
                $(this).val(value);
            }
            if (value.length > 1 && value.indexOf('+') === 0) {
                let prefix = false;
                if (value.substring(0, 2) == '+1') {
                    prefix = value.substring(0, 2);
                }
                if (value.length > 3) {
                    prefix = value.substring(0, 3);
                }
                if (prefix) {
                    $(this).parent().find(`[data-value="${prefix}"]`).click();
                    $(this).parent().find(`.mc-select`).click();
                    $(this).val(value.replace(prefix, ''));
                }
            }
        });

        /*
        * ----------------------------------------------------------
        * COMPONENTS
        * ----------------------------------------------------------
        */

        // Search
        $(global).on('click', '.mc-search-btn > i', function () {
            let parent = $(this).parent();
            let active = $(parent).mcActive();
            if (active) {
                setTimeout(() => { $(parent).find('input').val('') }, 50);
                setTimeout(() => { $(parent).find('input').trigger('change') }, 550);
            };
            $(parent).mcActive(!active);
            $(parent).find('input').get(0).focus();
            global.find('.mc-select ul').mcActive(false);
        });

        // Select
        $(global).on('click', '.mc-select', function (e) {
            if (!e.target || !$(e.target).is('input')) {
                let ul = $(this).find('ul');
                let active = ul.hasClass('mc-active');
                $(global).find('.mc-select ul').mcActive(false);
                ul.setClass('mc-active', !active);
                $(this).find('.mc-select-search').setClass('mc-active', !active);
                if (admin) {
                    MCAdmin.open_popup = active ? false : this;
                }
            }
        });

        $(global).on('click', '.mc-select li', function () {
            let select = $(this).closest('.mc-select');
            let value = $(this).data('value');
            let item = $(select).find(`[data-value="${value}"]`);
            select.find('li').mcActive(false);
            select.find('p').attr('data-value', value).html($(item).html());
            item.mcActive(true);
        });

        $(global).on('input', '.mc-select-search input', function (e) {
            let search = $(this).val();
            MCF.search(search, () => {
                let list = $(this).parent().parent().find('li');
                list.setClass('mc-hide', search.length);
                if (search.length) {
                    search = search.toLowerCase();
                    list.each(function () {
                        if ($(this).attr('data-value').includes(search) || $(this).attr('data-country').includes(search)) {
                            $(this).removeClass('mc-hide');
                        }
                    });
                }
            });
        });

        // Image uploader
        $(global).on('click', '.mc-input-image .image', function () {
            upload_target = $(this).parent();
            chat_editor.find('.mc-upload-files').click();
        });

        $(global).on('click', '.mc-input-image .image > .mc-icon-close', function (e) {
            MCF.ajax({ function: 'delete-file', path: $(this).parent().attr('data-value') });
            $(this).parent().removeAttr('data-value').css('background-image', '');
            e.preventDefault();
            return false;
        });
    }

}(jQuery));