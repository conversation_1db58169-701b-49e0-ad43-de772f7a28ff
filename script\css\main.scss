
/*
* 
* ==========================================================
* MAIN.SCSS
* ==========================================================
*
* Main style file of the plugin written in SCSS.
*
*/

@import "shared.scss";

/*
     
# GLOBAL
==========================================================

*/

@keyframes mc-chat-open-animation {
    0% {
        opacity: 0;
        bottom: -55px;
    }

    100% {
        opacity: 1;
        bottom: 0;
    }
}

@keyframes mc-typing {
    0% {
        width: 0;
    }

    100% {
        width: 15px;
    }
}

@keyframes mc-popup-animation {
    0% {
        opacity: 0;
        transform: translateY(100px) translateX(100px) scale(0);
    }

    100% {
        opacity: 1;
        transform: translateY(0) translateX(0) scale(1);
    }
}

@keyframes mc-header-agent-animation-1 {
    0% {
        left: -60px;
        opacity: 0;
    }

    50% {
        left: 20px;
        opacity: 1;
    }

    100% {
        left: -60px;
        opacity: 0;
    }
}

@keyframes mc-header-agent-animation-2 {
    0% {
        margin-left: 0;
    }

    50% {
        margin-left: 60px;
    }

    100% {
        margin-left: 0;
    }
}

.mc-shopify {
    [class*="mc-icon-"], .mc-bar-icons > div, .mc-dashboard-btn:empty {
        display: block;
    }

    .mc-editor:not(.mc-active) .mc-bar .mc-submit {
        display: none;
    }
}

.mc-progress {
    width: 100%;
    height: 2px;
    margin-top: 2px;
    margin-bottom: 7px;
    border-radius: 12px;
    background: rgba(144, 154, 165, 0.35);

    > div {
        height: 2px;
        background: rgb(144, 154, 165);
    }
}

div ul.mc-menu li:hover, .mc-select ul li:hover {
    color: $white !important;
}

.mc-main {
    ::-webkit-input-placeholder, .mc-articles-page ::-webkit-input-placeholder {
        color: #a5aeb6;
    }

    ::-moz-placeholder, .mc-articles-page ::-moz-placeholder {
        color: #a5aeb6;
    }

    :-ms-input-placeholder, .mc-articles-page :-ms-input-placeholder {
        color: #a5aeb6;
    }

    :-moz-placeholder, .mc-articles-page :-moz-placeholder {
        color: #a5aeb6;
    }

    li {
        margin: 0;
    }

    [data-color=""] {
        background-color: #f2f5f5;
    }

    [data-color="red"] {
        background-color: #eeccca;
    }

    [data-color="yellow"] {
        background-color: #ffe8b5;
    }

    [data-color="green"] {
        background-color: #c1e6c8;
    }

    [data-color="pink"] {
        background-color: #d1d3ec;
    }

    [data-color="gray"] {
        background-color: #dddddd;
    }

    [data-color="blue"] {
        background-color: #c2dcf0;
    }
}

.mc-ul > span {
    position: relative;
    display: block;
    padding-left: 20px;

    &:before {
        content: "";
        position: absolute;
        left: 0;
        top: 50%;
        width: 7px;
        height: 2px;
        margin-top: -1px;
        border-radius: 1px;
        background: $color-blue;
    }
}

/*

# CHAT
==========================================================

*/

.mc-chat {
    position: fixed;
    bottom: 100px;
    right: 30px;
    z-index: 99995;
    color: $color-black;
    direction: ltr;

    .mc-scroll-area {
        min-height: 450px;
        background-size: cover;
        margin: 0;

        > .mc-list {
            float: left;
            clear: both;
            width: 100%;
            min-height: 200px;
            box-sizing: border-box;

            .mc-notify-message {
                position: absolute;
                top: 95px;
                z-index: 9;
                max-height: 91px;
                overflow: hidden;
                animation: none;
                background-color: $white;
                width: 100%;
                max-width: calc(90% - 30px);
                border-radius: 6px;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.2);
                background: #f5f7fa;
            }
        }
    }

    b, strong {
        font-weight: 500;
    }

    ul {
        padding: 0;
        margin: 0;
        list-style: none;

        li {
            list-style: none;
        }
    }

    .mc-close-chat {
        position: absolute;
        right: 20px;
        top: 20px;
        font-size: 12px;
        line-height: 25px;
        color: #FFF;
        cursor: pointer;
        z-index: 999995;
    }

    .mc-responsive-close-btn {
        display: none;
    }

    .mc-scroll-area .mc-header {
        color: $white;
        box-shadow: 0 2px 1px rgba(0, 0, 0, 0.15);
        font-size: 14px;
        line-height: 26px;
        font-weight: 500;
        text-align: center;
        background-color: $color-blue;
        background-size: cover;

        .mc-title {
            font-weight: 500;
            font-size: 21px;
            line-height: 25px;
            letter-spacing: 0.3px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .mc-text {
            font-weight: 400;
            font-size: 13px;
            letter-spacing: 0.5px;
            line-height: 25px;
            height: 50px;
            overflow: hidden;
            color: rgba(255, 255, 255, 0.8);
        }

        .mc-profiles {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            transition: $transition;

            > div {
                margin: 0 10px;
                position: relative;

                > img {
                    width: 45px;
                    height: 45px;
                    border-radius: 50%;
                    margin: 0 !important;
                    display: block;
                    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.35);
                }

                > span {
                    display: block;
                    text-align: center;
                    overflow: hidden;
                    left: -35px;
                    right: -35px;
                    height: 28px;
                    top: -25px;
                    font-size: 13px;
                    letter-spacing: 0.3px;
                    padding: 0 15px;
                    line-height: 29px;
                    background: rgb(0, 8, 13) !important;
                    border-radius: 20px;
                    box-shadow: 0 2px 7px 0px rgba(0, 0, 0, 0.2);
                    opacity: 0;
                    position: absolute;
                }

                &:hover > span {
                    top: -40px;
                    opacity: 1;
                    transition: all 0.4s;
                }
            }
        }

        .mc-brand {
            display: flex;
            justify-content: center;
            padding-top: 25px;
            height: 50px;
            transition: $transition;

            img {
                max-height: 50px;
            }
        }

        .mc-dashboard-btn {
            position: absolute;
            width: 40px;
            height: 40px;
            line-height: 45px;
            text-align: center;
            background: rgba(0, 0, 0, 0.15);
            border-radius: 4px;
            cursor: pointer;
            left: -60px;
            opacity: .5;
            transition: all 0.5s;

            &:hover {
                background-color: rgba(0, 0, 0, 0.25);
            }

            &:before {
                line-height: 40px;
            }
        }

        &:hover .mc-dashboard-btn {
            left: 20px;
            opacity: 1;
        }

        &.mc-header-panel {
            padding: 5px;
            font-size: 17px;
            line-height: 46px;
            letter-spacing: .1px;
            position: absolute;
            left: 0;
            right: 0;
            top: 0 !important;
            opacity: 1 !important;
            white-space: nowrap;
            overflow: hidden;
            text-align: left;
            padding: 5px 60px 5px 30px;
            z-index: 9;

            > span:first-child {
                display: block;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .mc-dashboard-btn {
                opacity: 1;
                display: block;
                left: auto;
                right: 10px;
                font-size: 12px;
                background: none;
                top: 8px;

                &:hover {
                    background: rgba(0, 0, 0, 0.25);
                }

                &:before {
                    line-height: 40px;
                }
            }

            + div + .mc-editor {
                display: none;
            }
        }
    }

    .mc-header-main {
        padding: 0 20px 25px 20px;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        z-index: 9;

        > div {
            position: relative;
            bottom: auto;
        }

        > .mc-dashboard-btn {
            top: 10px;
            right: 14px;
            left: auto !important;
            background: none !important;
            font-size: 10px;
            width: 20px !important;
            height: 23px !important;
            line-height: 23px !important;
            text-align: right !important;
            display: block;
            z-index: 9;

            &:hover {
                opacity: 1;
            }

            &:before {
                line-height: 23px !important;
            }
        }

        .mc-title {
            padding: 30px 0 15px 0;
        }

        .mc-brand + .mc-title {
            padding-top: 22px;
        }

        & + .mc-list {
            margin-top: 218px;
        }

        &:hover > .mc-icon-close:not(:hover) {
            opacity: .5;
        }

        .mc-label-date-top {
            display: none;
        }
    }

    .mc-header-hide {
        display: none;

        & + .mc-scroll-area {
            border-top: 2px solid $color-blue;
        }
    }

    .mc-scroll-area.mc-texture-1 {
        background-image: url(../media/textures/texture-1.png);
    }

    .mc-texture-1 .mc-header {
        background-image: url(../media/textures/texture-1-2.png);
    }

    .mc-scroll-area.mc-texture-2 {
        background-image: url(../media/textures/texture-2.png);
    }

    .mc-texture-2 .mc-header {
        background-image: url(../media/textures/texture-2-2.png);
    }

    .mc-scroll-area.mc-texture-3 {
        background-image: url(../media/textures/texture-3.png);
    }

    .mc-texture-3 .mc-header {
        background-image: url(../media/textures/texture-3-2.png);
    }

    .mc-scroll-area.mc-texture-4 {
        background-image: url(../media/textures/texture-4.png);
    }

    .mc-texture-4 .mc-header {
        background-image: url(../media/textures/texture-4-2.png);
    }

    .mc-scroll-area.mc-texture-5 {
        background-image: url(../media/textures/texture-5.png);
    }

    .mc-texture-5 .mc-header {
        background-image: url(../media/textures/texture-5-2.png);
    }

    .mc-scroll-area.mc-texture-6 {
        background-image: url(../media/textures/texture-6.png);
    }

    .mc-texture-6 .mc-header {
        background-image: url(../media/textures/texture-6-2.png);
    }

    .mc-scroll-area.mc-texture-7 {
        background-image: url(../media/textures/texture-7.png);
    }

    .mc-texture-7 .mc-header {
        background-image: url(../media/textures/texture-7-2.png);
    }

    .mc-scroll-area.mc-texture-8 {
        background-image: url(../media/textures/texture-8.png);
    }

    .mc-texture-8 .mc-header {
        background-image: url(../media/textures/texture-8-2.png);
    }

    .mc-scroll-area.mc-texture-9 {
        background-image: url(../media/textures/texture-9.png);
    }

    .mc-texture-9 .mc-header {
        background-image: url(../media/textures/texture-9-2.png);
    }

    &.mc-no-conversations .mc-header-main:hover {

        .mc-profiles {
            opacity: 1;

            span {
                display: block;
            }
        }

        .mc-brand {
            opacity: 1;
        }
    }

    &.mc-no-conversations .mc-dashboard-conversations .mc-title {
        display: none;
    }

    &.mc-dashboard-disabled .mc-header-agent {
        .mc-dashboard-btn {
            display: none;
        }

        .mc-profile {
            animation: none;
            margin-left: 0 !important;
        }

        &:hover .mc-profile {
            margin-left: 0 !important;
        }
    }

    .mc-conversations-hidden {
        .mc-btn-all-conversations {
            display: inline-block !important;
        }

        > ul > li:nth-of-type(1n+4) {
            display: none;
        }
    }

    .mc-status-typing {


        &:before {
            display: none;
        }

        &:after {
            content: "...";
            position: absolute;
            width: 15px;
            left: calc(100% + 5px);
            bottom: 0;
            font-weight: 500;
            letter-spacing: 1px;
            overflow: hidden;
            white-space: nowrap;
            animation: mc-typing 1s infinite;
        }
    }

    .mc-header-agent {
        padding: 20px;
        position: absolute;
        left: 0;
        right: 0;
        top: 0 !important;
        opacity: 1 !important;
        z-index: 11;

        & + .mc-list {
            padding-top: 90px;
            margin-top: 0;
        }

        &:hover {
            .mc-profile {
                margin-left: 60px !important;
            }

            .mc-dashboard-btn {
                left: 20px !important;
                opacity: 1 !important;
            }
        }

        &.mc-header-animation {
            .mc-dashboard-btn {
                animation: mc-header-agent-animation-1 2.5s;
                animation-delay: 1.5s;
            }

            .mc-profile {
                animation: mc-header-agent-animation-2 2.5s;
                animation-delay: 1.5s;
            }
        }

        .mc-profile {
            display: flex;
            align-items: center;
            text-align: left;
            transition: $transition;

            img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                margin-right: 15px;
                display: block;
            }

            > div {
                height: 40px;

                span {
                    display: block;
                    line-height: 22px;
                }
            }

            .mc-name {
                letter-spacing: 0.5px;
                font-size: 14px;
                white-space: nowrap;
            }

            .mc-status {
                position: relative;
                font-weight: 400;
                font-size: 13px;
                padding-left: 15px;
                color: rgba(255, 255, 255, 0.7);
                letter-spacing: 0.3px;

                &:before {
                    content: "";
                    width: 8px;
                    height: 8px;
                    position: absolute;
                    border-radius: 50%;
                    margin-top: -4px;
                    top: 50%;
                    left: 0;
                    background: $border-color;
                }

                &.mc-status-online:before {
                    background: rgb(19, 202, 126);
                }
            }

            .mc-status-typing {
                padding-left: 0;
                float: left;
            }
        }
    }

    &.mc-dashboard-active {
        .mc-scroll-area {
            -ms-overflow-style: none;
        }

        .mc-dashboard-btn {
            display: none;
        }

        .mc-profiles,
        .mc-brand {
            opacity: 1 !important;

            span {
                display: block !important;
            }
        }

        &:not(.mc-panel-active) .mc-scroll-area {
            background: rgb(243, 243, 243);
        }

        .mc-editor {
            display: none;
        }

        .mc-header-main {
            padding: 0 20px 65px 20px;
            z-index: 0;
        }

        .mc-header-type-minimal {
            padding: 0 20px 31px 20px;
        }
    }

    &:not(.mc-dashboard-active) .mc-header-main > div {
        opacity: 1 !important;
        top: 0 !important;
    }

    &.mc-panel-active {
        .mc-editor {
            display: none;
        }
    }

    > .mc-body {
        display: none;
        position: absolute;
        width: 385px;
        max-height: 650px;
        background: $white;
        right: 0;
        bottom: 0;
        border-radius: 6px;
        box-shadow: 0 2px 15px rgba(0, 0, 0, 0.06), 0 3px 32px rgba(0, 0, 0, 0.16);
        z-index: 99995;
        text-align: left;
        flex-direction: column;
        overflow: hidden;
        transition: height .4s;
    }

    &.mc-active > .mc-body {
        display: flex;
        animation: mc-chat-open-animation 0.4s;
    }

    &.mc-notify-active {
        .mc-header-agent {
            & + .mc-list {
                padding-top: 170px !important;
            }
        }

        &:not(.mc-dashboard-active) {
            .mc-header-main {
                .mc-profiles, .mc-brand + div + .mc-text {
                    visibility: hidden;
                }

                & + .mc-list {

                    .mc-notify-message {
                        top: 130px;
                    }
                }
            }

            .mc-header-type-minimal + .mc-list {
                padding-top: 30px;
            }
        }
    }

    &.mc-queue-active {
        .mc-header-agent:hover .mc-profile {
            margin-left: 0 !important;
        }

        .mc-dashboard-btn {
            display: none;
        }

        .mc-dashboard-btn, .mc-profile {
            animation: none !important;
        }
    }

    .mc-editor {
        padding: 0;

        .mc-textarea {
            padding: 15px 120px 15px 15px;
            border-top: 1px solid rgb(222, 229, 236);
        }

        .mc-attachments {
            padding: 0 15px 10px 15px;

            &:empty {
                padding-bottom: 0;
            }
        }

        .mc-bar {
            padding: 15px 15px 15px 0;
        }

        .mc-btn {
            height: 22px;
            line-height: 24px;
            font-size: 11px;
            background: none;
            border: 1px solid $color-gray;
            color: $color-gray;
            transition: $transition;
            opacity: 0.5;

            &:hover {
                opacity: 1;
            }
        }

        .mc-bar-icons > div:not(:hover) {
            opacity: 0.5;
        }

        .mc-btn-saved-replies,
        .mc-btn-rich-messages {
            display: none;
        }

        .mc-loader {
            right: 15px;
            background: rgb(251, 251, 251);
        }

        &.mc-active {
            .mc-bar + .mc-popup.mc-emoji:after {
                right: 40px;
            }

            .mc-textarea {
                padding-right: 85px;
            }

            &.mc-disabled-2 .mc-textarea {
                padding-right: 80px;
            }

            .mc-bar-icons > div {
                margin-right: 0;
            }
        }

        &.mc-drag {
            left: 2px;
            bottom: 2px;
            border-radius: 4px;
            width: calc(100% - 4px);
        }

        &.mc-disabled-1 .mc-textarea {
            padding-right: 80px;
        }

        &.mc-disabled-2 .mc-textarea {
            padding-right: 50px;
        }
    }

    .mc-popup.mc-emoji {
        bottom: 160px;
        transform: none;

        &:after {
            left: auto;
            right: 8px;
        }
    }

    .mc-list,
    .mc-dashboard,
    .mc-panel {
        display: none;

        &.mc-active {
            display: block;
        }
    }

    .mc-dashboard > div,
    .mc-init-form {
        text-align: center;
        color: $color-gray;
        background: $white;
        margin: 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.2);
        border-radius: 4px;

        > .mc-title,
        > .mc-top,
        > div > .mc-title,
        > div > .mc-top {
            font-weight: 500;
            font-size: 15px;
            letter-spacing: .3px;
            line-height: 20px;
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid rgb(213, 213, 213);
        }
    }

    .mc-dashboard {
        position: relative;
        z-index: 2;
        margin-top: 210px;
        min-height: 350px;
        background: none;

        &.mc-active {
            animation: mc-fade-animation 0.5s;
        }

        > div {
            .mc-user-conversations {
                padding: 10px 0;

                li {
                    border: none;
                    padding: 10px 25px;
                    margin: 0;
                    text-align: left;
                    list-style: none;

                    .mc-time {
                        font-size: 12px;
                        opacity: 0.8;
                    }
                }

                > p {
                    padding: 25px;
                }

                &:empty {
                    padding: 0;
                }
            }

            > .mc-btn {
                width: auto;
                margin: 13px auto;
                font-weight: 500;
                color: rgb(74, 74, 74);
                border-radius: 4px;
                padding: 0 15px;
                background-color: transparent;
                font-size: 13px;

                &:hover {
                    background-color: $color-blue;
                    border-color: $color-blue;
                    color: $white;
                }
            }

            .mc-one-conversation:not(:empty) + .mc-btn-new-conversation, .mc-btn-all-conversations {
                display: none;
            }
        }

        .mc-input-btn {
            margin: 25px;

            input {
                line-height: 40px;
                min-height: 40px;
            }
        }

        .mc-articles {
            margin: 25px;
        }
    }

    .mc-header-type-minimal:not(.mc-header-agent) {
        & + .mc-list {
            margin-top: 160px;
        }

        & + div + .mc-dashboard {
            margin-top: 170px;
        }

        & + div + div + div + .mc-init-form {
            margin-top: 150px;
        }
    }

    .mc-panel {
        padding: 90px 30px 30px;
        min-height: calc(100% - 120px);

        &.mc-loading:before {
            top: 100px;
        }
    }

    &.mc-init-form-active {
        .mc-editor, .mc-scroll-area .mc-list, .mc-scroll-area .mc-dashboard {
            display: none;
        }

        .mc-header-main {
            padding: 0 20px 65px 20px;
        }

        .mc-scroll-area {
            background: rgb(243, 243, 243);
        }
    }

    .mc-init-form {
        margin-top: 210px;
        position: relative;
        z-index: 9;
        text-align: left;

        [id="otp"]:not(.mc-active) {
            display: none;
        }

        .mc-title {
            text-align: center;
        }

        .mc-text {
            padding: 0 25px;
            font-size: 13px;
            letter-spacing: 0.3px;
            line-height: 25px;
            margin: 15px 0 20px 0;
            color: $color-gray;
        }

        .mc-btn {
            margin: 25px;
        }

        .mc-input-btn {
            input, .mc-btn {
                margin: 0;
                width: auto;
                flex-grow: 1;
            }

            .mc-btn {
                white-space: nowrap;
                background: $white;
                color: $color-black;
                border: 1px solid $border-color;
                margin-left: -1px;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
                height: 40px;
                line-height: 40px;
                z-index: 8;
                position: relative;
                transition: color .3s, border-color .3s;

                &:hover {
                    color: $color-blue;
                    border-color: $color-blue;
                }
            }

            input {
                z-index: 2;
                min-width: 50%;
                border-top-right-radius: 0 !important;
                border-bottom-right-radius: 0 !important;
                position: relative;

                &:focus + .mc-btn {
                    z-index: 0;
                }
            }
        }

        .mc-top + .mc-form {
            padding-top: 10px;
        }

        .mc-form {
            padding: 0 25px;

            & + .mc-btn {
                margin-top: 25px;
            }

            & + .mc-form {
                margin-top: 10px;
            }

            &:empty {
                display: none;
            }
        }

        .mc-input > span.mc-active {
            background: $white !important;
        }

        .mc-info {
            margin: -10px 25px 25px 25px;
            padding-bottom: 15px;
            text-align: center;
        }

        .mc-link-area {
            padding: 25px 25px 0 25px;
        }

        .mc-buttons {
            padding: 25px;
            margin: 0;
            text-align: center;

            > div {
                margin: 0;
                border-radius: 30px;

                & + div {
                    margin-top: 15px;
                }
            }
        }

        > div > .mc-loading {
            left: 50%;
            margin: 15px 15px 15px -15px;
        }
    }

    .mc-privacy {
        > a {
            padding: 0 25px 25px 25px;
            display: block;
            text-decoration: none;
            color: $color-gray;
            opacity: 0.7;
            font-size: 13px;

            &:hover {
                opacity: 1;
            }
        }

        .mc-buttons {
            padding: 0 25px 25px 25px;
            justify-content: flex-start;
            margin: 0 -10px;
        }

        .mc-buttons a {
            margin: 0 10px;
        }

        .mc-title, .mc-buttons {
            text-align: left;
        }
    }

    .mc-popup-message {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 250px;
        box-shadow: 18px 16px 15px rgba(0, 0, 0, 0.06), 0 3px 15px rgba(0, 0, 0, 0.16);
        border-radius: 4px;
        letter-spacing: 0.3px;
        padding: 15px 20px 0 20px;
        background-color: $white;
        animation: mc-popup-animation 0.5s;

        &:before {
            content: "";
            position: absolute;
            bottom: -23px;
            right: 75px;
            width: 16px;
            height: 30px;
            background: rgba(0, 0, 0, 0.1);
            filter: blur(4px);
            transform: rotate(-35deg);
            z-index: -1;
        }

        &:after {
            content: "";
            position: absolute;
            bottom: -20px;
            right: 75px;
            display: block;
            border-width: 30px;
            border-style: solid;
            border-top-width: 2px;
            border-color: rgba(0, 0, 0, 0);
            border-right-color: rgb(255, 255, 255);
        }

        .mc-top + .mc-text {
            margin-top: 5px;
        }

        .mc-top {
            font-weight: 500;
            font-size: 15px;
            line-height: 25px;
        }

        .mc-text {
            font-size: 13px;
            line-height: 25px;
            padding-bottom: 15px;
            color: $color-gray;
            opacity: 0.9;
            background: $white;
        }

        .mc-icon-close {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 9px;
            opacity: 0.5;
            cursor: pointer;

            &:hover {
                opacity: 1;
            }
        }

        > img {
            width: 50px;
            height: 50px;
            left: -70px;
            top: 10px;
            border-radius: 50%;
            position: absolute;
        }
    }

    &.mc-chat-left {
        left: 30px;
        right: auto;

        .mc-chat-btn {
            left: 20px;
            right: auto;
        }

        > .mc-body {
            right: auto;
            left: 0;
        }

        .mc-popup-message {
            right: auto;
            left: 0;

            &:after {
                right: auto;
                left: 75px;
                transform: scaleX(-1);
            }

            &:before {
                right: auto;
                left: 75px;
                transform: rotate(35deg);
            }

            > img {
                right: -70px;
                left: auto;
            }
        }
    }

    .mc-departments-list, .mc-agents-list, .mc-channels-list {
        padding: 10px 0;

        > div {
            display: flex;
            align-items: center;
            position: relative;
            padding: 10px 25px;
            margin: 0;
            text-align: left;
            cursor: pointer;
            transition: all 0.4s;

            img, div {
                width: 30px;
                height: 30px;
            }

            div {
                border-radius: 50%;
            }

            span {
                color: rgb(74, 74, 74);
                padding: 0 0 0 15px;
                font-size: 13px;
                font-weight: 500;
                display: block;
            }

            &:hover {
                background-color: $background-gray;
            }
        }
    }

    .mc-agents-list {
        .mc-no-results {
            padding: 10px 0;
            display: block;
            font-size: 13px;
        }

        img {
            border-radius: 50%;
        }
    }

    .mc-label-date-top {
        top: 83px;
        height: 0;
    }
}

.mc-input > span:not(.mc-filled):not(.mc-active) + input[type=date]:not(:focus)::-webkit-datetime-edit {
    color: transparent;
}

.mc-chat-btn {
    position: fixed;
    visibility: visible;
    bottom: 20px;
    right: 20px;
    left: auto;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06), 0 3px 32px rgba(0, 0, 0, 0.16);
    transition: $transition;
    background-color: $color-blue;
    z-index: 999995;

    &:hover {
        background-color: $color-dark-blue;
    }

    img {
        max-width: 100%;
        width: 60px;
        height: 60px;
        position: absolute;
        left: 0;
        top: 0;
        margin: 0;
        min-height: 0 !important;
        min-width: 0 !important;
        z-index: 0;
        transition: $transition;
    }

    .mc-close {
        opacity: 0;
        transform: rotate(90deg);
    }
}

.mc-active .mc-chat-btn .mc-icon {
    opacity: 0;
}

.mc-active .mc-chat-btn .mc-close {
    transform: rotate(0deg);
    opacity: 1;
}

.mc-cloud {
    > .mc-body {
        padding-bottom: 28px;
    }

    &:not(.mc-cloud-white-label) .mc-overlay-panel {
        margin-bottom: 30px;
    }
}

.mc-cloud-brand {
    display: block;
    text-align: center;
    position: absolute;
    bottom: 0;
    height: 15px;
    background: #f3f3f3;
    left: 0;
    right: 0;
    padding: 7px 15px;
    z-index: 995;
}

.mc-cloud-brand img {
    height: 16px;
    margin: 0 auto;
    vertical-align: top;
    transition: $transition;
}

.mc-cloud-brand:hover img {
    opacity: .7;
}

.mc-cloud-white-label {
    .mc-cloud-brand {
        display: none !important;
    }

    > .mc-body {
        padding-bottom: 0 !important;
    }
}

/*

# RESPONSIVE
==========================================================

*/

@media (max-height: 678px) {
    .mc-chat .mc-body .mc-scroll-area,
    .mc-chat .mc-body .mc-dashboard {
        min-height: 200px;
    }

    .mc-chat .mc-scroll-area > .mc-list {
        min-height: 100%;
    }

    .mc-chat .mc-scroll-area .mc-header-main + .mc-list {
        min-height: 50px;
    }
}

@import "rtl.scss";
@import "responsive.scss";
