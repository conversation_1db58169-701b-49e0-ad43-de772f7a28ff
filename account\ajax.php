<?php

/*
 *
 * ===================================================================
 * CLOUD AJAX PHP FILE
 * ===================================================================
 *
 * � 2017-2025 masichat.com. All rights reserved.
 *
 */

// Prevent any output before JSON response
ob_start();
error_reporting(0); // Suppress all errors for clean JSON
ini_set('display_errors', 0);
ini_set('log_errors', 1);

require('functions.php');

if (isset($_POST['function'])) {
    if (in_array($_POST['function'], ['super-update-saas', 'super-get-affiliate-details', 'super-reset-affiliate', 'super-get-affiliates', 'super-membership-plans', 'super-get-customers', 'super-get-customer', 'super-delete-customer', 'super-save-customer', 'super-get-emails', 'super-save-emails', 'super-get-settings', 'super-save-settings', 'super-save-membership-plans', 'super-save-white-label']) && !super_admin()) {
        die();
    }
    switch ($_POST['function']) {
        case 'registration':
            ajax_response(account_registration($_POST['details']));
            break;
        case 'login':
            ajax_response(account_login($_POST['email'], $_POST['password']));
            break;
        case 'account':
            ajax_response(account());
            break;
        case 'account-user-details':
            ajax_response(account_get_user_details());
            break;
        case 'account-save':
            ajax_response(account_save($_POST['details']));
            break;
        case 'account-reset-password':
            ajax_response(account_reset_password(mc_isset($_POST, 'email'), mc_isset($_POST, 'token'), mc_isset($_POST, 'password')));
            break;
        case 'account-welcome':
            ajax_response(account_welcome());
            break;
        case 'account-delete':
            ajax_response(account_delete());
            break;
        case 'account-delete-agents-quota':
            ajax_response(account_delete_agents_quota());
            break;
        case 'verify':
            ajax_response(verify(mc_isset($_POST, 'email'), mc_isset($_POST, 'phone'), mc_isset($_POST, 'code_pairs')));
            break;
        case 'get-payments':
            ajax_response(membership_get_payments());
            break;
        case 'get-invoice':
            ajax_response(membership_get_invoice($_POST['payment_id']));
            break;
        case 'delete-invoice':
            ajax_response(unlink(MC_CLOUD_PATH . '\script\uploads\invoices\/' . $_POST['file_name']));
            break;
        case 'membership':
            ajax_response(membership_get_active());
            break;
        case 'super-login':
            ajax_response(super_login($_POST['email'], $_POST['password']));
            break;
        case 'purchase-white-label':
            ajax_response(membership_purchase_white_label(mc_isset($_POST, 'external_integration')));
            break;
        case 'purchase-credits':
            ajax_response(membership_purchase_credits($_POST['amount'], mc_isset($_POST, 'external_integration')));
            break;
        case 'set-auto-recharge-credits':
            ajax_response(membership_set_auto_recharge(mc_isset($_POST, 'enabled')));
            break;
        case 'super-get-customers':
            ajax_response(super_get_customers(mc_isset($_POST, 'membership')));
            break;
        case 'super-get-customer':
            ajax_response(super_get_customer($_POST['customer_id']));
            break;
        case 'super-delete-customer':
            ajax_response(super_delete_customer($_POST['customer_id']));
            break;
        case 'super-save-customer':
            ajax_response(super_save_customer($_POST['customer_id'], $_POST['details'], mc_isset($_POST, 'extra_details')));
            break;
        case 'super-get-emails':
            ajax_response(super_get_emails());
            break;
        case 'super-save-emails':
            ajax_response(super_save_emails($_POST['settings']));
            break;
        case 'super-get-settings':
            ajax_response(super_get_settings());
            break;
        case 'super-save-settings':
            ajax_response(super_save_settings($_POST['settings']));
            break;
        case 'super-membership-plans':
            ajax_response(super_membership_plans());
            break;
        case 'super-save-membership-plans':
            ajax_response(super_save_membership_plans($_POST['plans']));
            break;
        case 'super-get-affiliates':
            ajax_response(super_get_affiliates());
            break;
        case 'super-reset-affiliate':
            ajax_response(super_reset_affiliate($_POST['affiliate_id']));
            break;
        case 'super-get-affiliate-details':
            ajax_response(super_get_affiliate_details($_POST['affiliate_id']));
            break;
        case 'super-save-white-label':
            ajax_response(super_save_white_label($_POST['price']));
            break;
        case 'stripe-create-session':
            ajax_response(stripe_create_session($_POST['price_id'], $_POST['cloud_user_id']));
            break;
        case 'stripe-cancel-subscription':
            ajax_response(stripe_cancel_subscription());
            break;
        case 'rapyd-checkout':
            ajax_response(rapyd_create_checkout($_POST['price_id'], $_POST['cloud_user_id']));
            break;
        case 'verifone-checkout':
            ajax_response(verifone_create_checkout($_POST['price_id'], $_POST['cloud_user_id']));
            break;
        case 'verifone-cancel-subscription':
            ajax_response(verifone_cancel_subscription());
            break;
        case 'whatsapp-sync':
            ajax_response(cloud_meta_whatsapp_sync($_POST['code']));
            break;
        case 'messenger-sync':
            ajax_response(cloud_meta_messenger_sync($_POST['access_token']));
            break;
        case 'purchase-addon':
            ajax_response(cloud_addon_purchase($_POST['index']));
            break;
        case 'debug-test':
            ajax_response(['status' => 'success', 'message' => 'Debug test working', 'post_data' => $_POST]);
            break;
        case 'razorpay-create-subscription':
            ajax_response(razorpay_create_subscription($_POST['price_id'], $_POST['cloud_user_id']));
        case 'razorpay-cancel-subscription':
            ajax_response(razorpay_cancel_subscription());
        case 'shopify-subscription':
            ajax_response(shopify_subscription($_POST['price_id']));
        case 'shopify-cancel-subscription':
            ajax_response(shopify_cancel_subscription());
        case 'yoomoney-create-subscription':
            ajax_response(yoomoney_create_subscription($_POST['price_id']));
        case 'yoomoney-cancel-subscription':
            ajax_response(yoomoney_cancel_subscription());
        case 'save-referral-payment-information':
            ajax_response(account_save_referral_payment_information($_POST['method'], $_POST['details']));
        case 'get-referral-payment-information':
            ajax_response(super_get_user_data('referral_payment_info', get_active_account_id()));
        case 'super-update-saas':
            ajax_response(super_update_saas());
    }
}

function ajax_response($response) {
    // Clean any output buffer to ensure clean JSON
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Start fresh output buffer
    ob_start();

    header('Content-Type: application/json');
    header('Cache-Control: no-cache, must-revalidate');

    $json_response = $response === true ? '1' : (is_numeric($response) ? $response : json_encode($response, JSON_INVALID_UTF8_IGNORE));

    // Log the response for debugging
    error_log('AJAX Response: ' . $json_response);

    echo $json_response;
    exit;
}

?>