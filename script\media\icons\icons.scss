﻿@charset "UTF-8";

@font-face {
    font-family: "Masi Chat Icons";
    src: url("../media/icons/masi-chat.woff?v=4") format("woff"), url("../media/icons/masi-chat.ttf?v=4") format("truetype"), url("../media/icons/masi-chat.svg#masi-chat?v=4") format("svg");
    font-weight: normal;
    font-style: normal;
}

[class^="mc-icon-"]:before, [class*=" mc-icon-"]:before {
    font-family: "Masi Chat Icons" !important;
    font-style: normal !important;
    font-weight: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
    speak: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.mc-icon-arrow-up:before {
    content: "\61";
    transform: rotate(180deg);
    display: inline-block;
}

.mc-icon-arrow-down:before {
    content: "\61";
}

.mc-icon-shopify:before {
    content: "\e900";
}

.mc-icon-plus-2:before {
    content: "\4d";
}

.mc-icon-plus:before {
    content: "\30";
}

.mc-icon-back:before {
    content: "\41";
}

.mc-icon-file:before {
    content: "\42";
}

.mc-icon-check-circle:before {
    content: "\43";
}

.mc-icon-reload:before {
    content: "\44";
}

.mc-icon-help:before {
    content: "\45";
}

.mc-icon-clock:before {
    content: "\46";
}

.mc-icon-next:before {
    content: "\47";
}

.mc-icon-currency:before {
    content: "\48";
}

.mc-icon-language:before {
    content: "\49";
}

.mc-icon-dislike:before {
    content: "\4e";
}

.mc-icon-like:before {
    content: "\4f";
}

.mc-icon-send:before {
    content: "\50";
}

.mc-icon-refresh:before {
    content: "\51";
}

.mc-icon-woocommerce:before {
    content: "\52";
}

.mc-icon-social-fb:before {
    content: "\53";
}

.mc-icon-social-tw:before {
    content: "\54";
}

.mc-icon-social-li:before {
    content: "\55";
}

.mc-icon-social-pi:before {
    content: "\56";
}

.mc-icon-social-wa:before {
    content: "\57";
}

.mc-icon-social-m:before {
    content: "\58";
}

.mc-icon-bar-chart:before {
    content: "\59";
}

.mc-icon-calendar:before {
    content: "\62";
}

.mc-icon-chat:before {
    content: "\64";
}

.mc-icon-clip:before {
    content: "\65";
}

.mc-icon-download:before {
    content: "\66";
}

.mc-icon-envelope:before {
    content: "\67";
}

.mc-icon-marker:before {
    content: "\68";
}

.mc-icon-message:before {
    content: "\69";
}

.mc-icon-desktop:before {
    content: "\6a";
}

.mc-icon-plane:before {
    content: "\6b";
}

.mc-icon-phone:before {
    content: "\6c";
}

.mc-icon-settings:before {
    content: "\6d";
}

.mc-icon-user:before {
    content: "\6e";
}

.mc-icon-search:before {
    content: "\6f";
}

.mc-icon-close:before {
    content: "\70";
}

.mc-icon-message-add:before {
    content: "\71";
}

.mc-icon-emoji:before {
    content: "\72";
}

.mc-icon-menu:before {
    content: "\73";
}

.mc-icon-arrow-left:before {
    content: "\74";
}

.mc-icon-arrow-right:before {
    content: "\75";
}

.mc-icon-loader:before {
    content: "\76";
}

.mc-icon-check:before {
    content: "\77";
}

.mc-icon-delete:before {
    content: "\78";
}

.mc-icon-padlock:before {
    content: "\79";
}

.mc-icon-shuffle:before {
    content: "\7a";
}

.mc-icon-circle:before {
    content: "\e901";
}

.mc-icon-filter:before {
    content: "\e902";
}

.mc-icon-openai:before {
    content: "\e903";
}

.mc-icon-mic:before {
    content: "\e904";
}

.mc-icon-edit:before {
    content: "\e905";
}

.mc-icon-warning:before {
    content: "\e906";
}

.mc-icon-info:before {
    content: "\e907";
}

.mc-icon-files:before {
    content: "\e908";
}

.mc-icon-tag-line:before {
    content: "\e909";
}

.mc-icon-chatbot:before {
    content: "\e90a";
}

.mc-icon-automation:before {
    content: "\e915";
}

.mc-icon-sms:before {
    content: "\e91c";
}

.mc-icon-tag:before {
    content: "\f02b";
}

.mc-icon-play:before {
    content: "\f04b";
}

.mc-icon-pause:before {
    content: "\f04c";
}

.mc-icon-microphone:before {
    content: "\f130";
}
