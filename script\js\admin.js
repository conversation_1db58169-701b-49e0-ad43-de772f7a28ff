
/*
 * ==========================================================
 * ADMINISTRATION SCRIPT
 * ==========================================================
 *
 * Main Javascript admin file. © 2017-2025 masichat.com. All rights reserved.
 * 
 */

'use strict';

(function ($) {

    // Global
    var admin;
    var header;

    // Conversation  
    var conversations = [];
    var conversation_area;
    var conversations_area;
    var conversations_area_list;
    var conversations_user_details;
    var conversations_admin_list;
    var conversations_admin_list_ul;
    var conversations_filters;
    var saved_replies = false;
    var saved_replies_list = false;
    var woocommerce_products_box = false;
    var woocommerce_products_box_ul = false;
    var pagination = 1;
    var notes_panel;
    var tags_panel;
    var attachments_panel;
    var direct_message_box;
    var dialogflow_intent_box;
    var suggestions_area;
    var pagination_count = 1;
    var open_ai_button;

    // Users
    var users_area;
    var users_table;
    var users_table_menu;
    var users_filters;
    var users = {};
    var users_pagination = 1;
    var users_pagination_count = 1;
    var profile_box;
    var profile_edit_box;
    var away_mode = true;

    // Settings
    var settings_area;
    var automations_area;
    var automations_area_select;
    var automations_area_nav;
    var conditions_area;

    // Articles
    var articles_area;
    var articles_content;
    var articles_content_categories;
    var articles_category_select;
    var articles_category_parent_select;
    var articles_save_required = false;
    var articles_save_required_stop = true;

    // Chatbot
    var chatbot_area;
    var chatbot_files_table;
    var chatbot_website_table;
    var chatbot_qea_repeater;
    var chatbot_playground_editor;
    var chatbot_playground_area;
    var flows_nav;
    var flows_area;
    var flow_scroll_interval;
    var is_over_connector = false;
    var conversations_qea = [];
    var step_scroll_positions = [];

    // Miscellaneus
    var upload_input;
    var upload_target;
    var upload_function;
    var upload_on_success;
    var language_switcher_target;
    var timeout;
    var alertOnConfirmation;
    var alertOnCancel;
    var responsive = $(window).width() < 465;
    var scrolls = { last: 0, header: true, always_hidden: false };
    var localhost = location.hostname === 'localhost' || location.hostname === '127.0.0.1';
    var today = new Date();
    var is_busy = false;
    var agent_online = true;
    var active_interval = false;
    var pusher_timeout;
    var away_timeout;
    var temp;
    var overlay;
    var SITE_URL;
    var ND = 'undefined';
    var wp_admin = false;
    var touchmove;
    var touchmove_x;
    var touchmove_y;
    var editor_js = false;
    var editor_js_saving = false;
    var editor_js_loading = false;
    var interval = false;
    var active_keydown;
    var reports_area;
    var select_departments;
    var active_admin_area;

    /*
    * ----------------------------------------------------------
    * External plugins
    * ----------------------------------------------------------
    */

    // miniTip 1.5.3 | (c) 2011, James Simpson | Dual licensed under the MIT and GPL
    $.fn.miniTip = function (t) { var e = $.extend({ title: '', content: !1, delay: 300, anchor: 'n', event: 'hover', fadeIn: 200, fadeOut: 200, aHide: !0, maxW: '250px', offset: 5, stemOff: 0, doHide: !1 }, t); admin && 0 == admin.find('#miniTip').length && admin.append('<div id="miniTip" class="mc-tooltip"><div></div></div>'); var n = admin.find('#miniTip'), a = n.find('div'); return e.doHide ? (n.stop(!0, !0).fadeOut(e.fadeOut), !1) : this.each(function () { var t = $(this), o = e.content ? e.content : t.attr('title'); if ('' != o && void 0 !== o) { window.delay = !1; var i = !1, r = !0; e.content || t.removeAttr('title'), 'hover' == e.event ? (t.hover(function () { n.removeAttr('click'), r = !0, s.call(this) }, function () { r = !1, d() }), e.aHide || n.hover(function () { i = !0 }, function () { i = !1, setTimeout(function () { !r && !n.attr('click') && d() }, 20) })) : 'click' == e.event && (e.aHide = !0, t.click(function () { return n.attr('click', 't'), n.data('last_target') !== t ? s.call(this) : 'none' == n.css("display") ? s.call(this) : d(), n.data('last_target', t), $('html').unbind('click').click(function (t) { 'block' == n.css('display') && !$(t.target).closest('#miniTip').length && ($('html').unbind('click'), d()) }), !1 })); var s = function () { e.show && e.show.call(this, e), e.content && '' != e.content && (o = e.content), a.html(o), e.render && e.render(n), n.hide().width('').width(n.width()).css('max-width', e.maxW); var i = t.is('area'); if (i) { var r, s = [], d = [], c = t.attr('coords').split(','); function h(t, e) { return t - e } for (r = 0; r < c.length; r++)s.push(c[r++]), d.push(c[r]); var f = t.parent().attr('name'), l = $('img[usemap=\\#' + f + ']').offset(), p = parseInt(l.left, 10) + parseInt((parseInt(s.sort(h)[0], 10) + parseInt(s.sort(h)[s.length - 1], 10)) / 2, 10), u = parseInt(l.top, 10) + parseInt((parseInt(d.sort(h)[0], 10) + parseInt(d.sort(h)[d.length - 1], 10)) / 2, 10) } else u = parseInt(t.offset().top, 10), p = parseInt(t.offset().left, 10); var m = i ? 0 : parseInt(t.outerWidth(), 10), I = i ? 0 : parseInt(t.outerHeight(), 10), v = n.outerWidth(), w = n.outerHeight(), g = Math.round(p + Math.round((m - v) / 2)), T = Math.round(u + I + e.offset + 8), b = Math.round(v - 16) / 2 - parseInt(n.css('borderLeftWidth'), 10), y = 0, H = p + m + v + e.offset + 8 > parseInt($(window).width(), 10), W = v + e.offset + 8 > p, k = w + e.offset + 8 > u - $(window).scrollTop(), M = u + I + w + e.offset + 8 > parseInt($(window).height() + $(window).scrollTop(), 10), x = e.anchor; W || 'e' == e.anchor && !H ? 'w' != e.anchor && 'e' != e.anchor || (x = 'e', y = Math.round(w / 2 - 8 - parseInt(n.css('borderRightWidth'), 10)), b = -8 - parseInt(n.css('borderRightWidth'), 10), g = p + m + e.offset + 8, T = Math.round(u + I / 2 - w / 2)) : (H || 'w' == e.anchor && !W) && ('w' != e.anchor && 'e' != e.anchor || (x = 'w', y = Math.round(w / 2 - 8 - parseInt(n.css('borderLeftWidth'), 10)), b = v - parseInt(n.css('borderLeftWidth'), 10), g = p - v - e.offset - 8, T = Math.round(u + I / 2 - w / 2))), M || 'n' == e.anchor && !k ? 'n' != e.anchor && 's' != e.anchor || (x = 'n', y = w - parseInt(n.css('borderTopWidth'), 10), T = u - (w + e.offset + 8)) : (k || 's' == e.anchor && !M) && ('n' != e.anchor && 's' != e.anchor || (x = 's', y = -8 - parseInt(n.css('borderBottomWidth'), 10), T = u + I + e.offset + 8)), 'n' == e.anchor || 's' == e.anchor ? v / 2 > p ? (g = g < 0 ? b + g : b, b = 0) : p + v / 2 > parseInt($(window).width(), 10) && (g -= b, b *= 2) : k ? (T += y, y = 0) : M && (T -= y, y *= 2), delay && clearTimeout(delay), delay = setTimeout(function () { n.css({ 'margin-left': g + 'px', 'margin-top': T + 'px' }).stop(!0, !0).fadeIn(e.fadeIn) }, e.delay), n.attr('class', 'mc-tooltip ' + x) }, d = function () { (!e.aHide && !i || e.aHide) && (delay && clearTimeout(delay), delay = setTimeout(function () { c() }, e.delay)) }, c = function () { !e.aHide && !i || e.aHide ? (n.stop(!0, !0).fadeOut(e.fadeOut), e.hide && e.hide.call(this)) : setTimeout(function () { d() }, 200) } } }) };

    /*
    * ----------------------------------------------------------
    * Functions
    * ----------------------------------------------------------
    */

    $.fn.mcLanguageSwitcher = function (langs = [], source = '', active_language = false) {
        let code = `<div class="mc-language-switcher" data-source="${source}">`;
        let added = [];
        let element = $(this).hasClass('mc-language-switcher-cnt') ? $(this) : $(this).find('.mc-language-switcher-cnt');
        for (var i = 0; i < langs.length; i++) {
            let language = isString(langs[i]) ? langs[i] : langs[i][0];
            let id = isString(langs[i]) || !langs[i][1] ? false : langs[i][1];
            if (added.includes(language)) {
                continue;
            }
            code += `<span ${active_language == language ? 'class="mc-active" ' : ''}data-language="${language}"${id ? ' data-id="' + id + '"' : ''}><i class="mc-icon-close"></i><img src="${MC_URL}/media/flags/${language.toLowerCase()}.png" /></span>`;
            added.push(language);
        }
        element.find('.mc-language-switcher').remove();
        element.append(code + `<i data-mc-tooltip="${mc_('Add translation')}" class="mc-icon-plus"></i></div>`);
        element.mcInitTooltips();
        return this;
    }

    $.fn.mcShowLightbox = function (popup = false, action = '') {
        admin.find('.mc-lightbox').mcActive(false);
        overlay.mcActive(true);
        $(this).mcActive(true);
        if (popup) {
            $(this).addClass('mc-popup-lightbox').attr('data-action', action);
        } else {
            $(this).css({ 'margin-top': ($(this).outerHeight() / -2) + 'px', 'margin-left': ($(this).outerWidth() / -2) + 'px' })
        }
        $('body').addClass('mc-lightbox-active');
        setTimeout(() => { MCAdmin.open_popup = this }, 500);
        this.preventDefault;
        return this;
    }

    $.fn.mcHideLightbox = function () {
        $(this).find('.mc-lightbox,.mc-popup-lightbox').mcActive(false).removeClass('mc-popup-lightbox').removeAttr('data-action');
        overlay.mcActive(false);
        $('body').removeClass('mc-lightbox-active');
        MCAdmin.open_popup = false;
        return this;
    }

    $.fn.mcInitTooltips = function () {
        $(this).find('[data-mc-tooltip]').each(function () {
            $(this).miniTip({
                content: $(this).attr('data-mc-tooltip'),
                anchor: 's',
                delay: 500
            });
        });
        return this;
    }

    function infoBottom(text, type = false) {
        return MCAdmin.infoBottom(text, type);
    }

    function infoPanel(text, type = 'info', onConfirm = false, id = '', title = '', scroll = false, skip = false, onCancel = false) {
        return MCAdmin.infoPanel(text, type, onConfirm, id, title, scroll, skip, onCancel);
    }

    function activeUser(value) {
        return MCAdmin.activeUser(value);
    }

    function loading(element) {
        return MCAdmin.loading(element);
    }

    function loadingGlobal(show = true, is_overlay = true) {
        return MCAdmin.loadingGlobal(show, is_overlay);
    }

    function mc_(text) {
        return MC_TRANSLATIONS && text in MC_TRANSLATIONS ? MC_TRANSLATIONS[text] : text;
    }

    function isPWA() {
        return (window.matchMedia('(display-mode: standalone)').matches) || (window.navigator.standalone) || document.referrer.includes('android-app://');
    }

    function clearCache() {
        if (typeof caches !== ND) caches.delete('mc-pwa-cache');
    }

    function collapse(target, max_height) {
        MCAdmin.collapse(target, max_height);
    }

    function searchInput(input, searchFunction) {
        let icon = $(input).parent().find('i');
        let search = $(input).val();
        MCF.search(search, () => {
            icon.mcLoading(true);
            searchFunction(search, icon);
        });
    }

    function scrollPagination(area, check = false, offset = 0) {
        if (check) return $(area).scrollTop() + $(area).innerHeight() >= ($(area)[0].scrollHeight - 1);
        $(area).scrollTop($(area)[0].scrollHeight - offset);
    }

    function editorJSLoad(data = false) {
        if (editor_js_loading) return;
        if (editor_js === false) {
            editor_js = true;
            $.getScript(MC_URL + '/vendor/editorjs.js', () => {
                editorJSLoad(data);
            });
            return;
        }
        editorJSDestroy();
        editor_js_loading = true;
        editor_js = new EditorJS({
            data: isString(data) ? { time: Date.now(), blocks: [data ? { id: 'mc', type: 'raw', data: { html: data } } : { id: 'mc', type: 'paragraph', data: { text: '' } }] } : data,
            i18n: {
                messages: {
                    ui: {
                        'blockTunes': {
                            'toggler': {
                                'Click to tune': mc_('Click to tune')
                            },
                        },
                        'inlineToolbar': {
                            'converter': {
                                'Convert to': mc_('Convert to')
                            }
                        },
                        'toolbar': {
                            'toolbox': {
                                'Add': mc_('Add')
                            }
                        }
                    },
                    toolNames: {
                        'Text': mc_('Text'),
                        'Heading': mc_('Heading'),
                        'List': mc_('List'),
                        'Image': mc_('Image'),
                        'Code': mc_('Code'),
                        'Raw HTML': mc_('Raw HTML'),
                        'Bold': mc_('Bold'),
                        'Italic': mc_('Italic'),
                        'Link': mc_('Link'),
                    },
                    tools: {
                        'list': {
                            'Ordered': mc_('Ordered'),
                            'Unordered': mc_('Unordered')
                        }
                    },
                    blockTunes: {
                        'delete': {
                            'Delete': mc_('Delete')
                        },
                        'moveUp': {
                            'Move up': mc_('Move up')
                        },
                        'moveDown': {
                            'Move down': mc_('Move down')
                        }
                    },
                },
                direction: admin.hasClass('mc-rtl') ? 'rtl' : 'ltr'
            },
            tools: {
                list: {
                    class: List,
                    inlineToolbar: true
                },
                image: {
                    class: ImageTool,
                    config: {
                        uploader: {
                            uploadByFile(file) {
                                let form = new FormData();
                                form.append('file', file);
                                return new Promise((resolve) => {
                                    $.ajax({
                                        url: MC_URL + '/include/upload.php',
                                        cache: false,
                                        contentType: false,
                                        processData: false,
                                        data: form,
                                        type: 'POST',
                                        success: function (response) {
                                            response = JSON.parse(response);
                                            if (response[0] == 'success') {
                                                resolve({
                                                    success: 1,
                                                    file: {
                                                        url: response[1],
                                                    }
                                                })
                                            } else console.log(response);
                                        }
                                    })
                                })
                            }
                        }
                    }
                },
                header: Header,
                code: CodeTool,
                raw: RawTool
            },
            onReady: () => {
                editor_js_loading = false;
                articles_save_required_stop = true;
                articles_save_required = false;
            },
            onChange: () => {
                if (articles_save_required_stop) {
                    articles_save_required_stop = false;
                } else {
                    articles_save_required = true;
                }
            },
            minHeight: 50
        });
    }

    function editorJSHTML(blocks) {
        let code = '';
        blocks.map(block => {
            switch (block.type) {
                case 'header':
                    code += `<h${block.data.level}>${block.data.text}</h${block.data.level}>`;
                    break;
                case 'paragraph':
                    code += `<p>${block.data.text}</p>`;
                    break;
                case 'image':
                    code += `<img class="img-fluid" src="${block.data.file.url}" title="${block.data.caption}" /><em>${block.data.caption}</em>`;
                    break;
                case 'list':
                    code += '<ul class="mc-ul-' + block.data.style + '">';
                    block.data.items.forEach(function (li) {
                        code += `<li>${li}</li>`;
                    });
                    code += '</ul>';
                    break;
                case 'code':
                    code += `<code>${block.data.code}</code>`;
                    break;
                case 'raw':
                    code += `<div class="bxc-raw-html">${block.data.html}</div>`;
                    break;
            }
        });
        return code;
    }

    function editorJSDestroy() {
        if (typeof editor_js.destroy !== ND) {
            editor_js.destroy();
            editor_js = false;
        }
    }

    function isString(object) {
        return typeof object === 'string';
    }

    function pushState(url_parameters) {
        if (wp_admin) return;
        window.history.pushState('', '', url_parameters);
    }

    function cloudURL() {
        return MC_ADMIN_SETTINGS.cloud ? ('&cloud=' + MC_ADMIN_SETTINGS.cloud.token) : '';
    }

    function urlStrip(url) {
        return url.replace('https://', '').replace('http://', '').replace('www.', '').replace(/\/$/, '');
    }

    function saved_reply_search(search) {
        MCF.search(search, () => {
            let code = '';
            let all = search.length > 1 ? false : true;
            let area = saved_replies.find('.mc-replies-list > ul');
            let no_results = `<li class="mc-no-results">${mc_('No results found.')}</li>`;
            for (var i = 0; i < saved_replies_list.length; i++) {
                let name = saved_replies_list[i]['reply-name'];
                if (all || name.toLowerCase().includes(search) || name.replaceAll('-', ' ').toLowerCase().includes(search) || saved_replies_list[i]['reply-text'].toLowerCase().includes(search)) {
                    code += `<li><div>${name}</div><div>${saved_replies_list[i]['reply-text']}</div></li>`;
                }
            }
            area.html(code);
            if (!all && (MC_ADMIN_SETTINGS.dialogflow || MC_ADMIN_SETTINGS.chatbot_features)) {
                let icon = area.closest('.mc-popup').find('.mc-icon-search');
                icon.mcLoading(true);
                if (MC_ADMIN_SETTINGS.dialogflow) {
                    MCApps.dialogflow.getIntents((response) => {
                        let intents = MCApps.dialogflow.searchIntents(search, true);
                        let code_2 = '';
                        for (var i = 0; i < intents.length; i++) {
                            let text = intents[i].messages[0].text;
                            if (text && text.text) {
                                code_2 += `<li><div>${intents[i].displayName}</div><div>${intents[i].messages[0].text.text[0]}</div></li>`;
                            }
                        }
                        area.html(code_2 ? code + code_2 : (code ? code : no_results));
                        icon.mcLoading(false);
                    });
                } else {
                    MCF.ajax({
                        function: 'open-ai-message',
                        model: MC_ADMIN_SETTINGS.open_ai_model,
                        message: search
                    }, (response) => {
                        area.html(response[1] && (!response[5] || !response[5].unknow_answer) ? `${code}<li><div></div><div>${response[1]}</div></li>` : (code ? code : no_results));
                        icon.mcLoading(false);
                    });
                }
            } else if (!code) {
                area.html(no_results);
            }
        });
    }

    function whatsapp_direct_message_box(user_ids) {
        let box = admin.find('#mc-whatsapp-send-template-box');
        loadingGlobal();
        MCF.ajax({
            function: 'whatsapp-get-templates'
        }, (response) => {
            let code = '<option value=""></option>';
            let provider = response[0];
            let twilio = provider == 'twilio';
            response = response[1];
            if (response.error) {
                return infoBottom(response.error.message, 'error');
            }
            if (!Array.isArray(response)) {
                return infoBottom(response, 'error');
            }
            for (var i = 0; i < response.length; i++) {
                if (provider == 'official' && response[i].status == 'APPROVED' && (!MC_ACTIVE_AGENT.department || !response[i].department.length || response[i].department.includes(MC_ACTIVE_AGENT.department))) {
                    code += `<option value="${response[i].name}" data-languages="${response[i].languages}" data-phone-id="${response[i].phone_number_id}">${response[i].name} (${response[i].label})</option>`;
                }
                if (twilio) {
                    code += `<option value="${response[i].sid}">${response[i].friendly_name}</option>`;
                }
            }
            box.attr('data-provider', provider);
            box.find('#mc-whatsapp-send-template-list').html(code);
            MCForm.clear(box);
            box.find('.mc-direct-message-users').val(user_ids.length ? user_ids.join(',') : 'all');
            box.find('.mc-bottom > div').html('');
            box.find('.mc-loading').mcLoading(false);
            loadingGlobal(false);
            box.mcShowLightbox();
        });
    }

    function dialogDeleteFile(url, id, title) {
        window.open(url);
        infoPanel(`${mc_('For security reasons, delete the file after downloading it. Close this window to automatically delete it. File location:')}<pre>${url}</pre>`, 'info', false, id, title);
    }

    function touchEndEvent() {
        touchmove_x = false;
        touchmove_y = false;
        setTimeout(() => {
            if (touchmove) {
                touchmove[1].css('transform', '');
                touchmove[1].removeClass('mc-touchmove');
            }
        }, 100);
    }

    function getListConversation(conversation_id) {
        return conversations_admin_list_ul.find(`[data-conversation-id="${conversation_id}"]`);
    }

    /*
    * ----------------------------------------------------------
    * Apps
    * ----------------------------------------------------------
    */

    var MCApps = {

        dialogflow: {
            intents: false,
            qea: [],
            token: MCF.storage('dialogflow-token'),
            dialogflow_languages: [],
            original_response: false,
            smart_reply_busy: false,

            smartReply: function (message = false) {
                let conversation_id = MCChat.conversation.id;
                if (this.smart_reply_busy == conversation_id) {
                    return;
                }
                this.smart_reply_busy = conversation_id;
                MCF.ajax({
                    function: 'dialogflow-smart-reply',
                    message: message,
                    token: this.token,
                    conversation_id: conversation_id,
                    dialogflow_languages: this.dialogflow_languages,
                }, (response) => {
                    this.smart_reply_busy = false;
                    if (MCChat.conversation.id && conversation_id === MCChat.conversation.id) {
                        let suggestions = response.suggestions;
                        let code = '';
                        let is_bottom = conversations_area_list[0].scrollTop === (conversations_area_list[0].scrollHeight - conversations_area_list[0].offsetHeight);
                        let last_conversation_message = MCChat.conversation && MCChat.conversation.getLastMessage() ? MCChat.conversation.getLastMessage().message : false;
                        if (response.token) {
                            this.token = response.token;
                            MCF.storage('dialogflow-token', response.token);
                        }
                        for (var i = 0; i < suggestions.length; i++) {
                            if (suggestions[i] != last_conversation_message) {
                                code += `<span>${MCF.escape(suggestions[i])}</span>`;
                            }
                        }
                        suggestions_area.html(code);
                        if (is_bottom) {
                            MCChat.scrollBottom();
                        }
                    }
                    if (response.dialogflow_languages) {
                        this.dialogflow_languages = response.dialogflow_languages;
                    }
                });
            },

            showCreateIntentBox: function (message_id) {
                let question = '';
                let message = MCChat.conversation.getMessage(message_id);
                let response = message.message;
                if (MCF.isAgent(message.get('user_type'))) {
                    question = MCChat.conversation.getLastUserMessage(message.get('index'));
                    if (question && question.payload('mc-human-takeover')) {
                        question = MCChat.conversation.getLastUserMessage(question.get('index')).message;
                    } else {
                        question = question.message;
                    }
                } else {
                    question = response;
                    let agent_message = MCChat.conversation.getNextMessage(message.id, 'agent');
                    if (agent_message) {
                        response = agent_message.message;
                    }
                }
                if (dialogflow_intent_box.hasClass('mc-dialogflow-disabled')) {
                    MCF.ajax({
                        function: 'open-ai-get-qea-training'
                    }, (response) => {
                        let code = '<option value="">' + mc_('New Q&A') + '</option>';
                        for (var i = 0; i < response.length; i++) {
                            this.qea = response;
                            code += `<option value="${i}">${response[i][0][0]}</option>`;
                        }
                        dialogflow_intent_box.find('#mc-qea-select').html(code);
                    });
                    MCApps.openAI.generateQuestions(question);
                } else {
                    this.getIntents((response) => {
                        let code = '<option value="">' + mc_('New Intent') + '</option>';
                        for (var i = 0; i < response.length; i++) {
                            code += `<option value="${response[i].name}">${response[i].displayName}</option>`;
                        }
                        dialogflow_intent_box.find('#mc-intents-select').html(code);
                        MCApps.openAI.generateQuestions(question);
                    });
                }
                dialogflow_intent_box.attr('data-message-id', message.id);
                dialogflow_intent_box.find('.mc-type-text:not(.mc-first)').remove();
                dialogflow_intent_box.find('.mc-type-text input').val(question);
                dialogflow_intent_box.find('#mc-intents-select,#mc-qea-select').val('');
                dialogflow_intent_box.find('.mc-search-btn').mcActive(false).find('input').val('');
                this.searchIntents('');
                this.original_response = response;
                dialogflow_intent_box.find('textarea').val(response);
                dialogflow_intent_box.mcShowLightbox();
            },

            submitIntent: function (button) {
                if (loading(button)) return;
                let questions = [];
                let answer = dialogflow_intent_box.find('textarea').val();
                let intent_name = dialogflow_intent_box.find('#mc-intents-select,#mc-qea-select').val();
                let services = dialogflow_intent_box.find('#mc-train-chatbots').val();
                let is_open_ai = services == 'open-ai' || dialogflow_intent_box.hasClass('mc-dialogflow-disabled');
                dialogflow_intent_box.find('.mc-type-text input').each(function () {
                    if ($(this).val()) {
                        questions.push($(this).val());
                    }
                });
                if ((!answer && !intent_name) || questions.length == 0) {
                    MCForm.showErrorMessage(dialogflow_intent_box, 'Please insert the bot response and at least one user expression.');
                    $(button).mcLoading(false);
                } else {
                    let questions_answers;
                    if (is_open_ai) {
                        if (intent_name) {
                            this.qea[intent_name][0] = this.qea[intent_name][0].concat(questions);
                            questions_answers = this.qea[intent_name];
                        } else {
                            questions_answers = [[questions, answer]];
                        }
                    }
                    MCF.ajax({
                        function: is_open_ai ? 'open-ai-qea-training' : (!intent_name ? 'dialogflow-create-intent' : 'dialogflow-update-intent'),
                        questions_answers: questions_answers,
                        expressions: questions,
                        response: answer,
                        agent_language: dialogflow_intent_box.find('.mc-dialogflow-languages select').val(),
                        conversation_id: MCChat.conversation.id,
                        intent_name: intent_name,
                        update_index: intent_name,
                        services: services,
                        language: dialogflow_intent_box.find('.mc-dialogflow-languages select').val()
                    }, (response) => {
                        $(button).mcLoading(false);
                        if (response === true) {
                            admin.mcHideLightbox();
                            infoBottom('Training completed');
                        } else {
                            MCForm.showErrorMessage(dialogflow_intent_box, response.error && response.error.message ? response.error && response.error.message : 'Error');
                        }
                    });
                }
            },

            getIntents: function (onSuccess) {
                if (this.intents === false) {
                    MCF.ajax({ function: 'dialogflow-get-intents' }, (response) => {
                        this.intents = Array.isArray(response) ? response : [];
                        onSuccess(this.intents);
                    });
                } else {
                    onSuccess(this.intents);
                }
            },

            searchIntents: function (search, return_intents = false) {
                if (dialogflow_intent_box.hasClass('mc-dialogflow-disabled')) {
                    let all = search.length > 1 ? false : true;
                    let code = all ? `<option value="">${mc_('New Q&A')}</option>` : '';
                    let list = [];
                    search = search.toLowerCase();
                    for (var i = 0; i < this.qea.length; i++) {
                        if (all || (this.qea[i][0].join('').toLowerCase().includes(search) || this.qea[i][1].toLowerCase().includes(search))) {
                            code += `<option value="${i}">${this.qea[i][0][0]}</option>`;
                        }
                    }
                    dialogflow_intent_box.find('#mc-qea-select').html(code).change();
                    if (all) {
                        dialogflow_intent_box.find('textarea').val(this.original_response);
                    }
                } else {
                    let all = search.length > 1 ? false : true;
                    let code = all ? `<option value="">${mc_('New Intent')}</option>` : '';
                    let intents = this.intents;
                    let intents_list = [];
                    search = search.toLowerCase();
                    for (var i = 0; i < intents.length; i++) {
                        let found = all || intents[i].displayName.toLowerCase().includes(search);
                        if (!found && intents[i].trainingPhrases) {
                            let training_phrases = intents[i].trainingPhrases;
                            for (var j = 0; j < training_phrases.length; j++) {
                                for (var y = 0; y < training_phrases[j].parts.length; y++) {
                                    if (training_phrases[j].parts[y].text.toLowerCase().includes(search)) {
                                        found = true;
                                        break;
                                    }
                                }
                                if (found) break;
                            }
                        }
                        if (found) {
                            if (return_intents) {
                                intents_list.push(intents[i]);
                            } else {
                                code += `<option value="${intents[i].name}">${intents[i].displayName}</option>`;
                            }
                        }
                    }
                    if (return_intents) {
                        return intents_list;
                    } else {
                        dialogflow_intent_box.find('#mc-intents-select').html(code).change();
                    }
                    if (!search) {
                        dialogflow_intent_box.find('textarea').val(this.original_response);
                    }
                }
            },

            previewIntentDialogflow: function (name) {
                let code = '';
                let intent = this.getIntent(name);
                if (intent) {
                    let training_phrases = intent.trainingPhrases ? intent.trainingPhrases : [];
                    let count = training_phrases.length;
                    if (count > 1) {
                        for (var j = 0; j < count; j++) {
                            for (var y = 0; y < training_phrases[j].parts.length; y++) {
                                code += `<span>${training_phrases[j].parts[y].text}</span>`;
                                if (y == 15) break;
                            }
                        }
                        infoPanel(code, 'info', false, 'intent-preview-box', '', count > 10);
                    }
                }
            },

            previewIntent: function (index) {
                if (index) {
                    let code = '';
                    let qea = this.qea[index];
                    if (qea[0].length > 1) {
                        let max = qea[0].length > 15 ? 15 : qea[0].length;
                        for (var i = 0; i < max; i++) {
                            code += `<span>${qea[0][i]}</span>`;
                        }
                        infoPanel(code, 'info', false, 'qea-preview-box', '', max > 10);
                    }
                }
            },

            getIntent: function (name) {
                let code = '';
                for (var i = 0; i < this.intents.length; i++) {
                    if (this.intents[i].name == name) {
                        return this.intents[i];
                    }
                }
                return false;
            },

            translate: function (strings, language_code, onSuccess, message_ids, conversation_id) {
                if (strings.length) {
                    MCF.ajax({
                        function: 'google-translate',
                        strings: strings,
                        language_code: language_code,
                        token: this.token,
                        message_ids: message_ids,
                        conversation_id: conversation_id
                    }, (response) => {
                        this.token = response[1]
                        if (Array.isArray(response[0])) {
                            onSuccess(response[0]);
                        } else {
                            MCF.error(JSON.stringify(response[0]), 'MCApps.dialogflow.translate');
                            return false;
                        }
                    });
                }
            }
        },

        openAI: {
            urls_history: [],
            progress: 1,

            rewriteButton: function (value) {
                if (open_ai_button.length) {
                    open_ai_button.mcActive(value.length > 2 && value.indexOf(' '));
                }
            },

            rewrite: function (message, onSuccess) {
                MCF.ajax({
                    function: 'open-ai-message',
                    model: MC_ADMIN_SETTINGS.open_ai_model,
                    message: (MC_ADMIN_SETTINGS.open_ai_prompt_rewrite ? MC_ADMIN_SETTINGS.open_ai_prompt_rewrite : 'Make the following sentence more friendly and professional') + ` and use ${MC_LANGUAGE_CODES[MC_ADMIN_SETTINGS.active_agent_language]} language: """${message.replace('"', '\'')}"""`,
                    extra: 'rewrite'
                }, (response) => {
                    if (!response[0]) {
                        console.error('OpenAI: ' + JSON.stringify(response[1]));
                    }
                    MCConversations.previous_editor_text = message;
                    onSuccess(response);
                });
            },

            troubleshoot: function () {
                let status = MC_ADMIN_SETTINGS.open_ai_chatbot_status;
                if (status !== true) {
                    infoBottom(status == 'inactive' ? 'Enable the chatbot in Settings > Artificial Intelligence > OpenAI > Chatbot.' : (status == 'key' ? 'Enter the OpenAI API key in Settings > Artificial Intelligence > OpenAI > API key.' : 'The training data is ignored. Change the chatbot mode in Settings > Artificial Intelligence > OpenAI > Chatbot mode.'), 'error');
                }
                return status;
            },

            getCode: {
                set_data: function (data) {
                    let code = '';
                    let code_select_user_details = this.select_user_details();
                    if (!data || !data.length) {
                        data = [['', '']];
                    }
                    for (var i = 0; i < data.length; i++) {
                        code += `<div class="repeater-item"><div>${code_select_user_details.replace(`"${data[i][0]}"`, `"${data[i][0]}" selected`)}<div class="mc-setting"><input type="url" placeholder="${mc_('Enter the value')}" value="${data[i][1]}"></div></div><i class="mc-icon-close"></i></div>`;
                    }
                    return this.repeater_('Data', code);
                },

                actions: function (actions) {
                    let action_list = [['tags', 'Assign tags'], ['department', 'Assign a department'], ['agent', 'Assign an agent'], ['redirect', 'Go to URL'], ['open_article', 'Show an article'], ['transcript', 'Download transcript'], ['transcript_email', 'Email transcript'], ['send_email', 'Send email to user'], ['send_email_agents', 'Send email to agents'], ['archive_conversation', 'Archive the conversation'], ['human_takeover', 'Human takeover']];
                    let code = '';
                    let code_2 = '';
                    if (!actions || !actions.length) {
                        actions = [['tags', '']];
                    }
                    for (var i = 0; i < action_list.length; i++) {
                        code += `<option value="${action_list[i][0]}">${mc_(action_list[i][1])}</option>`;
                    }
                    for (var i = 0; i < actions.length; i++) {
                        code_2 += `<div class="repeater-item"><div><div class="mc-setting"><select>${code.replace(`"${actions[i][0]}"`, `"${actions[i][0]}" selected`)}</select></div>${this.action(actions[i][0], actions[i][1])}</div><i class="mc-icon-close"></i></div>`;
                    }
                    return this.repeater_('Actions', code_2);
                },

                action: function (action, value) {
                    let help = { tags: 'Enter tag names, separated by commas', department: 'Enter the department ID', agent: 'Enter the agent ID', redirect: 'Enter the URL', open_article: 'Enter the article ID', send_email: 'Enter a message', send_email_agents: 'Enter a message' };
                    let input_types = { department: 'number', agent: 'number', redirect: 'url' };
                    return ['send_email_agents', 'send_email', 'open_article', 'redirect', 'agent', 'department', 'tags'].includes(action) ? `<div class="mc-setting"><input type="${input_types[action] ? input_types[action] : 'text'}" value="${value}" placeholder="${mc_(help[action])}" value="${value}"></div>` : '';
                },

                select_user_details: function () {
                    let code = '<div class="mc-setting"><select>';
                    let user_details = [['full_name', 'Name'], ['email', 'Email'], ['password', 'Password']].concat(MCUsers.getExtraDetailsList());
                    for (var i = 0; i < user_details.length; i++) {
                        code += `<option value="${user_details[i][0]}">${mc_(user_details[i][1])}</option>`;
                    }
                    return code + `</select></div>`;
                },

                repeater_: function (title, code) {
                    return `<div class="mc-title">${mc_(title)}</div><div data-type="repeater" class="mc-setting mc-type-repeater"><div class="input"><div class="mc-repeater mc-repeater-block-${MCF.stringToSlug(title)}">${code}</div><div class="mc-btn mc-btn-white mc-repeater-add mc-icon"><i class="mc-icon-plus"></i>${mc_('Add new item')}</div></div></div>`;
                }
            },

            generateQuestions: function (expression) {
                if (MC_ADMIN_SETTINGS.open_ai_user_expressions && expression) {
                    let loader = dialogflow_intent_box.find('[data-value="add"]');
                    loader.mcLoading(true);
                    dialogflow_intent_box.find('.mc-open-ai-intent').remove();
                    MCF.ajax({ function: 'open-ai-user-expressions', message: expression }, (response) => {
                        let code = '';
                        for (var i = 0; i < response.length; i++) {
                            if (response[i]) {
                                code += `<div class="mc-setting mc-type-text mc-open-ai-intent"><input type="text" value="${response[i].replace(/"/g, '')}"></div>`;
                            }
                        }
                        if (code) dialogflow_intent_box.find('> div > .mc-type-text').last().after(code);
                        loader.mcLoading(false);
                    });
                }
            },

            flows: {
                flows: [],

                set: function (flow) {
                    if (typeof flow == 'string') {
                        flow = flow.trim().replaceAll('"', '').replaceAll('_', '-');
                        if (flow) {
                            flow = { name: flow, steps: [[[{ type: 'start', start: 'message', message: '', conditions: [], disabled: false }]], [[]]] };
                        }
                    } else {
                        for (var i = 0; i < this.flows.length; i++) {
                            if (this.flows[i].name == flow.name) {
                                this.flows[i] = flow;
                                this.show(flow.name);
                                return true;
                            }
                        }
                    }
                    this.flows.push(flow);
                    flows_nav.find('.mc-active').mcActive(false);
                    flows_nav.append(this.navCode(flow.name, true));
                    this.show(flow.name);
                },

                get: function (name = false) {
                    if (!name) {
                        name = this.getActiveName();
                    }
                    for (var i = 0; i < this.flows.length; i++) {
                        if (this.flows[i].name == name) {
                            if (!this.flows[i].steps) {
                                this.flows[i].steps = [];
                            }
                            return this.flows[i];
                        }
                    }
                    return false;
                },

                show: function (name = false) {
                    if (!name) {
                        name = this.getActiveName();
                    }
                    let flow = this.get(name);
                    let code = '';
                    let items;
                    if (flow) {
                        let letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
                        let previous_labels = [];
                        let labels = [];
                        for (var i = 0; i < flow.steps.length; i++) {
                            let block_cnts = flow.steps[i];
                            let letters_index = 0;
                            previous_labels = labels;
                            labels = [];
                            code += '<div><div>';
                            for (var j = 0; j < block_cnts.length; j++) {
                                let blocks = block_cnts[j];
                                code += '<div class="mc-flow-block-cnt">';
                                if (previous_labels[j]) {
                                    code += `<div class="mc-flow-block-cnt-name">${previous_labels[j]}</div>`;
                                }
                                for (var y = 0; y < blocks.length; y++) {
                                    if (blocks[y].type == 'start' && !Array.isArray(blocks[y].message)) blocks[y].message = [{ message: blocks[y].message }]; // Deprecated
                                    let text = blocks[y].message ? (Array.isArray(blocks[y].message) ? (blocks[y].message.length ? blocks[y].message[0].message : '') : blocks[y].message) : '';
                                    if (text) {
                                        text = `<div>${text.length > 45 ? text.substring(0, 45) + '...' : text}</div>`;
                                    }
                                    code += `<div class="mc-flow-block" data-type="${blocks[y].type}"><div>${mc_(MCF.slugToString(blocks[y].type))}</div>${text}`;
                                    switch (blocks[y].type) {
                                        case 'get_user_details':
                                            labels.push(false);
                                            break;
                                        case 'condition':
                                        case 'button_list':
                                            let is_condition = blocks[y].type == 'condition';
                                            let rows = is_condition ? [mc_('True'), mc_('False')] : blocks[y].options;
                                            if (is_condition) {
                                                code += `<div>`;
                                                items = blocks[y].conditions;
                                                for (var x = 0; x < items.length; x++) {
                                                    code += `<div>${MCF.slugToString(items[x][0] + ' ' + items[x][1])}${items[x][2] ? ': ' + items[x][2] : ''}</div>`;
                                                }
                                                code += `</div>`;
                                            }
                                            code += `<div class="mc-flow-connectors">`;
                                            for (var x = 0; x < rows.length; x++) {
                                                labels.push(letters[letters_index] + (x + 1));
                                                code += `<div>${rows[x]}<span>${labels[labels.length - 1]}</span></div>`;
                                            }
                                            code += `</div>`;
                                            letters_index++;
                                            break;
                                        case 'video':
                                            code += `<div>${blocks[y].url}</div>`;
                                            break;
                                        case 'action':
                                        case 'set_data':
                                            code += `<div>`;
                                            items = blocks[y][blocks[y].type == 'set_data' ? 'data' : 'actions'];
                                            for (var x = 0; x < items.length; x++) {
                                                code += `<div>${MCF.slugToString(items[x][0])}${items[x][1] ? ': ' + items[x][1] : ''}</div>`;
                                            }
                                            code += `</div>`;
                                            break;
                                        case 'rest_api':
                                            code += `<div>${blocks[y].url}</div>`;
                                            break;
                                    }
                                    code += '</div>';
                                }
                                if (blocks.length < 4) {
                                    code += '<div class="mc-flow-add-block mc-icon-plus"></div>';
                                }
                                code += '</div>';
                            }
                            code += '</div><div class="mc-flow-add-step mc-icon-plus"></div></div>';
                        }
                        flows_area.html(code);
                        $(admin).find('.mc-flow-scroll').mcActive((flow.steps.length * 250) > flows_area.outerWidth());
                    }
                },

                delete: function (flow_name) {
                    for (var i = 0; i < this.flows.length; i++) {
                        if (this.flows[i].name == flow_name) {
                            let nav = flows_nav.find(`[data-value="${flow_name}"]`);
                            this.flows[i].steps.forEach((step) => {
                                step.forEach((block_cnt) => {
                                    block_cnt.forEach((block) => {
                                        if (block.attachments) {
                                            block.attachments.forEach((attachment) => {
                                                MCF.ajax({ function: 'delete-file', path: attachment });
                                            });
                                        }
                                    });
                                });
                            });
                            this.flows.splice(i, 1);
                            if (nav.mcActive()) {
                                if (nav.prev().length) {
                                    nav.prev().click();
                                } else if (nav.next().length) {
                                    nav.next().click();
                                } else {
                                    flows_area.html('');
                                }
                            }
                            nav.remove();
                            return true;
                        }
                    }
                    return false;
                },

                save: function (onSuccess = false) {
                    MCF.ajax({ function: 'open-ai-flows-save', flows: JSON.stringify(this.flows) }, (response) => {
                        onSuccess(response);
                    });
                },

                navCode: function (name, active = false) {
                    return `<li${active ? ' class="mc-active"' : ''} data-value="${name}">${name}<i class="mc-icon-delete"></i></li>`;
                },

                getActive: function () {
                    return flows_nav.find('.mc-active');
                },

                getActiveName: function () {
                    return this.getActive().attr('data-value');
                },

                getActiveIndex: function () {
                    return this.getActive().index();
                },

                steps: {

                    get: function (flow_name = false, step_index = false) {
                        return MCApps.openAI.flows.get(flow_name).steps[step_index ? step_index : this.getActiveIndex()];
                    },

                    getActiveIndex: function () {
                        return MCApps.openAI.flows.blocks.getActive().parent().parent().parent().index();
                    }
                },

                blocks: {
                    set: function (block, flow_name = false, step_index = false, block_cnt_index = false, block_index = false) {
                        let indexes = this.getIndexes(flow_name, step_index, block_cnt_index, block_index);
                        let flow = MCApps.openAI.flows.get(flow_name);
                        if (flow) {
                            let stop = false;
                            if (flow.steps.length > indexes.step) {
                                let block_cnts = flow.steps[indexes.step];
                                if (block_cnts.length > indexes.cnt) {
                                    let blocks = block_cnts[indexes.cnt];
                                    if (blocks.length > indexes.block) {
                                        blocks[indexes.block] = block;
                                        stop = true;
                                    }
                                }
                                if (!stop) {
                                    flow.steps[indexes.step][indexes.cnt].push(block);
                                    stop = true;
                                }
                            }
                            if (!stop) {
                                flow.steps.push([[block]]);
                            }

                            // Generate next step
                            let step = flow.steps[indexes.step];
                            let next_step = flow.steps[MCApps.openAI.flows.steps.getActiveIndex() + 1];
                            let next_step_block_cnts_count = 0;
                            for (var i = 0; i < step.length; i++) {
                                for (var j = 0; j < step[i].length; j++) {
                                    switch (step[i][j].type) {
                                        case 'get_user_details':
                                            next_step_block_cnts_count += 1;
                                            break;
                                        case 'button_list':
                                            next_step_block_cnts_count += step[i][j].options.length;
                                            break;
                                        case 'condition':
                                            next_step_block_cnts_count += 2;
                                            break;
                                    }
                                }
                            }
                            if (next_step) {
                                if (next_step.length > next_step_block_cnts_count) {
                                    next_step.splice((next_step_block_cnts_count - 1) * -1, next_step_block_cnts_count);
                                } else {
                                    let i = next_step.length;
                                    for (i; i < next_step_block_cnts_count; i++) {
                                        next_step.splice(indexes.cnt, 0, [])
                                    }
                                }
                            } else {
                                next_step = [];
                                for (var i = 0; i < next_step_block_cnts_count; i++) {
                                    next_step.push([]);
                                }
                                if (next_step.length) {
                                    flow.steps.push(next_step);
                                }
                            }

                            // Update the flow
                            MCApps.openAI.flows.show(flow_name);
                            return true;
                        }
                        return false;
                    },

                    get: function (flow_name = false, step_index = false, block_cnt_index = false, block_index = false) {
                        let flow = MCApps.openAI.flows.get(flow_name);
                        if (flow) {
                            let indexes = this.getIndexes(flow_name, step_index, block_cnt_index, block_index);
                            return flow.steps[indexes.step][indexes.cnt][indexes.block];
                        }
                        return false;
                    },

                    add: function (block_type, flow_name = false, step_index = false, block_cnt_index = false, block_index = false) {
                        let attributes = { button_list: { message: '', options: [] }, message: { message: '' }, video: { message: '', url: '' }, get_user_details: { message: '', details: [] }, set_data: { data: [] }, action: { actions: [] }, rest_api: { url: '', method: '', headers: [], body: '', save_response: [] }, condition: { conditions: [] } };
                        let indexes = this.getIndexes(flow_name, step_index, block_cnt_index, block_index);
                        this.set(Object.assign(attributes[block_type], { type: block_type }), indexes.name, indexes.step, indexes.cnt, indexes.block);
                        MCApps.openAI.flows.show(flow_name);
                        setTimeout(() => {
                            flows_area.find('> div').eq(indexes.step).find('.mc-flow-block-cnt').eq(indexes.cnt).find('.mc-flow-block').eq(indexes.block).click();
                        }, 100);
                    },

                    delete: function (flow_name = false, step_index = false, block_cnt_index = false, block_index = false) {
                        let indexes = this.getIndexes(flow_name, step_index, block_cnt_index, block_index);
                        for (var i = 0; i < MCApps.openAI.flows.flows.length; i++) {
                            let flow = MCApps.openAI.flows.flows[i];
                            if (indexes.name == flow.name) {
                                if (['get_user_details', 'button_list', 'condition'].includes(flow.steps[indexes.step][indexes.cnt][indexes.block].type)) {
                                    let block_cnts_to_delete = this.delete_(i, indexes.step, indexes.cnt);
                                    let flow_new = Object.assign({}, flow);
                                    for (var j = 0; j < block_cnts_to_delete.length; j++) {
                                        flow.steps[block_cnts_to_delete[j][0]][block_cnts_to_delete[j][1]] = false;
                                    }
                                    flow.steps[indexes.step][indexes.cnt].splice(indexes.block, 1);
                                    flow_new.steps = [];
                                    for (var j = 0; j < flow.steps.length; j++) {
                                        let step = [];
                                        for (var k = 0; k < flow.steps[j].length; k++) {
                                            if (flow.steps[j][k]) {
                                                step.push(flow.steps[j][k]);
                                            }
                                        }
                                        if (step.length) {
                                            flow_new.steps.push(step);
                                        }
                                    }
                                    MCApps.openAI.flows.flows[i] = flow_new;
                                } else {
                                    flow.steps[indexes.step][indexes.cnt].splice(indexes.block, 1);
                                }
                                MCApps.openAI.flows.show(indexes.name);
                                return true;
                            }
                        }
                        return false;
                    },

                    delete_: function (i, step_index, block_cnt_index, all_next_block_cnt_indexes = []) {
                        let next_block_cnt_indexes = MCApps.openAI.flows.blocks.getNextCntIndexes(i, step_index, block_cnt_index);
                        for (var j = 0; j < next_block_cnt_indexes.length; j++) {
                            all_next_block_cnt_indexes.push([step_index + 1, next_block_cnt_indexes[j]]);
                            this.delete_(i, step_index + 1, next_block_cnt_indexes[j], all_next_block_cnt_indexes);
                        }
                        return all_next_block_cnt_indexes;
                    },

                    getActive: function () {
                        return flows_area.find('.mc-flow-add-block.mc-active, .mc-flow-block.mc-active');
                    },

                    getActiveIndex: function () {
                        let blocks = this.getActiveCnt().find('.mc-flow-block');
                        let index = blocks.index(this.getActive());
                        return index === -1 ? blocks.length : index;
                    },

                    getActiveCnt: function () {
                        return this.getActive().parent();
                    },

                    getActiveCntIndex: function () {
                        return this.getActive().parent().index();
                    },

                    getNextCnt: function (current_flow_index, current_step_index, current_block_cnts_index, current_connector_index = 0) {
                        let indexes = this.getNextCntIndexes(current_flow_index, current_step_index, current_block_cnts_index, current_connector_index);
                        return indexes.length > current_connector_index ? MCApps.openAI.flows.flows[current_flow_index].steps[current_step_index + 1][indexes[current_connector_index]] : false;
                    },

                    getNextCntIndexes: function (current_flow_index, current_step_index, current_block_cnt_index) {
                        let flow = MCApps.openAI.flows.flows[current_flow_index];
                        let next_block_cnt_indexees = [];
                        if (flow && flow.steps[current_step_index + 1]) {
                            let next_block_cnt_index = 0;
                            for (var i = 0; i <= current_block_cnt_index; i++) {
                                let current_blocks = flow.steps[current_step_index][i];
                                for (var j = 0; j < current_blocks.length; j++) {
                                    if (current_blocks[j].type == 'button_list') {
                                        for (var k = 0; k < current_blocks[j].options.length; k++) {
                                            if (i == current_block_cnt_index) {
                                                next_block_cnt_indexees.push(next_block_cnt_index);
                                            }
                                            next_block_cnt_index++;
                                        }
                                    } else if (current_blocks[j].type == 'get_user_details') {
                                        if (i == current_block_cnt_index) {
                                            next_block_cnt_indexees.push(next_block_cnt_index);
                                        }
                                        next_block_cnt_index++;
                                    } else if (current_blocks[j].type == 'condition') {
                                        for (var k = 0; k < 2; k++) {
                                            if (i == current_block_cnt_index) {
                                                next_block_cnt_indexees.push(next_block_cnt_index);
                                            }
                                            next_block_cnt_index++;
                                        }
                                    }
                                }
                            }
                        }
                        return next_block_cnt_indexees;
                    },

                    getPreviousCntIndex: function (current_flow_index, current_step_index, current_block_cnt_index) {
                        let previous_step = MCApps.openAI.flows.flows[current_flow_index].steps[current_step_index - 1];
                        if (previous_step) {
                            let index = 0;
                            for (var i = 0; i < previous_step.length; i++) {
                                let block_cnt = previous_step[i];
                                for (var j = 0; j < block_cnt.length; j++) {
                                    index += block_cnt[j].type == 'button_list' ? block_cnt[j].options.length : (block_cnt[j].type == 'get_user_details' ? 1 : (block_cnt[j].type == 'condition' ? 2 : 0));
                                }
                                if (index > current_block_cnt_index) {
                                    return i;
                                }
                            }
                            return index;
                        }
                    },

                    getIndexes: function (flow_name, step_index, block_cnt_index, block_index) {
                        return { name: flow_name ? flow_name : MCApps.openAI.flows.getActiveName(), step: step_index ? step_index : MCApps.openAI.flows.steps.getActiveIndex(), cnt: block_cnt_index ? block_cnt_index : this.getActiveCntIndex(), block: block_index ? block_index : this.getActiveIndex() };
                    },

                    activateLinkedCnts: function (active_block) {
                        let block_cnt = $(active_block).parent();
                        let active_flow_index = MCApps.openAI.flows.getActiveIndex();
                        let active_step_index = block_cnt.parent().parent().index();
                        let flows = flows_area.find('> div');
                        let previous_cnt = flows.eq(active_step_index - 1).find('.mc-flow-block-cnt').eq(MCApps.openAI.flows.blocks.getPreviousCntIndex(active_flow_index, active_step_index, block_cnt.index()));
                        if (!is_over_connector) {
                            let next_block_cnts = flows.eq(active_step_index + 1).find('.mc-flow-block-cnt');
                            let next_block_cnt_indexes = MCApps.openAI.flows.blocks.getNextCntIndexes(active_flow_index, active_step_index, block_cnt.index());
                            for (var i = 0; i < next_block_cnt_indexes.length; i++) {
                                next_block_cnts.eq(next_block_cnt_indexes[i]).mcActive(true);
                            }
                        }
                        flows_area.find('.mc-flow-connectors > div').mcActive(false);
                        previous_cnt.mcActive(true).find('.mc-flow-block-cnt-name').mcActive(true);
                        if (block_cnt.find('.mc-flow-block-cnt-name').length) {
                            previous_cnt.find('.mc-flow-connectors > div').mcActive(false).eq(parseInt(block_cnt.find('.mc-flow-block-cnt-name').html().substring(1)) - 1).mcActive(true);
                        }
                    }
                }
            },

            train: {
                urls: [],
                errors: [],
                base_url: false,
                start_urls: [],
                sitemap_processed_urls: [],
                active_source: false,
                history: [],
                training_button: false,
                extract_url: [],
                skip_files: [],

                files: function (onSuccess, index = 0) {
                    let files = upload_input.prop('files');
                    if (index >= files.length) {
                        return onSuccess(true);
                    }
                    let file_name = files[index].name;
                    if (this.isFile(file_name) && !this.skip_files.includes(file_name)) {
                        admin.find('#mc-embeddings-box p').html(mc_('We are processing the source') + '<pre>' + file_name + '</pre><span>' + mc_('Only {R} sources left to complete.').replace('{R}', files.length - index) + '</span>');
                        upload_input.mcUploadFiles((response) => {
                            response = JSON.parse(response);
                            if (response[0] == 'success') {
                                MCF.ajax({ function: 'open-ai-file-training', url: response[1] }, (response) => {
                                    if (this.isError(response)) {
                                        return;
                                    }
                                    this.files(onSuccess, index + 1);
                                });
                            }
                        }, index);
                    } else {
                        this.files(onSuccess, index + 1);
                    }
                },

                website: function (onSuccess, index = 0) {
                    if (index >= this.urls.length) {
                        return onSuccess(true);
                    }
                    let url = this.urls[index];
                    if (url && url.includes('http')) {
                        admin.find('#mc-embeddings-box p').html(mc_('We are processing the source') + '<pre>' + url + '</pre><span>' + mc_('Only {R} sources left to complete.').replace('{R}', this.urls.length - index) + '</span>');
                        if (url.includes('.xml')) {
                            MCF.ajax({ function: 'get-sitemap-urls', sitemap_url: url }, (response) => {
                                if (Array.isArray(response)) {
                                    this.urls = this.urls.concat(response);
                                } else {
                                    this.errors.push(response);
                                }
                                this.website(onSuccess, index + 1);
                            });
                        } else if (!this.sitemap_processed_urls.includes(url) && this.extract_url[index]) {
                            this.sitemap_processed_urls.push(url);
                            this.sitemap(url, (response) => {
                                this.urls = this.urls.concat(response);
                                this.website(onSuccess, index + 1);
                            });
                        } else {
                            MCF.ajax({ function: 'open-ai-url-training', url: url }, (response) => {
                                if (this.isError(response)) {
                                    return;
                                } else if (!response[0] && !response[1].includes('http-error-404') && !response[1].includes('http-error-302')) {
                                    if (response[1].includes('http-error') && index === 0) {
                                        this.errors.push(response[2]);
                                    } else if (response[0][0] !== true) {
                                        this.errors.push(response);
                                    }
                                }
                                this.website(onSuccess, index + 1);
                            });
                        }
                    } else {
                        if (url) {
                            this.errors.push(mc_('Use a valid URL starting with http. The URL {R} is not valid.').replace('{R}', url));
                        }
                        this.website(onSuccess, index + 1);
                    }
                },

                sitemap: function (url, onSuccess, sitemap_urls = []) {
                    admin.find('#mc-embeddings-box p').html(mc_('We are generating the sitemap') + '<pre>' + url + '</pre>');
                    MCF.ajax({ function: 'generate-sitemap', url: url }, (response) => {
                        onSuccess(response);
                    });
                },

                qea: function (onSuccess) {
                    admin.find('#mc-embeddings-box p').html(mc_('We are processing the Q&A.'));
                    let questions_answers = MCSettings.repeater.get(chatbot_qea_repeater.find('.mc-repeater').eq(0).find('> .repeater-item')).map((item) => {
                        return [
                            item['open-ai-faq-questions'].map((item) => {
                                return item.question;
                            }),
                            item['open-ai-faq-answer'],
                            item['open-ai-faq-function-calling-url'],
                            item['open-ai-faq-function-calling-method'],
                            item['open-ai-faq-function-calling-headers'],
                            item['open-ai-faq-function-calling-properties'].length ? item['open-ai-faq-function-calling-properties'].map((item) => {
                                return [item.name, item.description, item.allowed];
                            }) : false,
                            item['open-ai-faq-set-data'].map((item) => {
                                return [item.id, item.value];
                            })
                        ];
                    });
                    MCF.ajax({
                        function: 'open-ai-qea-training',
                        questions_answers: questions_answers,
                        reset: true
                    }, (response) => {
                        onSuccess(response);
                    });
                },

                articles: function (onSuccess) {
                    admin.find('#mc-embeddings-box p').html(mc_('We are processing the articles.'));
                    MCF.ajax({ function: 'open-ai-articles-training' }, (response) => {
                        onSuccess(response);
                    });
                },

                isFile: function (url) {
                    return url.includes('.pdf') || url.includes('.txt') || url.includes('.json') || url.includes('.csv');
                },

                isError: function (response) {
                    let is_limit = response[1] == 'chars-limit-exceeded';
                    let is_error = is_limit || (response[1] && response[1][0] && response[1][0].error);
                    if (is_error) {
                        chatbot_area.find('#mc-train-chatbot').mcLoading(false);
                        infoPanel(is_limit ? mc_('The chatbot cannot be trained with these sources because the limit of your plan is {R} characters. Upgrade your plan to increase the number of characters.').replace('{R}', response[2]) : response[1][0].error.message);
                    }
                    return is_error;
                }
            },

            playground: {
                messages: [],
                last_response: false,

                addMessage: function (message, user_type = 'user', attachments = []) {
                    chatbot_playground_area.append(`<div data-type="${user_type}"><div>${mc_(user_type == 'user' ? 'User' : 'Assistant')}<div><i class="mc-icon-close mc-btn-icon mc-btn-red"></i></div></div><div>${(new MCMessage({ id: 1, message: MCF.escape(message), creation_time: '0000-00-00 00:00:00', status_code: 0, user_type: 'agent', attachments: JSON.stringify(attachments) })).getCode()}</div></div>`);
                    chatbot_playground_area[0].scrollTop = chatbot_playground_area[0].scrollHeight;
                    this.messages.push([user_type, message]);
                }
            },

            init: function () {
                MCF.ajax({
                    function: 'open-ai-get-training-files'
                }, (response) => {
                    let code = ['', ''];
                    for (var i = 0; i < response.length; i++) {
                        if (!['mc-conversations', 'mc-articles', 'mc-database', 'mc-flows'].includes(response[i])) {
                            let is_file = this.train.isFile(response[i]);
                            code[is_file ? 1 : 0] += `<tr data-url="${response[i]}"><td><input type="checkbox" /></td><td>${is_file ? MCF.beautifyAttachmentName(response[i].split('/').pop()) : urlStrip(response[i])}</td><td></td><td><i class="mc-icon-delete"></i></td></tr>`;
                        }
                    }
                    chatbot_files_table.html(code[1]).mcLoading(false);
                    chatbot_website_table.html(code[0]).mcLoading(false);
                    chatbot_area.find('#mc-chatbot-delete-website').setClass('mc-hide', !code[0]);
                });
                MCF.ajax({
                    function: 'open-ai-get-qea-training'
                }, (response) => {
                    for (var i = 0; i < response.length; i++) { // Deprecated
                        if (response[i][0] && !Array.isArray(response[i][0])) { // Deprecated
                            response[i][0] = [response[i][0]]; // Deprecated
                        } // Deprecated
                    } // Deprecated
                    let faq = response.map(item => ({
                        'open-ai-faq-questions': item[0] ? item[0].map(item => ({ question: item })) : [''],
                        'open-ai-faq-answer': item[1],
                        'open-ai-faq-function-calling-url': item[2],
                        'open-ai-faq-function-calling-method': item[3],
                        'open-ai-faq-function-calling-headers': item[4],
                        'open-ai-faq-function-calling-properties': item[5] && item[5].length ? item[5].map(item => ({ name: item[0], description: item[1], allowed: item[2] })) : [['', '', '']],
                        'open-ai-faq-set-data': item[6] && item[6].length ? item[6].map(item => ({ id: item[0], value: item[1] })) : [['', '']],
                    }));
                    if (faq.length) {
                        chatbot_qea_repeater.find('> div > .mc-repeater').html(MCSettings.repeater.set(faq, chatbot_qea_repeater.find('> div > .mc-repeater > .repeater-item:last-child')));
                        chatbot_qea_repeater.find('.mc-enlarger').each(function () {
                            let selects = $(this).find('select');
                            let no_value_keys = ['transcript', 'transcript_email', 'human_takeover', 'archive_conversation'];
                            if ($(this).find('input').val() || no_value_keys.includes(selects.val())) {
                                $(this).mcActive(true);
                                if ($(this).hasClass('mc-enlarger-function-calling')) {
                                    $(this).closest('.repeater-item').find('.mc-qea-repeater-answer').addClass('mc-hide');
                                }
                            }
                            selects.each(function () {
                                if (no_value_keys.includes($(this).val())) {
                                    $(this).parent().next().find('input').addClass('mc-hide');
                                }
                            });
                        });
                    }
                });
            }
        },

        messenger: {

            check: function (conversation) {
                return ['fb', 'ig'].includes(conversation.get('source'));
            },

            send: function (PSID, facebook_page_id, message = '', attachments = [], metadata, message_id = false, onSuccess = false) {
                MCF.ajax({
                    function: 'messenger-send-message',
                    psid: PSID,
                    facebook_page_id: facebook_page_id,
                    message: message,
                    message_id: message_id,
                    attachments: attachments,
                    metadata: metadata
                }, (response) => {
                    if (onSuccess) onSuccess(response);
                    MCApps.unsupportedRichMessages(message, 'Messenger');
                });
            }
        },

        whatsapp: {

            check: function (conversation) {
                return conversation.get('source') == 'wa';
            },

            send: function (to, message = '', attachments = [], phone_id = false, onSuccess = false) {
                MCF.ajax({
                    function: 'whatsapp-send-message',
                    to: to,
                    message: message,
                    attachments: attachments,
                    phone_id: phone_id
                }, (response) => {
                    if (response.error) {
                        infoPanel(response.error.message, 'info', false, 'error-wa');
                    }
                    if (onSuccess) {
                        onSuccess(response);
                    }
                    MCApps.unsupportedRichMessages(message, 'WhatsApp');
                });
            },

            activeUserPhone: function (user = activeUser()) {
                return user.getExtra('phone') ? user.getExtra('phone').value.replace('+', '') : false
            }
        },

        telegram: {

            check: function (conversation) {
                return conversation.get('source') == 'tg';
            },

            send: function (chat_id, message = '', attachments = [], conversation_id = false, onSuccess = false) {
                MCF.ajax({
                    function: 'telegram-send-message',
                    chat_id: chat_id,
                    message: message,
                    attachments: attachments,
                    conversation_id: conversation_id
                }, (response) => {
                    if (onSuccess) onSuccess(response);
                    MCApps.unsupportedRichMessages(message, 'Telegram');
                });
            }
        },

        viber: {

            check: function (conversation) {
                return conversation.get('source') == 'vb';
            },

            send: function (viber_id, message = '', attachments = [], onSuccess = false) {
                MCF.ajax({
                    function: 'viber-send-message',
                    viber_id: viber_id,
                    message: message,
                    attachments: attachments
                }, (response) => {
                    if (onSuccess) onSuccess(response);
                    MCApps.unsupportedRichMessages(message, 'Viber');
                });
            }
        },

        zalo: {

            check: function (conversation) {
                return conversation.get('source') == 'za';
            },

            send: function (zalo_id, message = '', attachments = [], onSuccess = false) {
                MCF.ajax({
                    function: 'zalo-send-message',
                    zalo_id: zalo_id,
                    message: message,
                    attachments: attachments
                }, (response) => {
                    if (onSuccess) onSuccess(response);
                    MCApps.unsupportedRichMessages(message, 'Zalo');
                });
            }
        },

        twitter: {

            check: function (conversation) {
                return conversation.get('source') == 'tw';
            },

            send: function (twitter_id, message = '', attachments = [], onSuccess = false) {
                MCF.ajax({
                    function: 'twitter-send-message',
                    twitter_id: twitter_id,
                    message: message,
                    attachments: attachments
                }, (response) => {
                    if (onSuccess) onSuccess(response);
                    MCApps.unsupportedRichMessages(message, 'Twitter');
                });
            }
        },

        line: {
            check: function (conversation) {
                return conversation.get('source') == 'ln';
            },

            send: function (line_id, message = '', attachments = [], conversation_id = false, onSuccess = false) {
                MCF.ajax({
                    function: 'line-send-message',
                    line_id: line_id,
                    message: message,
                    attachments: attachments,
                    conversation_id: conversation_id
                }, (response) => {
                    if (onSuccess) onSuccess(response);
                    MCApps.unsupportedRichMessages(message, 'LINE');
                });
            }
        },

        wechat: {
            token: false,

            check: function (conversation) {
                return conversation.get('source') == 'wc';
            },

            send: function (open_id, message = '', attachments = [], onSuccess = false) {
                MCF.ajax({
                    function: 'wechat-send-message',
                    open_id: open_id,
                    message: message,
                    attachments: attachments,
                    token: this.token
                }, (response) => {
                    if (Array.isArray(response)) {
                        this.token = response[1];
                        response = response[0];
                    }
                    if (onSuccess) onSuccess(response);
                    MCApps.unsupportedRichMessages(message, 'WeChat');
                });
            }
        },

        aecommerce: {

            conversationPanel: function () {
                let code = '';
                let aecommerce_id = activeUser().getExtra('aecommerce-id');
                if (!this.panel) this.panel = conversations_area.find('.mc-panel-aecommerce');
                if (aecommerce_id && !loading(this.panel)) {
                    MCF.ajax({
                        function: 'aecommerce-get-conversation-details',
                        aecommerce_id: aecommerce_id.value
                    }, (response) => {
                        code = `<h3>${MC_ADMIN_SETTINGS.aecommerce_panel_title}</h3><div><div class="mc-split"><div><div class="mc-title">${mc_('Number of orders')}</div><span>${response.orders_count} ${mc_('orders')}</span></div><div><div class="mc-title">${mc_('Total spend')}</div><span>${response.total} ${response.currency_symbol}</span></div></div><div class="mc-title">${mc_('Cart')}</div><div class="mc-list-items mc-list-links mc-aecommerce-cart">`;
                        for (var i = 0; i < response.cart.length; i++) {
                            let product = response.cart[i];
                            code += `<a href="${product.url}" target="_blank" data-id="${product.id}"><span>#${product.id}</span> <span>${product.name}</span> <span>x ${product.quantity}</span></a>`;
                        }
                        code += (response.cart.length ? '' : '<p>' + mc_('The cart is currently empty.') + '</p>') + '</div>';
                        if (response.orders.length) {
                            code += `<div class="mc-title">${mc_('Orders')}</div><div class="mc-list-items mc-list-links mc-aecommerce-orders">`;
                            for (var i = 0; i < response.orders.length; i++) {
                                let order = response.orders[i];
                                let id = order.id;
                                code += `<a data-id="${id}" href="${order.url}" target="_blank"><span>#${order.id}</span> <span>${MCF.beautifyTime(order.time, true)}</span> <span>${order.price} ${response.currency_symbol}</span></a>`;
                            }
                            code += '</div>';
                        }
                        $(this.panel).html(code).mcLoading(false);
                        collapse(this.panel, 160);
                    });
                }
                $(this.panel).html(code);
            }
        },

        martfury: {

            conversationPanel: function () {
                let code = '';
                let martfury_id = activeUser().getExtra('martfury-id');
                if (!this.panel) this.panel = conversations_area.find('.mc-panel-martfury');
                if (martfury_id && !loading(this.panel)) {
                    MCF.ajax({
                        function: 'martfury-get-conversation-details',
                        martfury_id: martfury_id.value
                    }, (response) => {
                        $(this.panel).html(response).mcLoading(false);
                        collapse(this.panel, 160);
                    });
                }
                $(this.panel).html(code);
            }
        },

        whmcs: {

            conversationPanel: function () {
                let code = '';
                let whmcs_id = activeUser().getExtra('whmcs-id');
                if (!this.panel) this.panel = conversations_area.find('.mc-panel-whmcs');
                if (whmcs_id && !loading(this.panel)) {
                    MCF.ajax({
                        function: 'whmcs-get-conversation-details',
                        whmcs_id: whmcs_id.value
                    }, (response) => {
                        let services = ['products', 'addons', 'domains'];
                        code = `<h3>WHMCS</h3><div><div class="mc-split"><div><div class="mc-title">${mc_('Number of services')}</div><span>${response.services_count} ${mc_('services')}</span></div><div><div class="mc-title">${mc_('Total spend')}</div><span>${response.total} ${response.currency_symbol}</span></div></div></div>`;
                        for (var i = 0; i < services.length; i++) {
                            let items = response[services[i]];
                            if (items.length) {
                                code += `<div class="mc-title">${mc_(MCF.slugToString(services[i]))}</div><div class="mc-list-items">`;
                                for (var j = 0; j < items.length; j++) {
                                    code += `<div>${items[j].name}</div>`;
                                }
                                code += '</div>';
                            }
                        }
                        code += `<a href="${MC_ADMIN_SETTINGS.whmcs_url}/clientssummary.php?userid=${response['client-id']}" target="_blank" class="mc-btn mc-whmcs-link">${mc_('View on WHMCS')}</a>`;
                        $(this.panel).html(code).mcLoading(false);
                        collapse(this.panel, 160);
                    });
                }
                $(this.panel).html(code);
            }
        },

        perfex: {

            conversationPanel: function () {
                let perfex_id = activeUser().getExtra('perfex-id');
                conversations_area.find('.mc-panel-perfex').html(perfex_id ? `<a href="${MC_ADMIN_SETTINGS.perfex_url}/admin/clients/client/${perfex_id.value}" target="_blank" class="mc-btn mc-perfex-link">${mc_('View on Perfex')}</a>` : '');
            }
        },

        ump: {

            conversationPanel: function () {
                if (loading(this.panel)) return;
                if (!this.panel) this.panel = conversations_area.find('.mc-panel-ump');
                let code = '';
                let subscriptions;
                MCF.ajax({
                    function: 'ump-get-conversation-details'
                }, (response) => {
                    subscriptions = response.subscriptions;
                    if (subscriptions.length) {
                        code = '<i class="mc-icon-refresh"></i><h3>Membership</h3><div class="mc-list-names">';
                        for (var i = 0; i < subscriptions.length; i++) {
                            let expired = subscriptions[i].expired;
                            code += `<div${expired ? ' class="mc-expired"' : ''}><span>${subscriptions[i].label}</span><span>${mc_(expired ? 'Expired on' : 'Expires on')} ${MCF.beautifyTime(subscriptions[i].expire_time, false, !expired)}</span></div>`;
                        }
                        code += `</div><span class="mc-title">${mc_('Total spend')} ${response.total} ${response.currency_symbol}</span>`;
                    }
                    $(this.panel).html(code).mcLoading(false);
                    collapse(this.panel, 160);
                });
            }
        },

        armember: {

            conversationPanel: function () {
                let wp_user_id = activeUser().getExtra('wp-id');
                if (!this.panel) this.panel = conversations_area.find('.mc-panel-armember');
                if (!MCF.null(wp_user_id) && !loading(this.panel)) {
                    let code = '';
                    let subscriptions;
                    wp_user_id = wp_user_id.value;
                    MCF.ajax({
                        function: 'armember-get-conversation-details',
                        wp_user_id: wp_user_id
                    }, (response) => {
                        subscriptions = response.subscriptions;
                        if (subscriptions.length) {
                            code = `<i class="mc-icon-refresh"></i><h3>${mc_('Plans')}</h3><div class="mc-list-names">`;
                            for (var i = 0; i < subscriptions.length; i++) {
                                let expired = subscriptions[i].expired;
                                code += `<div${expired ? ' class="mc-expired"' : ''}><span>${subscriptions[i].arm_current_plan_detail.arm_subscription_plan_name}</span><span>${subscriptions[i].expire_time == 'never' ? '' : (mc_(expired ? 'Expired on' : 'Expires on') + ' ' + MCF.beautifyTime(subscriptions[i].expire_time, false, !expired))}</span></div>`;
                            }
                            code += `</div><span class="mc-title">${mc_('Total spend')} ${response.total} ${response.currency_symbol}<a href="${window.location.href.substr(0, window.location.href.lastIndexOf('/')) + '?page=arm_manage_members&member_id=' + activeUser().getExtra('wp-id').value}" target="_blank" class="mc-btn-text"><i class="mc-icon-user"></i> ${mc_('View member')}</a></span>`;
                        }
                        $(this.panel).html(code).mcLoading(false);
                        collapse(this.panel, 160);
                    });
                } else $(this.panel).html('');
            }
        },

        zendesk: {

            conversationPanel: function () {
                if (!MC_ADMIN_SETTINGS.zendesk_active) return;
                let zendesk_id = activeUser().getExtra('zendesk-id');
                let phone = activeUser().getExtra('phone');
                let email = activeUser().get('email');
                let panel = conversations_area.find('.mc-panel-zendesk');
                if ((zendesk_id || phone || email) && !loading(panel)) {
                    MCF.ajax({
                        function: 'zendesk-get-conversation-details',
                        conversation_id: MCChat.conversation.id,
                        zendesk_id: zendesk_id ? zendesk_id.value : false,
                        phone: phone ? phone.value : false,
                        email: email,
                    }, (response) => {
                        $(panel).html(response).mcLoading(false);
                        panel.find('.mc-zendesk-date').each(function () {
                            $(this).html(MCF.beautifyTime($(this).html()));
                        });
                        collapse(panel, 160);
                    });
                } else $(panel).html('');
            }
        },

        woocommerce: {
            timeout: false,

            conversationPanel: function () {
                if (loading(this.panel)) {
                    return;
                }
                if (!this.panel) {
                    this.panel = conversations_area.find('.mc-panel-woocommerce');
                }
                let code = '';
                MCF.ajax({
                    function: 'woocommerce-get-conversation-details'
                }, (response) => {
                    code = `<i class="mc-icon-refresh"></i><h3>WooCommerce</h3><div><div class="mc-split"><div><div class="mc-title">${mc_('Number of orders')}</div><span>${response.orders_count} ${mc_('orders')}</span></div><div><div class="mc-title">${mc_('Total spend')}</div><span>${response.total} ${response.currency_symbol}</span></div></div><div class="mc-title">${mc_('Cart')}<i class="mc-add-cart-btn mc-icon-plus"></i></div><div class="mc-list-items mc-list-links mc-woocommerce-cart">`;
                    for (var i = 0; i < response.cart.length; i++) {
                        let product = response.cart[i];
                        code += `<a href="${product.url}" target="_blank" data-id="${product.id}"><span>#${product.id}</span> <span>${product.name}</span> <span>x ${product.quantity}</span><i class="mc-icon-close"></i></a>`;
                    }
                    code += (response.cart.length ? '' : '<p>' + mc_('The cart is currently empty.') + '</p>') + '</div>';
                    if (response.orders.length) {
                        code += `<div class="mc-title">${mc_('Orders')}</div><div class="mc-list-items mc-woocommerce-orders mc-accordion">`;
                        for (var i = 0; i < response.orders.length; i++) {
                            let order = response.orders[i];
                            let id = order.id;
                            code += `<div data-id="${id}"><span><span>#${id}</span> <span>${MCF.beautifyTime(order.date, true)}</span><a href="${SITE_URL}/wp-admin/post.php?post=${id}&action=edit" target="_blank" class="mc-icon-next"></a></span><div></div></div>`;
                        }
                        code += '</div>';
                    }
                    $(this.panel).html(code).mcLoading(false);
                    collapse(this.panel, 160);
                });
            },

            conversationPanelOrder: function (order_id) {
                let accordion = this.panel.find(`[data-id="${order_id}"] > div`);
                accordion.html('');
                MCF.ajax({
                    function: 'woocommerce-get-order',
                    order_id: order_id
                }, (response) => {
                    let code = '';
                    let collapse = this.panel.find('.mc-collapse-btn:not(.mc-active)');
                    if (response) {
                        let products = response.products;
                        code += `<div class="mc-title">${mc_('Order total')}: <span>${response.total} ${response.currency_symbol}<span></div><div class="mc-title">${mc_('Order status')}: <span>${MCF.slugToString(response.status.replace('wc-', ''))}<span></div><div class="mc-title">${mc_('Date')}: <span>${MCF.beautifyTime(response.date, true)}<span></div><div class="mc-title">${mc_('Products')}</div>`;
                        for (var i = 0; i < products.length; i++) {
                            code += `<a href="${SITE_URL}?p=${products[i].id}" target="_blank"><span>#${products[i].id}</span> <span>${products[i].quantity} x</span> <span>${products[i].name}</span></a>`;
                        }
                        for (var i = 0; i < 2; i++) {
                            let key = i == 0 ? 'shipping' : 'billing';
                            if (response[key + '_address']) {
                                code += `<div class="mc-title">${mc_((i == 0 ? 'Shipping' : 'Billing') + ' address')}</div><div class="mc-multiline">${response[key + '_address'].replace(/\\n/g, '<br>')}</div>`;
                            }
                        }
                    }
                    if (collapse.length) {
                        collapse.click();
                    }
                    accordion.html(code);
                });
            },

            conversationPanelUpdate: function (product_id, action = 'added') {
                let busy = false;
                let count = 0;
                this.timeout = setInterval(() => {
                    if (!busy) {
                        MCF.ajax({
                            function: 'woocommerce-get-conversation-details'
                        }, (response) => {
                            let removed = true;
                            for (var i = 0; i < response.cart.length; i++) {
                                if (response.cart[i].id == product_id) {
                                    if (action == 'added') {
                                        count = 61;
                                    } else {
                                        removed = false;
                                    }
                                }
                            }
                            if (count > 60 || removed) {
                                this.conversationPanel();
                                conversations_area.find('.mc-add-cart-btn,.mc-woocommerce-cart > a i').mcLoading(false);
                                clearInterval(this.timeout);
                            }
                            count++;
                            busy = false;
                        });
                        busy = true;
                    }
                }, 1000);
            }
        },

        opencart: {

            conversationPanel: function () {
                let panel = conversations_area.find('.mc-panel-opencart');
                let opencart_id = activeUser().getExtra('opencart_id');
                let store_url = activeUser().getExtra('opencart_store_url');
                if (!opencart_id) {
                    return panel.html('');
                }
                if (loading(panel)) {
                    return;
                }
                MCF.ajax({
                    function: 'opencart-panel',
                    opencart_id: opencart_id.value,
                    store_url: store_url ? store_url.value : false,
                }, (response) => {
                    panel.html(response).mcLoading(false);
                    collapse(this.panel, 160);
                });
            },

            openOrder: function (order_id) {
                MCF.ajax({
                    function: 'opencart-order-details',
                    order_id: order_id
                }, (response) => {
                    MCAdmin.infoPanel(response, 'info', false, 'opencart-order-details', mc_('Order') + ' #' + order_id, true);
                });
            }
        },

        wordpress: {
            ajax: function (action, data, onSuccess) {
                $.ajax({
                    method: 'POST',
                    url: MC_WP_AJAX_URL,
                    data: $.extend({ action: 'mc_wp_ajax', type: action }, data)
                }).done((response) => {
                    if (onSuccess !== false) {
                        onSuccess(response);
                    }
                });
            }
        },

        is: function (name) {
            if (typeof MC_VERSIONS == ND) {
                return false;
            }
            switch (name) {
                case 'opencart':
                case 'zendesk':
                case 'twitter':
                case 'wechat':
                case 'line':
                case 'viber':
                case 'zalo':
                case 'telegram':
                case 'armember':
                case 'aecommerce':
                case 'martfury':
                case 'whmcs':
                case 'perfex':
                case 'ump':
                case 'messenger':
                case 'whatsapp':
                case 'woocommerce':
                case 'dialogflow':
                case 'slack':
                case 'tickets': return typeof MC_VERSIONS[name] != ND && MC_VERSIONS[name];
                case 'wordpress': return typeof MC_WP != ND;
                case 'mc': return true;
            }
            return false;
        },

        unsupportedRichMessages: function (message, app_name, unsupported = []) {
            unsupported.push('timetable', 'registration', 'table', 'inputs');
            if (!['Messenger', 'WhatsApp'].includes(app_name)) {
                unsupported.push('email');
            }
            for (var i = 0; i < unsupported.length; i++) {
                if (message.includes('[' + unsupported[i])) {
                    infoBottom('The {R} rich message is not supported by {R2}. The rich message was not sent to {R2}.'.replace(/{R}/g, MCF.slugToString(unsupported[i])).replace(/{R2}/g, app_name), 'error');
                }
            }
        },

        getName: function (source) {
            let names = { fb: 'Facebook', wa: 'WhatsApp', tm: 'Text message', ig: 'Instagram', tg: 'Telegram', tk: 'Tickets', wc: 'WeChat', em: 'Email', tw: 'Twitter', bm: 'Business Messages', vb: 'Viber', ln: 'LINE', za: 'Zalo' };
            return source in names ? names[source] : source;
        },

        itemsPanel: {

            pagination_reference: 1,
            panel_language: '',

            code: function (items, app_name, label = true) {
                let code = '';
                for (var i = 0; i < items.length; i++) {
                    code += `<li data-id="${items[i].id.split('/').pop()}"><div class="mc-image" style="background-image:url('${items[i].image ? (app_name == 'shopify' ? items[i].image.replace('.jpg', '_small.jpg') : items[i].image) : MC_URL + '/media/thumb.svg'}')"></div><div><span>${items[i].name ? items[i].name : items[i].title}</span><span>${items[i].price ? items[i].price : items[i].variants.edges[0].node.price} ${MC_ADMIN_SETTINGS.currency}</span></div></li>`;
                }
                return label ? (code ? code : `<p class="mc-no-results">${mc_('No products found')}</p>`) : code;
            },

            getAppInfo: function (app_name) {
                switch (app_name) {
                    case 'woocommerce':
                        return { area: woocommerce_products_box, ul: woocommerce_products_box_ul, search: 'woocommerce-search-products', filter: 'woocommerce-get-products', populate: 'woocommerce-products-popup', pagination: 'woocommerce-get-products' };
                    case 'shopify':
                        return { area: MCCloud.shopify_products_box, ul: MCCloud.shopify_products_box_ul, search: 'shopify-get-products', filter: 'shopify-get-products', populate: 'shopify-get-products', pagination: 'shopify-get-products' };
                }
            },

            search: function (input, app_name) {
                let app_info = this.getAppInfo(app_name);
                searchInput(input, (search, icon) => {
                    if (search) {
                        this.pagination_reference = 1;
                        MCF.ajax({
                            function: app_info.search,
                            search: search
                        }, (response) => {
                            if (app_name == 'shopify') {
                                this.pagination_reference = response[1];
                                response = response[0];
                            }
                            this.getAppInfo(app_name).ul.html(this.code(response, app_name));
                            $(icon).mcLoading(false);
                        });
                    } else {
                        this.populate(app_name, function () {
                            $(icon).mcLoading(false);
                        });
                    }
                });
            },

            filter: function (item, app_name) {
                let app_info = this.getAppInfo(app_name);
                let value = $(item).data('value');
                if (loading(app_info.ul)) return;
                app_info.ul.html('');
                this.pagination_reference = 1;
                MCF.ajax({
                    function: app_info.filter,
                    user_language: this.panel_language,
                    filters: { taxonomy: value },
                    collection: value
                }, (response) => {
                    if (app_name == 'shopify') {
                        this.pagination_reference = response[1];
                        response = response[0];
                    }
                    app_info.ul.html(this.code(response, app_name)).mcLoading(false);
                });
            },

            populate: function (app_name, onSuccess = false) {
                let app_info = this.getAppInfo(app_name);
                this.panel_language = activeUser() && MC_ADMIN_SETTINGS.languages && MC_ADMIN_SETTINGS.languages.includes(activeUser().language) ? activeUser().language : '';
                this.pagination_reference = 1;
                app_info.ul.html('').mcLoading(true);
                MCF.ajax({
                    function: app_info.populate,
                    user_language: this.panel_language
                }, (response) => {
                    let code = '';
                    let select = app_info.area.find('.mc-select');
                    for (var i = 0; i < response[1].length; i++) {
                        code += `<li data-value="${response[1][i].id}">${response[1][i].name}</li>`;
                    }
                    if (response[2]) {
                        this.pagination_reference = response[2];
                    }
                    if (response[3]) {
                        MC_ADMIN_SETTINGS.currency = response[3];
                    }
                    select.find('> p').html(mc_('All'));
                    select.find('ul').html(`<li data-value="" class="mc-active">${mc_('All')}</li>` + code);
                    app_info.ul.html(this.code(response[0], app_name)).mcLoading(false);
                    if (onSuccess !== false) {
                        onSuccess();
                    }
                });
            },

            pagination: function (area, app_name) {
                let app_info = this.getAppInfo(app_name);
                let filter = $(area).parent().find('.mc-select p').attr('data-value');
                if (!this.pagination_reference) return;
                app_info.ul.mcLoading(area);
                MCF.ajax({
                    function: app_info.pagination,
                    filters: { taxonomy: filter },
                    collection: filter,
                    pagination: this.pagination_reference,
                    user_language: this.panel_language
                }, (response) => {
                    if (app_name == 'shopify') {
                        this.pagination_reference = response[1];
                        response = response[0];
                    } else {
                        this.pagination_reference++;
                    }
                    app_info.ul.append(this.code(response, app_name, false)).mcLoading(false);
                    if (!response.length) {
                        this.pagination_reference = 0;
                    }
                });
            }
        }
    }

    /*
    * ----------------------------------------------------------
    * Settings
    * ----------------------------------------------------------
    */

    var MCSettings = {
        init: false,

        save: function (btn = false) {
            if (btn && loading(btn)) return;
            let external_settings = {};
            let settings = {};
            let tab = settings_area.find(' > .mc-tab > .mc-nav .mc-active').attr('id');
            switch (tab) {
                case 'tab-automations':
                    let active = automations_area_nav.find('.mc-active').attr('data-id');
                    MCSettings.automations.save((response) => {
                        infoBottom(response === true ? 'Automations saved' : response);
                        MCSettings.automations.populate();
                        automations_area_nav.find(`[data-id="${active}"]`).click();
                        if (btn) {
                            $(btn).mcLoading(false);
                        }
                    });
                    break;
                case 'tab-translations':
                    this.translations.updateActive();
                    MCF.ajax({
                        function: 'save-translations',
                        translations: JSON.stringify(this.translations.to_update)
                    }, () => {
                        infoBottom('Translations saved');
                        if (btn) {
                            $(btn).mcLoading(false);
                        }
                    });
                    break;
                default:
                    settings_area.find('.mc-setting').each((i, element) => {
                        let setting = this.get(element);
                        let setting_id = $(element).data('setting');
                        if (setting[0]) {
                            if (typeof setting_id != ND) {
                                let originals = false;
                                if ($(element).find('[data-language]').length) {
                                    let language = $(element).find('[data-language].mc-active');
                                    originals = setting[0] in this.translations.originals ? this.translations.originals[setting[0]] : false;
                                    this.translations.save(element, language.length ? language.attr('data-language') : false);
                                    if (originals) {
                                        if (typeof originals != 'string') {
                                            for (var key in originals) {
                                                originals[key] = [originals[key], setting[1][key][1]]
                                            }
                                        }
                                    }
                                }
                                if (!(setting_id in external_settings)) external_settings[setting_id] = {};
                                external_settings[setting_id][setting[0]] = [originals ? originals : setting[1], setting[2]];
                            } else {
                                settings[setting[0]] = [setting[1], setting[2]];
                            }
                        }
                    });
                    MCF.ajax({
                        function: 'save-settings',
                        settings: JSON.stringify(settings),
                        external_settings: external_settings,
                        external_settings_translations: this.translations.translations
                    }, () => {
                        if (btn) {
                            infoBottom('Settings saved. Reload to apply the changes.');
                            $(btn).mcLoading(false);
                        }
                        MCF.event('MCSettingsSaved', { settings: settings, external_settings: external_settings, external_settings_translations: this.translations.translations });
                    });
                    break;
            }
        },

        get: function (item) {
            item = $(item);
            let id = item.attr('id');
            let type = item.data('type');
            switch (type) {
                case 'upload':
                case 'range':
                case 'number':
                case 'text':
                case 'password':
                case 'color':
                case 'upload-file':
                    return [id, item.find('input').val(), type];
                case 'textarea':
                    return [id, item.find('textarea').val(), type];
                case 'select':
                    return [id, item.find('select').val(), type];
                case 'checkbox':
                    return [id, item.find('input').is(':checked'), type];
                case 'radio':
                    let value = item.find('input:checked').val();
                    if (MCF.null(value)) {
                        value = '';
                    }
                    return [id, value, type];
                case 'upload-image':
                    let url = item.find('.image').attr('data-value');
                    if (MCF.null(url)) {
                        url = '';
                    }
                    return [id, url, type];
                case 'multi-input':
                    let multi_inputs = {};
                    item.find('.input > div').each((i, element) => {
                        let setting = this.get(element);
                        if (setting[0]) {
                            multi_inputs[setting[0]] = [setting[1], setting[2]];
                        }
                    });
                    return [id, multi_inputs, type];
                case 'select-images':
                    return [id, item.find('.input > .mc-active').data('value'), type];
                case 'repeater':
                    return [id, this.repeater.get(item.find('.repeater-item')), type];
                case 'double-select':
                    let selects = {};
                    item.find('.input > div').each(function () {
                        let value = $(this).find('select').val();
                        if (value != -1) {
                            selects[$(this).attr('data-id')] = [value];
                        }
                    });
                    return [id, selects, type];
                case 'select-checkbox':
                    return [id, item.find('.mc-select-checkbox input:checked').map(function () { return $(this).attr('id') }).get(), type];
                case 'timetable':
                    let times = {};
                    item.find('.mc-timetable > [data-day]').each(function () {
                        let day = $(this).attr('data-day');
                        let hours = [];
                        $(this).find('> div > div').each(function () {
                            let name = $(this).html()
                            let value = $(this).attr('data-value');
                            if (MCF.null(value)) {
                                hours.push(['', '']);
                            } else if (value == 'closed') {
                                hours.push(['closed', 'Closed']);
                            } else {
                                hours.push([value, name]);
                            }
                        });
                        times[day] = hours;
                    });
                    return [id, times, type];
                case 'color-palette':
                    return [id, item.attr('data-value'), type];
            }
            return ['', '', ''];
        },

        set: function (id, setting) {
            let type = $(setting)[1];
            let value = $(setting)[0];
            id = `#${id}`;
            switch (type) {
                case 'color':
                case 'upload':
                case 'number':
                case 'text':
                case 'password':
                case 'upload-file':
                    settings_area.find(`${id} input`).val(value);
                    break;
                case 'textarea':
                    settings_area.find(`${id} textarea`).val(value);
                    break;
                case 'select':
                    settings_area.find(`${id} select`).val(value);
                    break;
                case 'checkbox':
                    settings_area.find(`${id} input`).prop('checked', value == 'false' ? false : value);
                    break;
                case 'radio':
                    settings_area.find(`${id} input[value="${value}"]`).prop('checked', true);
                    break;
                case 'upload-image':
                    if (value) {
                        settings_area.find(id + ' .image').attr('data-value', value).css('background-image', `url("${value}")`);
                    }
                    break;
                case 'multi-input':
                    for (var key in value) {
                        this.set(key, value[key]);
                    }
                    break;
                case 'range':
                    let range_value = value;
                    settings_area.find(id + ' input').val(range_value);
                    settings_area.find(id + ' .range-value').html(range_value);
                    break;
                case 'select-images':
                    settings_area.find(id + ' .input > div').mcActive(false);
                    settings_area.find(id + ` .input > [data-value="${value}"]`).mcActive(true);
                    break;
                case 'select-checkbox':
                    for (var i = 0; i < value.length; i++) {
                        settings_area.find(`input[id="${value[i]}"]`).prop('checked', true);
                    }
                    settings_area.find(id + ' .mc-select-checkbox-input').val(value.join(', '));
                    break;
                case 'repeater':
                    let content = this.repeater.set(value, settings_area.find(id + ' .repeater-item:last-child'));
                    if (content) {
                        settings_area.find(id + ' .mc-repeater').html(content);
                    }
                    break;
                case 'double-select':
                    for (var key in value) {
                        settings_area.find(`${id} .input > [data-id="${key}"] select`).val(value[key]);
                    }
                    break;
                case 'timetable':
                    for (var key in value) {
                        let hours = settings_area.find(`${id} [data-day="${key}"] > div > div`);
                        for (var i = 0; i < hours.length; i++) {
                            $(hours[i]).attr('data-value', value[key][i][0]).html(value[key][i][1]);
                        }
                    }
                    break;
                case 'color-palette':
                    if (value) {
                        settings_area.find(id).attr('data-value', value);
                    }
                    break;
            }
        },

        repeater: {
            set: function (values, repeater_item_content) {
                var html = '';
                this.clear($(repeater_item_content));
                repeater_item_content = $(repeater_item_content).html();
                if (values.length) {
                    $(repeater_item_content).find('> .mc-icon-close').remove();
                    for (var i = 0; i < values.length; i++) {
                        let item = $($.parseHTML(`<div>${repeater_item_content}</div>`));
                        for (var key in values[i]) {
                            MCSettings.input.set(item.find(`[data-id="${key}"]`), values[i][key]);
                        }
                        html += `<div class="repeater-item">${item.html().replaceAll('<i class="mc-icon-close"></i>', '')}<i class="mc-icon-close"></i></div>`;
                    }
                }
                return html;
            },

            get: function (items) {
                let items_array = [];
                $(items).each(function () {
                    let item = {};
                    let empty = true;
                    $(this).find('[data-id]').removeClass('mc-exclude');
                    $(this).find('.mc-repeater [data-id]').addClass('mc-exclude');
                    $(this).find('[data-id]:not(.mc-exclude)').each(function () {
                        let value = MCSettings.input.get(this);
                        if (empty && value && $(this).attr('type') != 'hidden' && $(this).attr('data-type') != 'auto-id') {
                            empty = false;
                        }
                        item[$(this).attr('data-id')] = value;
                    });
                    if (!empty) {
                        items_array.push(item);
                    }
                });
                return items_array;
            },

            add: function (item) {
                let parent = $(item).parent();
                item = $($.parseHTML(`<div>${parent.find('> .mc-repeater > .repeater-item:last-child').html()}</div>`));
                this.clear(item);
                item.find('.repeater-item:not(:first-child)').remove();
                item.find('[data-id]').each(function () {
                    MCSettings.input.reset(this);
                    if ($(this).data('type') == 'auto-id') {
                        let larger = 1;
                        parent.find('[data-type="auto-id"]').each(function () {
                            let index = parseInt($(this).val());
                            if (index > larger) {
                                larger = index;
                            }
                        });
                        $(this).attr('value', larger + 1);
                    }
                });
                parent.find('> .mc-repeater').append(`<div class="repeater-item">${item.html()}</div>`);
            },

            delete: function (item) {
                let parent = $(item).parent();
                let cnt = parent.parent();
                if (cnt.parent().find('.mc-repeater-upload').length) {
                    MCF.ajax({ function: 'delete-file', path: parent.find('input').val() });
                }
                if (cnt.find('> .repeater-item').length > 1) {
                    parent.remove();
                } else {
                    parent.find('[data-id]:not([data-type="auto-id"]').each((e, element) => {
                        MCSettings.input.reset(element);
                    });
                }
            },

            clear: function (item) {
                item.find('.mc-active').mcActive(false);
                item.find('input:not([data-type="auto-id"]').removeAttr('value checked');
                item.find('option').removeAttr('selected');
                item.find('.mc-hide').removeClass('mc-hide');
            }
        },

        input: {
            set: function (input, value) {
                input = $(input);
                if (typeof value != 'object') {
                    value = $.trim(value);
                }
                if (input.is('select')) {
                    input.find(`option[value="${value}"]`).attr('selected', '');
                } else if (input.is(':checkbox') && value && value != 'false') {
                    input.attr('checked', '');
                } else if (input.is('textarea')) {
                    input.html(value);
                } else {
                    let div = input.is('div');
                    if (input.hasClass('mc-repeater')) {
                        input.html(MCSettings.repeater.set(value, '<div>' + input.find('> .repeater-item').eq(0).html() + '</div>').replaceAll('mc-icon-close', 'mc-icon-close mc-sub-repeater-close'));
                    } else if (div || input.is('i') || input.is('li')) {
                        input.attr('data-value', value);
                        if (div && input.hasClass('image')) {
                            input.css('background-image', value ? `url("${value}")` : '');
                        }
                    } else {
                        input.attr('value', value);
                    }
                }
            },

            get: function (input) {
                input = $(input);
                if (input.is(':checkbox')) {
                    return input.is(':checked');
                } else if (input.hasClass('mc-repeater')) {
                    return MCSettings.repeater.get(input.find('> .repeater-item'));
                } else if (input.is('div') || input.is('i') || input.is('li')) {
                    let value = input.attr('data-value');
                    return value ? value : '';
                } else {
                    return input.val();
                }
                return '';
            },

            reset: function (input) {
                input = $(input);
                if (input.is('select')) {
                    input.val('').find('[selected]').removeAttr('selected');
                } else if (input.is(':checkbox')) {
                    input.removeAttr('checked').prop('checked', false);
                } else if (input.is('textarea')) {
                    input.val('');
                    input.html('');
                } else if (input.hasClass('mc-repeater')) {
                    input.find('.repeater-item:not(:first-child)').remove();
                } else {
                    input.removeAttr('value style data-value').val('');
                }
            }
        },

        initColorPicker: function (area = false) {
            $(area ? area : settings_area).find('.mc-type-color input').colorPicker({
                renderCallback: function (t, toggled) {
                    $(t.context).closest('.input').find('input').css('background-color', t.text);
                }
            });
        },

        getSettingObject: function (setting) {
            return $(setting)[0].hasAttribute('data-setting') ? $(setting) : $(setting).closest('[data-setting]');
        },

        visibility: function (index, visible) {
            let selectors = [['#push-notifications-onesignal-sw-url, #push-notifications-onesignal-app-id, #push-notifications-onesignal-api-key, #push-notifications-sw-path', '#push-notifications-id, #push-notifications-key'], ['#messenger-key, #messenger-path-btn', '#messenger-sync-btn'], ['#open-ai-assistant-id', '#open-ai-prompt,#open-ai-model, #open-ai-tokens, #open-ai-temperature, #open-ai-presence-penalty, #open-ai-frequency-penalty, #open-ai-logit-bias, #open-ai-custom-model, #open-ai-source-links']];
            settings_area.find(selectors[index][0]).mcActive(!visible);
            settings_area.find(selectors[index][1]).setClass('mc-hide', !visible);
        },

        open: function (id, scroll = false) {
            header.find('.mc-admin-nav #mc-settings').click();
            if (scroll) {
                setTimeout(() => {
                    settings_area.find('#tab-' + id).click().get(0).scrollIntoView();
                }, 300);
            }
        },

        automations: {
            items: { messages: [], emails: [], sms: [], popups: [], design: [], more: [] },
            translations: {},

            conditions: function () {
                let list = {
                    datetime: ['Date time', ['Is between', 'Is exactly'], 'dd/mm/yyy hh:mm - dd/mm/yyy hh:mm'],
                    repeat: ['Repeat', ['Every day', 'Every week', 'Every month', 'Every year']],
                    browsing_time: ['Browsing time', [], 'seconds'],
                    scroll_position: ['Scroll position', [], 'px'],
                    url: ['Current URL', ['Contains', 'Does not contain'], 'URLs parts separated by commas'],
                    referring: ['Referring URL', ['Contains', 'Does not contain'], 'URLs parts separated by commas'],
                    user_type: ['User type', ['Is visitor', 'Is lead', 'Is user', 'Is not visitor', 'Is not lead', 'Is not user']],
                    returning_visitor: ['Returning visitor', ['First time visitor', 'Returning visitor']],
                    countries: ['Country', ['Is included', 'Is not included', 'Is set', 'Is not set'], 'Country codes separated by commas'],
                    languages: ['Language', ['Is included', 'Is not included', 'Is set', 'Is not set'], 'Language codes separated by commas'],
                    cities: ['City', ['Is included', 'Is not included', 'Is set', 'Is not set'], 'Cities separated by commas'],
                    website: ['Website', ['Contains', 'Does not contain', 'Is set', 'Is not set'], 'URLs parts separated by commas'],
                    birthdate: ['Birthdate', ['Is between', 'Is exactly', 'Is set', 'Is not set'], 'dd/mm - dd/mm'],
                    company: ['Company', ['Is included', 'Is not included', 'Is set', 'Is not set'], 'Company names separated by commas'],
                    postal_code: ['Postal code', ['Is included', 'Is not included', 'Is set', 'Is not set'], 'Postal codes separated by commas'],
                    email: ['Email', ['Contains', 'Does not contain', 'Is set', 'Is not set'], 'Email addresses separated by commas'],
                    phone: ['Phone', ['Contains', 'Does not contain', 'Is set', 'Is not set'], 'Phone numbers separated by commas'],
                    creation_time: ['Creation time', ['Is between', 'Is exactly'], 'dd/mm/yyy hh:mm - dd/mm/yyy hh:mm'],
                    custom_variable: ['Custom variable', [], 'variable=value']
                };
                let user_extra_details = MCUsers.getExtraDetailsList(true);
                for (var i = 0; i < user_extra_details.length; i++) {
                    list[user_extra_details[i][0]] = [user_extra_details[i][1], ['Contains', 'Does not contain', 'Is set', 'Is not set'], 'Values separated by commas'];
                }
                return list;
            },

            get: function (onSuccess) {
                MCF.ajax({
                    function: 'automations-get'
                }, (response) => {
                    this.items = response[0];
                    this.translations = Array.isArray(response[1]) && !response[1].length ? {} : response[1];
                    onSuccess(response);
                });
            },

            save: function (onSuccess = false) {
                this.updateActiveItem();
                MCF.ajax({
                    function: 'automations-save',
                    automations: this.items,
                    translations: this.translations
                }, (response) => {
                    if (onSuccess) onSuccess(response);
                });
            },

            show: function (id = false, language = false) {
                this.updateActiveItem();
                let items = language ? (language in this.translations ? this.translations[language] : []) : this.items;
                let area = automations_area.find(' > .mc-tab > .mc-content');
                if (id === false) id = this.activeID();
                this.hide(false);
                for (var key in items) {
                    for (var i = 0; i < items[key].length; i++) {
                        let item = items[key][i];
                        if (item.id == id) {
                            for (var key in item) {
                                let element = area.find(`[data-id="${key}"]`);
                                if (element.hasClass('image')) {
                                    element.css('background-image', `url(${item[key]})`).attr('data-value', item[key]);
                                    if (!item[key]) {
                                        element.removeAttr('data-value');
                                    }
                                } else if (element.attr('type') == 'checkbox') {
                                    element.prop('checked', item[key]);
                                } else {
                                    element.val(item[key]);
                                }
                            }
                            this.setConditions(item.conditions, conditions_area);
                            conditions_area.parent().setClass('mc-hide', language);
                            area.mcLanguageSwitcher(this.getTranslations(id), 'automations', language);
                            return true;
                        }
                    }
                }
                return false;
            },

            add: function () {
                let id = MCF.random();
                let name = `${mc_('Item')} ${automations_area_nav.find('li:not(.mc-no-results)').length + 1}`;
                this.updateActiveItem();
                this.items[this.activeType()].push(this.itemArray(this.activeType(), id, name));
                this.hide(false);
                automations_area_nav.find('.mc-active').mcActive(false);
                automations_area_nav.find('.mc-no-results').remove();
                automations_area_nav.append(`<li class="mc-active" data-id="${id}">${name}<i class="mc-icon-delete"></i></li>`);
                automations_area.find('.mc-automation-values').find('input, textarea').val('');
                automations_area.mcLanguageSwitcher([], 'automations');
                conditions_area.html('');
            },

            delete: function (element) {
                this.items[this.activeType()].splice($(element).parent().index(), 1);
                $(element).parent().remove();
                this.hide();
                if (this.items[this.activeType()].length == 0) automations_area_nav.html(`<li class="mc-no-results">${mc_('No results found.')}</li>`);
            },

            populate: function (type = false) {
                if (type === false) {
                    type = this.activeType();
                }
                let code = '';
                let items = this.items[type];
                this.updateActiveItem();
                if (items.length) {
                    for (var i = 0; i < items.length; i++) {
                        code += `<li data-id="${items[i].id}">${items[i].name}<i class="mc-icon-delete"></i></li>`;
                    }
                } else {
                    code = `<li class="mc-no-results">${mc_('No results found.')}</li>`;
                }
                automations_area_nav.html(code);
                code = '';
                switch (type) {
                    case 'emails':
                        code = `<h2>${mc_('Subject')}</h2><div class="mc-setting mc-type-text"><div><input data-id="subject" type="text"></div></div>`;
                        break;
                    case 'popups':
                        code = `<h2>${mc_('Title')}</h2><div class="mc-setting mc-type-text"><div><input data-id="title" type="text"></div></div><h2>${mc_('Profile image')}</h2><div data-type="upload-image" class="mc-setting mc-type-upload-image"><div class="input"><div data-id="profile_image" class="image"><i class="mc-icon-close"></i></div></div></div><h2>${mc_('Message fallback')}</h2><div class="mc-setting mc-type-checkbox"><div><input data-id="fallback" type="checkbox"></div></div>`;
                        break;
                    case 'design':
                        code = `<h2>${mc_('Header title')}</h2><div class="mc-setting mc-type-text"><div><input data-id="title" type="text"></div></div>`;
                        for (var i = 1; i < 4; i++) {
                            code += `<h2>${mc_((i == 1 ? 'Primary' : (i == 2 ? 'Secondary' : 'Tertiary')) + ' color')}</h2><div data-type="color" class="mc-setting mc-type-color"><div class="input"><input data-id="color_${i}" type="text"><i class="mc-close mc-icon-close"></i></div></div>`;
                        }
                        for (var i = 1; i < 4; i++) {
                            code += `<h2>${mc_(i == 1 ? 'Header background image' : (i == 2 ? 'Header brand image' : 'Chat button icon'))}</h2><div data-type="upload-image" class="mc-setting mc-type-upload-image"><div class="input"><div data-id="${i == 1 ? 'background' : (i == 2 ? 'brand' : 'icon')}" class="image"><i class="mc-icon-close"></i></div></div></div>`;
                        }
                        break;
                    case 'more':
                        code = `<h2>${mc_('Department ID')}</h2><div class="mc-setting mc-type-number"><div><input data-id="department" type="number"></div></div><h2>${mc_('Agent ID')}</h2><div class="mc-setting mc-type-number"><div><input data-id="agent" type="number"></div></div><h2>${mc_('Tags')}</h2><div class="mc-setting mc-type-text"><div><input data-id="tags" type="text"></div></div><h2>${mc_('Article IDs')}</h2><div class="mc-setting mc-type-number"><div><input data-id="articles" type="text"></div></div><h2>${mc_('Articles category')}</h2><div class="mc-setting mc-type-number"><div><input data-id="articles_category" type="text"></div></div>`;
                        break;
                }
                automations_area.find('.mc-automation-extra').html(code);
                automations_area.attr('data-automation-type', type);
                MCSettings.initColorPicker(automations_area);
                this.hide();
            },

            updateActiveItem: function () {
                let id = this.activeID();
                if (id) {
                    let language = automations_area.find(`.mc-language-switcher [data-language].mc-active`).attr('data-language');
                    let type = this.activeType();
                    let items = language ? (language in this.translations ? this.translations[language][type] : []) : this.items[type];
                    for (var i = 0; i < items.length; i++) {
                        if (items[i].id == id) {
                            items[i] = { id: id, conditions: [] };
                            automations_area.find('.mc-automation-values').find('input,textarea,[data-type="upload-image"] .image').each(function () {
                                items[i][$(this).attr('data-id')] = $(this).hasClass('image') && $(this)[0].hasAttribute('data-value') ? $(this).attr('data-value') : ($(this).attr('type') == 'checkbox' ? $(this).is(':checked') : $(this).val());
                            });
                            items[i].conditions = this.getConditions(conditions_area);
                            if (MCF.null(items[i].name)) {
                                this.delete(automations_area_nav.find(`[data-id="${id}"] i`));
                            }
                            break;
                        }
                    }
                }
            },

            getConditions: function (conditions_area) {
                let conditions = [];
                conditions_area.find(' > div').each(function () {
                    let condition = [];
                    $(this).find('input,select').each(function () {
                        condition.push($(this).val());
                    });
                    if (condition[0] && condition[1] && (condition.length == 2 || condition[2] || ['is-set', 'is-not-set'].includes(condition[1]))) {
                        conditions.push(condition);
                    }
                });
                return conditions;
            },

            setConditions: function (conditions, conditions_area) {
                conditions_area.html('');
                if (conditions) {
                    for (var key in conditions) {
                        this.addCondition(conditions_area);
                        let condition = conditions_area.find(' > div:last-child');
                        condition.find('select').val(conditions[key][0]);
                        this.updateCondition(condition.find('select'));
                        condition.find(' > div').eq(1).find('select,input').val(conditions[key][1]);
                        if (conditions[key].length > 2) {
                            if (['is-set', 'is-not-set'].includes(conditions[key][1])) {
                                condition.find(' > div').eq(2).addClass('mc-hide');
                            } else {
                                condition.find(' > div').eq(2).find('input').val(conditions[key][2]);
                            }
                        }
                    }
                }
            },

            addCondition: function (conditions_area) {
                conditions_area.append(`<div><div class="mc-setting mc-type-select mc-condition-1"><select>${this.getAvailableConditions()}</select></div></div>`);
            },

            updateCondition: function (element) {
                $(element).parent().siblings().remove();
                let parent = $(element).parents().eq(1);
                if ($(element).val()) {
                    let condition = this.conditions()[$(element).val()];
                    let code = '';
                    if (condition[1].length) {
                        code = '<div class="mc-setting mc-type-select mc-condition-2"><select>';
                        for (var i = 0; i < condition[1].length; i++) {
                            code += `<option value="${MCF.stringToSlug(condition[1][i])}">${mc_(condition[1][i])}</option>`;
                        }
                        code += '</select></div>';
                    }
                    parent.append(code + (condition.length > 2 ? `<div class="mc-setting mc-type-text"><input placeholder="${mc_(condition[2])}" type="text"></div>` : ''));
                    parent.siblings().find('.mc-condition-1 select').each(function () {
                        let value = $(this).val();
                        $(this).html(MCSettings.automations.getAvailableConditions([value]));
                        $(this).val(value);
                    });
                } else {
                    parent.remove();
                }
            },

            getAvailableConditions: function (include = [], exclude = []) {
                let code = '<option value=""></option>';
                let existing_conditions = [];
                let conditions = this.conditions();
                conditions_area.find('.mc-condition-1 select').each(function () {
                    existing_conditions.push($(this).val());
                });
                for (var key in conditions) {
                    if (!exclude.includes(key) && (!existing_conditions.includes(key) || include.includes(key))) {
                        code += `<option value="${key}">${mc_(conditions[key][0])}</option>`;
                    }
                }
                return code;
            },

            addTranslation: function (id = false, type = false, language) {
                if (id === false) {
                    id = this.activeID();
                }
                if (type === false) {
                    type = this.activeType();
                }
                if (this.getTranslations(id).includes(id)) {
                    return console.warn('Automation translation already in array.');
                }
                if (!(language in this.translations)) {
                    this.translations[language] = { messages: [], emails: [], sms: [], popups: [], design: [] };
                }
                if (!(type in this.translations[language])) {
                    this.translations[language][type] = [];
                }
                this.translations[language][type].push(this.itemArray(type, id));
            },

            getTranslations: function (id = false) {
                let translations = [];
                if (id === false) id = this.activeID();
                for (var key in this.translations) {
                    let types = this.translations[key];
                    for (var key2 in types) {
                        let items = types[key2];
                        for (var i = 0; i < items.length; i++) {
                            if (items[i].id == id) {
                                translations.push(key);
                                break;
                            }
                        }
                    }
                }
                return translations;
            },

            deleteTranslation: function (id = false, language) {
                if (id === false) id = this.activeID();
                if (language in this.translations) {
                    let types = this.translations[language];
                    for (var key in types) {
                        let items = types[key];
                        for (var i = 0; i < items.length; i++) {
                            if (items[i].id == id) {
                                this.translations[language][key].splice(i, 1);
                                return true;
                            }
                        }
                    }
                }
                return false;
            },

            activeID: function () {
                let item = automations_area_nav.find('.mc-active');
                return item.length ? item.attr('data-id') : false;
            },

            activeType: function () {
                return automations_area_select.find('li.mc-active').data('value');
            },

            itemArray: function (type, id, name = '', message = '') {
                return $.extend({ id: id, name: name, message: message }, type == 'emails' ? { subject: '' } : (type == 'popups' ? { title: '', profile_image: '' } : (type == 'design' ? { title: '', color_1: '', color_2: '', color_3: '', background: '', brand: '', icon: '' } : {})));
            },

            hide: function (hide = true) {
                automations_area.find(' > .mc-tab > .mc-content').setClass('mc-hide', hide);
            }
        },

        translations: {
            translations: {},
            originals: {},
            to_update: {},

            add: function (language) {
                let setting = MCSettings.getSettingObject(language_switcher_target);
                let setting_id = setting.attr('id');
                let active_language = language_switcher_target.find('[data-language].mc-active');
                this.save(setting, active_language.length ? active_language.attr('data-language') : false);
                setting.find('textarea,input[type="text"]').val('');
                this.save(setting, language);
                language_switcher_target.remove();
                setting.mcLanguageSwitcher(this.getLanguageCodes(setting_id), 'settings', language);
            },

            delete: function (setting, language) {
                setting = MCSettings.getSettingObject(setting);
                let setting_id = setting.attr('id');
                delete this.translations[language][setting_id];
                setting.find(`.mc-language-switcher [data-language="${language}"]`).remove();
                this.activate(setting);
            },

            activate: function (setting, language = false) {
                setting = MCSettings.getSettingObject(setting);
                let setting_id = setting.attr('id');
                let values = language ? this.translations[language][setting_id] : this.originals[setting_id];
                if (isString(values)) {
                    setting.find('input, textarea').val(values);
                } else {
                    for (var key in values) {
                        setting.find('#' + key).find('input, textarea').val(isString(values[key]) ? values[key] : values[key][0]);
                    }
                }
            },

            updateActive: function () {
                let area = settings_area.find('.mc-translations-list')
                let translations = { 'front': {}, 'admin': {}, 'admin/js': {}, 'admin/settings': {} };
                let language_code = area.attr('data-value');
                if (MCF.null(language_code)) return;
                for (var key in translations) {
                    area.find(' > [data-area="' + key + '"] .mc-setting:not(.mc-new-translation)').each(function () {
                        translations[key][$(this).find('label').html()] = $(this).find('input').val();
                    });
                    area.find('> [data-area="' + key + '"] .mc-new-translation').each(function () {
                        let original = $(this).find('input:first-child').val();
                        let value = $(this).find('input:last-child').val();
                        if (original && value) {
                            translations[key][original] = value;
                        }
                    });
                }
                this.to_update[language_code] = translations;
            },

            save: function (setting, language = false) {
                setting = MCSettings.getSettingObject(setting);
                let values = {};
                let setting_id = $(setting).attr('id');
                if (setting.data('type') == 'multi-input') {
                    setting.find('.multi-input-textarea,.multi-input-text').each(function () {
                        values[$(this).attr('id')] = $(this).find('input, textarea').val();
                    });
                } else {
                    values = setting.find('input, textarea').val();
                }
                if (language) {
                    if (!(language in this.translations)) {
                        this.translations[language] = {};
                    }
                    this.translations[language][setting_id] = values;
                } else {
                    this.originals[setting_id] = values;
                }
            },

            load: function (language_code) {
                let area = settings_area.find('.mc-translations > .mc-content');
                area.find(' > .mc-hide').removeClass('mc-hide');
                this.updateActive();
                MCF.ajax({
                    function: 'get-translation',
                    language_code: language_code
                }, (translations) => {
                    if (language_code in this.to_update) translations = this.to_update[language_code];
                    let code = '';
                    let areas = ['front', 'admin', 'admin/js', 'admin/settings'];
                    for (var i = 0; i < areas.length; i++) {
                        let translations_area = translations[areas[i]];
                        code += `<div${!i ? ' class="mc-active"' : ''} data-area="${areas[i]}">`;
                        for (var key in translations_area) {
                            code += `<div class="mc-setting mc-type-text"><label>${key}</label><div><input type="text" value="${translations_area[key]}"></div></div>`;
                        }
                        code += '</div>';
                    }
                    area.find('.mc-translations-list').attr('data-value', language_code).html(code);
                    area.find('.mc-menu-wide li').mcActive(false).eq(0).mcActive(true);
                    area.mcLoading(false);
                });
                area.mcLoading(true);
            },

            getLanguageCodes: function (setting_id) {
                let languages = [];
                for (var key in this.translations) {
                    if (setting_id in this.translations[key]) {
                        languages.push(key);
                    }
                }
                return languages;
            }
        }
    }

    /*
    * ----------------------------------------------------------
    * Articles
    * ----------------------------------------------------------
    */

    var MCArticles = {
        category_list: [],
        page_url: false,

        get: function (onSuccess, article_id = false, categories = false, full = true, language = false) {
            MCF.ajax({
                function: 'get-articles',
                id: article_id,
                categories: categories,
                articles_language: language,
                full: full
            }, (response) => {
                onSuccess(response);
            });
        },

        save: function (onSuccess = false) {
            let id = this.activeID();
            let article;
            article = { id: id, title: articles_area.find('.mc-article-title input').val(), content: articles_area.find('.mc-article-content textarea').val(), link: articles_area.find('.mc-article-link input').val(), parent_category: articles_category_parent_select.val(), category: articles_category_select.val(), language: articles_area.find('.mc-language-switcher [data-language].mc-active').attr('data-language') };
            if (!article.title && !article.content) {
                return onSuccess(false);
            }
            if (article.language) {
                article.parent_id = this.activeID(true);
            }
            if (editor_js && typeof editor_js.save !== ND) {
                editor_js.save().then((outputData) => {
                    article.editor_js = outputData;
                    article.content = editorJSHTML(outputData.blocks);
                    this.save_2(article, onSuccess);
                }).catch((error) => {
                    console.log(error);
                });
            } else {
                this.save_2(article, onSuccess);
            }
        },

        save_2: function (article, onSuccess = false) {
            MCF.ajax({
                function: 'save-article',
                article: JSON.stringify(article)
            }, (response) => {
                let is_number = response !== true && !isNaN(response);
                articles_save_required = false;
                if (is_number) {
                    articles_content.attr('data-id', response);
                    article.id = response;
                    this.viewButton(response)
                    if (article.language) {
                        let translations = MCArticles.translations.get(article.parent_id);
                        articles_content.find(`.mc-language-switcher [data-language="${article.language}"]`).attr('data-id', response);
                        for (var i = 0; i < translations.length; i++) {
                            if (translations[i][0] == article.language) {
                                translations[i][1] = article.id;
                                MCArticles.translations.list[article.parent_id] = translations;
                                break;
                            }
                        }
                    }
                }
                if (!article.language) {
                    articles_area.find('.ul-articles .mc-active').html(article.title + '<i class="mc-icon-delete"></i>').attr('data-id', article.id);
                }
                if (onSuccess) {
                    onSuccess(response);
                }
                infoBottom(response === true || is_number ? 'Article saved' : response);
            });
        },

        show: function (article_id) {
            if (!article_id) {
                return;
            }
            loading(articles_content);
            this.get((response) => {
                articles_content.mcLoading(false);
                response = response[0];
                articles_content.mcLanguageSwitcher(this.translations.get(response.parent_id ? response.parent_id : article_id), 'articles', response.language);
                articles_content.attr('data-id', article_id);
                articles_area.find('.mc-article-title input').val(response.title);
                articles_area.find('.mc-article-link input').val(response.link);
                articles_area.find('#mc-article-id').html(`ID <span>${article_id}</span>`);
                articles_area.find('.mc-article-categories').setClass('mc-hide', response.language);
                if (editor_js || articles_area.find('#editorjs').length) {
                    editorJSLoad(response.editor_js ? (isString(response.editor_js) ? JSON.parse(response.editor_js) : response.editor_js) : response.content);
                } else {
                    articles_area.find('.mc-article-content textarea').val(response.content);
                }
                if (!response.language) {
                    articles_category_parent_select.val(response.parent_category);
                    articles_category_select.val(response.category);
                }
                this.viewButton(article_id)
                articles_save_required = false;
            }, article_id);
        },

        add: function () {
            let nav = articles_area.find('.ul-articles');
            nav.find('.mc-active').mcActive(false);
            nav.append(`<li class="mc-active"></li>`);
            articles_content.mcLanguageSwitcher([], 'articles');
            this.clear();
        },

        clear: function () {
            articles_content.removeAttr('data-id').removeClass('mc-hide');
            articles_content.find('input, textarea, select').val('');
            articles_content.find('input').prop('checked', false);
            editorJSLoad();
            this.viewButton();
            articles_save_required = false;
        },

        delete: function (article_id, onSuccess = false) {
            MCF.ajax({
                function: 'save-article',
                article: JSON.stringify({ id: article_id, delete: true })
            }, (response) => {
                this.clear();
                if (onSuccess) {
                    onSuccess(response);
                }
            });
        },

        populate: function (items, is_category = false) {
            let code = '';
            for (var i = 0; i < items.length; i++) {
                code += `<li data-id="${items[i].id}">${items[i].title}<i class="mc-icon-delete"></i></li>`;
            }
            articles_area.find(is_category ? '.ul-categories' : '.ul-articles').html(code);
            articles_area.find(is_category ? '.ul-categories > li' : '.ul-articles > li').eq(0).click();
            if (!items.length) {
                $(is_category ? articles_content_categories : articles_content).mcLoading(false).addClass('mc-hide');
            }
        },

        activeID: function (is_parent = false) {
            return is_parent ? articles_area.find('.ul-articles .mc-active').attr('data-id') : articles_content.attr('data-id');
        },

        viewButton: function (article_id = false) {
            if (this.page_url) {
                let button = articles_area.find('.mc-view-article');
                button.attr('href', article_id ? this.page_url + (this.is_url_rewrite ? (this.page_url.charAt(this.page_url.length - 1) == '/' ? '' : '/') + (this.cloud_chat_id ? this.cloud_chat_id + '/' : '') : '?article_id=') + article_id : '');
            }
        },

        categories: {
            list: [],

            save: function (onSuccess = false) {
                this.updateActive();
                MCF.ajax({
                    function: 'save-articles-categories',
                    categories: JSON.stringify(this.list)
                }, (response) => {
                    if (onSuccess) {
                        onSuccess(response);
                    }
                    infoBottom(response === true ? 'Categories saved' : response);
                })
            },

            show: function (category_id, language = false) {
                let index = this.getIndex(category_id);
                if (index !== false) {
                    let category = language ? this.list[index].languages[language] : this.list[index];
                    let image = articles_content_categories.find('#category-image');
                    this.updateActive();
                    articles_content_categories.find('#category-title').val(category.title);
                    articles_content_categories.find('#category-description').val(category.description);
                    articles_content_categories.find('#category-parent').prop('checked', category.parent ? true : false);
                    articles_content_categories.mcLanguageSwitcher($.map(this.list[index].languages, function (e, index) { return index }), 'article-categories', language);
                    if (category.image) {
                        image.attr('data-value', category.image).css('background-image', `url("${category.image}")`);
                    } else {
                        image.removeAttr('data-value style');
                    }
                    articles_content_categories.find('.category-parent').setClass('mc-hide', language);
                }
                articles_content_categories.mcLoading(false);
            },

            add: function () {
                let category_id = MCF.random();
                this.list.push({ id: category_id, title: '', description: '', image: '', languages: [] });
                let code = `<li data-id="${category_id}">${mc_('New category')}<i class="mc-icon-delete"></i></li>`;
                articles_area.find('.ul-categories').append(code);
                articles_area.find('.ul-categories li').eq(articles_area.find('.ul-categories li').length - 1).click();
                articles_content_categories.removeClass('mc-hide');
            },

            delete: function (category_id) {
                let index = this.getIndex(category_id);
                let ul = articles_area.find('.ul-categories');
                if (index !== false) {
                    this.list.splice(index, 1);
                    ul.find(`[data-id="${category_id}"]`).remove();
                    ul.find('li').eq(0).click();
                    return true;
                }
                return false;
            },

            update: function () {
                let selected_category = articles_category_select.val();
                let selected_category_parent = articles_category_parent_select.val();
                let code = ['', '<option></option>'];
                let ids = this.list.map(function (item) {
                    return item.id;
                });
                for (var i = 0; i < this.list.length; i++) {
                    code[this.list[i].parent ? 0 : 1] += `<option value="${this.list[i].id}">${this.list[i].title}</option>`;
                }
                articles_category_parent_select.html(code[0]);
                articles_category_select.html(code[1]);
                if (this.list.length) {
                    articles_category_parent_select.val(ids.includes(selected_category_parent) ? selected_category_parent : (articles_category_parent_select[0].selectedIndex > -1 ? this.list[articles_category_parent_select[0].selectedIndex].id : ''));
                    articles_category_select.val(ids.includes(selected_category) ? selected_category : (articles_category_select[0].selectedIndex > -1 ? this.list[articles_category_select[0].selectedIndex].id : ''));
                }
            },

            updateActive: function () {
                let id = this.activeID();
                if (id) {
                    let index = this.getIndex(id);
                    let language = articles_content_categories.find('.mc-language-switcher .mc-active').attr('data-language');
                    let category = { title: articles_content_categories.find('#category-title').val(), description: articles_content_categories.find('#category-description').val(), image: articles_content_categories.find('#category-image').attr('data-value') }
                    if (language) {
                        this.list[index].languages[language] = category;
                    } else {
                        category.id = MCF.stringToSlug(category.title);
                        category.parent = articles_content_categories.find('#category-parent').is(':checked');
                        category.languages = this.list[index].languages;
                        this.list[index] = category;
                        articles_area.find('.ul-categories .mc-active').html(category.title + '<i class="mc-icon-delete"></i>').attr('data-id', category.id);
                    }
                }
            },

            clear: function () {
                articles_content_categories.find('input, textarea').val('');
                articles_content_categories.find('#category-image').removeAttr('data-value style');
            },

            getIndex: function (category_id) {
                for (var i = 0; i < this.list.length; i++) {
                    if (this.list[i].id == category_id) {
                        return i;
                    }
                }
                return false;
            },

            activeID: function () {
                return articles_area.find('.ul-categories .mc-active').attr('data-id');
            },

            translations: {

                add: function (language_code, category_id = false) {
                    MCArticles.categories.updateActive();
                    if (!category_id) {
                        category_id = MCArticles.categories.activeID();
                    }
                    let index = MCArticles.categories.getIndex(category_id);
                    if (MCF.null(MCArticles.categories.list[index].languages)) MCArticles.categories.list[index].languages = {}; // Deprecated
                    MCArticles.categories.list[index].languages[language_code] = { title: '', description: '', image: '' };
                    articles_content_categories.mcLanguageSwitcher($.map(MCArticles.categories.list[index].languages, function (e, index) { return index }), 'article-categories', language_code);
                    MCArticles.categories.clear();
                },

                delete: function (language_code, category_id = false) {
                    let active_id = MCArticles.categories.activeID();
                    if (!category_id) {
                        category_id = MCArticles.categories.activeID(active_id);
                    }
                    delete MCArticles.categories.list[MCArticles.categories.getIndex(category_id)].languages[language_code];
                    MCArticles.categories.show(active_id);
                }
            }
        },

        translations: {
            list: {},

            add: function (language_code, article_id = false) {
                if (!article_id) {
                    article_id = MCArticles.activeID(true);
                }
                let translations = this.get(article_id);
                translations.push([language_code, false]);
                this.list[article_id] = translations;
                articles_content.mcLanguageSwitcher(translations, 'articles', language_code);
                MCArticles.clear();
            },

            delete: function (language_code, article_id = false) {
                if (!article_id) {
                    article_id = MCArticles.activeID(true);
                }
                let translations = this.get(article_id);
                for (var i = 0; i < translations.length; i++) {
                    if (translations[i][0] == language_code) {
                        MCArticles.delete(translations[i][1], (response) => {
                            if (response === true) {
                                translations.splice(i, 1);
                                this.list[article_id] = translations;
                                MCArticles.show(article_id);
                            }
                        });
                        break;
                    }
                }
                return false;
            },

            get: function (article_id) {
                return article_id in this.list ? this.list[article_id] : [];
            }
        }
    }

    /*
    * ----------------------------------------------------------
    * Reports
    * ----------------------------------------------------------
    */

    var MCReports = {
        chart: false,
        active_report: false,
        active_date_range: false,

        initChart: function (data, type = 'line', label_type = 1) {
            let values = [];
            let labels = [];
            let blues = MC_ADMIN_SETTINGS.color ? [MC_ADMIN_SETTINGS.color] : ['#049CFF', '#74C4F7', '#B9E5FF', '#0562A0', '#003B62', '#1F74C4', '#436786'];
            for (var key in data) {
                values.push(data[key][0]);
                labels.push(key);
            }
            if (type != 'line' && values.length > 6) {
                for (var i = 0; i < values.length; i++) {
                    blues.push('hsl(' + 210 + ', ' + Math.floor(Math.random() * 100) + '%, ' + Math.floor(Math.random() * 100) + '%)');
                }
            }
            if (this.chart) {
                this.chart.destroy();
            }
            this.chart = new Chart(reports_area.find('canvas'), {
                type: type,
                data: {
                    labels: labels,
                    datasets: values && Array.isArray(values[0]) ? [
                        {
                            data: values.map(row => row[0]),
                            backgroundColor: '#13ca7e'
                        },
                        {
                            data: values.map(row => row[1]),
                            backgroundColor: '#ca3434'
                        }
                    ] : [{
                        data: values,
                        backgroundColor: type == 'line' ? (MC_ADMIN_SETTINGS.color ? '#cbcbcb82' : '#028be530') : blues,
                        borderColor: type == 'line' ? (MC_ADMIN_SETTINGS.color ? MC_ADMIN_SETTINGS.color : '#049CFF') : '#FFFFFF',
                        borderWidth: 0,
                    }]
                },
                options: {
                    legend: {
                        display: false
                    },
                    scales: {
                        yAxes: [{
                            ticks: {
                                callback: function (tickValue, index, ticks) {
                                    return label_type == 1 ? tickValue : (label_type == 2 ? new Date(tickValue * 1000).toISOString().substr(11, 8) : tickValue);
                                },
                                beginAtZero: true
                            }
                        }],
                        xAxes: [{
                            ticks: {
                                beginAtZero: true
                            }
                        }],
                    },
                    tooltips: {
                        callbacks: {
                            label: function (tooltipItem, chartData) {
                                let index = tooltipItem.index;
                                let value = chartData.datasets[tooltipItem.datasetIndex].data[index];
                                switch (label_type) {
                                    case 1: return value;
                                    case 2: return new Date(values[index] * 1000).toISOString().substr(11, 8);
                                    case 3: return value + '%';
                                    case 4: let tds = reports_area.find('.mc-table tbody tr').eq(index).find('td'); return tds.eq(0).text() + ' ' + tds.eq(1).text();
                                }
                            },
                        },
                        displayColors: false
                    }
                }
            });
        },

        initTable: function (header, data, inverse = false) {
            let code = '<thead><tr>';
            let index = data[Object.keys(data)[0]].length - 1;
            let list = [];
            for (var i = 0; i < header.length; i++) {
                code += `<th>${header[i]}</th>`;
            }
            code += '</tr></thead><tbody>';
            for (var key in data) {
                if (data[key][index] != 0) {
                    list.push([key, data[key][index]]);
                }
            }
            if (inverse) {
                list.reverse();
            }
            for (var i = 0; i < list.length; i++) {
                code += `<tr><td><div>${list[i][0]}</div></td><td>${list[i][1]}</td></tr>`;
            }
            code += '</tbody>';
            reports_area.find('table').html(code);
        },

        initReport: function (name = false, date_range = false) {
            let area = reports_area.find('.mc-tab > .mc-content');
            date_range = MCF.null(date_range) ? [false, false] : date_range.split(' - ');
            area.mcLoading(true);
            if (name) {
                this.active_report = name;
            }
            if (!this.active_report) {
                return;
            }
            this.active_date_range = date_range;
            this.getData(this.active_report, date_range[0], date_range[1], (response) => {
                if (response == false) {
                    area.addClass('mc-no-results-active');
                } else {
                    area.removeClass('mc-no-results-active');
                    this.initChart(response.data, response.chart_type, response.label_type);
                    this.initTable(response.table, response.data, response.table_inverse);
                    reports_area.find('.mc-reports-title').html(response.title);
                    reports_area.find('.mc-reports-text').html(response.description);
                    reports_area.find('.mc-collapse-btn').remove();
                    if (!responsive) {
                        collapse(reports_area.find('.mc-collapse'), reports_area.find('canvas').outerHeight() - 135);
                    }
                }
                area.mcLoading(false);
            });
        },

        getData: function (name, date_start = false, date_end = false, onSuccess) {
            MCF.ajax({
                function: 'reports',
                name: name,
                date_start: date_start,
                date_end: date_end,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
            }, (response) => {
                onSuccess(response);
            });
        },

        initDatePicker: function () {
            let settings = {
                ranges: {},
                locale: {
                    format: 'DD/MM/YYYY',
                    separator: ' - ',
                    applyLabel: mc_('Apply'),
                    cancelLabel: mc_('Cancel'),
                    fromLabel: mc_('From'),
                    toLabel: mc_('To'),
                    customRangeLabel: mc_('Custom'),
                    weekLabel: mc_('W'),
                    daysOfWeek: [
                        mc_('Su'),
                        mc_('Mo'),
                        mc_('Tu'),
                        mc_('We'),
                        mc_('Th'),
                        mc_('Fr'),
                        mc_('Sa')
                    ],
                    monthNames: [
                        mc_('January'),
                        mc_('February'),
                        mc_('March'),
                        mc_('April'),
                        mc_('May'),
                        mc_('June'),
                        mc_('July'),
                        mc_('August'),
                        mc_('September'),
                        mc_('October'),
                        mc_('November'),
                        mc_('December')
                    ],
                    firstDay: 1
                },
                showCustomRangeLabel: true,
                alwaysShowCalendars: true,
                autoApply: true,
                opens: admin.hasClass('mc-rtl') ? 'left' : 'right'
            };
            settings.ranges[mc_('Today')] = [moment(), moment()];
            settings.ranges[mc_('Yesterday')] = [moment().subtract(1, 'days'), moment().subtract(1, 'days')];
            settings.ranges[mc_('Last 7 Days')] = [moment().subtract(6, 'days'), moment()];
            settings.ranges[mc_('Last 30 Days')] = [moment().subtract(29, 'days'), moment()];
            settings.ranges[mc_('This Month')] = [moment().startOf('month'), moment().endOf('month')];
            settings.ranges[mc_('Last Month')] = [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')];
            reports_area.find('#mc-date-picker').daterangepicker(settings).val('');
        },

        export: function (onSuccess) {
            MCF.ajax({
                function: 'reports-export',
                name: this.active_report,
                date_start: this.active_date_range[0],
                date_end: this.active_date_range[1],
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
            }, (response) => {
                onSuccess(response);
            });
        },

        open: function (id) {
            header.find('.mc-admin-nav #mc-reports').click();
            setTimeout(() => {
                reports_area.find('#' + id).click().get(0).scrollIntoView();
            }, 300);
        }
    }

    /*
    * ----------------------------------------------------------
    * Users
    * ----------------------------------------------------------
    */

    var MCUsers = {
        real_time: null,
        datetime_last_user: '2000-01-01 00:00:00',
        sorting: ['creation_time', 'DESC'],
        user_types: ['visitor', 'lead', 'user'],
        user_main_fields: ['id', 'first_name', 'last_name', 'email', 'password', 'profile_image', 'user_type', 'creation_time', 'token', 'last_activity', 'department'],
        search_query: '',
        init: false,
        busy: false,
        table_extra: false,
        history: [],

        get: function (onSusccess, is_online_users = false, is_users_pagination = false) {
            let filters = [];
            for (var i = 0; i < users_filters.length; i++) {
                filters.push(users_filters.eq(i).find('li.mc-active').data('value'));
            }
            if (!is_users_pagination) {
                loading(users_table);
            }
            MCF.ajax({
                function: is_online_users ? 'get-online-users' : 'get-users',
                sorting: is_online_users ? this.sorting[0] : this.sorting,
                pagination: is_users_pagination ? users_pagination : false,
                user_types: this.user_types,
                search: this.search_query,
                extra: this.table_extra,
                department: filters[0],
                source: filters[1],
                tag: filters[2],
            }, (response) => {
                onSusccess(response);
                users_table.mcLoading(false);
            });
        },

        filter: function (user_type) {
            if (user_type == 'all') {
                user_type = ['visitor', 'lead', 'user'];
            } else if (user_type == 'agent') {
                user_type = ['agent', 'admin'];
            } else {
                user_type = [user_type];
            }
            this.user_types = user_type;
            users_pagination = 1;
            users_pagination_count = 1;
            this.get((response) => {
                this.populate(response);
            }, user_type[0] == 'online');
        },

        sort: function (field, direction = 'DESC') {
            this.sorting = [field, direction];
            users_pagination = 1;
            users_pagination_count = 1;
            this.get((response) => {
                this.populate(response);
            });
        },

        search: function (input) {
            searchInput(input, (search, icon) => {
                users_pagination = 1;
                users_pagination_count = 1;
                this.search_query = search;
                this.get((response) => {
                    this.user_types = ['visitor', 'lead', 'user'];
                    this.populate(response);
                    $(icon).mcLoading(false);
                    users_table_menu.find('li').mcActive(false).eq(0).mcActive(true);
                });
            });
        },

        populate: function (response) {
            let code = '';
            let count = response.length;
            if (count) {
                for (var i = 0; i < count; i++) {
                    code += this.getRow(new MCUser(response[i], response[i].extra));
                }
            } else {
                code = `<p class="mc-no-results">${mc_('No users found.')}</p>`;
            }
            users_table.parent().scrollTop(0);
            users_table.find('tbody').html(code);
            if (this.user_types.includes('agent')) {
                MCF.ajax({
                    function: 'get-online-users',
                    agents: true
                }, (response) => {
                    let ids = [];
                    for (var i = 0; i < response.length; i++) {
                        ids.push(response[i].id);
                    }
                    users_table.find('[data-user-id]').each(function () {
                        $(this).find('.mc-td-profile').addClass('mc-' + (ids.includes($(this).attr('data-user-id')) ? 'online' : 'offline'));
                    });
                });
            }
        },

        update: function () {
            if (!this.busy) {
                let checks = ['user', 'visitor', 'lead', 'agent'];
                let populate = checks.includes(this.user_types[0]) && !this.search_query;
                let filter = users_table_menu.find('.mc-active').data('type');
                if (filter == 'online') {
                    this.filter(filter);
                } else {
                    this.busy = true;
                    MCF.ajax({
                        function: 'get-new-users',
                        datetime: this.datetime_last_user
                    }, (response) => {
                        let count = response.length;
                        this.busy = false;
                        if (count > 0) {
                            let code = '';
                            for (var i = 0; i < count; i++) {
                                let user = new MCUser(response[i]);
                                users[user.id] = user;
                                this.updateMenu('add', user.type);
                                if (populate) {
                                    code += this.getRow(user);
                                }
                            }
                            if (populate) {
                                users_table.find('tbody').prepend(code);
                                if (checks.includes(filter)) {
                                    let selector = '';
                                    for (var i = 0; i < checks.length; i++) {
                                        selector += checks[i] == filter ? '' : `[data-user-type="${checks[i]}"],`;
                                    }
                                    users_table.find(selector.slice(0, -1)).remove();
                                }
                            }
                            this.datetime_last_user = response[0].creation_time;
                        }
                    });
                }
            }
        },

        getRow: function (user) {
            if (user instanceof MCUser) {
                let code = '';
                for (var i = 0; i < this.table_extra.length; i++) {
                    let slug = this.table_extra[i];
                    code += `<td class="mc-td-${slug}">${this.user_main_fields.includes(slug) ? user.get(slug) : user.getExtra(slug)}</td>`;
                }
                return `<tr data-user-id="${user.id}" data-user-type="${user.type}"><td><input type="checkbox" /></td><td class="mc-td-profile"><a class="mc-profile"><img loading="lazy" src="${user.image}" /><span>${user.name}</span></a></td>${code}<td class="mc-td-email">${user.get('email')}</td><td class="mc-td-ut">${mc_(user.type)}</td><td>${MCF.beautifyTime(user.get('last_activity'), true)}</td><td>${MCF.beautifyTime(user.get('creation_time'))}</td></tr>`;
            } else {
                MCF.error('User not of type MCUser', 'MCUsers.getRow');
                return false;
            }
        },

        updateRow: function (user) {
            let row = users_table.find(`[data-user-id="${user.id}"]`);
            if (row.length) {
                let menu_active = users_table_menu.find('.mc-active').data('type');
                if ((user.type != menu_active) && !(user.type == 'admin' && menu_active == 'agent') && menu_active != 'all') {
                    let counter = admin.find(`[data-type="${user.type == 'admin' ? 'agent' : user.type}"] span`);
                    let count = parseInt(counter.attr('data-count'));
                    counter.html(count + 1).attr('data-count', count + 1);
                    row.remove();
                } else {
                    row.replaceWith(this.getRow(user));
                }
            } else {
                users_table.find('tbody').append(this.getRow(user));
            }
        },

        updateMenu: function (action = 'all', type = false) {
            let user_types = ['all', 'user', 'lead', 'visitor'];
            if (action == 'all') {
                MCF.ajax({
                    function: 'count-users'
                }, (response) => {
                    for (var i = 0; i < user_types.length; i++) {
                        this.updateMenuItem('set', user_types[i], response[user_types[i]]);
                    }
                });
            } else {
                this.updateMenuItem(action, type);
            }
        },

        updateMenuItem: function (action = 'set', type = false, count = 1) {
            let item = users_table_menu.find(`[data-type="${type}"] span`);
            let user_types = ['user', 'lead', 'visitor'];
            if (action != 'set') {
                count = parseInt(item.attr('data-count')) + (1 * (action == 'add' ? 1 : -1));
            }
            item.html(`(${count})`).attr('data-count', count);
            count = 0;
            for (var i = 0; i < user_types.length; i++) {
                count += parseInt(users_table_menu.find(`[data-type="${user_types[i]}"] span`).attr('data-count'));
            }
            users_table_menu.find(`[data-type="all"] span`).html(`(${count})`).attr('data-count', count);
        },

        delete: function (user_ids) {
            loading(users_table);
            if (Array.isArray(user_ids)) {
                if (MC_ADMIN_SETTINGS.cloud) {
                    user_ids = MCCloud.removeAdminID(user_ids);
                    if (!user_ids.length) {
                        return;
                    }
                }
                MCF.ajax({
                    function: 'delete-users',
                    user_ids: user_ids
                }, () => {
                    for (var i = 0; i < user_ids.length; i++) {
                        delete users[user_ids[i]];
                        users_table.find(`[data-user-id="${user_ids[i]}"]`).remove();
                        conversations_admin_list_ul.find(`[data-user-id="${user_ids[i]}"]`).remove();
                        MCF.event('MCUserDeleted', user_ids[i]);
                    }
                    if (users_table.find('[data-user-id]').length == 0) {
                        this.filter(users_table_menu.find('.mc-active').data('type'));
                    }
                    infoBottom('Users deleted');
                    this.updateMenu();
                    users_table.mcLoading(false);
                });
            } else {
                users[user_ids].delete(() => {
                    let conversation = conversations_admin_list_ul.find(`[data-user-id="${user_ids}"]`);
                    if (activeUser().id == user_ids) {
                        activeUser(false);
                    }
                    if (conversation.mcActive()) {
                        MCChat.conversation = false;
                        setTimeout(() => { MCConversations.clickFirst() }, 300);
                    }
                    delete users[user_ids];
                    users_table.find(`[data-user-id="${user_ids}"]`).remove();
                    conversation.remove();
                    admin.mcHideLightbox();
                    infoBottom('User deleted');
                    this.updateMenu();
                    users_table.mcLoading(false);
                });
            }
        },

        startRealTime: function () {
            if (MCPusher.active) {
                return;
            }
            this.stopRealTime();
            this.real_time = setInterval(() => {
                this.update();
            }, 1000);
        },

        stopRealTime: function () {
            clearInterval(this.real_time);
        },

        csv: function () {
            MCF.ajax({ function: 'csv-users', users_id: MCUsers.getSelected() }, (response) => {
                dialogDeleteFile(response, 'mc-export-users-close', 'Users exported');
                window.open(response);
            });
        },

        updateUsersActivity: function () {
            MCF.updateUsersActivity(agent_online ? MC_ACTIVE_AGENT.id : -1, activeUser() ? activeUser().id : -1, function (response) {
                MCUsers.setActiveUserStatus(response == 'online');
            });
        },

        setActiveAgentStatus: function (online = true) {
            let label = online ? 'online' : 'offline';
            agent_online = online;
            header.find('[data-value="status"]').html(mc_(MCF.slugToString(label))).attr('class', 'mc-' + label);
            if (MCPusher.active) {
                if (online) {
                    MCPusher.presence();
                    if (MC_ADMIN_SETTINGS.routing == 'queue' || MC_ADMIN_SETTINGS.routing == 'routing') {
                        MCF.ajax({ function: 'assign-conversations-active-agent', is_queue: MC_ADMIN_SETTINGS.routing == 'queue' }, () => {
                            MCConversations.update();
                        });
                    }
                } else {
                    MCPusher.presenceUnsubscribe();
                }
            }
            if (!MC_ADMIN_SETTINGS.reports_disabled) {
                MCF.ajax({ function: 'reports-update', name: label });
            }
        },

        setActiveUserStatus: function (online = true) {
            let labels = conversations_area.find('.mc-conversation .mc-top > .mc-labels');
            labels.find('.mc-status-online').remove();
            if (online) labels.prepend(`<span class="mc-status-online">${mc_('Online')}</span>`);
            MCChat.user_online = online;
        },

        onlineUserNotification: function (member) {
            let notification = MC_ADMIN_SETTINGS.online_users_notification;
            if (notification) {
                let message = member.info.first_name + ' ' + member.info.last_name;
                let icon = this.userProfileImage(member.info.profile_image);
                if (MC_ADMIN_SETTINGS.push_notifications && member.info.id && !this.history.includes(member.info.id)) {
                    MCF.ajax({
                        function: 'push-notification',
                        title: notification,
                        message: message,
                        icon: icon,
                        interests: MC_ACTIVE_AGENT.id,
                        user_id: member.info.id
                    });
                } else if (MCConversations.desktop_notifications) {
                    MCChat.desktopNotification(notification, message, icon, false, member.info.id);
                }
                this.history.push(member.info.id);
            }
        },

        userProfileImage: function (url) {
            return !url || url.indexOf('user.svg') ? MC_ADMIN_SETTINGS.notifications_icon : url;
        },

        getSelected: function () {
            let user_ids = [];
            users_table.find('tr').each(function () {
                if ($(this).find('td input[type="checkbox"]').is(':checked')) {
                    user_ids.push($(this).attr('data-user-id'));
                }
            });
            return user_ids;
        },

        getExtraDetailsList: function (only_custom = false) {
            return profile_edit_box.find('.mc-additional-details .mc-edit-box > ' + (only_custom ? '.mc-custom-detail' : '.mc-input')).map(function () { return [[$(this).attr('id'), $(this).find('span').html().trim()]] }).get();
        }
    }

    /*
    * ----------------------------------------------------------
    * Conversations 
    * ----------------------------------------------------------
    */

    var MCConversations = {
        real_time: null,
        datetime_last_conversation: false,
        user_typing: false,
        desktop_notifications: false,
        flash_notifications: false,
        busy: false,
        busy_2: false,
        is_search: false,
        menu_count_ajax: false,
        previous_editor_text: false,

        // Open the conversations tab
        open: function (conversation_id = -1, user_id) {
            if (conversation_id != -1) {
                this.openConversation(conversation_id, user_id);
            }
            admin.mcHideLightbox();
            header.find('.mc-admin-nav a').mcActive(false).parent().find('#mc-conversations').mcActive(true);
            admin.find(' > main > div').mcActive(false);
            conversations_area.mcActive(true).find('.mc-board').removeClass('mc-no-conversation');
            select_departments.find(' > p').attr('data-id', '').attr('data-value', '').html(mc_('None'));
            this.notes.update([]);
            this.tags.update([]);
            this.startRealTime();
        },

        // Open a single conversation
        openConversation: function (conversation_id, user_id = false, scroll = true) {
            MCChat.label_date.mcActive(false);
            MCChat.label_date_show = false;
            if (this.busy_2 == conversation_id) {
                return;
            }
            if (user_id === false && conversation_id) {
                this.busy_2 = conversation_id;
                MCF.ajax({
                    function: 'get-user-from-conversation',
                    conversation_id: conversation_id
                }, (response) => {
                    this.busy_2 = false;
                    if (!MCF.null(response.id)) {
                        this.openConversation(conversation_id, response.id, scroll);
                    } else {
                        MCF.error('Conversation not found', 'MCAdmin.openConversation');
                    }
                });
            } else {
                let new_user = MCF.null(users[user_id]) || !(users[user_id].details.email);
                let conversation = conversations_area.find(`[data-conversation-id="${conversation_id}"]`);
                let conversation_list = conversations_admin_list_ul.find('li');
                conversations_area_list.html('');
                conversations_area_list.mcLoading(true);

                // Init the user
                if (new_user) {
                    activeUser(new MCUser({ 'id': user_id }));
                    activeUser().update(() => {
                        users[user_id] = activeUser();
                        this.updateUserDetails();
                    });
                } else {
                    activeUser(users[user_id]);
                    this.updateCurrentURL();
                }
                if (MCPusher.active) {
                    MCPusher.event('client-typing', (response) => {
                        if (response.user_id == activeUser().id) {
                            MCConversations.typing(true);
                            clearTimeout(pusher_timeout);
                            pusher_timeout = setTimeout(() => { MCConversations.typing(false) }, 1000);
                        }
                    });
                    MCPusher.event('new-message', () => {
                        MCChat.update();
                    });
                    MCPusher.event('agent-active-conversation-changed', (response) => {
                        if (response.previous_conversation_id == conversation_id) {
                            conversations_area.find('.mc-conversation-busy').remove();
                        }
                    }, 'agents');
                    MCPusher.event('init', (response) => {
                        MCConversations.updateCurrentURL(response.current_url);
                    });
                    MCPusher.event('message-status-update', (response) => {
                        if (MCChat.conversation) {
                            MCChat.conversation.updateMessagesStatus(response.message_ids);
                        }
                    });
                }
                if (MC_ADMIN_SETTINGS.smart_reply) {
                    suggestions_area.html('');
                }

                // Open the conversation
                conversation_list.mcActive(false);
                if (!MC_ADMIN_SETTINGS.departments_show) {
                    conversation_list.attr('data-color', '');
                }
                conversation.mcActive(true);
                if (conversation_id != -1) {
                    this.busy_2 = conversation_id;
                    activeUser().getFullConversation(conversation_id, (response) => {
                        let conversation_status_code = response.status_code;
                        let select = conversations_filters.eq(0);
                        let select_status_code = select.find('.mc-active').attr('data-value');
                        this.busy_2 = false;
                        MCChat.setConversation(response);
                        MCChat.populate();
                        this.setReadIcon(conversation_status_code);
                        conversations_area.find('.mc-conversation-busy').remove();
                        this.updateUserDetails();
                        conversations_area.find('.mc-top > a').html(response.get('title'));

                        // Automatic translation
                        MCAdmin.must_translate = MC_ADMIN_SETTINGS.translation && activeUser().language && MC_ADMIN_SETTINGS.active_agent_language != activeUser().language;
                        if (MCAdmin.must_translate) {
                            let strings = [];
                            let message_ids = [];
                            let message_user_types = [];
                            let messages_translated = [];
                            for (var i = 0; i < response.messages.length; i++) {
                                let message = response.messages[i];
                                if (message.message) {
                                    if ((message.payload('original-message') && (!message.payload('original-message-language') || message.payload('original-message-language') == MC_ADMIN_SETTINGS.active_agent_language)) || (message.payload('translation') && (!message.payload('translation-language') || message.payload('translation-language') == MC_ADMIN_SETTINGS.active_agent_language))) {
                                        messages_translated.push(message);
                                    } else {
                                        strings.push(message.message);
                                        message_ids.push(message.id);
                                        message_user_types.push(message.get('user_type'));
                                    }
                                }
                            }
                            if (strings.length) {
                                MCApps.dialogflow.translate(strings, MC_ADMIN_SETTINGS.active_agent_language, (response_translate) => {
                                    if (response_translate) {
                                        for (var i = 0; i < response_translate.length; i++) {
                                            let message = MCChat.conversation.getMessage(message_ids[i]);
                                            message.payload('translation', response_translate[i])
                                            message.payload('translation-language', MC_ADMIN_SETTINGS.active_agent_language);
                                            this.openConversation_2(message_ids[i], message_user_types[i]);
                                        }
                                    }
                                    if (MC_ADMIN_SETTINGS.smart_reply) {
                                        this.openConversation_1(MCChat.conversation, suggestions_area);
                                    }
                                }, message_ids, conversation_id);
                            } else if (MC_ADMIN_SETTINGS.smart_reply) {
                                this.openConversation_1(MCChat.conversation, suggestions_area);
                            }
                            for (var i = 0; i < messages_translated.length; i++) {
                                this.openConversation_2(messages_translated[i].id, messages_translated[i].get('user_type'));
                            }
                        }

                        // Departments
                        if (select_departments.length) {
                            let department = response.get('department') ? this.getDepartments(response.get('department')) : false;
                            let color = department ? department['department-color'] : '';
                            if (!MC_ADMIN_SETTINGS.departments_show) {
                                conversation_list.attr('data-color', '');
                            }
                            getListConversation(conversation_id).attr('data-color', color);
                            select_departments.find(' > p').attr('data-id', department ? department['department-id'] : '').attr('data-value', color).html(department ? department['department-name'] + '<span></span>' : mc_('None'));
                        }

                        // Agent assignment
                        let select_agents = conversations_area.find('#conversation-agent');
                        if (select_agents.length) {
                            let item = select_agents.find(`[data-id="${response.get('agent_id')}"]`);
                            select_agents.find(' > p').attr('data-value', item.data('id')).html(item.html());
                        }

                        // Activate the conversation
                        if ([1, 2, '1', '2'].includes(conversation_status_code)) {
                            conversation_status_code = 0;
                        }
                        if (select_status_code != conversation_status_code && !$(conversations_admin_list).find('.mc-search-btn').mcActive() && !MCConversations.filters()[1] && !MCConversations.filters()[3]) {
                            select.find(`[data-value="${conversation_status_code}"]`).click();
                            select.find('ul').mcActive(false);
                        }
                        if (responsive) {
                            this.mobileOpenConversation();
                        }
                        if (!conversation.length && (select_status_code == conversation_status_code || (select_status_code == 0 && conversation_status_code == 1))) {
                            conversations_admin_list_ul.prepend(MCConversations.getListCode(response));
                        }
                        conversations_admin_list_ul.find('li').mcActive(false);
                        conversation.mcActive(true);
                        if (scroll) {
                            this.scrollTo();
                        }
                        this.notificationsCounterReset(conversation_id, conversation);
                        conversations_area_list.mcInitTooltips();

                        // Check if another agent has the conversation open
                        let busy = response.get('busy');
                        if (busy) {
                            conversations_area.find('.mc-editor > .mc-labels').prepend(`<span data-agent="${busy.id}" class="mc-status-warning mc-conversation-busy">${busy.first_name} ${busy.last_name} ${mc_('is replying to this conversation')}</span>`);
                        }

                        // App panels
                        if (MCApps.is('woocommerce')) {
                            MCApps.woocommerce.conversationPanel();
                        }
                        if (MCApps.is('ump')) {
                            MCApps.ump.conversationPanel();
                        }
                        if (MCApps.is('perfex')) {
                            MCApps.perfex.conversationPanel();
                        }
                        if (MCApps.is('whmcs')) {
                            MCApps.whmcs.conversationPanel();
                        }
                        if (MCApps.is('aecommerce')) {
                            MCApps.aecommerce.conversationPanel();
                        }
                        if (MCApps.is('martfury')) {
                            MCApps.martfury.conversationPanel();
                        }
                        if (MCApps.is('armember')) {
                            MCApps.armember.conversationPanel();
                        }
                        if (MCApps.is('zendesk')) {
                            MCApps.zendesk.conversationPanel();
                        }
                        if (MCApps.is('opencart')) {
                            MCApps.opencart.conversationPanel();
                        }
                        if (activeUser() && MC_ADMIN_SETTINGS.cloud) {
                            if (MCCloud.shopify.panel) {
                                MCCloud.shopify.panel.html('');
                            }
                            MCCloud.shopify.conversationPanel();
                        }

                        // Notes and Tags
                        this.notes.update(response.details.notes);
                        this.tags.update(response.details.tags);

                        // Attachments
                        this.attachments();

                        // Suggestions
                        if (MC_ADMIN_SETTINGS.smart_reply && !MCAdmin.must_translate) {
                            this.openConversation_1(response, suggestions_area);
                        }

                        // Rating
                        for (var i = response.messages.length - 1; i > 0; i--) {
                            let payload = response.messages[i].get('payload');
                            if (payload.rating) {
                                conversations_area.find('.mc-profile-list > ul').append(`<li data-id="rating"><i class="mc-icon mc-icon-${payload.rating == 1 ? 'like' : 'dislike'}"></i><span>${mc_('User rating')}</span><label>${mc_(payload.rating == 1 ? 'Helpful' : 'Not helpful')}${payload.message ? ' - ' + payload.message : ''}</label></li>`);
                                break;
                            }
                        }

                        // Source tasks
                        if (response.get('source') == 'em') {
                            this.cc(response.get('extra').split(','));
                        }
                        if (response.get('source') == 'wa') {
                            let datetime = response.getLastUserMessage();
                            if (datetime) {
                                if ((new Date()) - (new Date(datetime.get('creation_time').replace(' ', 'T'))) > 86400000) {
                                    infoBottom(mc_('You can\'t send a WhatsApp message more than 24 hours after the user\'s last message—use a template instead.') + '<i id="mc-whatsapp-alert-btn" class="mc-icon-social-wa"</i>', 'info');
                                }
                            }
                        }

                        admin.on('click', '#mc-whatsapp-alert-btn', function () {
                            MCConversations.showDirectMessageBox('whatsapp', [activeUser().id]);
                        });


                        // Populate user conversations on the bottom right area
                        activeUser().getConversations(function (response) {
                            conversations_area.find('.mc-user-conversations').html(response.length == 1 ? '' : activeUser().getConversationsCode(response)).prev().setClass('mc-hide', response.length == 1);
                        });

                        // Search
                        if (this.is_search) {
                            let search = conversations_admin_list.find('.mc-search-btn input').val();
                            for (var i = 0; i < response.messages.length; i++) {
                                if (response.messages[i].message.toLowerCase().includes(search)) {
                                    let id = response.messages[i].id;
                                    setTimeout(() => {
                                        let label = '';
                                        conversations_area_list.find(`> div`).each(function () {
                                            let message = $(this);
                                            if (message.attr('data-id') == id) {
                                                message.addClass('mc-highlight');
                                                setTimeout(() => { message.removeClass('mc-highlight') }, 3600);
                                                MCChat.label_date.html(label);
                                                MCChat.label_date_show = true;
                                                if (message.index()) {
                                                    message.prev()[0].scrollIntoView();
                                                } else {
                                                    message[0].scrollIntoView();
                                                }
                                            } else if (message.hasClass('mc-label-date')) {
                                                label = message.html();
                                            }
                                        });
                                    }, 300);
                                }
                            }
                        }
                        conversations_area_list.mcLoading(false);
                    });
                } else {
                    MCChat.clear();
                    conversations_admin_list_ul.find('li').mcActive(false);
                    conversations_area_list.mcLoading(false);
                    conversations_area.find('.mc-top > a').html('');
                    if (!new_user) {
                        this.updateUserDetails();
                    }
                }

                // More settings
                conversations_area.find('.mc-board').removeClass('mc-no-conversation');
                MCUsers.updateUsersActivity();
                this.startRealTime();
                if (MCF.getURL('conversation') != conversation_id && conversation_id != -1) {
                    pushState('?conversation=' + conversation_id);
                }
            }
        },

        openConversation_1: function (response, suggestions_area) {
            let message = response.getLastUserMessage();
            suggestions_area.html('');
            if (message && message.payload('mc-human-takeover')) {
                message = response.getLastUserMessage(message.get('index'));
            }
            if (message) {
                MCApps.dialogflow.smartReply(message.message);
            }
        },

        openConversation_2: function (message_id, user_type) {
            let message = MCChat.conversation.getMessage(message_id);
            if (message) {
                let is_translation = MCF.isAgent(user_type) && user_type != 'bot';
                conversations_area_list.find(`[data-id="${message_id}"]`).replaceWith(message.getCode());
                conversations_area_list.find(`[data-id="${message_id}"] .mc-menu`).prepend(`<li data-value="${is_translation ? 'translation' : 'original'}">${mc_(is_translation ? 'View translation' : 'View original message')}</li>`);
            }
        },

        // [Deprecated] this method is obsolete and it will be removed soon
        populate: function (conversation_id, user_id, scroll) {
            this.openConversation(conversation_id, user_id, scroll);
        },

        // Populate conversations
        populateList: function (response) {
            let code = '';
            conversations = [];
            for (var i = 0; i < response.length; i++) {
                code += this.getListCode(response[i]);
                conversations.push(new MCConversation([new MCMessage(response[i])], response[i]));
            }
            if (!code) {
                code = `<p class="mc-no-results">${mc_('No conversations found.')}</p>`;
            }
            conversations_admin_list_ul.html(code);
            this.updateMenu();
            MCF.event('MCAdminConversationsLoaded', { conversations: response });
        },

        // Update the left conversations list with latest conversations or messages 
        update: function () {
            if (!this.busy && conversations_filters.eq(0).find('p').attr('data-value') == 0 && this.datetime_last_conversation) {
                let filters = MCConversations.filters();
                this.busy = true;
                MCF.ajax({
                    function: 'get-new-conversations',
                    datetime: this.datetime_last_conversation,
                    department: filters[1],
                    source: filters[2],
                    tag: filters[3],
                    agent_id: filters[4]
                }, (response) => {
                    this.busy = false;
                    if (response.length) {
                        let code_pending = '';
                        let code_not_pending = '';
                        let active_conversation_id = MCChat.conversation ? MCChat.conversation.id : -1;
                        let li_not_pending;
                        let scroll_to_conversation = false;
                        let id_check = [];
                        if (response[0].last_update_time) {
                            this.datetime_last_conversation = response[0].last_update_time;
                        }
                        for (var i = 0; i < response.length; i++) {
                            if (!id_check.includes(response[i].id)) {
                                let message = new MCMessage(response[i]);
                                let conversation = new MCConversation([message], response[i]);
                                let status_code = conversation.status_code;
                                let is_pending_status_code = status_code == 2 || (MC_ADMIN_SETTINGS.order_by_date && (status_code == 0 || status_code == 1));
                                let user_id = conversation.user_id;
                                let conversation_id = conversation.id;
                                let conversation_li = getListConversation(conversation_id);
                                let is_existing_conversation = conversation_li.length;
                                let user_type = message.get('user_type');
                                let message_text = conversation.get('message');
                                let active_conversation = active_conversation_id == conversation_id;
                                let is_user = !MCF.isAgent(user_type);
                                if (!message_text && message.payload('preview')) {
                                    message_text = message.payload('preview');
                                }
                                if (is_pending_status_code && (!active_conversation || MCF.visibility_status == 'hidden') && (is_user || message.payload('human-takeover-message-confirmation'))) {
                                    let notifications_counter = MCF.storage('notifications-counter');
                                    if (!notifications_counter) {
                                        notifications_counter = {};
                                    }
                                    if (!notifications_counter[conversation_id]) {
                                        notifications_counter[conversation_id] = [];
                                    }
                                    if (!notifications_counter[conversation_id].includes(message.id)) {
                                        notifications_counter[conversation_id].push(message.id);
                                        MCF.storage('notifications-counter', notifications_counter);
                                    }
                                }
                                let conversation_code = this.getListCode(conversation, null);

                                // Active conversation
                                if (active_conversation) {
                                    this.updateUserDetails();
                                    if (is_existing_conversation) {
                                        if (message_text) {
                                            conversation_li.replaceWith(conversation_code);
                                        }
                                        this.setStatus(status_code, conversation_id);
                                    } else {
                                        scroll_to_conversation = true;
                                    }
                                } else if (is_existing_conversation) {

                                    // Conversation already in list but not active
                                    conversations[conversation_li.index()] = conversation;
                                    getListConversation(conversation_id).remove();
                                }

                                // Add the user to the global users array if it doesn't exists
                                if (!(user_id in users)) {
                                    users[user_id] = new MCUser({ id: user_id, first_name: conversation.get('first_name'), last_name: conversation.get('last_name'), profile_image: conversation.get('profile_image'), user_type: user_type });
                                }

                                // New or unactive conversation 
                                if (!active_conversation || !is_existing_conversation) {
                                    if (is_pending_status_code) {
                                        code_pending += conversation_code;
                                        conversations.unshift(conversation);
                                    } else if (status_code == 0 || status_code == 1) {
                                        li_not_pending = conversations_admin_list_ul.find('[data-conversation-status="2"]').last();
                                        if (li_not_pending.length) {
                                            conversations.splice(li_not_pending.index() + 1, 0, conversation);
                                            code_not_pending += conversation_code;
                                        } else {
                                            code_pending += conversation_code;
                                        }
                                    }
                                    if (activeUser() && user_id == activeUser().id) {
                                        activeUser().getConversations((response) => {
                                            conversations_area.find('.mc-user-conversations').html(activeUser().getConversationsCode(response));
                                        });
                                    }
                                    MCF.event('MCAdminNewConversation', { conversation: conversation });
                                }

                                // Update user
                                if (activeUser() && (message.payload('event') == 'update-user' || (users[user_id].type != user_type))) {
                                    activeUser().update(() => {
                                        this.updateUserDetails();
                                        users[activeUser().id] = activeUser();
                                    });
                                }

                                // Desktop, flash, sounds notifications
                                let message_preview = message.payload('preview');
                                if (!MCChat.tab_active && status_code == 2 && (is_user || message_preview) && (message_text || conversation.getAttachments().length || message_preview)) {
                                    if (this.desktop_notifications) {
                                        let user_details = [users[user_id].nameBeautified, users[user_id].image];
                                        MCChat.desktopNotification(user_details[0], message_preview ? message_preview : message_text, user_details[1], conversation_id, user_id);
                                    }
                                    if (this.flash_notifications) {
                                        MCChat.flashNotification();
                                    }
                                    if (MCChat.audio && MC_ADMIN_SETTINGS.sound) {
                                        MCChat.playSound();
                                    }
                                }
                                id_check.push(conversation_id);
                            }
                        }
                        if (!MCConversations.is_search) {
                            if (code_pending) {
                                conversations_admin_list_ul.prepend(code_pending);
                            }
                            if (code_not_pending) {
                                $(code_not_pending).insertAfter(li_not_pending);
                            }
                            if (scroll_to_conversation) {
                                this.scrollTo();
                            }
                            this.updateMenu();
                        }

                        // Update notifications counter
                        for (var i = 0; i < MCChat.notifications.length; i++) {
                            let is_found = false;
                            for (var j = 0; j < conversations.length; j++) {
                                if (conversations[j].id == MCChat.notifications[i][0]) {
                                    is_found = conversations[j].status_code == 2;
                                    break;
                                }
                            }
                            if (!is_found) {
                                MCChat.notifications.splice(i, 1);
                                i--;
                            }
                        }
                    }
                });
                if (MC_ADMIN_SETTINGS.assign_conversation_to_agent || MC_ACTIVE_AGENT.department) {
                    let ids = conversations_admin_list_ul.find(' > li').map(function () { return $(this).attr('data-conversation-id') }).get();
                    if (ids.length) {
                        MCF.ajax({
                            function: 'check-conversations-assignment',
                            conversation_ids: ids,
                            agent_id: MC_ADMIN_SETTINGS.assign_conversation_to_agent ? MC_ACTIVE_AGENT.id : false,
                            department: MC_ACTIVE_AGENT.department
                        }, (response) => {
                            if (response) {
                                for (var i = 0; i < response.length; i++) {
                                    getListConversation(response[i]).remove();
                                }
                            }
                        });
                    }
                }
            }
        },

        // Update the top left filter
        updateMenu: function () {
            let count = conversations_admin_list_ul.find('[data-conversation-status="2"]').length;
            let item = conversations_filters.eq(0);
            let span = item.find(' > p span');
            if (count == 100 || this.menu_count_ajax || MC_ADMIN_SETTINGS.order_by_date) {
                let status_code = item.find('li.mc-active').data('value');
                this.menu_count_ajax = true;
                MCF.ajax({
                    function: 'count-conversations',
                    status_code: status_code == 0 ? 2 : status_code
                }, (response) => {
                    span.html(`(${response})`);
                });
            } else {
                span.html(`(${count})`);
            }
        },

        // Return the code of the message menu
        messageMenu: function (agent, message = false, replay = false) {
            let code = (message && MC_ADMIN_SETTINGS.chatbot_features ? `<li data-value="bot">${mc_('Train chatbot')}</li>` : '') + ((agent && !MC_ADMIN_SETTINGS.supervisor && MC_ADMIN_SETTINGS.allow_agent_delete_message) || (MC_ADMIN_SETTINGS.supervisor && MC_ADMIN_SETTINGS.allow_supervisor_delete_message) ? `<li data-value="delete">${mc_('Delete')}</li>` : '') + (replay ? `<li data-value="reply">${mc_('Reply')}</li>` : '');
            return `<i class="mc-menu-btn mc-icon-menu"></i><ul class="mc-menu">${code}</ul>`;
        },

        // Update the users details of the conversations area
        updateUserDetails() {
            if (!activeUser()) return;
            conversations_area.find(`[data-user-id="${activeUser().id}"] .mc-name`).html(activeUser().name);
            conversations_area.find(`.mc-top > a`).html(MCChat.conversation ? MCChat.conversation.title : activeUser().name);
            conversations_user_details.find('.mc-profile').setProfile();
            MCProfile.populate(activeUser(), conversations_area.find('.mc-profile-list'));
        },

        // Set the read status icon
        setReadIcon(status_code) {
            let unread = status_code == 2;
            conversations_area.find('.mc-top [data-value="read"],.mc-top [data-value="unread"]').mcActive([0, 1, 2].includes(parseInt(status_code))).attr('data-value', unread ? 'read' : 'unread').attr('data-mc-tooltip', mc_(unread ? 'Mark as read' : 'Mark as unread')).parent().mcInitTooltips().find('i').attr('class', unread ? 'mc-icon-check-circle' : 'mc-icon-circle');
        },

        // Set a conversation as read 
        setStatus(status_code, conversation_id = false, update_left_list = false) {
            if (!conversation_id) {
                conversation_id = MCChat.conversation.id;
            }
            if (conversation_id) {
                conversations[getListConversation(conversation_id).index()].set('status_code', status_code);
                this.setReadIcon(status_code);
                if (update_left_list) {
                    conversations_admin_list_ul.find(`[data-conversation-id="${conversation_id}"]`).attr('data-conversation-status', status_code);
                }
            }
        },

        // Return the conversation code of the left conversations list
        getListCode: function (conversation, status) {
            if (!(conversation instanceof MCConversation)) {
                conversation = new MCConversation([new MCMessage(conversation)], conversation);
            }
            let message = conversation.getCode(true);
            let label_new = '';
            let tags = MC_ADMIN_SETTINGS.tags_show ? conversation.get('tags') : '';
            let department_id = conversation.get('department');
            let notification_counter = '';
            let time = conversation.get('last_update_time');
            let is_active = MCChat.conversation && MCChat.conversation.id == conversation.id;
            if (MCF.null(status)) {
                status = conversation.status_code;
            }
            if (tags) {
                tags = MCConversations.tags.codeLeft(tags);
            }
            if (!MCChat.conversation || !is_active || MCF.visibility_status == 'hidden') {
                notification_counter = MCF.storage('notifications-counter');
                if (notification_counter && notification_counter[conversation.id] && notification_counter[conversation.id].length) {
                    if (status == 2) {
                        notification_counter = `<span class="mc-notification-counter">${notification_counter[conversation.id].length}</span>`;
                    } else {
                        notification_counter[conversation.id] = [];
                        MCF.storage('notifications-counter', notification_counter);
                        notification_counter = '';
                    }
                } else {
                    notification_counter = '';
                }
            }
            if (!time) {
                time = conversation.getLastMessage() ? conversation.getLastMessage().get('creation_time') : conversation.get('creation_time');
            }
            return `<li${is_active ? ' class="mc-active"' : ''} data-user-id="${conversation.get('user_id')}" data-conversation-id="${conversation.id}" data-conversation-status="${status}"${department_id ? ` data-department="${department_id}"${MC_ADMIN_SETTINGS.departments_show ? ' data-color="' + this.getDepartments(department_id)['department-color'] + '"' : ''}` : ''}${!MCF.null(conversation.get('source')) ? ` data-conversation-source="${conversation.get('source')}"` : ''}>${label_new + notification_counter}<div class="mc-profile"><img loading="lazy" src="${conversation.get('profile_image')}"><span class="mc-name">${conversation.get('first_name') + ' ' + conversation.get('last_name')}</span>${tags}<span class="mc-time">${MCF.beautifyTime(time)}</span></div><p>${message}</p></li>`;
        },

        // Start or stop the real time update of left conversations list and chat 
        startRealTime: function () {
            if (MCPusher.active) {
                return;
            }
            this.stopRealTime();
            this.real_time = setInterval(() => {
                this.update();
                this.updateCurrentURL();
            }, 10000);
            MCChat.startRealTime();
        },
        stopRealTime: function () {
            clearInterval(this.real_time);
            MCChat.stopRealTime();
        },

        // Transcript generation and download
        transcript: function (conversation_id, user_id, action = false, onSuccess = false) {
            MCF.ajax({
                function: 'transcript',
                conversation_id: conversation_id
            }, (response) => {
                if (action == 'email') {
                    if (!activeUser() || activeUser().id != user_id || activeUser().get('email')) {
                        MCChat.sendEmail(MC_ADMIN_SETTINGS.transcript_message, [[response, response]], user_id, (response2) => {
                            if (onSuccess) {
                                onSuccess(response2 === true ? response : response2);
                            }
                        });
                    }
                } else {
                    if (onSuccess) {
                        onSuccess(response);
                    }
                    window.open(response);
                }
            });
        },

        // Set the typing status
        typing: function (typing) {
            if (typing) {
                if (!MCChat.user_online) {
                    MCUsers.setActiveUserStatus(true);
                }
                if (!this.user_typing) {
                    conversations_area.find('.mc-conversation .mc-top > .mc-labels').append('<span class="mc-status-typing">' + mc_('Typing') + '</span>');
                    this.user_typing = true;
                }
            } else if (this.user_typing) {
                conversations_area.find('.mc-conversation .mc-top .mc-status-typing').remove();
                this.user_typing = false;
            }
        },

        // Scroll the left conversations list to the active conversation
        scrollTo: function () {
            let active = conversations_admin_list_ul.find('.mc-active');
            let offset = active.length ? active[0].offsetTop : 0;
            conversations_admin_list_ul.parent().scrollTop(offset - (responsive ? 120 : 80));
        },

        // Search conversations
        search: function (input) {
            if (!input) return;
            searchInput(input, (search, icon) => {
                pagination_count = 1;
                if (search.length > 1) {
                    MCF.ajax({
                        function: 'search-conversations',
                        search: search
                    }, (response) => {
                        MCConversations.populateList(response);
                        $(icon).mcLoading(false);
                        this.scrollTo();
                        this.is_search = true;
                    });
                } else {
                    let filters = MCConversations.filters();
                    pagination = 1;
                    MCF.ajax({
                        function: 'get-conversations',
                        status_code: filters[0],
                        department: filters[1],
                        source: filters[2],
                        tag: filters[3],
                        agent_id: filters[4]
                    }, (response) => {
                        MCConversations.populateList(response);
                        $(icon).mcLoading(false);
                        this.is_search = false;
                        if (MCChat.conversation) {
                            getListConversation(MCChat.conversation.id).mcActive(true);
                            this.scrollTo();
                        }
                    });
                }
            });
        },

        // Reset the notifications counter of a conversation in the left list
        notificationsCounterReset: function (conversation_id, conversation = false) {
            let notification_counter = MCF.storage('notifications-counter');
            if (notification_counter && notification_counter[conversation_id]) {
                if (!conversation) {
                    conversation = conversations_admin_list_ul.find('[data-conversation-id="' + conversation_id + '"]');
                }
                let span = conversation.find('.mc-notification-counter');
                notification_counter[conversation_id] = [];
                MCF.storage('notifications-counter', notification_counter);
                span.addClass('mc-fade-out');
                setTimeout(() => {
                    span.remove();
                }, 200);
            }
        },

        // Get the page URL of the user
        updateCurrentURL: function (url = false) {
            if (url) {
                this.ucurl(url);
            } else if (MCChat.user_online && activeUser() && activeUser().getExtra('current_url')) {
                MCF.ajax({
                    function: 'current-url'
                }, (response) => {
                    if (response) this.ucurl(response);
                });
            }
        },

        ucurl(url) {
            let extra = activeUser().getExtra('current_url');
            url = urlStrip(url);
            conversations_area.find('.mc-profile-list [data-id="current_url"] label').attr('data-value', url).html(url);
            if (extra) {
                extra.value = url;
                activeUser().setExtra('current_url', extra);
            }
        },

        // Update the department of a conversation
        assignDepartment: function (conversation_id, department, onSuccess) {
            MCF.ajax({
                function: 'update-conversation-department',
                conversation_id: conversation_id,
                department: department,
                message: MCChat.conversation.getLastMessage().message
            }, (response) => {
                onSuccess(response);
            });
        },

        // Update the agent assignged to a conversation
        assignAgent: function (conversation_id, agent_id, onSuccess = false) {
            MCF.ajax({
                function: 'update-conversation-agent',
                conversation_id: conversation_id,
                agent_id: agent_id,
                message: MCChat.conversation.getLastMessage().message
            }, (response) => {
                if (onSuccess) onSuccess(response);
            });
        },

        // Update the UI to display the active department of the conversation
        setActiveDepartment: function (department_id) {
            if (MCChat.conversation && MCChat.conversation.get('department') == department_id) {
                return;
            }
            let department = department_id ? this.getDepartments(department_id) : false;
            let departmnet_color = department ? department['department-color'] : '';
            let conversation_li = getListConversation(MCChat.conversation.id);
            let department_filter = MCConversations.filters()[1];
            select_departments.find(' > p').attr('data-id', department ? department['department-id'] : '').attr('data-value', departmnet_color).html((department ? department['department-name'] : mc_('None')) + '<span></span>').next().mcActive(false);
            MCChat.conversation.set('department', department_id);
            if ((department_filter && department_filter != department_id) || (MC_ACTIVE_AGENT.user_type == 'agent' && MC_ACTIVE_AGENT.department && MC_ACTIVE_AGENT.department != department_id)) {
                conversation_li.remove();
                MCConversations.clickFirst();
            } else {
                conversation_li.attr('data-color', departmnet_color);
            }
            infoBottom('Department updated. The agents have been notified.');
        },

        // Get all departmnts or a single department
        getDepartments: function (department_id = false) {
            if (department_id) {
                for (var i = 0; i < MC_ADMIN_SETTINGS.departments.length; i++) {
                    if (MC_ADMIN_SETTINGS.departments[i]['department-id'] == department_id) {
                        return MC_ADMIN_SETTINGS.departments[i];
                    }
                }
                return false;
            }
            return MC_ADMIN_SETTINGS.departments;
        },

        // Update the UI to display the active agent of the conversation
        setActiveAgent: function (agent_id) {
            let select = conversations_area.find('#conversation-agent');
            let li = select.find(`[data-id="${agent_id}"]`);
            MCChat.conversation.set('agent_id', agent_id);
            select.find(' > p').attr('data-value', li.data('id')).html(li.html()).next().mcActive(false);
            if (MC_ACTIVE_AGENT.user_type == 'agent' && (!MC_ADMIN_SETTINGS.assign_conversation_to_agent || agent_id)) {
                getListConversation(MCChat.conversation.id).remove();
                MCConversations.clickFirst();
            }
            if (agent_id) {
                infoBottom('Agent assigned. The agent has been notified.');
            }
        },

        // Get the selected conversations from the left list
        getSelectedConversations: function () {
            return conversations_admin_list_ul.find('.mc-active').map(function () {
                return { id: $(this).attr('data-conversation-id'), user_id: $(this).attr('data-user-id'), status_code: $(this).attr('data-conversation-status') };
            }).toArray();
        },

        // Mobile conversations menu
        mobileOpenConversation: function () {
            conversations_area.find('.mc-admin-list').mcActive(false);
            conversation_area.mcActive(true);
            header.addClass('mc-hide');
        },

        mobileCloseConversation: function () {
            conversations_admin_list_ul.find('li.mc-active').mcActive(false);
            conversations_area.find('.mc-admin-list').mcActive(true);
            conversations_area.find('.mc-conversation,.mc-user-details').removeClass('mc-active');
            admin.find('.mc-menu-mobile [data-value="panel"]').mcActive(false);
            header.removeClass('mc-hide');
            window.history.replaceState({}, document.title, MCF.URL());
        },

        // Trigger the click event of the first conversation
        clickFirst: function (conversation_li = false) {
            if (!conversation_li) {
                conversation_li = conversations_admin_list_ul.find('li:first-child');
            }
            if (conversation_li.length) {
                conversation_li.click();
                MCConversations.scrollTo();
            } else {
                conversations_area.find('.mc-board').addClass('mc-no-conversation');
                if (!conversations_admin_list_ul.find('li').length) {
                    conversations_admin_list_ul.html(`<p class="mc-no-results">${mc_('No conversations found.')}</p>`);
                }
                if (MCF.getURL('conversation')) {
                    window.history.replaceState({}, document.title, MCF.URL().replace('?conversation=' + MCF.getURL('conversation'), ''));
                }
            }
        },

        // Saved replies
        savedReplies: function (textarea, value) {
            let last_char = value.charAt(textarea.selectionStart - 1);
            let pre_text = value.substr(0, value.lastIndexOf('#'));
            if (last_char == '#') {
                if (value.length > 1 && value.charAt(textarea.selectionStart - 2) == '#') {
                    $(textarea).val(pre_text.substr(0, pre_text.length - 1));
                    return conversation_area.find('.mc-btn-saved-replies').click();
                }
                MCChat.editor_listening = true;
            }
            if (MCChat.editor_listening && last_char == ' ') {
                let keyword = value.substr(value.lastIndexOf('#') + 1).replace(' ', '');
                MCChat.editor_listening = false;
                for (var i = 0; i < saved_replies_list.length; i++) {
                    if (saved_replies_list[i]['reply-name'] == keyword) {
                        $(textarea).val(pre_text + saved_replies_list[i]['reply-text']);
                        return;
                    }
                }
            }
        },

        // Conversation attachments panel
        attachments: function () {
            if (attachments_panel.length) {
                let attachments = MCChat.conversation.getAttachments();
                let code = '';
                let code_filters = '';
                let file_types = [];
                for (var i = attachments.length - 1; i > -1; i--) {
                    let file_type = MCF.getFileType(attachments[i][1]);
                    code += `<a href="${attachments[i][1]}" target="_blank"><i class="mc-icon mc-icon-download"></i>${attachments[i][0]}</a>`;
                    if (!file_types.includes(file_type)) {
                        file_types.push(file_type);
                    }
                }
                if (attachments.length > 4 && file_types.length > 1) {
                    code_filters = `<div id="mc-attachments-filter" class="mc-select"><p>${mc_('All')}</p><ul><li data-value="">${mc_('All')}</li>`;
                    for (var i = 0; i < file_types.length; i++) {
                        code_filters += `<li data-value="${file_types[i]}">${mc_(MCF.slugToString(file_types[i]) + 's')}</li>`;
                    }
                    code_filters += `</ul></div>`;
                }
                $(attachments_panel).html(code ? `<h3${code_filters ? ' class="mc-flex"' : ''}>${mc_('Attachments')}${code_filters}</h3><div class="mc-list-items mc-list-links mc-list-icon">${code}</div>` : '');
                collapse(attachments_panel, 160);
            }
        },

        // Get conversations filters values
        filters: function () {
            let values = [];
            for (var i = 0; i < conversations_filters.length; i++) {
                values.push(conversations_filters.eq(i).find('li.mc-active').data('value'));
            }
            return values;
        },

        // Notes
        notes: {
            busy: false,

            add: function (conversation_id, user_id, name, message, onSuccess = false, note_id = false) {
                MCF.ajax({
                    function: note_id ? 'update-note' : 'add-note',
                    conversation_id: conversation_id,
                    user_id: user_id,
                    note_id: note_id,
                    name: name,
                    message: message
                }, (response) => {
                    if (onSuccess) {
                        onSuccess(response);
                    }
                });
            },

            update: function (notes, add = false) {
                if (notes_panel.length) {
                    let code = '';
                    let div = notes_panel.find(' > div');
                    if (notes) {
                        for (var i = 0; i < notes.length; i++) {
                            let note = notes[i];
                            code += `<div data-id="${note.id}"><span${MC_ADMIN_SETTINGS.notes_hide_information ? ' class="mc-note-hide-info"' : ''}>${MC_ADMIN_SETTINGS.notes_hide_information ? '' : note.name}${MC_ADMIN_SETTINGS.notes_hide_information || !note.date ? '' : `<span>${MCF.beautifyTime(note.date, true)}</span>`}${MC_ACTIVE_AGENT.id == note.user_id ? '<i class="mc-edit-note mc-icon-edit"></i><i class="mc-delete-note mc-icon-close"></i>' : ''}</span><span class="mc-note-text">${note.message.replace(/\n/g, '<br>')}</span></div>`;
                        }
                        code = code.autoLink({ target: '_blank' });
                    }
                    if (add) {
                        div.append(code);
                    } else {
                        div.html(code);
                    }
                    div.attr('style', '');
                    notes_panel.find('.mc-collapse-btn').remove();
                    collapse(notes_panel, 155);
                    this.busy = false;
                }
            },

            delete: function (conversation_id, note_id, onSuccess = false) {
                if (this.busy) return;
                this.busy = true;
                MCF.ajax({
                    function: 'delete-note',
                    conversation_id: conversation_id,
                    note_id: note_id
                }, (response) => {
                    this.busy = false;
                    if (onSuccess) onSuccess(response);
                });
            }
        },

        // Tags
        tags: {
            busy: false,

            update: function (tags) {
                if (tags_panel.length) {
                    let code = '';
                    let div = tags_panel.find(' > div');
                    for (var i = 0; i < tags.length; i++) {
                        let tag = this.get(tags[i]);
                        if (tag) {
                            code += this.code(tag);
                        }
                    }
                    div.html(code);
                    this.busy = false;
                }
            },

            get: function (tag_name) {
                for (var i = 0; i < MC_ADMIN_SETTINGS.tags.length; i++) {
                    if (tag_name == MC_ADMIN_SETTINGS.tags[i]['tag-name']) {
                        return MC_ADMIN_SETTINGS.tags[i];
                    }
                }
            },

            getAll: function (active_tags = []) {
                let code = '';
                for (var i = 0; i < MC_ADMIN_SETTINGS.tags.length; i++) {
                    code += this.code(i, active_tags.includes(MC_ADMIN_SETTINGS.tags[i]['tag-name']));
                }
                return code;
            },

            code: function (index_or_tag, active) {
                let tag = isNaN(index_or_tag) ? index_or_tag : MC_ADMIN_SETTINGS.tags[index_or_tag];
                if (tag) {
                    let name = tag['tag-name'];
                    return `<span data-value="${name}" data-color="${tag['tag-color']}"${active ? ' class="mc-active"' : ''}>${mc_(name)}</span>`;
                }
                return '';
            },

            codeLeft: function (tags) {
                let code = '<span class="mc-tags-area">';
                for (var i = 0; i < tags.length; i++) {
                    let tag = this.get(tags[i]);
                    if (tag) {
                        code += `<i class="mc-icon-tag" data-color-text="${tag['tag-color']}"></i>`;
                    }
                }
                return code + '</span>';
            }
        },

        // Direct message
        showDirectMessageBox: function (type, user_ids = []) {
            if (type == 'whatsapp') {
                whatsapp_direct_message_box(user_ids);
            } else {
                let email = type == 'custom_email';
                let names = { sms: 'text message', custom_email: 'email', message: 'chat message' };
                MCForm.clear(direct_message_box);
                direct_message_box.find('.mc-direct-message-users').val(user_ids.length ? user_ids.join(',') : 'all');
                direct_message_box.find('.mc-bottom > div').html('');
                direct_message_box.find('.mc-top-bar > div:first-child').html(mc_(`Send a ${names[type]}`));
                direct_message_box.find('.mc-loading').mcLoading(false);
                direct_message_box.find('.mc-direct-message-subject').mcActive(email).find('input').attr('required', email);
                direct_message_box.attr('data-type', type);
                direct_message_box.mcShowLightbox();
            }
        },

        // Miscellaneous 
        getDeliveryFailedMessage: function (source) {
            return `<i class="mc-icon-warning mc-delivery-failed" data-mc-tooltip="${mc_('Message not delivered to {R}.').replace('{R}', MCApps.getName(source))}" data-mc-tooltip-init></i>`;
        },

        cc: function (cc) {
            let code = '';
            for (var i = 0; i < cc.length; i++) {
                if (cc[i]) {
                    code += `<li data-id="cc"><i class="mc-icon mc-icon-envelope"></i><span>CC</span><label>${cc[i]}</label></li>`;
                }
            }
            conversations_area.find('[data-id="cc"]').remove();
            conversations_area.find('[data-id="email"]').attr('data-em', 'true').after(code);
        }
    }

    /* 
    * ----------------------------------------------------------
    * Profile
    * ----------------------------------------------------------
    */

    var MCProfile = {

        // Get all profile settings
        getAll: function (profile_area) {
            return MCForm.getAll(profile_area);
        },

        // Get a single setting
        get: function (input) {
            return MCForm.get(input);
        },

        // Set a single setting
        set: function (item, value) {
            return MCForm.set(item, value);
        },

        // Display the user box
        show: function (user_id) {
            loadingGlobal();
            activeUser(new MCUser({ 'id': user_id }));
            activeUser().update(() => {
                this.populate(activeUser(), profile_box.find('.mc-profile-list'));
                profile_box.find('.mc-profile').setProfile();
                activeUser().getConversations((response) => {
                    let user_type = activeUser().type;
                    let html = activeUser().getConversationsCode(response);
                    if (MCF.isAgent(user_type)) {
                        this.agentData();
                    }
                    profile_box.find('.mc-user-conversations').html(html).prev().setClass('mc-hide', !html);
                    profile_box.find('.mc-top-bar [data-value]').mcActive(false);
                    if (!MCF.null(activeUser().get('email'))) {
                        profile_box.find('.mc-top-bar [data-value="email"]').mcActive(true);
                    }
                    if (activeUser().getExtra('phone')) {
                        if (MC_ADMIN_SETTINGS.sms) {
                            profile_box.find('.mc-top-bar [data-value="sms"]').mcActive(true);
                        }
                        profile_box.find('.mc-top-bar [data-value="whatsapp"]').mcActive(true);
                    }
                    this.boxClasses(profile_box, user_type);
                    profile_box.attr('data-user-id', activeUser().id).mcShowLightbox();
                    loadingGlobal(false, false);
                    MCF.event('MCProfileBoxOpened', { user_id: user_id });
                });
                users[user_id] = activeUser();
                if (MCF.getURL('user') != user_id && !MCF.getURL('conversation')) {
                    pushState('?user=' + user_id);
                }
            });
        },

        showEdit: function (user) {
            if (user instanceof MCUser) {
                let password = profile_edit_box.find('#password input');
                let current_user_type = user.type;
                let select = profile_edit_box.find('#user_type select');
                profile_edit_box.removeClass('mc-user-new').attr('data-user-id', user.id);
                profile_edit_box.find('.mc-top-bar .mc-save').html(`<i class="mc-icon-check"></i>${mc_('Save changes')}`);
                profile_edit_box.find('.mc-profile').setProfile();
                profile_edit_box.find('.mc-unlisted-detail').remove();
                profile_edit_box.find('input,select,textara').removeClass('mc-error');

                // Custom details
                let code = '';
                let default_ids = profile_edit_box.find('.mc-additional-details [id]').map(function () { return this.id; }).get().concat(['wp-id', 'perfex-id', 'whmcs-id', 'aecommerce-id', 'facebook-id', 'ip', 'os', 'current_url', 'country_code', 'browser_language', 'browser', 'martfury-id', 'martfury-session']);
                for (var id in user.extra) {
                    if (!default_ids.includes(id)) {
                        code += `<div id="${id}" data-type="text" class="mc-input mc-unlisted-detail"><span>${mc_(user.extra[id].name)}</span><input type="text"></div>`;
                    }
                }
                profile_edit_box.find('.mc-additional-details .mc-edit-box').append(code);

                // Set values
                this.populateEdit(user, profile_edit_box);
                this.updateRequiredFields(current_user_type);

                // User type select
                if (MC_ACTIVE_AGENT.user_type == 'admin' && MCF.isAgent(current_user_type)) {
                    select.html(`<option value="agent">${mc_('Agent')}</option><option value="admin"${current_user_type == 'admin' ? ' selected' : ''}>${mc_('Admin')}</option>`);
                }

                // Password
                if (password.val()) {
                    password.val('********');
                }

                // Cloud
                if (MC_ADMIN_SETTINGS.cloud) {
                    profile_edit_box.setClass('mc-cloud-admin', user.id == 1);
                }

                // Show the edit box
                this.boxClasses(profile_edit_box, current_user_type);
                profile_edit_box.mcShowLightbox();
                MCF.event('MCProfileEditBoxOpened', { user_id: user.id });
            } else {
                MCF.error('User not of type MCUser', 'MCUsers.showEdit');
                return false;
            }
        },

        // Populate profile
        populate: function (user, profile_area) {
            let exclude = ['first_name', 'last_name', 'password', 'profile_image'];
            let code = '';
            if (profile_area.hasClass('mc-profile-list-conversation') && MCChat.conversation) {
                let source = MCChat.conversation.get('source');
                code = this.profileRow('conversation-id', MCChat.conversation.id, mc_('Conversation ID'));
                if (!MCF.null(source)) {
                    code += this.profileRow('conversation-source', MCApps.getName(source), mc_('Source'));
                }
            }
            if (MC_ACTIVE_AGENT.user_type != 'admin') {
                exclude.push('token');
            }
            for (var key in user.details) {
                if (!exclude.includes(key)) {
                    code += this.profileRow(key, user.get(key), key == 'id' ? 'User ID' : key);
                }
            }
            profile_area.html(`<ul>${code}</ul>`);
            code = '';
            if (user.isExtraEmpty()) {
                MCF.ajax({
                    function: 'get-user-extra',
                    user_id: user.id
                }, (response) => {
                    for (var i = 0; i < response.length; i++) {
                        let slug = response[i].slug;
                        user.setExtra(slug, response[i]);
                        code += this.profileRow(slug, response[i].value, response[i].name);
                    }
                    profile_area.find('ul').append(code);
                    collapse(profile_area, 145);
                    MCF.event('MCUserLoaded', user);
                });
            } else {
                for (var key in user.extra) {
                    let info = user.getExtra(key);
                    code += this.profileRow(key, info.value, info.name);
                }
                profile_area.find('ul').append(code);
                collapse(profile_area, 145);
                MCF.event('MCUserLoaded', user);
            }
        },

        profileRow: function (key, value, name = key) {
            if (!value) return '';
            let icons = { id: 'user', full_name: 'user', email: 'envelope', phone: 'phone', user_type: 'user', last_activity: 'calendar', creation_time: 'calendar', token: 'shuffle', currency: 'currency', location: 'marker', country: 'marker', address: 'marker', city: 'marker', postal_code: 'marker', browser: 'desktop', os: 'desktop', current_url: 'next', timezone: 'clock' };
            let icon = `<i class="mc-icon mc-icon-${key in icons ? icons[key] : 'plane'}"></i>`;
            let lowercase;
            let image = false;
            switch (key) {
                case 'last_activity':
                case 'creation_time':
                    value = MCF.beautifyTime(value);
                    break;
                case 'user_type':
                    value = MCF.slugToString(value);
                    break;
                case 'country':
                case 'country_code':
                case 'language':
                case 'browser_language':
                    let flag = value;
                    if (value == 'ar' && (key == 'language' || key == 'browser_language')) {
                        flag = 'arc';
                    }
                    icon = `<img src="${MC_URL}/media/flags/${flag.toLowerCase()}.png" />`;
                    break;
                case 'browser':
                    lowercase = value.toLowerCase();
                    if (lowercase.includes('chrome')) {
                        image = 'chrome';
                    } else if (lowercase.includes('edge')) {
                        image = 'edge';
                    } else if (lowercase.includes('firefox')) {
                        image = 'firefox';
                    } else if (lowercase.includes('opera')) {
                        image = 'opera';
                    } else if (lowercase.includes('safari')) {
                        image = 'safari';
                    }
                    break;
                case 'os':
                    lowercase = value.toLowerCase();
                    if (lowercase.includes('windows')) {
                        image = 'windows';
                    } else if (lowercase.includes('mac') || lowercase.includes('apple') || lowercase.includes('ipad') || lowercase.includes('iphone')) {
                        image = 'apple';
                    } else if (lowercase.includes('android')) {
                        image = 'android';
                    } else if (lowercase.includes('linux')) {
                        image = 'linux';
                    } else if (lowercase.includes('ubuntu')) {
                        image = 'ubuntu';
                    }
                    break;
                case 'conversation-source':
                    image = value.toLowerCase();
                case 'browser':
                case 'os':
                case 'conversation-source':
                    if (image) {
                        icon = `<img src="${MC_URL}/media/${key == 'conversation-source' ? 'apps' : 'devices'}/${image}.svg" />`;
                    }
                    break;
                case 'current_url':
                    value = urlStrip(value);
                    break;
            }
            return `<li data-id="${key}">${icon}<span>${mc_(MCF.slugToString(name))}</span><label>${value}</label></li>`;
        },

        // Populate profile edit box
        populateEdit: function (user, profile_edit_area) {
            profile_edit_area.find('.mc-details .mc-input').each((i, element) => {
                this.set(element, user.details[$(element).attr('id')]);
            });
            profile_edit_area.find('.mc-additional-details .mc-input').each((i, element) => {
                let key = $(element).attr('id');
                if (key in user.extra) {
                    this.set(element, user.extra[key].value);
                } else {
                    this.set(element, '');
                }
            });
        },

        // Clear the profile edit area
        clear: function (profile_edit_area) {
            MCForm.clear(profile_edit_area);
        },

        // Check for errors on user input
        errors: function (profile_edit_area) {
            return MCForm.errors(profile_edit_area.find('.mc-details'));
        },

        // Display a error message
        showErrorMessage: function (profile_edit_area, message) {
            MCForm.showErrorMessage(profile_edit_area, message);
        },

        // Agents data area
        agentData: function () {
            let code = `<div class="mc-title">${mc_('Feedback rating')}</div><div class="mc-rating-area mc-loading"></div>`;
            let area = profile_box.find('.mc-agent-area');
            area.html(code);
            MCF.ajax({
                function: 'get-rating'
            }, (response) => {
                if (response[0] == 0 && response[1] == 0) {
                    code = `<p class="mc-no-results">${mc_('No ratings yet.')}</p>`;
                } else {
                    let total = response[0] + response[1];
                    let positive = response[0] * 100 / total;
                    let negative = response[1] * 100 / total;
                    code = `<div><div>${mc_('Helpful')}</div><span data-count="${response[0]}" style="width: ${Math.round(positive * 2)}px"></span><div>${positive.toFixed(2)} %</div></div><div><div>${mc_('Not helpful')}</div><span data-count="${response[1]}" style="width: ${Math.round(negative * 2)}px"></span><div>${negative.toFixed(2)} %</div></div><p class="mc-rating-count">${total} ${mc_('Ratings')}</p>`;
                }
                area.find('.mc-rating-area').html(code).mcLoading(false);
            });
        },

        boxClasses: function (box, user_type = false) {
            $(box).removeClass('mc-type-admin mc-type-agent mc-type-lead mc-type-user mc-type-visitor').addClass(`${user_type != false ? `mc-type-${user_type}` : ''} mc-agent-${MC_ACTIVE_AGENT.user_type}`);
        },

        updateRequiredFields: function (user_type) {
            let agent = MCF.isAgent(user_type);
            profile_edit_box.find('#password input').prop('required', agent);
            profile_edit_box.find('#email input').prop('required', agent);
        }
    }

    /*
    * ----------------------------------------------------------
    * Init
    * ----------------------------------------------------------
    */

    var MCAdmin = {

        infoBottom: function (text, type = false) {
            var card = admin.find('.mc-info-card');
            if (!type) {
                card.removeClass('mc-info-card-error mc-info-card-warning mc-info-card-info');
                clearTimeout(timeout);
                timeout = setTimeout(() => { card.mcActive(false) }, 5000);
            } else if (type == 'error') {
                card.addClass('mc-info-card-error');
            } else {
                card.addClass('mc-info-card-info');
            }
            card.html(`<h3>${mc_(text)}</h3>`).mcActive(true);
        },

        infoPanel: function (text, type = 'info', onConfirm = false, id = '', title = '', scroll = false, skip = false, onCancel = false) {
            if (skip && onConfirm) {
                return onConfirm();
            }
            let box = admin.find('.mc-dialog-box').attr('data-type', type);
            let p = box.find('p');
            box.attr('id', id).setClass('mc-scroll-area', scroll).css('height', scroll ? (parseInt($(window).height()) - 200) + 'px' : '');
            box.find('.mc-title').html(mc_(title));
            p.html((type == 'alert' ? mc_('Are you sure?') + ' ' : '') + mc_(text));
            box.mcActive(true).css({ 'margin-top': (box.outerHeight() / -2) + 'px', 'margin-left': (box.outerWidth() / -2) + 'px' });
            overlay.mcActive(true);
            alertOnConfirmation = onConfirm;
            alertOnCancel = onCancel;
            setTimeout(() => { MCAdmin.open_popup = box }, 500);
        },

        genericPanel: function (id, title, content, buttons = [], attrs = '', scroll = false) {
            let buttons_code = '';
            let cnt = admin.find('#mc-generic-panel');
            for (var i = 0; i < buttons.length; i++) {
                buttons[i] = isString(buttons[i]) || buttons[i] instanceof String ? [buttons[i], false] : buttons[i];
                buttons_code += `<a id="mc-${MCF.stringToSlug(buttons[i][0])}" class="mc-btn${buttons[i][1] ? ` mc-icon` : ``}">${buttons[i][1] ? `<i class="mc-icon-${buttons[i][1]}"></i>` : ``} ${mc_(buttons[i][0])}</a>`;
            }
            cnt.html(`<div class="mc-lightbox mc-${id}-box"${attrs}><div class="mc-info"></div><div class="mc-top-bar"><div>${mc_(title)}</div>
                        <div>
                            ${buttons_code}
                            <a class="mc-close mc-btn-icon mc-btn-red">
                                <i class="mc-icon-close"></i>
                            </a>
                        </div>
                    </div>
                    <div class="mc-main${scroll ? ' mc-scroll-area' : ''}">
                        ${content}
                    </div>
             </div>`);
            cnt.find('> div').mcShowLightbox();
        },

        activeUser: function (value) {
            if (typeof value == ND) {
                return window.mc_current_user;
            } else {
                window.mc_current_user = value;
            }
        },

        loadingGlobal: function (show = true, is_overlay = true) {
            admin.find('.mc-loading-global').mcActive(show);
            if (is_overlay) {
                overlay.mcActive(show);
                $('body').setClass('mc-lightbox-active', show);
            }
        },

        loading: function (element) {
            if ($(element).mcLoading()) {
                return true;
            }
            $(element).mcLoading(true);
            return false;
        },

        collapse(target, max_height) {
            target = $(target);
            let content = target.find('> div, > ul');
            content.css({ 'height': '', 'max-height': '' });
            target.find('.mc-collapse-btn').remove();
            if (target.hasClass('mc-collapse') && $(content).prop('scrollHeight') > max_height) {
                target.mcActive(true).attr('data-height', max_height);
                target.append(`<a class="mc-btn-text mc-collapse-btn">${mc_('View more')}</a>`);
                content.css({ 'height': max_height + 'px', 'max-height': max_height + 'px' });
            };
        },

        open_popup: false,
        must_translate: false,
        is_logout: false,
        conversations: MCConversations,
        users: MCUsers,
        settings: MCSettings,
        profile: MCProfile,
        apps: MCApps
    }
    window.MCAdmin = MCAdmin;

    $(document).ready(function () {

        admin = $('.mc-admin');
        header = admin.find('> .mc-header');
        conversations_area = admin.find('.mc-area-conversations');
        conversation_area = conversations_area.find('.mc-conversation');
        conversations_area_list = conversations_area.find('.mc-conversation .mc-list');
        conversations_admin_list = conversations_area.find('.mc-admin-list');
        conversations_admin_list_ul = conversations_admin_list.find('.mc-scroll-area ul');
        conversations_filters = conversations_admin_list.find('.mc-select');
        conversations_user_details = conversations_area.find('.mc-user-details');
        users_area = admin.find('.mc-area-users');
        users_table = users_area.find('.mc-table-users');
        users_table_menu = users_area.find('.mc-menu-users');
        users_filters = users_area.find('.mc-filter-btn .mc-select');
        profile_box = admin.find('.mc-profile-box');
        profile_edit_box = admin.find('.mc-profile-edit-box');
        settings_area = admin.find('.mc-area-settings');
        automations_area = settings_area.find('.mc-automations-area');
        conditions_area = automations_area.find('.mc-conditions');
        automations_area_select = automations_area.find(' > .mc-select');
        automations_area_nav = automations_area.find(' > .mc-tab > .mc-nav > ul');
        reports_area = admin.find('.mc-area-reports');
        articles_area = admin.find('.mc-area-articles');
        articles_content = articles_area.find('.mc-content-articles');
        articles_content_categories = articles_area.find('.mc-content-categories');
        articles_category_parent_select = admin.find('#article-parent-categories');
        articles_category_select = admin.find('#article-categories');
        saved_replies = conversations_area.find('.mc-replies');
        overlay = admin.find('.mc-lightbox-overlay');
        SITE_URL = typeof MC_URL != ND ? MC_URL.substr(0, MC_URL.indexOf('-content') - 3) : '';
        woocommerce_products_box = conversations_area.find('.mc-woocommerce-products');
        woocommerce_products_box_ul = woocommerce_products_box.find(' > div > ul');
        notes_panel = conversations_area.find('.mc-panel-notes');
        tags_panel = conversations_area.find('.mc-panel-tags');
        attachments_panel = conversations_area.find('.mc-panel-attachments');
        direct_message_box = admin.find('.mc-direct-message-box');
        wp_admin = MCApps.is('wordpress') && $('.wp-admin').length;
        dialogflow_intent_box = admin.find('.mc-dialogflow-intent-box');
        suggestions_area = conversations_area.find('.mc-editor > .mc-suggestions');
        open_ai_button = conversations_area.find('.mc-btn-open-ai');
        select_departments = conversations_area.find('#conversation-department');
        upload_input = admin.find('.mc-upload-form-admin .mc-upload-files');
        chatbot_area = admin.find('.mc-area-chatbot');
        chatbot_files_table = chatbot_area.find('#mc-table-chatbot-files');
        chatbot_website_table = chatbot_area.find('#mc-table-chatbot-website');
        chatbot_qea_repeater = chatbot_area.find('#mc-chatbot-qea');
        chatbot_playground_editor = chatbot_area.find('.mc-playground-editor');
        chatbot_playground_area = chatbot_area.find('.mc-playground .mc-scroll-area');
        flows_area = chatbot_area.find('[data-id="flows"] > .mc-content');
        flows_nav = chatbot_area.find('#mc-flows-nav');

        // Browser history
        window.onpopstate = function () {
            admin.mcHideLightbox();
            if (responsive && conversations_area.mcActive() && conversation_area.mcActive()) {
                MCConversations.mobileCloseConversation();
            }
            if (MCF.getURL('user')) {
                if (!users_area.mcActive()) {
                    header.find('.mc-admin-nav #mc-users').click();
                }
                MCProfile.show(MCF.getURL('user'));
            } else if (MCF.getURL('area')) {
                header.find('.mc-admin-nav #mc-' + MCF.getURL('area')).click();
            } else if (MCF.getURL('conversation')) {
                if (!conversations_area.mcActive()) {
                    header.find('.mc-admin-nav #mc-conversations').click();
                }
                MCConversations.openConversation(MCF.getURL('conversation'));
            } else if (MCF.getURL('setting')) {
                if (!settings_area.mcActive()) {
                    header.find('.mc-admin-nav #mc-settings').click();
                }
                settings_area.find('#tab-' + MCF.getURL('setting')).click();
            } else if (MCF.getURL('report')) {
                if (!reports_area.mcActive()) {
                    header.find('.mc-admin-nav #mc-reports').click();
                }
                reports_area.find('#' + MCF.getURL('report')).click();
            }
        };

        if (MCF.getURL('area')) {
            setTimeout(() => { header.find('.mc-admin-nav #mc-' + MCF.getURL('area')).click() }, 300);
        }

        // Installation
        if (typeof MC_ADMIN_SETTINGS == ND) {
            let area = admin.find('.mc-intall');
            let url = window.location.href.replace('/admin', '').replace('.php', '').replace(/#$|\/$/, '');
            $(admin).on('click', '.mc-submit-installation', function () {
                if (loading(this)) return;
                let message = false;
                let account = area.find('#first-name').length;
                if (MCForm.errors(area)) {
                    message = account ? 'All fields are required. Minimum password length is 8 characters. Be sure you\'ve entered a valid email.' : 'All fields are required.';
                } else {
                    if (account && area.find('#password input').val() != area.find('#password-check input').val()) {
                        message = 'The passwords do not match.';
                    } else {
                        MCF.cookie('SA_' + 'VGCKMENS', 0, 0, 'delete');
                        if (url.includes('?')) {
                            url = url.substr(0, url.indexOf('?'));
                        }
                        $.ajax({
                            method: 'POST',
                            url: url + '/include/ajax.php',
                            data: {
                                function: 'installation',
                                details: $.extend(MCForm.getAll(area), { url: url })
                            }
                        }).done((response) => {
                            if (isString(response)) {
                                response = JSON.parse(response);
                            }
                            if (response != false) {
                                response = response[1];
                                if (response === true) {
                                    setTimeout(() => {
                                        window.location.href = url + '/admin.php?refresh=true';
                                    }, 1000);
                                    return;
                                } else {
                                    switch (response) {
                                        case 'connection-error':
                                            message = 'Masi Chat cannot connect to the database. Please check the database information and try again.';
                                            break;
                                        case 'missing-details':
                                            message = 'Missing database details! Please check the database information and try again.';
                                            break;
                                        case 'missing-url':
                                            message = 'Masi Chat cannot get the plugin URL.';
                                            break;
                                        default:
                                            message = response;
                                    }
                                }
                            } else {
                                message = response;
                            }
                            if (message !== false) {
                                MCForm.showErrorMessage(area, message);
                                $('html, body').animate({ scrollTop: 0 }, 500);
                            }
                            $(this).mcLoading(false);
                        });
                    }
                }
                if (message !== false) {
                    MCForm.showErrorMessage(area, message);
                    $('html, body').animate({ scrollTop: 0 }, 500);
                    $(this).mcLoading(false);
                }
            });
            // Envato verification URL call removed
            return;
        }

        // Initialization
        if (!admin.length) {
            return;
        }
        loadingGlobal();
        admin.removeAttr('style');
        if (isPWA()) {
            admin.addClass('mc-pwa');
        }
        if (localhost) {
            clearCache();
        }
        if (admin.find(' > .mc-rich-login').length) {
            return;
        }
        MCF.storage('notifications-counter', []);
        if (MC_ADMIN_SETTINGS.pusher) {
            MCPusher.active = true;
            MCPusher.init(() => {
                MCPusher.presence(1, () => {
                    MCUsers.updateUsersActivity();
                });
                MCPusher.event('update-conversations', () => {
                    MCConversations.update();
                }, 'agents');
                MCPusher.event('set-agent-status', (response) => {
                    if (response.agent_id == MC_ACTIVE_AGENT.id) {
                        MCUsers.setActiveAgentStatus(response.status == 'online');
                        away_mode = false;
                    }
                }, 'agents');
                initialization();
            });
        } else {
            initialization();
            setInterval(function () {
                MCUsers.updateUsersActivity();
            }, 10000);
        }
        MCUsers.table_extra = users_table.find('th[data-extra]').map(function () { return $(this).attr('data-field') }).get();
        if (typeof MC_CLOUD_FREE != ND && MC_CLOUD_FREE) {
            setTimeout(() => { location.reload() }, 3600000);
        }

        // On Masi Chat close
        $(window).on('beforeunload', function () {
            if (activeUser()) {
                $.ajax({ method: 'POST', url: MC_AJAX_URL, data: { function: 'on-close' } });
            }
        });

        // Keyboard shortcuts
        $(window).keydown(function (e) {
            let code = e.which;
            let valid = false;
            active_keydown = code;
            if ([13, 27, 32, 37, 38, 39, 40, 46, 90].includes(code)) {
                if (admin.find('.mc-dialog-box').mcActive()) {
                    let target = admin.find('.mc-dialog-box');
                    switch (code) {
                        case 46:
                        case 27:
                            target.find('.mc-cancel').click();
                            break;
                        case 32:
                        case 13:
                            target.find(target.attr('data-type') != 'info' ? '.mc-confirm' : '.mc-close').click();
                            break;
                    }
                    valid = true;
                } else if ([38, 40, 46, 90].includes(code) && conversations_area.mcActive() && !admin.find('.mc-lightbox').mcActive()) {
                    let editor = conversations_area.find('.mc-editor textarea');
                    let is_editor_focus = editor.is(':focus');
                    if (code == 46) {
                        if (is_editor_focus || e.target.tagName == 'INPUT') {
                            return;
                        }
                        let target = conversations_area.find(' > div > .mc-conversation');
                        target.find('.mc-top [data-value="' + (target.attr('data-conversation-status') == 3 ? 'delete' : 'archive') + '"]').click();
                        valid = true;
                    } else if (e.ctrlKey) {
                        let target = conversations_admin_list_ul.find('.mc-active');
                        if (code == 40) {
                            target.next().click();
                        } else if (code == 38) {
                            target.prev().click();
                        } else if (code == 90 && is_editor_focus && MCConversations.previous_editor_text) {
                            editor.val(MCConversations.previous_editor_text);
                            MCConversations.previous_editor_text = false;
                            valid = true;
                        }
                        if (code == 38 || code == 40) {
                            valid = true;
                            MCConversations.scrollTo();
                        }
                    }
                } else if ([37, 39].includes(code) && users_area.mcActive() && admin.find('.mc-lightbox').mcActive()) {
                    let target = users_table.find(`[data-user-id="${activeUser().id}"]`);
                    target = code == 39 ? target.next() : target.prev();
                    if (target.length) {
                        admin.mcHideLightbox();
                        MCProfile.show(target.attr('data-user-id'));
                    }
                    valid = true;
                } else if (code == 27 && admin.find('.mc-lightbox').mcActive()) {
                    admin.mcHideLightbox();
                    valid = true;
                } else if (code == 46) {
                    let target = admin.find('.mc-search-btn.mc-active');
                    if (target.length) {
                        target.find('i').click();
                        valid = true;
                    }
                } else if (code == 13 && chatbot_area.find('.mc-playground-editor textarea').is(':focus')) {
                    chatbot_area.find('.mc-playground-editor [data-value="send"]').click();
                    valid = true;
                }
                if (valid) {
                    e.preventDefault();
                }
            }
        });

        $(window).keyup(function (e) {
            active_keydown = false;
        });

        // Check if the admin is active
        $(document).on('click keydown mousemove', function () {
            MCF.debounce(() => {
                if (!MCChat.tab_active) {
                    MCF.visibilityChange();
                }
                MCChat.tab_active = true;
                clearTimeout(active_interval);
                active_interval = setTimeout(() => {
                    MCChat.tab_active = false
                }, 10000);
            }, '#3', 8000);
            if (!responsive && MC_ADMIN_SETTINGS.away_mode) {
                MCF.debounce(() => {
                    if (away_mode) {
                        MCUsers.setActiveAgentStatus();
                        clearTimeout(away_timeout);
                        away_timeout = setTimeout(() => {
                            MCUsers.setActiveAgentStatus(false);
                        }, 600000);
                    }
                }, '#4', 558000);
            }
        });

        // Image from clipboard
        document.onpaste = function (pasteEvent) {
            let item = pasteEvent.clipboardData.items[0];
            if (item.type.indexOf('image') === 0) {
                var reader = new FileReader();
                reader.onload = function (event) {
                    let data = event.target.result.split(',')
                    let bytes = data[0].indexOf('base64') >= 0 ? atob(data[1]) : decodeURI(data[1])
                    let ia = new Uint8Array(bytes.length)
                    for (let i = 0; i < bytes.length; i++) {
                        ia[i] = bytes.charCodeAt(i)
                    }
                    let form = new FormData();
                    form.append('file', new Blob([ia], { type: data[0].split(':')[1].split(';')[0] }), 'image_print.jpg');
                    MCF.upload(form, function (response) { MCChat.uploadResponse(response) });
                };
                reader.readAsDataURL(item.getAsFile());
            }
        }

        // Updates and apps
        let messages = [mc_('Envato purchase code check has been removed.'), `${mc_('Your license key is expired. Please purchase a new license')} <a href="https://masichat.com/shop/{R}" target="_blank">${mc_('here')}</a>.`];
        $(header).on('click', '.mc-version', function () {
            let box = admin.find('.mc-updates-box');
            MCF.ajax({
                function: 'get-versions'
            }, (response) => {
                let code = '';
                let names = { mc: 'Masi Chat', slack: 'Slack', dialogflow: 'Artificial Intelligence', tickets: 'Tickets', woocommerce: 'Woocommerce', ump: 'Ultimate Membership Pro', perfex: 'Perfex', whmcs: 'WHMCS', aecommerce: 'Active eCommerce', messenger: 'Messenger', whatsapp: 'WhatsApp', armember: 'ARMember', telegram: 'Telegram', viber: 'Viber', line: 'LINE', wechat: 'WeChat', zalo: 'Zalo', twitter: 'Twitter', zendesk: 'Zendesk', martfury: 'Martfury', opencart: 'OpenCart', zalo: 'Zalo' };
                let updates = false;
                for (var key in response) {
                    if (MCApps.is(key)) {
                        let updated = MC_VERSIONS[key] == response[key];
                        if (!updated) {
                            updates = true;
                        }
                        code += `<div class="mc-input"><span>${names[key]}</span><div${updated ? ' class="mc-green"' : ''}>${updated ? mc_('You are running the latest version.') : mc_('Update available! Please update now.')} ${mc_('Your version is')} V ${MC_VERSIONS[key]}.</div></div>`;
                    }
                }
                if (updates) {
                    box.find('.mc-update').removeClass('mc-hide');
                } else {
                    box.find('.mc-update').addClass('mc-hide');
                }
                loadingGlobal(false);
                box.find('.mc-main').prepend(code);
                box.mcShowLightbox();
            });
            loadingGlobal();
            box.mcActive(false);
            box.find('.mc-input').remove();
        });

        $(admin).on('click', '.mc-updates-box .mc-update', function () {
            if (loading(this)) return;
            let box = admin.find('.mc-updates-box');
            MCF.ajax({
                function: 'update',
                domain: MC_URL
            }, (response) => {
                let error = '';
                if (MCF.errorValidation(response, 'envato-purchase-code-not-found')) {
                    error = messages[0]
                } else if (MCF.errorValidation(response)) {
                    error = MCF.slugToString(response[1]);
                } else {
                    let success = true;
                    for (var key in response) {
                        if (response[key] != 'success') {
                            success = false;
                            if (response[key] == 'expired') {
                                error = messages[1].replace('{R}', key);
                            }
                            if (response[key] == 'license-key-not-found') {
                                error = mc_('License key for the {R} app missing. Add it in Settings > Apps.').replace('{R}', MCF.slugToString(key.replace('dialogflow', 'Artificial Intelligence')));
                            }
                            break;
                        }
                    }
                    if (!success && !error) {
                        error = JSON.stringify(response);
                    }
                }
                clearCache();
                if (!error) {
                    infoBottom('Update completed.');
                    location.reload();
                } else {
                    MCForm.showErrorMessage(box, error);
                }
                $(this).mcLoading(false);
            });
        });

        setTimeout(function () {
            let last = MCF.storage('last-update-check');
            let today_arr = [today.getMonth(), today.getDate()];
            if (MC_ADMIN_SETTINGS.cloud) {
                return;
            }
            if (last == false || today_arr[0] != last[0] || (today_arr[1] > (last[1] + 10))) {
                MCF.storage('last-update-check', today_arr);
                if (MC_ADMIN_SETTINGS.auto_updates) {
                    MCF.ajax({
                        function: 'update',
                        domain: MC_URL
                    }, (response) => {
                        if (!isString(response) && !Array.isArray(response)) {
                            infoBottom('Automatic update completed. Reload the admin area to apply the update.');
                            clearCache();
                        }
                    });
                } else if (MC_ACTIVE_AGENT.user_type == 'admin') {
                    MCF.ajax({
                        function: 'updates-available'
                    }, (response) => {
                        if (response === true) {
                            infoBottom(`${mc_('Update available.')} <span onclick="$(\'.mc-version\').click()">${mc_('Click here to update now')}</span>`, 'info');
                        }
                    });
                }
            }
        }, 1000);

        $(admin).on('click', '.mc-apps > div:not(.mc-disabled)', function () {
            let box = admin.find('.mc-app-box');
            let app_name = $(this).data('app');
            let is_cloud = MC_ADMIN_SETTINGS.cloud;
            let is_active = MCApps.is(app_name) && (!is_cloud || MC_CLOUD_ACTIVE_APPS.includes(app_name));
            let ga = '?utm_source=plugin&utm_medium=admin_area&utm_campaign=plugin';
            if (!is_cloud) {
                MCF.ajax({
                    function: 'app-get-key',
                    app_name: app_name
                }, (response) => {
                    box.find('input').val(response);
                });
            }
            box.setClass('mc-active-app', is_active);
            box.find('input').val('');
            box.find('.mc-top-bar > div:first-child').html($(this).find('h2').html());
            box.find('p').html($(this).find('p').html());
            box.attr('data-app', app_name);
            box.find('.mc-btn-app-setting').mcActive(is_active);
            box.find('.mc-btn-app-puchase').attr('href', 'https://masichat.com/shop/' + app_name + ga);
            box.find('.mc-btn-app-details').attr('href', (is_cloud ? WEBSITE_URL : 'https://masichat.com/') + app_name + ga);
            box.mcShowLightbox();
        });

        $(admin).on('click', '.mc-app-box .mc-activate', function () {
            let box = admin.find('.mc-app-box');
            let key = box.find('input').val();
            let app_name = box.attr('data-app');
            if (key || MC_ADMIN_SETTINGS.cloud) {
                if (loading(this)) return;
                MCF.ajax({
                    function: 'app-activation',
                    app_name: app_name,
                    key: key
                }, (response) => {
                    if (MCF.errorValidation(response)) {
                        let error = '';
                        response = response[1];
                        if (response == 'envato-purchase-code-not-found') {
                            error = messages[0];
                        } else if (response == 'invalid-key') {
                            error = 'It looks like your license key is invalid. If you believe this is an error, please contact support.';
                        } else if (response == 'expired') {
                            error = messages[1].replace('{R}', key);
                        } else if (response == 'app-purchase-code-limit-exceeded') {
                            error = MCF.slugToString(app_name) + ' app purchase code limit exceeded.';
                        } else {
                            error = 'Error: ' + response;
                        }
                        MCForm.showErrorMessage(box, error);
                        $(this).mcLoading(false);
                    } else {
                        infoBottom('Activation complete! Page reload in progress...');
                        setTimeout(function () {
                            location.reload();
                        }, 1000);
                    }
                });
            } else {
                MCForm.showErrorMessage(box, 'Please insert the license key.');
            }
        });

        $(admin).on('click', '.mc-app-box .mc-btn-app-setting', function () {
            settings_area.find('#tab-' + $(this).closest('[data-app]').attr('data-app')).click();
            admin.mcHideLightbox();
        });

        // Desktop and flash notifications
        if (typeof Notification !== ND && (MC_ADMIN_SETTINGS.desktop_notifications == 'all' || MC_ADMIN_SETTINGS.desktop_notifications == 'agents') && !MC_ADMIN_SETTINGS.push_notifications) {
            MCConversations.desktop_notifications = true;
        }

        if (['all', 'agents'].includes(MC_ADMIN_SETTINGS.flash_notifications)) {
            MCConversations.flash_notifications = true;
        }

        // Cron jobs
        if (today.getDate() != MCF.storage('admin-clean')) {
            setTimeout(function () {
                MCF.ajax({ function: 'cron-jobs' });
                MCF.storage('admin-clean', today.getDate());
            }, 10000);
        }

        // Collapse button
        $(admin).on('click', '.mc-collapse-btn', function () {
            let active = $(this).mcActive();
            let height = active ? $(this).parent().data('height') + 'px' : '';
            $(this).html(mc_(active ? 'View more' : 'Close'));
            $(this).parent().find('> div, > ul').css({ 'height': height, 'max-height': height });
            $(this).mcActive(!active);
        });

        // Close lightbox popup
        $(admin).on('click', '.mc-popup-close', function () {
            admin.mcHideLightbox();
        });

        /*
        * ----------------------------------------------------------
        * Responsive
        * ----------------------------------------------------------
        */

        if (responsive) {

            conversations_user_details.find('> .mc-scroll-area').prepend('<div class="mc-profile"><img><span class="mc-name"></span></div>');

            $(admin).on('click', '.mc-menu-mobile > i', function () {
                $(this).toggleClass('mc-active');
                MCAdmin.open_popup = $(this).parent();
            });

            $(admin).on('click', '.mc-menu-mobile a', function () {
                $(this).closest('.mc-menu-mobile').find(' > i').mcActive(false);
            });

            $(admin).on('click', '.mc-menu-wide,.mc-nav', function () {
                $(this).toggleClass('mc-active');
            });

            $(admin).on('click', '.mc-menu-wide > ul > li, .mc-nav > ul > li', function (e) {
                let menu = $(this).parent().parent();
                menu.find('li').mcActive(false);
                menu.find('> div:not(.mc-menu-wide):not(.mc-btn)').html($(this).html());
                menu.mcActive(false);
                if (menu.find('> .mc-menu-wide').length) {
                    menu.closest('.mc-scroll-area').scrollTop(menu.next()[0].offsetTop - (admin.hasClass('mc-header-hidden') ? 70 : 130));
                }
                e.preventDefault();
                return false;
            });

            $(admin).find('.mc-admin-list .mc-scroll-area, main > div > .mc-scroll-area,.mc-area-settings > .mc-tab > .mc-scroll-area,.mc-area-reports > .mc-tab > .mc-scroll-area').on('scroll', function () {
                let scroll = $(this).scrollTop();
                if (scrolls.last < (scroll - 10) && scrolls.header) {
                    admin.addClass('mc-header-hidden');
                    scrolls.header = false;
                } else if (scrolls.last > (scroll + 10) && !scrolls.header && !scrolls.always_hidden) {
                    admin.removeClass('mc-header-hidden');
                    scrolls.header = true;
                }
                scrolls.last = scroll;
            });

            $(admin).on('click', '.mc-search-btn i,.mc-filter-btn i', function () {
                if ($(this).parent().mcActive()) {
                    admin.addClass('mc-header-hidden');
                    scrolls.always_hidden = true;
                } else {
                    scrolls.always_hidden = false;
                    if (conversations_admin_list_ul.parent().scrollTop() < 10) {
                        admin.removeClass('mc-header-hidden');
                    }
                }
            });

            $(admin).on('click', '.mc-top .mc-btn-back', function () {
                MCConversations.mobileCloseConversation();
            });

            $(users_table).find('th:first-child').html(mc_('Order by'));

            $(users_table).on('click', 'th:first-child', function () {
                $(this).parent().toggleClass('mc-active');
            });

            // Touch move
            document.addEventListener('touchstart', (e) => {
                touchmove_x = e.changedTouches[0].clientX;
                touchmove_y = e.changedTouches[0].clientY;
            }, false);

            document.addEventListener('touchend', () => {
                touchEndEvent();
            }, false);

            document.addEventListener('touchmove', (e) => {
                var x_up = e.changedTouches[0].clientX;
                var x_diff = touchmove_x - x_up;
                var y_up = e.changedTouches[0].clientY;
                var y_diff = touchmove_y - y_up;
                if (Math.abs(x_diff) > Math.abs(y_diff)) {
                    var target_sub = [];
                    touchmove = conversations_area.mcActive() ? [conversations_admin_list_ul.find('.mc-active'), conversations_area_list, 1] : [users_table.find(`[data-user-id="${activeUser().id}"]`), profile_box, 2];
                    if (x_diff > 150) {

                        // Left
                        if (touchmove[2] == 1) {
                            touchmove[0].next().click();
                        } else {
                            target_sub = touchmove[0].next();
                        }
                        touchEndEvent();
                    } else if (x_diff < -150) {

                        // Right
                        if (touchmove[2] == 1) {
                            touchmove[0].prev().click();
                        } else {
                            target_sub = touchmove[0].prev();
                        }
                        touchEndEvent();
                    }
                    if (touchmove[2] == 2 && target_sub.length) {
                        admin.mcHideLightbox();
                        MCProfile.show(target_sub.attr('data-user-id'));
                    }
                    if (x_diff > 80 || x_diff < -80) {
                        touchmove[1].css('transform', 'translateX(' + (x_diff * -1) + 'px)');
                        touchmove[1].addClass('mc-touchmove');
                    }
                }
            }, false);
        } else {
            if (!MC_ADMIN_SETTINGS.hide_conversation_details) {
                conversations_user_details.mcActive(true);
            } else {
                conversations_area.find('.mc-menu-mobile [data-value="panel"]').mcActive(true);
            }
        }

        if ($(window).width() < 913) {
            $(conversations_area).on('click', '> .mc-btn-collapse', function () {
                $(this).toggleClass('mc-active');
                conversations_area.find($(this).hasClass('mc-left') ? '.mc-admin-list' : '.mc-user-details').toggleClass('mc-active');
            });
        }

        /*
        * ----------------------------------------------------------
        * Left nav
        * ----------------------------------------------------------
        */

        $(header).on('click', ' .mc-admin-nav a', function () {
            active_admin_area = $(this).attr('id').substr(3);
            MCAdmin.active_admin_area = active_admin_area;
            header.find('.mc-admin-nav a').mcActive(false);
            admin.find(' > main > div').mcActive(false);
            admin.find('.mc-area-' + active_admin_area).mcActive(true);
            $(this).mcActive(true);
            MCF.deactivateAll();
            switch (active_admin_area) {
                case 'conversations':
                    if (!responsive && !MCF.getURL('conversation')) {
                        MCConversations.clickFirst();
                    }
                    MCConversations.update();
                    MCConversations.startRealTime();
                    MCUsers.stopRealTime();
                    break;
                case 'users':
                    MCUsers.startRealTime();
                    MCConversations.stopRealTime();
                    if (!MCUsers.init) {
                        loadingGlobal();
                        users_pagination = 1;
                        users_pagination_count = 1;
                        MCUsers.get((response) => {
                            MCUsers.populate(response);
                            MCUsers.updateMenu();
                            MCUsers.init = true;
                            MCUsers.datetime_last_user = MCF.dateDB('now');
                            loadingGlobal(false);
                        });
                    }
                    break;
                case 'settings':
                    if (!MCSettings.init) {
                        loadingGlobal();
                        MCF.ajax({
                            function: 'get-all-settings'
                        }, (response) => {
                            if (response) {
                                let translations = response['external-settings-translations'];
                                if (response['slack-agents']) {
                                    let code = '';
                                    for (var key in response['slack-agents'][0]) {
                                        code += `<div data-id="${key}"><select><option value="${response['slack-agents'][0][key]}"></option></select></div>`;
                                    }
                                    settings_area.find('#slack-agents .input').html(code);
                                }
                                MCSettings.translations.translations = Array.isArray(translations) && !translations.length ? {} : translations;
                                delete response['external-settings-translations'];
                                for (var key in response) {
                                    MCSettings.set(key, response[key]);
                                }
                            }
                            if (MCF.getURL('refresh_token')) {
                                admin.find('#google-refresh-token input').val(MCF.getURL('refresh_token'));
                                MCSettings.save();
                                infoBottom('Synchronization completed.');
                                admin.find('#google')[0].scrollIntoView();
                            }
                            settings_area.find('textarea').each(function () {
                                $(this).autoExpandTextarea();
                                $(this).manualExpandTextarea();
                            });
                            settings_area.find('[data-setting] .mc-language-switcher-cnt').each(function () {
                                $(this).mcLanguageSwitcher(MCSettings.translations.getLanguageCodes($(this).closest('[data-setting]').attr('id')), 'settings');
                            });
                            MCSettings.init = true;
                            loadingGlobal(false);
                            if (response && !MC_ADMIN_SETTINGS.cloud) {
                                MCSettings.visibility(0, response['push-notifications'] && response['push-notifications'][0]['push-notifications-provider'][0] == 'pusher');
                            }
                            MCSettings.visibility(1, response['messenger'] ? response['messenger'][0]['messenger-sync-mode'][0] != 'manual' : true);
                            MCSettings.visibility(2, response['open-ai'] ? response['open-ai'][0]['open-ai-mode'][0] != 'assistant' : true);
                            MCF.event('MCSettingsLoaded', response);
                        });
                    }
                    MCUsers.stopRealTime();
                    MCConversations.stopRealTime();
                    break;
                case 'reports':
                    if (reports_area.mcLoading()) {
                        $.getScript(MC_URL + '/vendor/moment.min.js', () => {
                            $.getScript(MC_URL + '/vendor/daterangepicker.min.js', () => {
                                $.getScript(MC_URL + '/vendor/chart.min.js', () => {
                                    MCReports.initDatePicker();
                                    MCReports.initReport('conversations');
                                    reports_area.mcLoading(false);
                                });
                            });
                        });
                    }
                    MCUsers.stopRealTime();
                    MCConversations.stopRealTime();
                    break;
                case 'articles':
                    let nav = articles_area.find('.mc-menu-wide li').eq(0);
                    if (articles_area.mcLoading()) {
                        nav.mcActive(true).next().mcActive(false);
                        MCF.ajax({
                            function: 'init-articles-admin'
                        }, (response) => {
                            MCArticles.categories.list = response[1];
                            MCArticles.translations.list = response[2];
                            MCArticles.page_url = response[3];
                            MCArticles.is_url_rewrite = response[4];
                            MCArticles.cloud_chat_id = response[5];
                            MCArticles.populate(response[0]);
                            MCArticles.populate(response[1], true);
                            MCArticles.categories.update();
                            articles_area.mcLoading(false);
                        });
                    } else {
                        nav.click();
                    }
                    MCUsers.stopRealTime();
                    MCConversations.stopRealTime();
                    break;
                case 'chatbot':
                    if (chatbot_files_table.mcLoading()) {
                        MCApps.openAI.init();
                    }
                    MCApps.openAI.troubleshoot();
            }
            let url_area = MCF.getURL('area');
            if (url_area != active_admin_area && ((active_admin_area == 'conversations' && !MCF.getURL('conversation')) || (active_admin_area == 'users' && !MCF.getURL('user')) || (active_admin_area == 'settings' && !MCF.getURL('setting')) || (active_admin_area == 'reports' && !MCF.getURL('report')) || (active_admin_area == 'articles' && !MCF.getURL('article')) || (active_admin_area == 'chatbot' && !MCF.getURL('chatbot')))) {
                pushState('?area=' + active_admin_area);
            }
        });

        $(header).on('click', '.mc-profile', function () {
            $(this).next().toggleClass('mc-active');
        });

        $(header).on('click', '[data-value="logout"],.logout', function () {
            MCAdmin.is_logout = true;
            MCF.ajax({ function: 'on-close' });
            MCUsers.stopRealTime();
            MCConversations.stopRealTime();
            setTimeout(() => { MCF.logout() }, 300);
        });

        $(header).on('click', '[data-value="edit-profile"],.edit-profile', function () {
            loadingGlobal();
            let user = new MCUser({ id: MC_ACTIVE_AGENT.id });
            user.update(() => {
                activeUser(user);
                conversations_area.find('.mc-board').addClass('mc-no-conversation');
                conversations_admin_list_ul.find('.mc-active').mcActive(false);
                MCProfile.showEdit(user);
            });
        });

        $(header).on('click', '[data-value="status"]', function () {
            let is_offline = !$(this).hasClass('mc-online');
            MCUsers.setActiveAgentStatus(is_offline);
            away_mode = is_offline;
        });

        $(header).find('.mc-account').setProfile(MC_ACTIVE_AGENT['full_name'], MC_ACTIVE_AGENT['profile_image']);

        /*
        * ----------------------------------------------------------
        * Conversations area
        * ----------------------------------------------------------
        */

        // Open the conversation clicked on the left menu
        $(conversations_admin_list_ul).on('click', 'li', function () {
            if (active_keydown == 17) {
                $(this).mcActive(!$(this).mcActive());
            } else {
                MCConversations.openConversation($(this).attr('data-conversation-id'), $(this).attr('data-user-id'), false);
                MCF.deactivateAll();
            }
        });

        // Open the user conversation clicked on the bottom right area or user profile box
        $(admin).on('click', '.mc-user-conversations li', function () {
            MCConversations.openConversation($(this).attr('data-conversation-id'), activeUser().id, $(this).attr('data-conversation-status'));
        });

        // Archive, delete or restore conversations
        $(conversations_area).on('click', '.mc-top ul a', function () {
            let status_code;
            let selected_conversations = MCConversations.getSelectedConversations();
            let selected_conversations_length = selected_conversations.length;
            let multi_selection = selected_conversations_length > 1;
            let message = multi_selection ? 'All the selected conversations will be ' : 'The conversation will be ';
            let value = $(this).attr('data-value');
            let on_success = (response, action) => {
                let success = response.includes('.txt');
                $(this).mcLoading(false);
                if (action == 'email') {
                    actioninfoBottom(success ? mc_('Transcript sent to user\'s email.') + ' <a href="' + response + '" target="_blank">' + mc_('View transcript') + '</a>' : 'Transcript sending error: ' + response, success ? '' : 'error');
                }
            }
            switch (value) {
                case 'inbox':
                    status_code = 1;
                    message += 'restored.';
                    break;
                case 'archive':
                    message += 'archived.';
                    status_code = 3;
                    break;
                case 'delete':
                    message += 'deleted.';
                    status_code = 4;
                    break;
                case 'empty-trash':
                    status_code = 5;
                    message = 'All conversations in the trash (including their messages) will be deleted permanently.'
                    break;
                case 'transcript':
                    let action = $(this).attr('data-action');
                    if (action == 'email' && (!activeUser() || !activeUser().get('email'))) {
                        action = '';
                    }
                    MCConversations.transcript(selected_conversations[0].id, selected_conversations[0].user_id, action, (response) => on_success(response, action));
                    loading(this);
                    break;
                case 'read':
                    status_code = 1;
                    message += 'marked as read.';
                    break;
                case 'unread':
                    status_code = 2;
                    message += 'marked as unread.';
                    break;
                case 'panel':
                    $([conversations_user_details, this]).toggleClass('mc-active');
                    break;
            }
            if (status_code) {
                infoPanel(message, 'alert', function () {
                    let active_conversations_filter = conversations_filters.eq(0).find('p').attr('data-value');
                    let last_conversation_id = selected_conversations[selected_conversations_length - 1].id;
                    for (var i = 0; i < selected_conversations_length; i++) {
                        let conversation = selected_conversations[i];
                        MCF.ajax({
                            function: 'update-conversation-status',
                            conversation_id: conversation.id,
                            status_code: status_code
                        }, () => {
                            let conversation_li = conversations_admin_list_ul.find(`[data-conversation-id="${conversation.id}"]`);
                            if ([0, 3, 4].includes(status_code)) {
                                for (var j = 0; j < conversations.length; j++) {
                                    if (conversations[j].id == conversation.id) {
                                        conversations[j].set('status_code', status_code);
                                        break;
                                    }
                                }
                            }
                            if (MC_ADMIN_SETTINGS.close_message && status_code == 3) {
                                MCF.ajax({ function: 'close-message', conversation_id: conversation.id, bot_id: MC_ADMIN_SETTINGS.bot_id });
                                if (MC_ADMIN_SETTINGS.close_message_transcript) {
                                    MCConversations.transcript(conversation.id, conversation.user_id, 'email', (response) => on_success(response));
                                }
                            }
                            if ([0, 1, 2].includes(status_code)) {
                                conversation_li.attr('data-conversation-status', status_code);
                                MCConversations.updateMenu();
                            }
                            if (MCChat.conversation && MCApps.is('slack') && [3, 4].includes(status_code)) {
                                MCF.ajax({ function: 'archive-slack-channels', conversation_user_id: MCChat.conversation.get('user_id') });
                            }
                            if ((active_conversations_filter == 0 && [3, 4].includes(status_code)) || (active_conversations_filter == 3 && [0, 1, 2, 4].includes(status_code)) || (active_conversations_filter == 4 && status_code != 4)) {
                                let previous = false;
                                MCConversations.updateMenu();
                                if (MCChat.conversation && MCChat.conversation.id == conversation.id) {
                                    previous = conversation_li.prev();
                                    MCChat.conversation = false;
                                }
                                conversation_li.remove();
                                if (conversation.id == last_conversation_id) {
                                    MCConversations.clickFirst(previous);
                                }
                            }
                            if (active_conversations_filter == 4 && status_code == 5) {
                                conversations_admin_list_ul.find('li').remove();
                                MCConversations.updateMenu();
                                MCConversations.clickFirst();
                            }
                        });
                        if (MCChat.conversation && MCChat.conversation.id == conversation.id) {
                            MCChat.conversation.set('status_code', status_code);
                            MCConversations.setReadIcon(status_code);
                        }
                    }
                });
            }
        });

        // Saved replies
        MCF.ajax({
            function: 'saved-replies'
        }, (response) => {
            let code = `<p class="mc-no-results">${mc_('No saved replies found. Add new saved replies via Settings > Admin.')}</p>`;
            if (Array.isArray(response)) {
                if (response.length && response[0]['reply-name']) {
                    code = '';
                    saved_replies_list = response;
                    for (var i = 0; i < response.length; i++) {
                        code += `<li><div>${response[i]['reply-name']}</div><div>${response[i]['reply-text'].replace(/\\n/g, '\n')}</div></li>`;
                    }
                }
            }
            saved_replies.find('.mc-replies-list > ul').html(code).mcLoading(false);
        });

        $(conversations_area).on('click', '.mc-btn-saved-replies', function () {
            saved_replies.mcTogglePopup(this);
            saved_replies.find('.mc-search-btn').mcActive(true).find('input').get(0).focus();
        });

        $(saved_replies).on('click', '.mc-replies-list li', function () {
            MCChat.insertText($(this).find('div:last-child').text().replace(/\\n/g, '\n'));
            MCF.deactivateAll();
            admin.removeClass('mc-popup-active');
        });

        $(saved_replies).on('input', '.mc-search-btn input', function () {
            saved_reply_search($(this).val().toLowerCase());
        });

        $(saved_replies).on('click', '.mc-search-btn i', function () {
            MCF.searchClear(this, () => { saved_reply_search('') });
        });

        $(admin).on('click', '.mc-btn-open-ai', function () {
            if (!MCChat.conversation || loading(this)) return;
            let is_editor = $(this).hasClass('mc-btn-open-ai-editor');
            let textarea = is_editor ? conversations_area.find('.mc-editor textarea') : dialogflow_intent_box.find('textarea');
            MCApps.openAI.rewrite(textarea.val(), (response) => {
                $(this).mcLoading(false);
                if (response[0]) {
                    textarea.val(is_editor ? '' : response[1]);
                    if (is_editor) {
                        MCChat.insertText(response[1]);
                    }
                }
            });
        });

        // Pagination for conversations
        $(conversations_admin_list).find('.mc-scroll-area').on('scroll', function () {
            if (!is_busy && !MCConversations.is_search && scrollPagination(this, true) && pagination_count) {
                let parent = conversations_area.find('.mc-admin-list');
                let filters = MCConversations.filters();
                is_busy = true;
                parent.append('<div class="mc-loading-global mc-loading"></div>');
                MCF.ajax({
                    function: 'get-conversations',
                    pagination: pagination,
                    status_code: filters[0],
                    department: filters[1],
                    source: filters[2],
                    tag: filters[3],
                    agent_id: filters[4]
                }, (response) => {
                    setTimeout(() => { is_busy = false }, 500);
                    pagination_count = response.length;
                    if (pagination_count) {
                        let code = '';
                        for (var i = 0; i < pagination_count; i++) {
                            let conversation = new MCConversation([new MCMessage(response[i])], response[i]);
                            code += MCConversations.getListCode(conversation);
                            conversations.push(conversation);
                        }
                        pagination++;
                        conversations_admin_list_ul.append(code);
                    }
                    parent.find(' > .mc-loading').remove();
                    MCF.event('MCAdminConversationsLoaded', { conversations: response });
                });
            }
        });

        // Event: message deleted
        $(document).on('MCMessageDeleted', function () {
            let last_message = MCChat.conversation.getLastMessage();
            if (last_message != false) {
                conversations_admin_list_ul.find('li.mc-active p').html(last_message.message);
            } else {
                conversations_admin_list_ul.find('li.mc-active').remove();
                MCConversations.clickFirst();
                MCConversations.scrollTo();
            }
        });

        // Event: message sent
        $(document).on('MCMessageSent', function (e, response) {
            let conversation_id = response.conversation_id;
            let item = getListConversation(conversation_id);
            let message_part = mc_('Error. Message not sent to');
            let conversation = response.conversation;
            let user = response.user;
            if (response.conversation_status_code) {
                MCConversations.updateMenu();
            }
            if (MCApps.messenger.check(conversation)) {
                MCApps.messenger.send(user.getExtra('facebook-id').value, conversation.get('extra'), response.message, response.attachments, response.message_id, response.message_id, (response) => {
                    for (var i = 0; i < response.length; i++) {
                        if (response[i] && response[i].error) {
                            infoPanel(message_part + ' Messenger: ' + response[i].error.message, 'info', false, 'error-fb');
                        }
                    }
                });
            }
            if (MCApps.whatsapp.check(conversation)) {
                MCApps.whatsapp.send(MCApps.whatsapp.activeUserPhone(user), response.message, response.attachments, conversation.get('extra'), (response) => {
                    if (response.ErrorCode || (response.meta && !response.meta.success)) {
                        infoPanel(message_part + ' WhatsApp: ' + ('ErrorCode' in response ? response.errorMessage : response.meta.developer_message), 'info', false, 'error-wa');
                    }
                });
            }
            if (MCApps.telegram.check(conversation)) {
                MCApps.telegram.send(conversation.get('extra'), response.message, response.attachments, conversation_id, (response) => {
                    if (!response || !response.ok) {
                        infoPanel(message_part + ' Telegram: ' + JSON.stringify(response), 'info', false, 'error-tg');
                    }
                });
            }
            if (MCApps.viber.check(conversation)) {
                MCApps.viber.send(user.getExtra('viber-id').value, response.message, response.attachments, (response) => {
                    if (!response || response.status_message != 'ok') {
                        infoPanel(message_part + ' Viber: ' + JSON.stringify(response), 'info', false, 'error-vb');
                    }
                });
            }
            if (MCApps.zalo.check(conversation)) {
                MCApps.zalo.send(user.getExtra('zalo-id').value, response.message, response.attachments, (response) => {
                    if (response && response.error.error) {
                        infoPanel(message_part + ' Zalo: ' + response.error.message ? response.error.message : response.message, 'info', false, 'error-za');
                    }
                });
            }
            if (MCApps.twitter.check(conversation)) {
                MCApps.twitter.send(user.getExtra('twitter-id').value, response.message, response.attachments, (response_2) => {
                    if (response_2 && !response_2.event) {
                        infoPanel(JSON.stringify(response_2), 'info', false, 'error-tw');
                    } else if (response.attachments.length > 1) {
                        infoBottom('Only the first attachment was sent to Twitter.');
                    }
                });
            }
            if (MCApps.line.check(conversation)) {
                MCApps.line.send(user.getExtra('line-id').value, response.message, response.attachments, conversation_id, (response) => {
                    if (response.error) {
                        infoPanel(message_part + ' LINE: ' + JSON.stringify(response), 'info', false, 'error-ln');
                    }
                });
            }
            if (MCApps.wechat.check(conversation)) {
                MCApps.wechat.send(user.getExtra('wechat-id').value, response.message, response.attachments, (response) => {
                    if (!response || response.errmsg != 'ok') {
                        infoPanel(message_part + ' WeChat: ' + JSON.stringify(response), 'info', false, 'error-wc');
                    }
                });
            }
            if (MC_ADMIN_SETTINGS.smart_reply) {
                suggestions_area.html('');
            }
            if (MC_ADMIN_SETTINGS.assign_conversation_to_agent && MCF.null(conversation.get('agent_id'))) {
                MCConversations.assignAgent(conversation_id, MC_ACTIVE_AGENT.id, () => {
                    if (MCChat.conversation.id == conversation_id) {
                        MCChat.conversation.set('agent_id', MC_ACTIVE_AGENT.id);
                        $(conversations_area).find('#conversation-agent > p').attr('data-value', MC_ACTIVE_AGENT.id).html(MC_ACTIVE_AGENT.full_name);
                    }
                });
            }
        });

        // Event: new message of active chat conversation received
        $(document).on('MCNewMessagesReceived', function (e, response) {
            let messages = response.messages;
            for (var i = 0; i < messages.length; i++) {
                let message = messages[i];
                let payload = message.payload();
                let agent = MCF.isAgent(message.get('user_type'));
                setTimeout(function () {
                    conversation_area.find('.mc-top .mc-status-typing').remove();
                }, 300);
                if (MCAdmin.must_translate) {
                    let message_html = conversation_area.find(`[data-id="${message.id}"]`);
                    let message_original = payload['original-message'] ? payload['original-message'] : false;
                    if (message_original) {
                        message_html.replaceWith(message.getCode());
                        conversation_area.find(`[data-id="${message.id}"] .mc-menu`).prepend(`<li data-value="translation">${mc_('View translation')}</li>`);
                        if (MC_ADMIN_SETTINGS.smart_reply) {
                            MCApps.dialogflow.smartReply(MCF.escape(message_original));
                        }
                    } else if (message.message) {
                        MCApps.dialogflow.translate([message.message], MC_ADMIN_SETTINGS.active_agent_language, (response_2) => {
                            if (response_2) {
                                message.payload('translation', response_2[0]);
                                message.payload('translation-language', MC_ADMIN_SETTINGS.active_agent_language);
                                message_html.replaceWith(message.getCode());
                                conversation_area.find(`[data-id="${message.id}"] .mc-menu`).prepend(`<li data-value="original">${mc_('View original message')}</li>`);
                            }
                            if (MC_ADMIN_SETTINGS.smart_reply) {
                                MCApps.dialogflow.smartReply(response_2[0]);
                            }
                            conversations_admin_list_ul.find(`[data-conversation-id="${response.conversation_id}"] p`).html(response_2[0]);
                        }, [message.id], MCChat.conversation.id);
                    }
                } else if (MC_ADMIN_SETTINGS.smart_reply) {
                    MCApps.dialogflow.smartReply(message.message);
                }
                if (payload) {
                    if (payload.department) {
                        MCConversations.setActiveDepartment(payload.department);
                    }
                    if (payload.agent) {
                        MCConversations.setActiveAgent(payload.agent);
                    }
                }
                if ('ErrorCode' in payload || (payload.errors && payload.errors.length)) {
                    infoPanel('Error. Message not sent to WhatsApp. Error message: ' + (payload.ErrorCode ? payload.ErrorCode : payload.errors[0].title));
                }
                if ('whatsapp-templates' in payload) {
                    infoBottom(`Message sent as text message.${'whatsapp-template-fallback' in payload ? ' The user has been notified via WhatsApp Template notification.' : ''}`);
                }
                if ('whatsapp-template-fallback' in payload && !('whatsapp-templates' in payload)) {
                    infoBottom('The user has been notified via WhatsApp Template notification.');
                }
                if (!agent && MCChat.conversation.id == response.conversation_id && !MCChat.user_online) {
                    MCUsers.setActiveUserStatus();
                }
            }
            MCConversations.update();
        });

        // Event: new conversation created 
        $(document).on('MCNewConversationCreated', function () {
            MCConversations.update();
        });

        // Event: email notification sent
        $(document).on('MCEmailSent', function () {
            infoBottom(`The user has been notified by email.`);
        });

        // Event: SMS notification sent
        $(document).on('MCSMSSent', function () {
            infoBottom('The user has been notified by text message.');
        });

        // Event: Message notifications
        $(document).on('MCNotificationsSent', function (e, response) {
            infoBottom(`The user ${response.includes('cron') ? 'will be' : 'has been'} notified by email${response.includes('sms') ? ' and text message' : ''}.`);
        });

        // Event: user typing status change
        $(document).on('MCTyping', function (e, response) {
            MCConversations.typing(response);
        });

        // Conversations search
        $(conversations_admin_list).on('input', '.mc-search-btn input', function () {
            MCConversations.search(this);
        });

        $(conversations_area).on('click', '.mc-admin-list .mc-search-btn i', function () {
            MCF.searchClear(this, () => { MCConversations.search($(this).next()) });
        });

        // Conversations filter
        $(conversations_filters).on('click', 'li', function (e) {
            let parent = conversations_admin_list_ul.parent();
            if (loading(parent)) {
                e.preventDefault()
                return false;
            }
            setTimeout(() => {
                let filters = MCConversations.filters();
                pagination = 1;
                pagination_count = 1;
                MCF.ajax({
                    function: 'get-conversations',
                    status_code: filters[0],
                    department: filters[1],
                    source: filters[2],
                    tag: filters[3],
                    agent_id: filters[4]
                }, (response) => {
                    MCConversations.populateList(response);
                    conversation_area.attr('data-conversation-status', filters[0]);
                    if (response.length) {
                        if (!responsive) {
                            if (MCChat.conversation) {
                                let conversation = getListConversation(MCChat.conversation.id);
                                if (conversation.length) {
                                    conversation.mcActive(true);
                                } else if (filters[0] == MCChat.conversation.status_code) {
                                    conversations_admin_list_ul.prepend(MCConversations.getListCode(MCChat.conversation));
                                } else {
                                    MCConversations.clickFirst();
                                }
                            } else {
                                MCConversations.clickFirst();
                            }
                            MCConversations.scrollTo();
                        }
                    } else {
                        conversations_area.find('.mc-board').addClass('mc-no-conversation');
                        MCChat.conversation = false;
                    }
                    $(this).closest('.mc-filter-btn').attr('data-badge', conversations_filters.slice(1).toArray().reduce((acc, filter) => acc + !!$(filter).find('li.mc-active').data('value'), 0));
                    parent.mcLoading(false);
                });
            }, 100);
        });

        // Display the user details box
        $(conversations_area).on('click', '.mc-user-details .mc-profile,.mc-top > a', function () {
            let user_id = conversations_admin_list_ul.find('.mc-active').attr('data-user-id');
            if (activeUser().id != user_id) {
                activeUser(users[user_id]);
            }
            MCProfile.show(activeUser().id);
        });

        // Right profile list methods
        $(admin).on('click', '.mc-profile-list-conversation li', function () {
            let label = $(this).find('label');
            let label_value = label.html();
            switch ($(this).attr('data-id')) {
                case 'location':
                    let location = label_value.replace(', ', '+');
                    infoPanel('<iframe src="https://maps.google.com/maps?q=' + location + '&output=embed"></iframe>', 'map');
                    break;
                case 'timezone':
                    MCF.getLocationTimeString(activeUser().extra, (response) => {
                        loadingGlobal(false);
                        infoPanel(response);
                    });
                    break;
                case 'current_url':
                    window.open('//' + (MCF.null(label.attr('data-value')) ? label_value : label.attr('data-value')));
                    break;
                case 'conversation-source':
                    let source = label_value.toLowerCase();
                    if (source == 'whatsapp' && activeUser().getExtra('phone')) {
                        window.open('https://wa.me/' + MCApps.whatsapp.activeUserPhone());
                    } else if (source == 'facebook') {
                        window.open('https://www.facebook.com/messages/t/' + MCChat.conversation.get('extra'));
                    } else if (source == 'instagram') {
                        window.open('https://www.instagram.com/direct/inbox/');
                    } else if (source == 'twitter') {
                        window.open('https://twitter.com/messages/');
                    }
                    break;
                case 'wp-id':
                    window.open(window.location.href.substr(0, window.location.href.lastIndexOf('/')) + '/user-edit.php?user_id=' + activeUser().getExtra('wp-id').value);
                    break;
                case 'envato-purchase-code':
                    loadingGlobal();
                    MCF.ajax({
                        function: 'envato',
                        purchase_code: label_value
                    }, (response) => {
                        let code = '';
                        if (response && response.item) {
                            response.name = response.item.name;
                            for (var key in response) {
                                if (isString(response[key]) || !isNaN(response[key])) {
                                    code += `<b>${MCF.slugToString(key)}</b> ${response[key]} <br>`;
                                }
                            }
                            loadingGlobal(false);
                            infoPanel(code, 'info', false, 'mc-envato-box');
                        } else {
                            infoBottom(MCF.slugToString(response));
                        }
                    });
                    break;
                case 'email':
                case 'cc':
                    if (MCChat.conversation && MCChat.conversation.get('source') == 'em') {
                        let cc = MCChat.conversation.get('extra').split(',');
                        let code = `<div data-type="repeater" class="mc-setting mc-type-repeater"><div class="input"><div class="mc-repeater">`;
                        for (var i = 0; i < cc.length; i++) {
                            code += `<div class="repeater-item"><div><input data-id="cc" type="text" value="${cc[i]}"></div><i class="mc-icon-close"></i></div>`;
                        }
                        code += `</div><div class="mc-btn mc-btn-white mc-repeater-add mc-icon"><i class="mc-icon-plus"></i>${mc_('Add new item')}</div></div></div>`;
                        MCAdmin.genericPanel('cc', 'Manage CC', code, ['Save changes'], '', true);
                    }
                    break;
            }
        });

        $(conversations_user_details).on('click', '.mc-user-details-close', function () {
            conversations_area.find('.mc-menu-mobile [data-value="panel"]').click().mcActive(true);
        });

        // Dialogflow
        $(dialogflow_intent_box).on('click', '.mc-intent-add [data-value="add"]', function () {
            dialogflow_intent_box.find('> div > .mc-type-text').last().after('<div class="mc-setting mc-type-text"><input type="text"></div>');
        });

        $(dialogflow_intent_box).on('click', '.mc-intent-add [data-value="previous"],.mc-intent-add [data-value="next"]', function () {
            let input = dialogflow_intent_box.find('.mc-first input');
            let message = input.val();
            let next = $(this).attr('data-value') == 'next';
            let messages = MCChat.conversation.getUserMessages();
            let messages_length = messages.length;
            for (var i = 0; i < messages_length; i++) {
                if (messages[i].message == message && ((next && i < (messages_length - 1)) || (!next && i > 0))) {
                    i = i + (next ? 1 : -1);
                    input.val(messages[i].message);
                    dialogflow_intent_box.attr('data-message-id', messages[i].id);
                    MCApps.openAI.generateQuestions(messages[i].message);
                    break;
                }
            }
        });

        $(dialogflow_intent_box).on('click', '.mc-send', function () {
            MCApps.dialogflow.submitIntent(this);
        });

        $(dialogflow_intent_box).on('input', '.mc-search-btn input', function () {
            MCApps.dialogflow.searchIntents($(this).val());
        });

        $(dialogflow_intent_box).on('click', '.mc-search-btn i', function () {
            MCF.searchClear(this, () => { MCApps.dialogflow.searchIntents($(this).val()) });
        });

        $(dialogflow_intent_box).on('click', '#mc-intent-preview', function () {
            MCApps.dialogflow.previewIntentDialogflow(dialogflow_intent_box.find('#mc-intents-select').val());
        });

        $(dialogflow_intent_box).on('click', '#mc-qea-preview', function () {
            MCApps.dialogflow.previewIntent(dialogflow_intent_box.find('#mc-qea-select').val());
        });

        $(dialogflow_intent_box).on('change', '#mc-intents-select', function () {
            let intent = $(this).val();
            dialogflow_intent_box.find('.mc-bot-response').css('opacity', intent ? .5 : 1).find('textarea').val(intent ? MCApps.dialogflow.getIntent(intent).messages[0].text.text[0] : MCApps.dialogflow.original_response);
            dialogflow_intent_box.find('#mc-train-chatbots').val(intent ? 'dialogflow' : '');
        });

        $(dialogflow_intent_box).on('change', '#mc-qea-select', function () {
            let qea = $(this).val();
            dialogflow_intent_box.find('.mc-bot-response').setClass('mc-disabled', qea).find('textarea').val(qea ? MCApps.dialogflow.qea[qea][1] : MCApps.dialogflow.original_response);
            dialogflow_intent_box.find('#mc-train-chatbots').val(qea ? 'dialogflow' : '');
        });

        $(dialogflow_intent_box).on('change', 'textarea', function () {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                MCApps.dialogflow.original_response = dialogflow_intent_box.find('textarea').val();
            }, 500);
        });

        $(dialogflow_intent_box).on('change', '#mc-train-chatbots', function () {
            dialogflow_intent_box.find('.mc-type-text:not(.mc-first)').setClass('mc-hide', $(this).val() == 'open-ai');
        });

        // Departments
        $(select_departments).on('click', 'li', function (e) {
            let select = $(this).parent().parent();
            if ($(this).data('id') == select.find(' > p').attr('data-id')) {
                setTimeout(() => { $(this).mcActive(false); }, 100);
                return true;
            }
            if (!MCChat.conversation) {
                $(this).parent().mcActive(false);
                e.preventDefault();
                return false;
            }
            if (!select.mcLoading()) infoPanel(`${mc_('All agents assigned to the new department will be notified. The new department will be')} ${$(this).html()}.`, 'alert', () => {
                let value = $(this).data('id');
                select.mcLoading(true);
                MCConversations.assignDepartment(MCChat.conversation.id, value, () => {
                    MCConversations.setActiveDepartment(value);
                    select.mcLoading(false);
                });
            });
            e.preventDefault();
            return false;
        });

        // Agent assignment
        $(conversations_area).on('click', '#conversation-agent li', function (e) {
            let select = $(this).parent().parent();
            let agent_id = $(this).data('id');
            if (agent_id == select.find(' > p').attr('data-value')) return true;
            if (!MCChat.conversation) {
                $(this).parent().mcActive(false);
                e.preventDefault();
                return false;
            }
            if (!select.mcLoading()) {
                infoPanel(`${mc_('The new agent will be')} ${$(this).html()}.`, 'alert', () => {
                    select.mcLoading(true);
                    let selected_conversations = MCConversations.getSelectedConversations();
                    selected_conversations.forEach(conversation => {
                        MCConversations.assignAgent(conversation.id, agent_id, () => {
                            if (MCChat.conversation && MCChat.conversation.id == conversation.id) {
                                MCConversations.setActiveAgent(agent_id);
                            }
                        });
                    });
                    select.mcLoading(false);
                });
            }
            e.preventDefault();
            return false;
        });

        // Notes and Tags 
        notes_panel.on('click', '> i,.mc-edit-note', function (e) {
            let note = $(this).hasClass('mc-edit-note') ? $(this).closest('[data-id]') : false;
            MCAdmin.genericPanel('notes', note ? 'Edit note' : 'Add new note', `<div class="mc-setting mc-type-textarea"><textarea${note ? ' data-id="' + note.attr('data-id') + '"' : ''} placeholder="${mc_('Write here your note...')}">${note ? note.find('.mc-note-text').html().replace(/<a\s+href="([^"]*)".*?>(.*?)<\/a>/gi, '$1').replaceAll('<br>', '\n') : ''}</textarea></div>`, [[note ? 'Update note' : 'Add note', note ? 'check' : 'plus']]);
            if (MCApps.is('dialogflow') && MC_ADMIN_SETTINGS.note_data_scrape) {
                let options = '';
                for (var key in MC_ADMIN_SETTINGS.note_data_scrape) {
                    options += `<option value="${key}">${MC_ADMIN_SETTINGS.note_data_scrape[key]}</option>`;
                }
                admin.find('#mc-add-note').parent().prepend(`<div id="note-ai-scraping" class="mc-setting mc-type-select"><select><option value="">${mc_('Data scraping')}</option>${options}</select></div>`);
            }
            e.preventDefault();
            return false;
        });

        $(admin).on('change', '#note-ai-scraping select', function () {
            let value = $(this).val();
            if (!value || loading($(this).parent())) {
                return;
            }
            MCF.ajax({
                function: 'data-scraping',
                conversation_id: MCChat.conversation.id,
                prompt_id: value
            }, (response) => {
                if (response && response.error) {
                    console.error(response);
                    return infoBottom(response.error.message, 'error');
                }
                $(this).parent().mcLoading(false);
                let textarea = admin.find('.mc-notes-box textarea');
                textarea.val((textarea.val() + '\n' + response).trim());
            });
        });

        notes_panel.on('click', '.mc-delete-note', function () {
            let item = $(this).parents().eq(1);
            MCConversations.notes.delete(MCChat.conversation.id, item.attr('data-id'), (response) => {
                if (response === true) {
                    item.remove();
                } else {
                    MCF.error(response);
                }
            });
        });

        $(admin).on('click', '#mc-add-note, #mc-update-note', function () {
            let textarea = $(this).parent().parents().eq(1).find('textarea');
            let message = textarea.val();
            let note_id = textarea.attr('data-id');
            if (message.length == 0) {
                MCForm.showErrorMessage(admin.find('.mc-notes-box'), 'Please write something...');
            } else {
                if (loading(this)) return;
                message = MCF.escape(message);
                MCConversations.notes.add(MCChat.conversation.id, MC_ACTIVE_AGENT.id, MC_ACTIVE_AGENT.full_name, message, (response) => {
                    if (Number.isInteger(response) || response === true) {
                        $(this).mcLoading(false);
                        admin.mcHideLightbox();
                        if (note_id) {
                            notes_panel.find(`[data-id="${note_id}"]`).remove();
                        }
                        MCConversations.notes.update([{ id: note_id ? note_id : response, conversation_id: MCChat.conversation.id, user_id: MC_ACTIVE_AGENT.id, name: MC_ACTIVE_AGENT['full_name'], message: message }], true);
                        textarea.val('');
                        infoBottom(note_id ? 'Note successfully updated.' : 'New note successfully added.');
                    } else {
                        MCForm.showErrorMessage(response);
                    }
                }, note_id);
            }
        });

        tags_panel.on('click', '> i', function (e) {
            let code = MCConversations.tags.getAll(MCChat.conversation.details.tags);;
            let tags = MCChat.conversation.details.tags;
            MCAdmin.genericPanel('tags', 'Manage tags', code ? '<div class="mc-tags-cnt">' + code + '</div>' : '<p>' + mc_('Add tags from Settings > Admin > Tags.') + '</p>', ['Save tags']);
            e.preventDefault();
            return false;
        });

        $(admin).on('click', '.mc-tags-cnt > span', function () {
            $(this).toggleClass('mc-active');
        });

        $(admin).on('click', '#mc-add-tag', function () {
            $('<input type="text">').insertBefore(this);
        });

        $(admin).on('click', '#mc-save-tags', function () {
            if (loading(this)) return;
            let tags = admin.find('.mc-tags-box').find('span.mc-active').map(function () {
                return $(this).attr('data-value');
            }).toArray();
            let conversation_id = MCChat.conversation.id;
            MCF.ajax({
                function: 'update-tags',
                conversation_id: conversation_id,
                tags: tags
            }, (response) => {
                $(this).mcLoading(false);
                if (response === true) {
                    MCConversations.tags.update(tags);
                    if (MCChat.conversation && conversation_id == MCChat.conversation.id) {
                        let tag_filter = MCConversations.filters()[3];
                        let conversation_li = getListConversation(conversation_id);
                        MCChat.conversation.set('tags', tags);
                        if (tag_filter && !tags.includes(tag_filter)) {
                            conversation_li.remove();
                            MCConversations.clickFirst();
                        } else if (MC_ADMIN_SETTINGS.tags_show) {
                            let tags_area = conversation_li.find('.mc-tags-area');
                            let code = MCConversations.tags.codeLeft(tags);
                            if (tags_area.length) {
                                tags_area.replaceWith(code);
                            } else {
                                $(code).insertAfter(conversation_li.find('.mc-name'));
                            }
                        }
                    }
                }
                admin.mcHideLightbox();
                infoBottom(response === true ? 'Tags have been successfully updated.' : response);
            });
        });

        // Suggestions
        $(suggestions_area).on('click', 'span', function () {
            MCChat.insertText($(this).text());
            suggestions_area.html('');
        });

        $(suggestions_area).on('mouseover', 'span', function () {
            timeout = setTimeout(() => { $(this).addClass('mc-suggestion-full'); }, 2500);
        });

        $(suggestions_area).on('mouseout', 'span', function () {
            clearTimeout(timeout);
            suggestions_area.find('span').removeClass('mc-suggestion-full');
        });

        // Message menu
        $(conversations_area).on('click', '.mc-list .mc-menu > li', function () {
            let message = $(this).closest('[data-id]');
            let message_id = message.attr('data-id');
            let message_ = MCChat.conversation.getMessage(message_id);
            let message_user_type = message_.get('user_type');
            let value = $(this).attr('data-value');
            switch (value) {
                case 'delete':
                    if (MCChat.user_online) {
                        MCF.ajax({
                            function: 'update-message',
                            message_id: message_id,
                            message: '',
                            attachments: [],
                            payload: { 'event': 'delete-message' }
                        }, () => {
                            MCChat.conversation.deleteMessage(message_id);
                            message.remove();
                        });
                    } else {
                        MCChat.deleteMessage(message_id);
                    }
                    break;
                case 'translation':
                case 'original':
                    let is_translation = value == 'translation';
                    let keys = ['translation', 'translation-language', 'original-message', 'original-message-language'];
                    let original_payload = keys.map(key => message_.payload(key)).concat(message_.details.message);
                    message_.set('message', is_translation ? message_.payload('translation') || message_.get('message') : message_.payload('original-message') || message_.get('message'));
                    keys.forEach(key => delete message_.details.payload[key]);
                    message.replaceWith(message_.getCode().replace('mc-menu">', `mc-menu"><li data-value="${is_translation ? 'original' : 'translation'}">${mc_(is_translation ? 'View original message' : 'View translation')}</li>`));
                    keys.forEach((key, i) => message_.payload(key, original_payload[i]));
                    message_.details.message = original_payload[4];
                    break;
                case 'bot':
                    MCApps.dialogflow.showCreateIntentBox($(this).closest('[data-id]').attr('data-id'));
                    break;
                case 'reply':
                    let is_agent = MCF.isAgent(message_user_type);
                    let text = message_.get('message');
                    if (!text) {
                        message_.attachments.forEach((attachment) => {
                            text += '<i class="mc-icon-clip"></i>' + attachment[0];
                        });
                    }
                    conversations_area.find('.mc-editor [data-reply]').remove();
                    conversations_area.find('.mc-editor').prepend(`<div data-reply="${message_id}"${is_agent ? ' class="mc-reply-color"' : ''}><span>${(is_agent && admin) || (!is_agent && !admin) ? mc_('You') : message_.get('full_name')}</span>${text}<i class="mc-icon-close" onclick="MCChat.cancelReply()"></i></div>`);
                    conversations_area_list.addClass('mc-reply-active');
                    break;
            }
        });

        // Conversations filter
        $(conversations_area).on('click', '.mc-filter-btn i', function () {
            $(this).parent().toggleClass('mc-active');
        });

        $(conversations_area).on('click', '.mc-filter-star', function () {
            $(this).parent().find('li').mcActive(false);
            $(this).parent().find(`li[data-value="${$(this).mcActive() ? '' : $(this).attr('data-value')}"]`).last().mcActive(true).click();
            $(this).toggleClass('mc-active');
        });

        // Attachments filter
        attachments_panel.on('click', '#mc-attachments-filter li', function () {
            let links = attachments_panel.find('a:not(.mc-collapse-btn)');
            let file_type = $(this).attr('data-value');
            links.each(function () {
                $(this).setClass('mc-hide', file_type && MCF.getFileType($(this).attr('href')) != file_type);
            });
            collapse(attachments_panel, 160);
        });

        // CC
        $(admin).on('click', '.mc-cc-box #mc-save-changes', function () {
            let cc = admin.find('.mc-cc-box .repeater-item input').map(function () {
                return $(this).val();
            }).get().join(',');
            loading(this);
            MCF.ajax({
                function: 'update-conversation-extra',
                conversation_id: MCChat.conversation.id,
                extra: cc ? cc : 'NULL'
            }, () => {
                MCChat.conversation.set('extra', cc);
                MCConversations.cc(cc.split(','));
                $(this).mcLoading(false);
                admin.mcHideLightbox();
            });
        });

        /*
        * ----------------------------------------------------------
        * Users area
        * ----------------------------------------------------------
        */

        // Open user box by URL
        if (MCF.getURL('user')) {
            header.find('.mc-admin-nav #mc-users').click();
            setTimeout(() => { MCProfile.show(MCF.getURL('user')) }, 500);
        }

        // Checkbox selector
        $(users_table).on('click', 'th :checkbox', function () {
            users_table.find('td :checkbox').prop('checked', $(this).prop('checked'));
        });

        $(users_table).on('click', ':checkbox', function () {
            let button = users_area.find('[data-value="delete"]');
            if (users_table.find('td input:checked').length) {
                button.removeAttr('style');
            } else {
                button.hide();
            }
        });

        // Table menu filter
        $(users_table_menu).on('click', 'li', function () {
            MCUsers.filter($(this).data('type'));
        });

        // Filters
        $(users_filters).on('click', 'li', function () {
            let button = users_filters.closest('.mc-filter-btn');
            setTimeout(() => {
                MCUsers.get((response) => {
                    MCUsers.populate(response);
                });
                button.attr('data-badge', users_filters.toArray().reduce((acc, filter) => acc + !!$(filter).find('li.mc-active').data('value'), 0));
            }, 100);
            button.mcActive(false);
        });

        // Search users
        $(users_area).on('input', '.mc-search-btn input', function () {
            MCUsers.search(this);
        });

        $(users_area).on('click', '.mc-search-btn i', function () {
            MCF.searchClear(this, () => { MCUsers.search($(this).next()) });
        });

        // Sorting
        $(users_table).on('click', 'th:not(:first-child)', function () {
            let direction = $(this).hasClass('mc-order-asc') ? 'DESC' : 'ASC';
            $(this).toggleClass('mc-order-asc');
            $(this).siblings().mcActive(false);
            $(this).mcActive(true);
            MCUsers.sort($(this).data('field'), direction);
        });

        // Pagination for users
        $(users_table).parent().on('scroll', function () {
            if (!is_busy && !MCUsers.search_query && scrollPagination(this, true) && users_pagination_count) {
                is_busy = true;
                users_area.append('<div class="mc-loading-global mc-loading mc-loading-pagination"></div>');
                MCUsers.get((response) => {
                    setTimeout(() => { is_busy = false }, 500);
                    users_pagination_count = response.length;
                    if (users_pagination_count) {
                        let code = '';
                        for (var i = 0; i < users_pagination_count; i++) {
                            let user = new MCUser(response[i], response[i].extra);
                            code += MCUsers.getRow(user);
                            users[user.id] = user;
                        }
                        users_pagination++;
                        users_table.find('tbody').append(code);
                    }
                    users_area.find(' > .mc-loading-pagination').remove();
                }, false, true);
            }
        });

        // Delete user button
        $(profile_edit_box).on('click', '.mc-delete', function () {
            if (MC_ACTIVE_AGENT.id == activeUser().id) {
                return infoBottom('You cannot delete yourself.', 'error');
            }
            infoPanel('This user will be deleted permanently including all linked data, conversations, and messages.', 'alert', function () {
                MCUsers.delete(activeUser().id);
            });
        });

        // Display user box
        $(users_table).on('click', 'td:not(:first-child)', function () {
            MCProfile.show($(this).parent().attr('data-user-id'));
        });

        // Display edit box
        $(profile_box).on('click', '.mc-top-bar .mc-edit', function () {
            MCProfile.showEdit(activeUser());
        });

        // Display new user box
        $(users_area).on('click', '.mc-new-user', function () {
            profile_edit_box.addClass('mc-user-new');
            profile_edit_box.find('.mc-top-bar .mc-profile span').html(mc_('Add new user'));
            profile_edit_box.find('.mc-top-bar .mc-save').html(`<i class="mc-icon-check"></i>${mc_('Add user')}`);
            profile_edit_box.find('input,select,textara').removeClass('mc-error');
            profile_edit_box.removeClass('mc-cloud-admin');
            if (MC_ACTIVE_AGENT.user_type == 'admin') {
                profile_edit_box.find('#user_type').find('select').html(`<option value="user">${mc_('User')}</option><option value="agent">${mc_('Agent')}</option><option value="admin">${mc_('Admin')}</option>`);
            }
            MCProfile.clear(profile_edit_box);
            MCProfile.boxClasses(profile_edit_box);
            MCProfile.updateRequiredFields('user');
            profile_edit_box.mcShowLightbox();
        });

        // Add or update user
        $(profile_edit_box).on('click', '.mc-save', function () {
            if (loading(this)) return;
            let new_user = (profile_edit_box.hasClass('mc-user-new') ? true : false);
            let user_id = profile_edit_box.attr('data-user-id');

            // Get settings
            let settings = MCProfile.getAll(profile_edit_box.find('.mc-details'));
            let settings_extra = MCProfile.getAll(profile_edit_box.find('.mc-additional-details'));
            let output = {};
            $.map(settings, function (value, key) {
                settings[key] = value[0];
            });

            // Errors check
            if (MCProfile.errors(profile_edit_box)) {
                MCProfile.showErrorMessage(profile_edit_box, MCF.isAgent(profile_edit_box.find('#user_type :selected').val()) ? 'First name, last name, password and a valid email are required. Minimum password length is 8 characters.' : (profile_edit_box.find('#password').val().length < 8 ? 'Minimum password length is 8 characters.' : 'First name is required.'));
                $(this).mcLoading(false);
                return;
            }
            if (MC_ACTIVE_AGENT.id == activeUser().id && settings.user_type[0] == 'agent' && MC_ACTIVE_AGENT.user_type == 'admin') {
                MCProfile.showErrorMessage(profile_edit_box, 'You cannot change your status from admin to agent.');
                $(this).mcLoading(false);
                return;
            }
            if (!settings.user_type) {
                settings.user_type = 'user';
            }

            // Save the settings
            MCF.ajax({
                function: (new_user ? 'add-user' : 'update-user'),
                user_id: user_id,
                settings: settings,
                settings_extra: settings_extra
            }, (response) => {
                if (MCF.errorValidation(response, 'duplicate-email') || MCF.errorValidation(response, 'duplicate-phone')) {
                    MCProfile.showErrorMessage(profile_edit_box, `This ${MCF.errorValidation(response, 'duplicate-email') ? 'email' : 'phone number'} is already in use.`);
                    $(this).mcLoading(false);
                    return;
                }
                if (new_user) {
                    user_id = response;
                    activeUser(new MCUser({ 'id': user_id }));
                }
                activeUser().update(() => {
                    users[user_id] = activeUser();
                    if (new_user) {
                        MCProfile.clear(profile_edit_box);
                        MCUsers.update();
                    } else {
                        MCUsers.updateRow(activeUser());
                        if (conversations_area.mcActive()) {
                            MCConversations.updateUserDetails();
                        }
                        if (user_id == MC_ACTIVE_AGENT.id) {
                            MCF.loginCookie(response[1]);
                            MC_ACTIVE_AGENT.full_name = activeUser().name;
                            MC_ACTIVE_AGENT.profile_image = activeUser().image;
                            header.find('.mc-account').setProfile();
                        }
                    }
                    if (new_user) {
                        profile_edit_box.find('.mc-profile').setProfile(mc_('Add new user'));
                    }
                    $(this).mcLoading(false);
                    if (!new_user) {
                        admin.mcHideLightbox();
                    }
                    infoBottom(new_user ? 'New user added' : 'User updated');
                });
                MCF.event('MCUserUpdated', { new_user: new_user, user_id: user_id });
            });
        });

        // Set and unset required visitor fields
        $(profile_edit_box).on('change', '#user_type', function () {
            let value = $(this).find("option:selected").val();
            MCProfile.boxClasses(profile_edit_box, value);
            MCProfile.updateRequiredFields(value);
        });

        // Open a user conversation
        $(profile_box).on('click', '.mc-user-conversations li', function () {
            MCConversations.open($(this).attr('data-conversation-id'), $(this).find('[data-user-id]').attr('data-user-id'));
        });

        // Start a new user conversation
        $(profile_box).on('click', '.mc-start-conversation', function () {
            MCConversations.open(-1, activeUser().id);
            MCConversations.openConversation(-1, activeUser().id);
            if (responsive) {
                MCConversations.mobileOpenConversation();
            }
        });

        // Show direct message box from user profile
        $(profile_box).on('click', '.mc-top-bar [data-value]', function () {
            MCConversations.showDirectMessageBox($(this).attr('data-value'), [activeUser().id]);
        });

        // Top icons menu
        $(users_area).on('click', '.mc-top-bar [data-value]', function () {
            let value = $(this).data('value');
            let user_ids = MCUsers.getSelected();
            switch (value) {
                case 'whatsapp':
                    whatsapp_direct_message_box(user_ids);
                    break;
                case 'message':
                case 'custom_email':
                case 'sms':
                    MCConversations.showDirectMessageBox(value, user_ids);
                    break;
                case 'csv':
                    MCUsers.csv();
                    break;
                case 'delete':
                    if (user_ids.includes(MC_ACTIVE_AGENT.id)) {
                        return infoBottom('You cannot delete yourself.', 'error');
                    }
                    infoPanel('All selected users will be deleted permanently including all linked data, conversations, and messages.', 'alert', () => {
                        MCUsers.delete(user_ids);
                        $(this).hide();
                        users_table.find('th:first-child input').prop('checked', false);
                    });
                    break;
            }
        });

        // Direct message
        $(admin).on('click', '.mc-send-direct-message', function () {
            let type = $(this).attr('data-type') ? $(this).attr('data-type') : direct_message_box.attr('data-type');
            let whatsapp = type == 'whatsapp';
            let box = whatsapp ? admin.find('#mc-whatsapp-send-template-box') : direct_message_box;
            let subject = box.find('.mc-direct-message-subject input').val();
            let message = whatsapp ? '' : box.find('textarea').val();
            let user_ids = box.find('.mc-direct-message-users').val().replace(/ /g, '');
            let template_name = false;
            let template_languages = false;
            let parameters = [];
            let phone_number_id = false;
            if (whatsapp) {
                let select = box.find('#mc-whatsapp-send-template-list');
                template_name = select.val();
                template_languages = select.find('option:selected').attr('data-languages');
                phone_number_id = select.find('option:selected').attr('data-phone-id');
                parameters = ['header', 'body', 'button'].map(id => box.find(`#mc-whatsapp-send-template-${id}`).val());
            }
            if (MCForm.errors(box)) {
                MCForm.showErrorMessage(box, 'Please complete the mandatory fields.');
            } else {
                if (loading(this)) {
                    return;
                }
                let user_details = [];
                if (message.includes('recipient_name') || parameters.join('').includes('recipient_name')) {
                    user_details.push('first_name', 'last_name');
                }
                if (message.includes('recipient_email') || parameters.join('').includes('recipient_email')) {
                    user_details.push('email');
                }
                if (type == 'message') {
                    MCF.ajax({
                        function: 'direct-message',
                        user_ids: user_ids,
                        message: message
                    }, (response) => {
                        $(this).mcLoading(false);
                        let send_email = MC_ADMIN_SETTINGS.notify_user_email;
                        let send_sms = MC_ADMIN_SETTINGS.sms_active_users;
                        if (MCF.errorValidation(response)) {
                            return MCForm.showErrorMessage(box, 'An error has occurred. Please make sure all user ids are correct.');
                        }
                        if (send_email || send_sms) {
                            MCF.ajax({
                                function: 'get-users-with-details',
                                user_ids: user_ids,
                                details: user_details.concat(send_email && send_sms ? ['email', 'phone'] : [send_email ? 'email' : 'phone'])
                            }, (response) => {
                                if (send_email && response.email.length) {
                                    recursiveSending(response, 'email', message, 0, send_sms ? response.phone : [], 'email', subject);
                                } else if (send_sms && response.phone.length) {
                                    recursiveSending(response, 'phone', message, 0, [], 'sms');
                                } else {
                                    admin.mcHideLightbox();
                                }
                            });
                        }
                        infoBottom(`${MCF.slugToString(type)} sent to all users.`);
                    });
                } else {
                    let slug = type == 'custom_email' ? 'email' : 'phone';
                    MCF.ajax({
                        function: 'get-users-with-details',
                        user_ids: user_ids,
                        details: user_details.concat([slug])
                    }, (response) => {
                        if (response[slug].length) {
                            recursiveSending(response, slug, message, 0, [], type, subject, template_name, parameters, template_languages, phone_number_id);
                        } else {
                            $(this).mcLoading(false);
                            return MCForm.showErrorMessage(box, 'No users found.');
                        }
                    });
                }
            }
        });

        function recursiveSending(user_ids, slug, message, i = 0, user_ids_sms = [], type, subject = false, template_name = false, parameters = false, template_languages = false, phone_number_id = false) {
            let settings = { whatsapp: ['whatsapp-send-template', 'messages', ' a phone number.', 'direct-whatsapp'], email: ['create-email', 'emails', ' an email address.', false], custom_email: ['send-custom-email', 'emails', ' an email address.', 'direct-emails'], sms: ['send-sms', 'text messages', ' a phone number.', 'direct-sms'] }[type];
            MCF.ajax({
                function: settings[0],
                to: user_ids[slug][i].value,
                recipient_id: user_ids[slug][i].id,
                sender_name: MC_ACTIVE_AGENT['full_name'],
                sender_profile_image: MC_ACTIVE_AGENT['profile_image'],
                subject: subject,
                message: message,
                template_name: template_name,
                parameters: parameters,
                template_languages: template_languages,
                template: false,
                phone_id: phone_number_id,
                user_name: user_ids.first_name ? (user_ids.first_name[i].value + ' ' + user_ids.last_name[i].value).trim() : false,
                user_email: user_ids.email ? user_ids.email[i].value : false
            }, (response) => {
                let user_ids_length = user_ids[slug].length;
                let box = type == 'whatsapp' ? admin.find('#mc-whatsapp-send-template-box') : direct_message_box;
                box.find('.mc-bottom > div').html(`${mc_('Sending')} ${mc_(settings[1])}... ${i + 1} / ${user_ids_length}`);
                if (response) {
                    if (response !== true && (('status' in response && response.status == 400) || ('error' in response && ![131030, 131009].includes(response.error.code)))) {
                        MCForm.showErrorMessage(box, response.error ? response.error.message : `${response.message} Details at ${response.more_info}`);
                        console.error(response);
                        box.find('.mc-loading').mcLoading(false);
                        box.find('.mc-bottom > div').html('');
                        return;
                    }
                    if (i < user_ids_length - 1) {
                        return recursiveSending(user_ids, slug, message, i + 1, user_ids_sms, type, subject, template_name, parameters, template_languages, phone_number_id);
                    } else {
                        if (user_ids_sms.length) {
                            recursiveSending(user_ids_sms, slug, message, 0, [], 'sms', false);
                        } else {
                            admin.mcHideLightbox();
                            if (settings[3]) {
                                MCF.ajax({ function: 'reports-update', name: settings[3], value: message.substr(0, 18) + ' | ' + user_ids_length });
                            }
                        }
                        infoBottom(user_ids_length == 1 ? 'The message has been sent.' : mc_('The message was sent to all users who have' + settings[2]));
                    }
                } else {
                    console.warn(response);
                }
            });
        }

        // User filters
        $(users_area).on('click', '.mc-filter-btn > i', function () {
            $(this).parent().toggleClass('mc-active');
        });

        /*
        * ----------------------------------------------------------
        * Settings area
        * ----------------------------------------------------------
        */

        // Open settings area by URL
        if (MCF.getURL('setting')) {
            MCSettings.open(MCF.getURL('setting'), true);
        }

        // Settings history
        $(settings_area).on('click', ' > .mc-tab > .mc-nav [id]', function () {
            let id = $(this).attr('id').substr(4);
            if (MCF.getURL('setting') != id) {
                pushState('?setting=' + id);
            }
        });

        // Upload
        $(admin).on('click', '.mc-repeater-upload, [data-type="upload-image"] .image, [data-type="upload-file"] .mc-btn, #mc-chatbot-add-files, #mc-import-settings a, #mc-import-users a', function () {
            let extensions = '';
            upload_target = this;
            if ($(this).attr('id') == 'mc-chatbot-add-files') {
                extensions = '.pdf,.txt,.json,.csv';
                chatbot_files_table.find('.mc-pending').remove();
                MCApps.openAI.train.skip_files = [];
                upload_function = function () {
                    let files = upload_input.prop('files');
                    let code = '';
                    for (var i = 0; i < files.length; i++) {
                        let size = parseInt(files[i].size / 1000);
                        code += `<tr class="mc-pending" data-name="${files[i].name}"><td><input type="checkbox" /></td><td>${files[i].name}<label>${mc_('Pending')}</label></td><td>${size ? size : 1} KB</td><td><i class="mc-icon-delete"></i></td></tr>`;
                    }
                    chatbot_files_table.append(code);
                };
            } else if ($(this).parent().parent().attr('id') == 'mc-import-settings') {
                extensions = '.json';
                upload_on_success = (response) => {
                    if (loading(this)) return;
                    MCF.ajax({
                        function: 'import-settings',
                        file_url: response
                    }, (response) => {
                        if (response) {
                            infoBottom('Settings saved. Reload to apply the changes.');
                        } else {
                            infoPanel(response);
                        }
                        $(this).mcLoading(false);
                    });
                    upload_on_success = false;
                }
            } else if ($(this).parent().parent().attr('id') == 'mc-import-users') {
                extensions = '.csv';
                upload_on_success = (response) => {
                    if (loading(this)) return;
                    MCF.ajax({
                        function: 'import-users',
                        file_url: response
                    }, (response) => {
                        if (response) {
                            infoBottom('Users imported successfully.');
                        } else {
                            infoPanel(response);
                        }
                        $(this).mcLoading(false);
                    });
                    upload_on_success = false;
                }
            } else if ($(this).hasClass('image')) {
                extensions = '.png,.jpg,.jpeg,.gif,.webp';
            } else if ($(this).hasClass('mc-repeater-upload')) {
                upload_on_success = (response) => {
                    let parent = $(this).parent();
                    if (parent.find('.repeater-item:last-child input').val()) {
                        MCSettings.repeater.add(this);
                    }
                    parent.find('.repeater-item:last-child input').val(response);
                }
            }
            upload_input.attr('accept', extensions).prop('value', '').click();
        });

        $(settings_area).on('click', '[data-type="upload-image"] .image > i', function (e) {
            MCF.ajax({ function: 'delete-file', path: $(this).parent().attr('data-value') });
            $(this).parent().removeAttr('data-value').css('background-image', '');
            e.preventDefault();
            return false;
        });

        // Repeater
        $(admin).on('click', '.mc-repeater-add', function () {
            MCSettings.repeater.add(this);
        });

        $(admin).on('click', '.repeater-item > i', function () {
            setTimeout(() => {
                MCSettings.repeater.delete(this);
            }, 100);
        });

        // Color picker
        MCSettings.initColorPicker();

        $(settings_area).find('[data-type="color"]').focusout(function () {
            let color = $(this).find('input').val();
            if (color == 'rgb(255, 255, 255)' && ['color-admin-1', 'color-1'].includes($(this).attr('id'))) {
                color = '';
            }
            setTimeout(() => {
                $(this).find('input').val(color);
                $(this).find('.color-preview').css('background-color', color);
            }, 300);
            MCSettings.set($(this).attr('id'), [color, 'color']);
        });

        $(settings_area).on('click', '.mc-type-color .input i', function (e) {
            $(this).parent().find('input').removeAttr('style').val('');
        });

        // Color palette
        $(settings_area).on('click', '.mc-color-palette span', function () {
            let active = $(this).mcActive();
            $(this).closest('.mc-repeater').find('.mc-active').mcActive(false);
            $(this).mcActive(!active);
        });

        $(settings_area).on('click', '.mc-color-palette ul li', function () {
            $(this).parent().parent().attr('data-value', $(this).data('value')).find('span').mcActive(false);
        });

        // Select images
        $(settings_area).on('click', '[data-type="select-images"] .input > div', function () {
            $(this).siblings().mcActive(false);
            $(this).mcActive(true);
        });

        // Select checkbox
        $(settings_area).on('click', '.mc-select-checkbox-input', function () {
            $(this).toggleClass('mc-active');
        });

        $(settings_area).on('click', '.mc-select-checkbox input', function () {
            let parent = $(this).closest('[data-type]');
            parent.find('.mc-select-checkbox-input').val(MCSettings.get(parent)[1].join(', '));
        });

        // Save
        $(settings_area).on('click', '.mc-save-changes', function () {
            MCSettings.save(this);
        });

        // Miscellaneous
        $(settings_area).on('change', '#saved-replies [data-id="reply-name"], [data-id="rich-message-name"]', function () {
            $(this).val($(this).val().replace(/\s+/g, '-').replace(/-+/g, '-').replace(/^-+/, '').replace(/-+$/, '').replace(/ /g, ''));
        });

        $(settings_area).on('change', '#user-additional-fields [data-id="extra-field-name"]', function () {
            $(this).parent().next().find('input').val(MCF.stringToSlug($(this).val()));
        });

        $(settings_area).on('click', '#timetable-utc input', function () {
            if (!$(this).val()) {
                $(this).val(today.getTimezoneOffset() / 60);
            }
        });

        $(settings_area).on('click', '#dialogflow-sync-btn a', function (e) {
            let url = 'https://accounts.google.com/o/oauth2/auth?scope=https%3A%2F%2Fwww.googleapis.com/auth/dialogflow%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcloud-translation%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcloud-language&response_type=code&access_type=offline&redirect_uri=' + MC_URL + '/apps/dialogflow/functions.php&client_id={client_id}&prompt=consent';
            if (MC_ADMIN_SETTINGS.cloud && settings_area.find('#google-sync-mode select').val() == 'auto') {
                if (MC_ADMIN_SETTINGS.credits) {
                    window.open(url.replace('{client_id}', MC_ADMIN_SETTINGS.google_client_id));
                    e.preventDefault();
                    return false;
                }
            } else {
                let client_id = settings_area.find('#google-client-id input').val();
                if (client_id && settings_area.find('#google-client-secret input').val()) {
                    window.open(url.replace('{client_id}', client_id));
                } else {
                    infoPanel('Before continuing enter Client ID and Client secret. Check the docs for more details.');
                }
                e.preventDefault();
                return false;
            }
        });

        $(settings_area).on('click', '#dialogflow-redirect-url-btn a', function (e) {
            infoPanel(`<pre>${MC_URL}/apps/dialogflow/functions.php</pre>`);
            e.preventDefault();
            return false;
        });

        $(settings_area).on('click', '#dialogflow-saved-replies a', function (e) {
            infoPanel('', 'alert', () => {
                if (!loading(this)) {
                    MCF.ajax({ function: 'dialogflow-saved-replies' }, () => {
                        $(this).mcLoading(false);
                    });
                }
            });
            e.preventDefault();
            return false;
        });

        $(settings_area).on('click', '#test-email-user a, #test-email-agent a', function () {
            let email = $(this).parent().find('input').val();
            if (email && email.indexOf('@') > 0 && !loading(this)) {
                MCF.ajax({
                    function: 'send-test-email',
                    to: email,
                    email_type: $(this).parent().parent().attr('id') == 'test-email-user' ? 'user' : 'agent'
                }, (response) => {
                    infoPanel(response === true ? 'The message has been sent.' : response, 'info');
                    $(this).mcLoading(false);
                });
            }
        });

        $(settings_area).on('click', '#email-server-troubleshoot a', function (e) {
            settings_area.find('#test-email-user input').val(MC_ACTIVE_AGENT.email).next().click()[0].scrollIntoView({ behavior: 'smooth' });
            e.preventDefault();
            return false;
        });

        $(settings_area).on('click', '#test-sms-user a, #test-sms-agent a', function () {
            let phone_number = $(this).parent().find('input').val();
            if (phone_number && !loading(this)) {
                MCF.ajax({
                    function: 'send-sms',
                    message: 'Hello World!',
                    to: phone_number
                }, (response) => {
                    infoPanel(response && ['sent', 'queued'].includes(response.status) ? 'The message has been sent.' : `<pre>${JSON.stringify(response)}</pre>`);
                    $(this).mcLoading(false);
                });
            }
        });

        $(settings_area).on('click', '.mc-timetable > div > div > div', function () {
            let timetable = $(this).closest('.mc-timetable');
            let active = $(this).mcActive();
            $(timetable).find('.mc-active').mcActive(false);
            if (active) {
                $(this).mcActive(false).find('.mc-custom-select').remove();
            } else {
                let select = $(timetable).find('> .mc-custom-select').html();
                $(timetable).find(' > div .mc-custom-select').remove();
                $(this).append(`<div class="mc-custom-select">${select}</div>`).mcActive(true);
            }
        });

        $(settings_area).on('click', '.mc-timetable .mc-custom-select span', function () {
            let value = [$(this).html(), $(this).attr('data-value')];
            $(this).closest('.mc-timetable').find('> div > div > .mc-active').html(value[0]).attr('data-value', value[1]);
            $(this).parent().mcActive(false);
        });

        $(settings_area).on('click', '#system-requirements a', function (e) {
            let code = '';
            MCF.ajax({
                function: 'system-requirements'
            }, (response) => {
                for (var key in response) {
                    code += `<div class="mc-input"><span>${mc_(MCF.slugToString(key))}</span><div${response[key] ? ' class="mc-green"' : ''}>${response[key] ? mc_('Success') : mc_('Error')}</div></div>`;
                }
                loadingGlobal(false);
                MCAdmin.genericPanel('requirements', 'System requirements', code);
            });
            loadingGlobal();
            e.preventDefault();
            return false;
        });

        $(settings_area).on('click', '#mc-path a', function (e) {
            MCF.ajax({
                function: 'path'
            }, (response) => {
                infoPanel(`<pre>${response}</pre>`);
            });
            e.preventDefault();
            return false;
        });

        $(settings_area).on('click', '#mc-url a', function (e) {
            infoPanel(`<pre>${MC_URL}</pre>`);
            e.preventDefault();
            return false;
        });

        $(settings_area).on('click', '#delete-leads a', function (e) {
            if (!$(this).mcLoading()) {
                infoPanel('All leads, including all the linked conversations and messages, will be deleted permanently.', 'alert', () => {
                    $(this).mcLoading(true);
                    MCF.ajax({
                        function: 'delete-leads'
                    }, () => {
                        infoPanel('Leads and conversations successfully deleted.');
                        $(this).mcLoading(false);
                    });
                });
            }
            e.preventDefault();
            return false;
        });

        $(settings_area).on('click', '#mc-export-settings a', function (e) {
            e.preventDefault();
            if (loading(this)) return;
            MCF.ajax({
                function: 'export-settings'
            }, (response) => {
                dialogDeleteFile(response, 'mc-export-settings-close', 'Settings exported');
                $(this).mcLoading(false);
            });
            return false;
        });

        $(admin).on('click', '#mc-export-settings-close .mc-close, #mc-export-users-close .mc-close, #mc-export-report-close .mc-close', function () {
            MCF.ajax({ function: 'delete-file', path: admin.find('.mc-dialog-box p pre').html() });
        });

        if (!MC_ADMIN_SETTINGS.cloud) {
            $(settings_area).on('change', '#push-notifications-provider select', function () {
                let is_pusher = $(this).val() == 'pusher';
                MCSettings.visibility(0, is_pusher);
                MCF.ajax({ function: 'update-sw', url: is_pusher ? 'https://js.pusher.com/beams/service-worker.js' : 'https://cdn.onesignal.com/sdks/web/v16/OneSignalSDK.sw.js' });
            });

            $(settings_area).on('click', '#push-notifications-sw-path a', function (e) {
                let path = urlStrip(location.href.replace(location.host, '')).replace('admin.php', '');
                if (path.includes('?')) {
                    path = path.substring(0, path.indexOf('?'));
                }
                infoPanel('<pre>' + path + '</pre>');
                e.preventDefault();
                return false;
            });
        }

        $(settings_area).on('click', '#push-notifications-btn a', function (e) {
            if (MC_ADMIN_SETTINGS.cloud || settings_area.find('#push-notifications-provider select').val() == 'onesignal') {
                if (typeof OneSignal != ND) {
                    OneSignal.Slidedown.promptPush({ force: true });
                } else {
                    MCF.serviceWorker.initPushNotifications();
                }
            } else {
                Notification.requestPermission();
            }
            e.preventDefault();
            return false;
        });

        $(settings_area).on('input', '#mc-search-settings', function () {
            let search = $(this).val().toLowerCase();
            MCF.search(search, () => {
                let code = '';
                let dropdown = settings_area.find('.mc-search-dropdown-items');
                if (search.length > 2) {
                    let navs = settings_area.find('> .mc-tab > .mc-nav li').map(function () { return $(this).text().trim() }).get();
                    settings_area.find('.mc-setting').each(function () {
                        let keywords = $(this).attr('data-keywords');
                        let id = $(this).attr('id');
                        if ((keywords && keywords.includes(search)) || (id && id.replaceAll('-', '-').includes(search)) || $(this).find('.mc-setting-content').text().toLowerCase().includes(search)) {
                            let index = $(this).parent().index();
                            code += `<div data-tab-index="${index}" data-setting="${$(this).attr('id')}">${navs[index]} > ${$(this).find('h2').text()}</div>`;
                        }
                    });
                }
                dropdown.html(code);
                if (dropdown.outerHeight() > $(window).height() - 100) {
                    dropdown.css('max-height', $(window).height() - 100);
                    dropdown.addClass('mc-scroll-area');
                } else {
                    dropdown.removeClass('mc-scroll-area');
                }
            });
        });

        $(settings_area).on('click', '.mc-search-dropdown-items div', function () {
            let index = $(this).attr('data-tab-index');
            let item = settings_area.find('> .mc-tab > .mc-nav li').eq(index);
            item.click();
            item.get(0).scrollIntoView();
            settings_area.find('#' + $(this).attr('data-setting'))[0].scrollIntoView();
        });

        // Slack  
        $(settings_area).on('click', '#slack-button a', () => {
            window.open('https://masichat.com/synch/?service=slack&plugin_url=' + MC_URL + cloudURL());
            return false;
        });

        $(settings_area).on('click', '#slack-test a', function (e) {
            if (loading(this)) return;
            MCF.ajax({
                function: 'send-slack-message',
                user_id: false,
                full_name: MC_ACTIVE_AGENT['full_name'],
                profile_image: MC_ACTIVE_AGENT['profile_image'],
                message: 'Lorem ipsum dolor sit amete consectetur adipiscing elite incidido labore et dolore magna aliqua.',
                attachments: [['Example link', MC_URL + '/media/user.svg'], ['Example link two', MC_URL + '/media/user.svg']],
                channel: settings_area.find('#slack-channel input').val()
            }, (response) => {
                if (MCF.errorValidation(response)) {
                    if (response[1] == 'slack-not-active') {
                        infoPanel('Please first activate Slack, then save the settings and reload the admin area.');
                    } else {
                        infoPanel('Error. Response: ' + JSON.stringify(response));
                    }
                } else {
                    infoPanel(response[0] == 'success' ? 'Slack message successfully sent. Check your Slack app!' : JSON.stringify(response));
                }
                $(this).mcLoading(false);
            });
            e.preventDefault();
            return false;
        });

        $(settings_area).on('click', '#tab-slack', function () {
            let input = settings_area.find('#slack-agents .input');
            input.html('<div class="mc-loading"></div>');
            MCF.ajax({
                function: 'slack-users'
            }, (response) => {
                let code = '';
                if (MCF.errorValidation(response, 'slack-token-not-found')) {
                    code = `<p>${mc_('Synchronize Slack and save changes before linking agents.')}</p>`;
                } else {
                    let select = '<option value="-1"></option>';
                    for (var i = 0; i < response.agents.length; i++) {
                        select += `<option value="${response.agents[i].id}">${response.agents[i].name}</option>`;
                    }
                    for (var i = 0; i < response.slack_users.length; i++) {
                        code += `<div data-id="${response.slack_users[i].id}"><label>${response.slack_users[i].name}</label><select>${select}</select></div>`;
                    }
                }
                input.html(code);
                MCSettings.set('slack-agents', [response.saved, 'double-select']);
            });
        });

        $(settings_area).on('click', '#slack-archive-channels a', function (e) {
            e.preventDefault();
            if (loading(this)) return;
            MCF.ajax({
                function: 'archive-slack-channels'
            }, (response) => {
                if (response === true) {
                    infoPanel('Slack channels archived successfully!');
                }
                $(this).mcLoading(false);
            });
        });

        $(settings_area).on('click', '#slack-channel-ids a', function (e) {
            e.preventDefault();
            if (loading(this)) return;
            MCF.ajax({
                function: 'slack-channels',
                code: true
            }, (response) => {
                infoPanel(response, 'info', false, '', '', true);
                $(this).mcLoading(false);
            });
        });


        // Messenger, WhatsApp, Text messages, Twitter, Telegram, Viber, Zalo
        $(settings_area).on('click', '#whatsapp-twilio-btn a, #whatsapp-twilio-get-configuartion-btn a, #sms-btn a, #wechat-btn a, #twitter-callback a, #viber-webhook a, #zalo-webhook a, [data-id="line-webhook"], #messenger-path-btn a', function (e) {
            let id = $(this).closest('[id]').attr('id');
            let extra = '';
            e.preventDefault();
            if (id == 'line') {
                extra = $(this).closest('.repeater-item').find('[data-id="line-secret"]').val();
                if (!extra) {
                    return;
                } else {
                    extra = '?line_secret=' + extra;
                }
            }
            infoPanel(`<pre>${MC_URL + (id == 'sms-btn' ? '/include/api.php' : ('/apps/' + (id.includes('-') ? id.substring(0, id.indexOf('-')) : id) + '/post.php')) + extra + cloudURL().replace('&', extra ? '&' : '?')}</pre>`);
            return false;
        });

        $(settings_area).on('click', '[data-id="telegram-numbers-button"], #viber-button a, #whatsapp-360-button a', function (e) {
            let calls = { 'telegram-button': ['#telegram-token input', 'telegram-synchronization', ['result', true]], 'viber-button': ['#viber-token input', 'viber-synchronization', ['status_message', 'ok']], 'whatsapp-360-button': ['#whatsapp-360-key input', 'whatsapp-360-synchronization', ['success', true]] };
            let id = $(this).parent().attr('id');
            let token;
            let is_additional_number = false;
            if (!id) {
                let buttons = { 'telegram-numbers-button': 'telegram-button' };
                let inputs = { 'telegram-button': 'telegram-numbers-token' };
                id = buttons[$(this).attr('data-id')];
                token = $(this).closest('.repeater-item').find(`[data-id="${inputs[id]}"]`).val().trim();
                is_additional_number = true;
            } else {
                token = settings_area.find(calls[id][0]).val().trim();
            }
            calls = calls[id];
            e.preventDefault();
            if (!token || loading(this)) return false;
            MCF.ajax({
                function: calls[1],
                token: token,
                cloud_token: cloudURL(),
                is_additional_number: is_additional_number
            }, (response) => {
                infoPanel(calls[2][0] in response && response[calls[2][0]] == calls[2][1] ? 'Synchronization completed.' : JSON.stringify(response));
                $(this).mcLoading(false);
            });
            return false;
        });

        $(settings_area).on('click', '#whatsapp-test-template a', function (e) {
            e.preventDefault();
            let phone = $(this).parent().find('input').val();
            if (!phone || loading(this)) return;
            MCF.ajax({
                function: 'whatsapp-send-template',
                to: phone
            }, (response) => {
                infoPanel(response ? ('error' in response ? (response.error.message ? response.error.message : response.error) : 'Message sent, check your WhatsApp!') : response);
                $(this).mcLoading(false);
            });
            return false;
        });

        $(settings_area).on('click', '#twitter-subscribe a', function (e) {
            e.preventDefault();
            if (loading(this)) return false;
            MCF.ajax({
                function: 'twitter-subscribe',
                cloud_token: cloudURL()
            }, (response) => {
                infoPanel(response === true ? 'Synchronization completed.' : JSON.stringify(response));
                $(this).mcLoading(false);
            });
            return false;
        });

        if (!MC_ADMIN_SETTINGS.cloud) {
            $(settings_area).on('click', '#messenger-sync-btn a', () => {
                window.open('https://masichat.com/synch/?service=messenger&plugin_url=' + MC_URL + cloudURL());
                return false;
            });
        }

        $(settings_area).on('change', '#messenger-sync-mode select', function () {
            MCSettings.visibility(1, $(this).val() != 'manual');
        });

        $(settings_area).on('click', '#messenger-unsubscribe a', function (e) {
            e.preventDefault();
            infoPanel('', 'alert', () => {
                if (loading(this)) return false;
                MCF.ajax({
                    function: 'messenger-unsubscribe'
                }, (response) => {
                    $(this).mcLoading(false);
                    if (response) {
                        infoPanel(JSON.stringify(response));
                    } else {
                        settings_area.find('#messenger-pages .repeater-item > i').click();
                        setTimeout(() => {
                            MCSettings.save();
                            infoPanel('Operation successful.');
                        }, 300);
                    }
                });
            });
            return false;
        });

        $(settings_area).on('change', '#open-ai-mode select', function () {
            MCSettings.visibility(2, $(this).val() != 'assistant');
        });

        // WordPress
        $(settings_area).on('click', '#wp-sync a', function (e) {
            e.preventDefault();
            if (loading(this)) return false;
            MCApps.wordpress.ajax('wp-sync', {}, (response) => {
                if (response === true || response === '1') {
                    MCUsers.update();
                    infoPanel('WordPress users successfully imported.');
                } else {
                    infoPanel('Error. Response: ' + JSON.stringify(response));
                }
                $(this).mcLoading(false);
            });
            return false;
        });

        $('body').on('click', '#wp-admin-bar-logout', function () {
            MCF.logout(false);
        });

        $(settings_area).on('click', '#whatsapp-clear-flows a', function (e) {
            e.preventDefault();
            MCF.ajax({
                function: 'whatsapp-clear-flows'
            });
        });

        // Translations
        $(settings_area).on('click', '#tab-translations', function () {
            let nav = settings_area.find('.mc-translations > .mc-nav > ul');
            if (!nav.html()) {
                let code = '';
                for (var key in MC_LANGUAGE_CODES) {
                    if (key == 'en') continue;
                    code += `<li data-code="${key}"><img src="${MC_URL}/media/flags/${key}.png" />${MC_LANGUAGE_CODES[key]}</li>`;
                }
                nav.html(code);
            }
        });

        $(settings_area).on('click', '.mc-translations .mc-nav li', function () {
            MCSettings.translations.load($(this).data('code'));
        });

        $(settings_area).on('click', '.mc-translations .mc-menu-wide li', function () {
            settings_area.find(`.mc-translations [data-area="${$(this).data('value')}"]`).mcActive(true).siblings().mcActive(false);
        });

        $(settings_area).on('click', '.mc-add-translation', function () {
            settings_area.find('.mc-translations-list > .mc-active').prepend(`<div class="mc-setting mc-type-text mc-new-translation"><input type="text" placeholder="${mc_('Enter original text...')}"><input type="text" placeholder="${mc_('Enter translation...')}"></div></div>`);
        });

        $(settings_area).on('input', '.mc-search-translation input', function () {
            let search = $(this).val().toLowerCase();
            MCF.search(search, () => {
                if (search.length > 1) {
                    settings_area.find('.mc-translations .mc-content > .mc-active label').each(function () {
                        let value = $(this).html().toLowerCase();
                        if (value.includes(search) && value != temp) {
                            let scroll_area = settings_area.find('.mc-scroll-area');
                            scroll_area[0].scrollTop = 0;
                            scroll_area[0].scrollTop = $(this).position().top - 80;
                            temp = value;
                            return false;
                        }
                    });
                }
            });
        });

        // Email piping manual sync
        $(settings_area).on('click', '[data-id="email-piping-sync"]', function (e) {
            if (loading(this)) return;
            MCF.ajax({
                function: 'email-piping',
                force: true
            }, (response) => {
                infoPanel(response === true ? 'Syncronization completed.' : response);
                $(this).mcLoading(false);
            });
            e.preventDefault();
        });

        // Automations
        $(settings_area).on('click', '#tab-automations', function () {
            MCSettings.automations.get(() => {
                MCSettings.automations.populate();
                loadingGlobal(false);
            }, true);
            loadingGlobal();
        });

        $(admin).on('click', '.mc-add-condition', function () {
            MCSettings.automations.addCondition($(this).prev());
        });

        $(admin).on('change', '.mc-condition-1 select', function () {
            MCSettings.automations.updateCondition(this);
        });

        $(admin).on('change', '.mc-condition-2 select', function () {
            $(this).parent().next().setClass('mc-hide', ['is-set', 'is-not-set'].includes($(this).val()));
        });

        $(automations_area_select).on('click', 'li', function () {
            MCSettings.automations.populate($(this).data('value'));
        });

        $(automations_area_nav).on('click', 'li', function () {
            MCSettings.automations.show($(this).attr('data-id'));
        });

        $(automations_area).on('click', '.mc-add-automation', function () {
            MCSettings.automations.add();
        });

        $(automations_area_nav).on('click', 'li i', function () {
            infoPanel(`The automation will be deleted permanently.`, 'alert', () => {
                MCSettings.automations.delete(this);
            });
        });

        // Conflicts alerts and warnings
        let warning_messages = ['Settings > {R} won\'t work if Settings > {R2} is active.'];
        $(settings_area).on('click', '.mc-setting', function (e) {
            let id = $(this).attr('id');
            if ($(this).hasClass('mc-type-multi-input')) {
                id = $(e.target).parent().attr('id');
            }
            switch (id) {
                case 'close-chat':
                case 'close-message':
                    if (settings_area.find('#close-active input').is(':checked') && settings_area.find('#close-chat input').is(':checked')) {
                        infoBottom(warning_messages[0].replace('{R}', 'Messages > Close message').replace('{R2}', 'Chat > Close chat'), 'info');
                    }
                    break;
                case 'chat-timetable':
                case 'follow-message':
                case 'queue':
                case 'routing':
                    if (((id == 'queue' && settings_area.find('#queue-active input').is(':checked')) || (id == 'routing' && settings_area.find('#routing input').is(':checked')) || (id == 'follow-message' && settings_area.find('#follow-active input').is(':checked')) || (id == 'chat-timetable' && settings_area.find('#chat-timetable-active input').is(':checked'))) && settings_area.find('#dialogflow-human-takeover-active input').is(':checked')) {
                        infoBottom('Since Settings > Artificial Intelligence > Human takeover is active, this option will only take effect during human takeover.', 'info');
                    }
                    break;
                case 'notify-agent-email':
                case 'push-notifications':
                case 'sms':
                    if (((id == 'sms' && settings_area.find('#sms-active-agents input').is(':checked')) || (id == 'push-notifications' && settings_area.find('#push-notifications-active input').is(':checked')) || (id == 'notify-agent-email' && settings_area.find('#notify-agent-email input').is(':checked'))) && settings_area.find('#dialogflow-human-takeover-active input').is(':checked')) {
                        infoBottom('Since Settings > Artificial Intelligence > Human takeover is active, notifications will be sent only after the human takeover.', 'info');
                    }
                    break;
                case 'privacy':
                    if (settings_area.find('#privacy-active input').is(':checked') && settings_area.find('#registration-required select').val()) {
                        infoBottom(warning_messages[0].replace('{R}', 'Messages > Privacy message').replace('{R2}', 'Users > Require registration'), 'info');
                    }
                    break;
                case 'google-multilingual-translation':
                    if ($(e.target).is(':checked') && settings_area.find('#open-ai-active input').is(':checked') && !settings_area.find('#open-ai-training-data-language select').val()) {
                        infoBottom('If your OpenAI training data isn\'t in English, set the default language under OpenAI > Training data language.', 'info');
                    }
                    break;
                case 'open-ai-prompt':
                    infoBottom('Custom prompts may break the chatbot, we advise leaving it empty.', 'info');
                    break;
                case 'open-ai-tokens':
                case 'open-ai-temperature':
                case 'open-ai-presence-penalty':
                case 'open-ai-frequency-penalty':
                case 'open-ai-logit-bias':
                case 'open-ai-custom-model':
                    infoBottom('This is an advanced setting, incorrect values may break the chatbot. If you\'re unsure, leave it empty.', 'info');
                    break;
                case 'open-ai-user-train-conversations':
                    if ($(e.target).is(':checked')) {
                        infoBottom('This method is not recommended. See the docs for details.', 'info');
                    }
                    break;
            }
        });

        $(users_table_menu).on('click', '[data-type="online"]', function () {
            if (!MC_ADMIN_SETTINGS.visitors_registration) {
                infoBottom('Settings > Users > Register all visitors must be enabled to see online users.', 'info');
            }
        });

        $(settings_area).on('click', '#dialogflow-active,#dialogflow-welcome,#dialogflow-departments', function (e) {// Depreceated
            infoBottom('Warning! We will stop supporting Dialogflow by the end of 2025. All its features will be available through OpenAI. Please use OpenAI instead of Dialogflow.', 'info');
        });

        $(settings_area).on('change', '#registration-required select', function () {
            let value = $(this).val();
            if (['registration-login'].includes(value) && !settings_area.find('[id="reg-email"] input').is(':checked')) {
                infoBottom('The email field is required to activate the login form.', 'info');
            }
            if (value && settings_area.find('#privacy-active input').is(':checked')) {
                infoBottom(warning_messages[0].replace('{R}', 'Messages > Privacy message').replace('{R2}', 'Users > Require registration'), 'info');
            }
        });

        /*
        * ----------------------------------------------------------
        * Chatbot area
        * ----------------------------------------------------------
        */

        $(chatbot_area).on('click', '#mc-flow-add', function () {
            MCAdmin.genericPanel('flow-add', 'Enter the flow name', '<div class="mc-setting"><input type="text"></div>', ['Add new flow']);
        });

        $(admin).on('click', '#mc-add-new-flow', function () {
            MCApps.openAI.flows.set(admin.find('.mc-flow-add-box input').val().replace(/[^a-zA-Z\u00C0-\u1FFF\u2C00-\uD7FF\uAC00-\uD7A3]/g, ''));
            admin.mcHideLightbox();
        });

        $(flows_area).on('click', '.mc-flow-block', function () {
            flows_area.find('.mc-flow-block,.mc-flow-add-block').mcActive(false);
            $(this).mcActive(true);
            let block = MCApps.openAI.flows.blocks.get();
            let code;
            let code_2 = '';
            let type = $(this).attr('data-type');
            let code_repeater = `<div class="mc-title">{R}</div><div data-type="repeater" class="mc-setting mc-type-repeater"><div class="input"><div class="mc-repeater">{R2}</div><div class="mc-btn mc-btn-white mc-repeater-add mc-icon"><i class="mc-icon-plus"></i>${mc_('Add new item')}</div></div></div>`;
            let code_conditions = `<div class="mc-title">${mc_('Conditions')}</div><div class="mc-flow-conditions"></div><div class="mc-add-condition mc-btn mc-icon mc-btn-white"><i class="mc-icon-plus"></i>${mc_('Add condition')}</div>`;
            let code_message = `<div class="mc-title">${mc_('Message')}</div><div class="mc-setting"><textarea placeholder="${mc_('The message sent to the user...')}">${block.message}</textarea></div>`;
            let code_select_user_details = MCApps.openAI.getCode.select_user_details();
            switch (type) {
                case 'start':
                    code = `<div class="mc-title">${mc_('Start event')}</div><div class="mc-setting"><select class="mc-flow-start-select"><option value="message"${block.start == 'message' ? ' selected' : ''}>${mc_('User message')}</option><option value="conversation"${block.start == 'conversation' ? ' selected' : ''}>${mc_('New conversation started')}</option><option value="load"${block.start == 'load' ? ' selected' : ''}>${mc_('On page load')}</option></select></div><div class="mc-title mc-title-flow-start${block.start == 'message' ? `` : ` mc-hide`}">${mc_('User message')}</div><div data-type="repeater" class="mc-setting mc-flow-start-messages mc-type-repeater"><div class="input"><div class="mc-repeater"><div class="repeater-item"><div class="mc-setting"><textarea data-id="message"></textarea></div><i class="mc-icon-close"></i></div></div><div class="mc-btn mc-btn-white mc-repeater-add mc-icon"><i class="mc-icon-plus"></i>${mc_('Add message')}</div></div></div>${code_conditions}<div class="mc-title">${mc_('Disabled')}</div><div class="mc-setting"><input type="checkbox" id="mc-flow-disabled"${block.disabled ? ' checked' : ''}></div>`;
                    break;
                case 'button_list':
                    if (!block.options || !block.options.length) {
                        block.options = [''];
                    }
                    for (var i = 0; i < block.options.length; i++) {
                        code_2 += `<div class="repeater-item"><div><input data-id type="text" value="${block.options[i]}"></div><i class="mc-icon-close"></i></div>`;
                    }
                    code = code_message + code_repeater.replace(`{R}`, mc_('Buttons')).replace(`{R2}`, code_2);
                    break;
                case 'message':
                    if (!block.attachments || !block.attachments.length) {
                        block.attachments = [''];
                    }
                    for (var i = 0; i < block.attachments.length; i++) {
                        code_2 += `<div class="repeater-item"><div><input data-id type="text" value="${block.attachments[i]}" placeholder="${mc_('Enter a link...')}"></div><i class="mc-icon-close"></i></div>`;
                    }
                    code = code_message + code_repeater.replace(`{R}`, mc_('Attachments')).replace(`{R2}`, code_2).replace(`</div></div></div>`, `</div><i class="mc-repeater-upload mc-btn-icon mc-icon-clip"></i></div></div>`);
                    break;
                case 'video':
                    code = `${code_message}<div class="mc-title">${mc_('Video URL')}</div><div class="mc-setting"><input type="url" placeholder="${mc_('Enter a YouTube or Vimeo link...')}" value="${block.url}"></div>`;
                    break;
                case 'get_user_details':
                    if (!block.details || !block.details.length) {
                        block.details = [['', '', false]];
                    }
                    for (var i = 0; i < block.details.length; i++) {
                        code_2 += `<div class="repeater-item"><div>${code_select_user_details.replace(`"${block.details[i][0]}"`, `"${block.details[i][0]}" selected`)}<div class="mc-setting"><input type="text" placeholder="${mc_('Enter a description...')}" value="${block.details[i][1]}" /></div><div class="mc-setting"><label>${mc_('Required')}</label><input type="checkbox" ${block.details[i][2] ? ' checked' : ''}></div></div><i class="mc-icon-close"></i></div>`;
                    }
                    code = code_message + code_repeater.replace(`{R}`, mc_('User details')).replace(`{R2}`, code_2).replace('mc-type-repeater', 'mc-type-repeater mc-repeater-block-user-details');
                    break;
                case 'set_data':
                    code = MCApps.openAI.getCode.set_data(block.data);
                    break;
                case 'action':
                    code = MCApps.openAI.getCode.actions(block.actions);
                    break;
                case 'rest_api':
                    let keys = ['headers', 'save_response'];
                    code = `<div class="mc-title">${mc_('URL')}</div><div class="mc-setting"><input type="url" class="mc-rest-api-url" value="${block.url}"></div><div class="mc-title">${mc_('Method')}</div><div class="mc-setting"><select class="mc-rest-api-method"><option value="GET"${block.method == 'GET' ? ' selected' : ''}>GET</option><option value="POST"${block.method == 'POST' ? ' selected' : ''}>POST</option><option value="PUT"${block.method == 'PUT' ? ' selected' : ''}>PUT</option><option value="PATH"${block.method == 'PATH' ? ' selected' : ''}>PATH</option><option value="DELETE"${block.method == 'DELETE' ? ' selected' : ''}>DELETE</option></select></div><div class="mc-title">${mc_('Body')}</div><div class="mc-setting"><textarea placeholder="JSON">${block.body}</textarea></div>`;
                    for (var i = 0; i < keys.length; i++) {
                        let values = block[keys[i]];
                        if (!values || !values.length) {
                            values = [['', '']];
                        }
                        code += `<div class="mc-title">${mc_(MCF.slugToString(keys[i]))}</div><div data-type="repeater" class="mc-setting mc-type-repeater mc-repeater-block-rest-api mc-rest-api-${keys[i]}"><div class="input"><div class="mc-repeater">`;
                        for (var j = 0; j < values.length; j++) {
                            code += `<div class="repeater-item"><div>${i == 1 ? code_select_user_details.replace(`"${values[j][0]}"`, `"${values[j][0]}" selected`) : `<div class="mc-setting"><input type="text" placeholder="${mc_('Key')}" value="${values[j][0]}" /></div>`}<div class="mc-setting"><input type="text" placeholder="${mc_(i == 1 ? 'e.g. data.id' : 'Value')}" value="${values[j][1]}" /></div></div><i class="mc-icon-close"></i></div>`;
                        }
                        code += `</div><div class="mc-btn mc-btn-white mc-repeater-add mc-icon"><i class="mc-icon-plus"></i>${mc_('Add new item')}</div></div></div>`;
                    }
                    break;
                case 'condition':
                    code = code_conditions;
                    break;
            }
            code += `<div id="mc-block-delete" class="mc-btn-text"><i class="mc-icon-delete"></i>${mc_('Delete')}</div>`
            MCAdmin.genericPanel('flow-block', MCF.slugToString(type), code, ['Save changes'], '', true);
            if (type == 'start' || type == 'condition') {
                if (!Array.isArray(block.message)) block.message = [{ message: block.message }]; // Depreceated
                let repeater = admin.find('.mc-flow-start-messages .mc-repeater');
                let code = MCSettings.repeater.set(block.message, repeater.find('.repeater-item:last-child'));
                MCSettings.automations.setConditions(block.conditions, admin.find('.mc-flow-conditions'));
                if (code) {
                    repeater.html(code);
                }
            }
            step_scroll_positions = [];
            flows_area.find('> div').each(function () {
                step_scroll_positions.push($(this).find('> div')[0].scrollTop);
            });
        });

        $(admin).on('click', '.mc-flow-block-box #mc-save-changes', function () {
            let block = MCApps.openAI.flows.blocks.get();
            let box = admin.find('.mc-flow-block-box');
            block.message = box.find('textarea').val();
            switch (block.type) {
                case 'start':
                    block.message = MCSettings.repeater.get(box.find('.mc-flow-start-messages .repeater-item'));
                    block.start = box.find('select').val();
                    block.disabled = box.find('#mc-flow-disabled').is(':checked');
                    block.conditions = MCSettings.automations.getConditions(box.find('.mc-flow-conditions'));
                    if (block.message.length && block.message[0].message.trim().split(' ').length < 3) {
                        return box.find('.mc-info').mcActive(true).html(mc_('The message must contain at least 3 words.'));
                    }
                    break;
                case 'button_list':
                    block.options = box.find('.mc-repeater input').map(function () { return $(this).val().trim() }).get().filter(function (value) { return value != '' });
                    break;
                case 'message':
                    block.attachments = box.find('.mc-repeater input').map(function () { return $(this).val().trim() }).get().filter(function (value) { return value != '' });
                    break;
                case 'video':
                    block.url = box.find('input').val();
                    break;
                case 'get_user_details':
                    block.details = box.find('.repeater-item').map(function () { return [[$(this).find('select').val(), $(this).find('input[type=text]').val(), $(this).find('input[type=checkbox]').is(':checked')]] }).get();
                    break;
                case 'action':
                case 'set_data':
                    block[block.type == 'action' ? 'actions' : 'data'] = box.find('.repeater-item').map(function () { return [[$(this).find('select').val(), $(this).find('input').length ? $(this).find('input').val().replace(/https?:\/\/|["|:]/g, '') : '']] }).get();
                    break;
                case 'rest_api':
                    block.headers = box.find('.mc-rest-api-headers .repeater-item').map(function () { return [[$(this).find('input').eq(0).val(), $(this).find('input').eq(1).val()]] }).get();
                    block.save_response = box.find('.mc-rest-api-save_response .repeater-item').map(function () { return [[$(this).find('select').val(), $(this).find('input').val()]] }).get();
                    block.url = box.find('.mc-rest-api-url').val();
                    block.method = box.find('.mc-rest-api-method').val();
                    block.body = box.find('textarea').val();
                    delete block.message;
                    break;
                case 'condition':
                    block.conditions = MCSettings.automations.getConditions(box.find('.mc-flow-conditions'));
                    break;
            }
            MCApps.openAI.flows.blocks.set(block);
            flows_area.find('> div').each(function () {
                $(this).find('> div')[0].scrollTop = step_scroll_positions[$(this).index()];
            });
            admin.mcHideLightbox();
        });

        $(admin).on('change', '.mc-repeater-block-actions select', function () {
            $(this).parent().next().remove();
            $(this).parent().parent().append(MCApps.openAI.getCode.action($(this).val(), ''));
        });

        $(admin).on('change', '.mc-flow-start-select', function () {
            admin.find('.mc-title-flow-start, .mc-flow-start-messages').setClass('mc-hide', $(this).val() != 'message');
        });

        $(flows_area).on('mouseleave', '.mc-flow-connectors > div, .mc-flow-block', function () {
            flows_area.find('.mc-flow-block-cnt').mcActive(false);
            is_over_connector = false;
            if ($(this).parent().hasClass('mc-flow-connectors')) {
                MCApps.openAI.flows.blocks.activateLinkedCnts($(this).closest('.mc-flow-block'));
            } else {
                flows_area.find('.mc-flow-connectors > div').mcActive(false);
            }
        });

        $(flows_area).on('mouseenter', '.mc-flow-connectors > div', function () {
            let block_cnt = $(this).closest('.mc-flow-block').parent();
            let next_block_cnt_indexes = MCApps.openAI.flows.blocks.getNextCntIndexes(MCApps.openAI.flows.getActiveIndex(), block_cnt.parent().parent().index(), block_cnt.index());
            is_over_connector = true;
            flows_area.find('> div').eq(block_cnt.parent().parent().index() + 1).find('.mc-flow-block-cnt').mcActive(false).eq(next_block_cnt_indexes[$(this).index()]).mcActive(true);
        });

        $(flows_area).on('mouseenter', '.mc-flow-block', function () {
            MCApps.openAI.flows.blocks.activateLinkedCnts(this);
        });

        $(flows_area).on('click', '.mc-flow-add-block', function () {
            flows_area.find('.mc-flow-block,.mc-flow-add-block').mcActive(false);
            $(this).mcActive(true);
            let active_blocks = MCApps.openAI.flows.steps.get()[MCApps.openAI.flows.blocks.getActiveCntIndex()].map(item => item.type);
            let all = !active_blocks.some(element => ['message', 'button_list', 'video', 'get_user_details', 'condition'].includes(element));
            let nav_items = [['set_data', 'Set data'], ['action', 'Action'], ['condition', 'Condition'], ['rest_api', 'REST API']];
            let code = '';
            for (var i = 0; i < nav_items.length; i++) {
                if (!active_blocks.includes(nav_items[i][0])) {
                    code += `<li data-value="${nav_items[i][0]}">${mc_(nav_items[i][1])}</li>`;
                }
            }
            MCAdmin.genericPanel('flows-blocks-nav', '', `<ul class="mc-menu">${all ? `<li>${mc_('Messages')} <ul><li data-value="message">${mc_('Send message')}</li><li data-value="button_list">${mc_('Send button list')}</li><li data-value="video">${mc_('Send video')}</li></ul></li>` : ``}<li>${mc_('More')} <ul>${all ? `<li data-value="get_user_details">${mc_('Get user details')}</li>` : ``}${code}</ul></li></ul>`);
        });

        $(admin).on('click', '#mc-block-delete', function () {
            MCApps.openAI.flows.blocks.delete();
            admin.mcHideLightbox();
        });

        $(flows_nav).on('click', 'li', function () {
            MCApps.openAI.flows.show($(this).attr('data-value'));
        });

        $(flows_nav).on('click', 'li i', function (e) {
            infoPanel('The flow will be deleted.', 'alert', () => {
                MCApps.openAI.flows.delete($(this).parent().attr('data-value'));
            });
            e.preventDefault();
            return false;
        });

        $(admin).on('click', '.mc-flows-blocks-nav-box [data-value]', function () {
            MCApps.openAI.flows.blocks.add($(this).data('value'));
            admin.mcHideLightbox();
        });

        $(admin).on('mouseenter', '.mc-flow-scroll', function () {
            let is_back = $(this).hasClass('mc-icon-arrow-left');
            flow_scroll_interval = setInterval(() => {
                flows_area[0].scrollLeft += 10 * (is_back ? -1 : 1);
            }, 10);
        });

        $(admin).on('mouseleave', '.mc-flow-scroll', function () {
            clearInterval(flow_scroll_interval);
        });

        $(chatbot_area).on('click', '#mc-train-chatbot', function (e) {
            let success_text = 'The chatbot has been successfully trained.';
            let nav = chatbot_area.find('.mc-nav [data-value="conversations"]');
            infoPanel('<br><br><br><br><br>', 'info', false, 'mc-embeddings-box');
            e.preventDefault();
            if (MC_ADMIN_SETTINGS.cloud && MCCloud.creditsAlert(this, e)) {
                return false;
            }
            if (loading(this)) {
                return false;
            }
            if (chatbot_area.find('.mc-menu-chatbot .mc-active').attr('data-type') == 'flows') {
                MCApps.openAI.flows.save((response) => {
                    infoPanel(success_text, 'info', false, false, 'Success');
                });
                $(this).mcLoading(false);
                return;
            }
            if (nav.mcActive()) {
                let to_update = [];
                for (var i = 0; i < conversations_qea.length; i++) {
                    let qea = chatbot_area.find(`#mc-chatbot-conversations [data-index="${i}"]`);
                    if (qea.length) {
                        qea = [qea.find('input').val(), qea.find('textarea').val()];
                        if (qea[0] != $('<textarea />').html(conversations_qea[i].question).text() || qea[1] != $('<textarea />').html(conversations_qea[i].answer).text()) {
                            conversations_qea[i].question = qea[0];
                            conversations_qea[i].answer = qea[1];
                            to_update.push(conversations_qea[i]);
                        }
                    } else {
                        conversations_qea[i].question = false;
                        conversations_qea[i].answer = false;
                        to_update.push(conversations_qea[i]);
                    }
                }
                if (to_update.length) {
                    MCF.ajax({
                        function: 'open-ai-save-conversation-embeddings',
                        qea: to_update
                    }, (response) => {
                        if (response[0] === true) {
                            infoPanel(success_text, 'info', false, false, 'Success');
                        } else if (!MCApps.openAI.train.isError(response[0])) {
                            infoPanel(response[0]);
                        }
                        nav.click();
                        $(this).mcLoading(false);
                    });
                } else {
                    $(this).mcLoading(false);
                    infoPanel(success_text, 'info', false, false, 'Success');
                }
                return;
            }
            MCApps.openAI.train.errors = [];

            // Files
            let index = 0;
            MCApps.openAI.train.files((response) => {

                // Website
                MCApps.openAI.train.urls = chatbot_area.find('[data-id="open-ai-sources-url"]').map(function () { return $(this).val().trim() }).get();
                MCApps.openAI.train.extract_url = chatbot_area.find('[data-id="open-ai-sources-extract-url"]').map(function () { return $(this).is(':checked') }).get();
                MCApps.openAI.train.website((response) => {

                    // Q&A
                    MCApps.openAI.train.qea((response) => {

                        // Articles
                        MCApps.openAI.train.articles((response) => {

                            // Finish
                            MCApps.openAI.init();
                            chatbot_area.find('#mc-repeater-chatbot-website .repeater-item i').click();

                            if (MCApps.openAI.train.errors.length) {
                                infoPanel(mc_('The chatbot has been trained with errors. Check the console for more details.') + '\n\n<pre>' + MCApps.openAI.train.errors.join('<br>').replaceAll('false,','') + '</pre>', 'info', false, 'mc-errors-list-box', false, true);
                                console.error(MCApps.openAI.train.errors);
                            } else if (!MCApps.openAI.train.isError(response)) {
                                infoPanel(success_text, 'info', false, false, 'Success');
                            }
                            $(this).mcLoading(false);
                        });
                    });
                });
            });
            return false;
        });

        $(chatbot_area).on('click', '#mc-table-chatbot-files td i, #mc-table-chatbot-website td i, #mc-chatbot-delete-files, #mc-chatbot-delete-website, #mc-chatbot-delete-all-training, #mc-chatbot-delete-all-training-conversations', function () {
            let is_i = $(this).is('i');
            let tr = is_i ? $(this).closest('tr') : false;
            if (is_i && tr.hasClass('mc-pending')) {
                MCApps.openAI.train.skip_files.push(tr.attr('data-name'));
                tr.remove();
                return;
            }
            infoPanel('The training data will be permanently deleted.', 'alert', () => {
                let sources_to_delete = [];
                let id = $(this).attr('id');
                if (is_i) {
                    sources_to_delete = [tr.attr('data-url')];
                } else if (id == 'mc-chatbot-delete-all-training') {
                    sources_to_delete = 'all';
                } else if (id == 'mc-chatbot-delete-all-training-conversations') {
                    sources_to_delete = 'all-conversations';
                } else {
                    let table = $(id == 'mc-chatbot-delete-files' ? chatbot_files_table : chatbot_website_table);
                    if (!table.find('input:checked').length) {
                        table.find('input').prop('checked', true);
                    }
                    table.find('tr').each(function () {
                        if ($(this).find('input:checked').length) {
                            if ($(this).hasClass('mc-pending')) {
                                MCApps.openAI.train.skip_files.push($(this).attr('data-name'));
                                $(this).remove();
                            } else {
                                let url = $(this).attr('data-url');
                                sources_to_delete.push(url);
                                if (MCApps.openAI.train.sitemap_processed_urls.indexOf(url) > -1) {
                                    MCApps.openAI.train.sitemap_processed_urls[MCApps.openAI.train.sitemap_processed_urls.indexOf(url)] = false;
                                }
                            }
                        }
                    });
                }
                if (sources_to_delete.length) {
                    if (loading(this)) return;
                    MCF.ajax({
                        function: 'open-ai-embeddings-delete',
                        sources_to_delete: sources_to_delete
                    }, (response) => {
                        MCApps.openAI.init();
                        if (sources_to_delete == 'all') {
                            chatbot_area.find('.mc-nav [data-value="info"]').click();
                        } else if (sources_to_delete == 'all-conversations') {
                            chatbot_area.find('.mc-nav [data-value="conversations"]').click();
                        }
                        if (response === true) {
                            infoBottom('Training data deleted.');
                        } else {
                            infoPanel(response);
                        }
                        $(this).mcLoading(false);
                    });
                }
            });
            return false;
        });

        $(chatbot_area).on('click', '.mc-nav [data-value="conversations"]', function () {
            let area = chatbot_area.find('#mc-chatbot-conversations');
            if (loading(area)) return;
            MCF.ajax({
                function: 'open-ai-get-conversation-embeddings'
            }, (response) => {
                if (!response.length) {
                    area.html(`<p class="mc-no-results">${mc_('No conversations found.')}</p>`);
                } else {
                    let code = '';
                    conversations_qea = response;
                    for (var i = 0; i < response.length; i++) {
                        code += `<div class="repeater-item" data-value="${response[i].id}" data-index=${i}><div><label>${mc_('Question')}</label><input data-id="q" type="text" value="${response[i].question}" /></div><div class="mc-qea-repeater-answer"><label>${mc_('Answer')}</label><textarea data-id="a">${response[i].answer}</textarea></div><i class="mc-icon-close"></i></div>`;
                    }
                    area.find('.mc-repeater').html(code);
                }
                area.mcLoading(false);
            });
        });

        $(chatbot_area).on('click', '.mc-nav [data-value="info"]', function () {
            let area = chatbot_area.find('#mc-chatbot-info');
            if (loading(area)) return;
            MCF.ajax({
                function: 'open-ai-get-information'
            }, (response) => {
                let list = [['files', 'Files'], ['website', 'Website URLs'], ['qea', 'Q&A'], ['flows', 'Flows'], ['articles', 'Articles'], ['conversations', 'Conversations']];
                let code = `<h2>${mc_('Sources')}</h2><p>`;
                for (var i = 0; i < list.length; i++) {
                    code += response[list[i][0]] ? `${response[list[i][0]][1]} ${mc_(list[i][1])} (${response[list[i][0]][0]} ${mc_('chars')})<br>` : '';
                }
                code += `</p><h2>${mc_('Total detected characters')}</h2><p>${response.total} ${mc_('chars') + (response.limit ? ' / ' + response.limit + ' ' + mc_('limit') : '')}</p><hr><div id="mc-chatbot-delete-all-training" class="mc-btn mc-btn-white">${mc_('Delete all training data')}</div>`;
                area.html(code);
                area.mcLoading(false);
            });
        });

        $(chatbot_area).on('click', '.mc-menu-chatbot [data-type]', function (e) {
            let type = $(this).data('type');
            switch (type) {
                case 'flows':
                case 'training':
                case 'playground':
                    let area = chatbot_area.find(`> [data-id="${type}"]`);
                    chatbot_area.find('> [data-id]').mcActive(false)
                    area.mcActive(true);
                    if (type == 'flows' && area.mcLoading()) {
                        MCF.ajax({ function: 'open-ai-flows-get' }, (response) => {
                            for (var i = 0; i < response.length; i++) {
                                if (response[i] && response[i].steps && response[i].name) {
                                    MCApps.openAI.flows.flows.push(response[i]);
                                }
                            }
                            let code = '';
                            for (var i = 0; i < response.length; i++) {
                                if (response[i]) {
                                    code += MCApps.openAI.flows.navCode(response[i].name);
                                }
                            }
                            flows_nav.html(code);
                            flows_nav.find('li:first-child').click();
                            area.mcLoading(false);
                        });
                    }
                    break;
                case 'settings':
                    MCSettings.open('dialogflow', true);
                    e.preventDefault;
                    return false;
            }
        });

        $(chatbot_qea_repeater).on('click', '.mc-enlarger-function-calling', function () {
            $(this).parent().parent().find('.mc-qea-repeater-answer').addClass('mc-hide');
        });

        $(chatbot_qea_repeater).on('change', '[data-id="open-ai-faq-set-data"] select', function () {
            $(this).parent().next().find('input').setClass('mc-hide', ['transcript', 'transcript_email', 'human_takeover', 'archive_conversation'].includes($(this).val()));
        });

        $(chatbot_qea_repeater).on('input click', '[data-id="open-ai-faq-answer"]', function () {
            $(this).prev().find('i').mcActive($(this).val().length > 2 && $(this).val().indexOf(' '));
        });

        $(chatbot_qea_repeater).on('click', '.mc-qea-repeater-answer > label > i', function () {
            let textarea = $(this).closest('.repeater-item').find('[data-id="open-ai-faq-answer"]');
            if (loading(this)) return;
            MCApps.openAI.rewrite(textarea.val(), (response) => {
                $(this).mcLoading(false);
                if (response[0]) {
                    textarea.val(response[1]);
                }
            });
        });

        $(chatbot_playground_editor).on('click', '[data-value="add"], [data-value="send"]', function () {
            let textarea = chatbot_playground_editor.find('textarea');
            let message = textarea.val().trim();
            textarea.val('');
            if (message) {
                MCApps.openAI.playground.addMessage(message, chatbot_playground_editor.find('[data-value="user"], [data-value="assistant"]').attr('data-value'));
            }
            if ($(this).data('value') == 'send') {
                let length = MCApps.openAI.playground.messages.length;
                if (length && !loading(this)) {
                    MCF.ajax({
                        function: 'open-ai-playground-message',
                        messages: MCApps.openAI.playground.messages
                    }, (response) => {
                        if (response[0]) {
                            if (response[1]) {
                                MCApps.openAI.playground.addMessage(response[1], 'assistant', response[6]);
                                if (response[4]) {
                                    let code = '';
                                    for (var key in response[4].usage) {
                                        if (['string', 'number'].includes(typeof response[4].usage[key])) {
                                            code += `<b>${MCF.slugToString(key)}</b>: ${response[4].usage[key]}<br>`;
                                        }
                                    }
                                    MCApps.openAI.playground.last_response = response[4];
                                    chatbot_area.find('.mc-playground-info').html(code + `<div id="mc-playground-query" class="mc-btn-text">${mc_('View code')}</div>${response[4].embeddings ? `<div id="mc-playground-embeddings" class="mc-btn-text">${mc_('Embeddings')}</div>` : ``}`);
                                    if (response[4].payload) {
                                        MCApps.openAI.playground.messages[length - 1].push(response[4].payload);
                                    }
                                }
                            }
                            if (response[8]) {
                                conversations_admin_list_ul.find(`[data-conversation-id="${response[8]}"]`).remove();
                            }
                        } else {
                            infoPanel(response);
                            console.error(response);
                        }
                        $(this).mcLoading(false);
                    });
                }
            }
        });

        $(chatbot_area).on('click', '#mc-playground-query', function () {
            infoPanel('<pre>' + JSON.stringify(MCApps.openAI.playground.last_response.query, null, 4).replaceAll('\\"', '"') + '</pre>', 'info', false, 'mc-playground-query-panel', false, true);
        });

        $(chatbot_area).on('click', '#mc-playground-embeddings', function () {
            let code = '';
            let embeddings = MCApps.openAI.playground.last_response.embeddings;
            for (var i = embeddings.length - 1; i > -1; i--) {
                code += `<span><b>${mc_('Source')}</b>: ${embeddings[i].source ? embeddings[i].source.autoLink({ target: '_blank' }) : ''}<br><b>${mc_('Score')}</b>: ${embeddings[i].score}<br><span>${embeddings[i].text}</span></span>`;
            }
            infoPanel(code, 'info', false, 'mc-playground-embeddings-panel', false, true);
        });

        $(chatbot_playground_editor).on('click', '[data-value="clear"]', function () {
            MCApps.openAI.playground.messages = [];
            chatbot_playground_area.html('');
            chatbot_area.find('.mc-playground-info').html('');
        });

        $(chatbot_playground_area).on('click', '.mc-icon-close', function () {
            let element = $(this).closest('[data-type]');
            MCApps.openAI.playground.messages.splice(element.index(), 1);
            element.remove();
        });

        $(chatbot_playground_area).on('click', '.mc-rich-chips .mc-btn', function () {
            chatbot_playground_editor.find('textarea').val($(this).html());
            chatbot_playground_editor.find('[data-value="send"]').click();
        });

        $(chatbot_playground_editor).on('click', '[data-value="user"], [data-value="assistant"]', function () {
            let is_user = $(this).attr('data-value') == 'user';
            $(this).attr('data-value', is_user ? 'assistant' : 'user').html('<i class="mc-icon-reload"></i> ' + mc_(is_user ? 'Assistant' : 'User'));
        });

        $(admin).on('click', '#open-ai-troubleshoot a, #google-troubleshoot a', function (e) {
            let id = $(this).parent().attr('id');
            e.preventDefault();
            if (id != 'google-troubleshoot' && ![true, 'mode'].includes(MCApps.openAI.troubleshoot())) {
                return false;
            }
            if (loading(this)) return;
            MCF.ajax({
                function: $(this).parent().attr('id')
            }, (response) => {
                if (response === true) {
                    infoBottom('Success. No issues found.');
                } else {
                    infoPanel(response);
                }
                $(this).mcLoading(false);
                $(conversations_area).find('.mc-admin-list .mc-select li.mc-active').click();
            });
            return false;
        });

        /*
        * ----------------------------------------------------------
        * Articles area
        * ----------------------------------------------------------
        */

        $(articles_area).on('click', '.ce-settings__button--delete.ce-settings__button--confirm', function () {
            let url = articles_area.find('.image-tool--filled img').attr('src');
            if (url) {
                MCF.ajax({ function: 'delete-file', path: url });
            }
        });

        $(articles_area).on('click', '.ul-articles li', function (e) {
            infoPanel('The changes will be lost.', 'alert', () => {
                MCArticles.show($(this).attr('data-id'));
                articles_area.find('.mc-scroll-area:not(.mc-nav)').scrollTop(0);
            }, false, false, false, !articles_save_required, () => {
                $(this).parent().find('li').mcActive(false);
                $(this).parent().find(`[data-id="${MCArticles.activeID()}"]`).mcActive(true);
            });
        });

        $(articles_area).on('click', '.ul-categories li', function (e) {
            MCArticles.categories.show($(this).attr('data-id'));
        });

        $(articles_area).on('click', '.mc-add-article', function () {
            MCArticles.add();
        });

        $(articles_area).on('click', '.mc-add-category', function () {
            MCArticles.categories.add();
        });

        $(articles_area).on('click', '.mc-nav i', function (e) {
            let parent = $(this).parent();
            let nav = parent.closest('ul');
            let is_category = nav.hasClass('ul-categories');
            infoPanel(`The ${is_category ? 'category' : 'article'} will be deleted permanently.`, 'alert', () => {
                let id = parent.attr('data-id');
                if (is_category) {
                    MCArticles.categories.delete(id);
                } else {
                    articles_area.find('#editorjs .image-tool__image-picture').each(function () {
                        MCF.ajax({ function: 'delete-file', path: $(this).attr('src') });
                    });
                    if (!id) {
                        return parent.remove();
                    }
                    loading(articles_content);
                    MCArticles.delete(id, (response) => {
                        articles_content.mcLoading(false);
                        editorJSDestroy();
                        if (nav.find('li').length > 1) {
                            setTimeout(() => {
                                if (parent.prev().length) {
                                    parent.prev().click();
                                } else {
                                    parent.next().click();
                                }
                                parent.remove();
                            }, 300);
                        }
                    });
                }
            });
            e.preventDefault();
            return false;
        });

        $(articles_area).on('click', '.mc-menu-wide li', function () {
            let type = $(this).data('type');
            if (type == 'settings') {
                MCSettings.open('articles', true);
            } else if (type == 'reports') {
                MCReports.open('articles-searches');
            } else {
                articles_area.attr('data-type', type);
                MCArticles.categories.update();
            }
        });

        $(articles_area).on('click', '.mc-save-articles', function () {
            if (loading(this)) return;
            if (articles_area.attr('data-type') == 'categories') {
                MCArticles.categories.save((response) => {
                    $(this).mcLoading(false);
                });
            } else {
                MCArticles.save((response) => {
                    $(this).mcLoading(false);
                });
            }
        });

        $(articles_area).on('change input', 'input, textarea, select', function () {
            articles_save_required = true;
        });

        $(articles_category_select).on('change', function () {
            if (!articles_category_parent_select.val()) {
                infoBottom('Select a parent category first.', 'error');
                $(this).val('');
            }
        });

        /*
        * ----------------------------------------------------------
        * Reports area
        * ----------------------------------------------------------
        */

        $(reports_area).on('click', '.mc-nav [id]', function () {
            let id = $(this).attr('id');
            MCReports.active_report = false;
            reports_area.find('#mc-date-picker').val('');
            reports_area.attr('class', 'mc-area-reports mc-active mc-report-' + id);
            MCReports.initReport($(this).attr('id'));
            if (MCF.getURL('report') != id) {
                pushState('?report=' + id);
            }
        });

        $(reports_area).on('change', '#mc-date-picker', function () {
            MCReports.initReport(false, $(this).val());
        });

        $(reports_area).on('click', '.mc-report-export', function () {
            if ($(this).mcLoading()) return;
            MCReports.export((response) => {
                $(this).mcLoading(false);
                if (response) {
                    dialogDeleteFile(response, 'mc-export-report-close', 'Report exported')
                }
            });
        });

        if (MCF.getURL('report')) {
            if (!reports_area.mcActive()) {
                header.find('.mc-admin-nav #mc-reports').click();
            }
            setTimeout(() => {
                reports_area.find('#' + MCF.getURL('report')).click()
            }, 500);
        }

        /*
        * ----------------------------------------------------------
        * Woocommerce
        * ----------------------------------------------------------
        */

        // Panel reload button
        $(conversations_area).on('click', '.mc-panel-woocommerce > i', function () {
            MCApps.woocommerce.conversationPanel();
        });

        // Get order details
        $(conversations_area).on('click', '.mc-woocommerce-orders > div > span', function (e) {
            let parent = $(this).parent();
            if (!$(e.target).is('span')) return;
            if (!parent.mcActive()) {
                MCApps.woocommerce.conversationPanelOrder(parent.attr('data-id'));
            }
        });

        // Products popup 
        $(conversations_area).on('click', '.mc-btn-woocommerce', function () {
            if (woocommerce_products_box_ul.mcLoading() || (activeUser() != false && activeUser().language != MCApps.itemsPanel.panel_language)) {
                MCApps.itemsPanel.populate('woocommerce');
            }
            woocommerce_products_box.find('.mc-search-btn').mcActive(true).find('input').get(0).focus();
            woocommerce_products_box.mcTogglePopup(this);
        });

        // Products popup pagination
        $(woocommerce_products_box).find('.mc-woocommerce-products-list').on('scroll', function () {
            if (scrollPagination(this, true)) {
                MCApps.itemsPanel.pagination(this, 'woocommerce');
            }
        });

        // Products popup filter
        $(woocommerce_products_box).on('click', '.mc-select li', function () {
            MCApps.itemsPanel.filter(this, 'woocommerce');
        });

        // Products popup search
        $(woocommerce_products_box).on('input', '.mc-search-btn input', function () {
            MCApps.itemsPanel.search(this, 'woocommerce');
        });

        $(woocommerce_products_box).on('click', '.mc-search-btn i', function () {
            MCF.searchClear(this, () => { MCApps.itemsPanel.search($(this).next(), 'woocommerce') });
        });

        // Cart popup insert product
        $(woocommerce_products_box).on('click', '.mc-woocommerce-products-list li', function () {
            let action = woocommerce_products_box.attr('data-action');
            let id = $(this).data('id');
            if (MCF.null(action)) {
                MCChat.insertText(`{product_card id="${id}"}`);
            } else {
                woocommerce_products_box_ul.mcLoading(true);
                conversations_area.find('.mc-add-cart-btn').mcLoading(true);
                MCChat.sendMessage(-1, '', [], (response) => {
                    if (response) {
                        MCApps.woocommerce.conversationPanelUpdate(id);
                        admin.mcHideLightbox();
                    }
                }, { 'event': 'woocommerce-update-cart', 'action': 'cart-add', 'id': id });
            }
            MCF.deactivateAll();
            admin.removeClass('mc-popup-active');
        });

        // Cart add product
        $(conversations_area).on('click', '.mc-panel-woocommerce .mc-add-cart-btn', function () {
            if ($(this).mcLoading()) return;
            if (MCChat.user_online) {
                MCApps.itemsPanel.populate('woocommerce');
                woocommerce_products_box.mcShowLightbox(true, 'cart-add');
            } else {
                infoPanel('The user is offline. Only the carts of online users can be updated.');
            }
        });

        // Cart remove product
        $(conversations_area).on('click', '.mc-panel-woocommerce .mc-list-items > a > i', function (e) {
            let id = $(this).parent().attr('data-id');
            MCChat.sendMessage(-1, '', [], () => {
                MCApps.woocommerce.conversationPanelUpdate(id, 'removed');
            }, { 'event': 'woocommerce-update-cart', 'action': 'cart-remove', 'id': id });
            $(this).mcLoading(true);
            e.preventDefault();
            return false;
        });

        /*
        * ----------------------------------------------------------
        * Apps functions
        * ----------------------------------------------------------
        */

        // Ump
        $(conversations_area).on('click', '.mc-panel-ump > i', function () {
            MCApps.ump.conversationPanel();
        });

        // ARMember
        $(conversations_area).on('click', '.mc-panel-armember > i', function () {
            MCApps.armember.conversationPanel();
        });

        // OpenCart
        $(conversations_area).on('click', '.mc-panel-opencart > i', function () {
            MCApps.opencart.conversationPanel();
        });

        $(conversations_area).on('click', '.mc-opencart-orders > a', function () {
            MCApps.opencart.openOrder($(this).attr('data-id'));
        });

        $(settings_area).on('click', '#opencart-sync a', function (e) {
            e.preventDefault();
            if (loading(this)) return;
            MCF.ajax({
                function: 'opencart-sync'
            }, (response) => {
                $(this).mcLoading(false);
                infoPanel(response === true ? 'Users successfully imported.' : response);
            });
        });

        // Perfex, whmcs, aecommerce
        $(settings_area).on('click', '#perfex-sync a, #whmcs-sync a, #perfex-articles-sync a, #whmcs-articles-sync a, #aecommerce-sync a, #aecommerce-sync-admins a, #aecommerce-sync-sellers a, #martfury-sync a, #martfury-sync-sellers a', function (e) {
            if (loading(this)) return;
            let function_name = $(this).closest('[id]').attr('id');
            let articles = function_name.indexOf('article') > 0;
            MCF.ajax({
                function: function_name
            }, (response) => {
                if (response === true) {
                    if (!articles) {
                        MCUsers.update();
                    }
                    infoPanel(articles ? 'Articles successfully imported.' : 'Users successfully imported.');
                } else {
                    infoPanel('Error. Response: ' + JSON.stringify(response));
                }
                $(this).mcLoading(false);
            });
            e.preventDefault();
        });

        // Zendesk
        $(conversations_area).on('click', '#mc-zendesk-btn', function (e) {
            if (loading(this)) return;
            MCF.ajax({
                function: 'zendesk-create-ticket',
                conversation_id: MCChat.conversation.id
            }, (response) => {
                if (response === true) {
                    MCApps.zendesk.conversationPanel();
                } else {
                    infoPanel('Error. Response: ' + JSON.stringify(response));
                }
                $(this).mcLoading(false);
            });
            e.preventDefault();
        });

        $(conversations_area).on('click', '#mc-zendesk-update-ticket', function (e) {
            if (loading(this)) return;
            MCF.ajax({
                function: 'zendesk-update-ticket',
                conversation_id: MCChat.conversation.id,
                zendesk_ticket_id: $(this).closest('[data-id]').attr('data-id')
            }, () => {
                $(this).mcLoading(false);
            });
            e.preventDefault();
            return false;
        });

        /*
        * ----------------------------------------------------------
        * Miscellaneous
        * ----------------------------------------------------------
        */

        $(admin).on('click', '.mc-enlarger', function () {
            $(this).mcActive(true);
        });

        $(admin).on('mouseenter', '[data-mc-tooltip-init]', function () {
            $(this).parent().mcInitTooltips();
            $(this).removeAttr('data-mc-tooltip-init');
            $(this).trigger('mouseenter');
        });

        // Language switcher
        $(admin).on('click', '.mc-language-switcher > i', function () {
            let switcher = $(this).parent();
            let active_languages = switcher.find('[data-language]').map(function () { return $(this).attr('data-language') }).get();
            let code = '';
            active_languages.push('en');
            for (var key in MC_LANGUAGE_CODES) {
                if (!active_languages.includes(key)) {
                    code += `<div data-language="${key}"><img src="${MC_URL}/media/flags/${key}.png" />${mc_(MC_LANGUAGE_CODES[key])}</div>`;
                }
            }
            language_switcher_target = switcher;
            MCAdmin.genericPanel('languages', 'Choose a language', code, [], ' data-source="' + switcher.attr('data-source') + '"', true);
        });

        $(admin).on('click', '.mc-language-switcher img', function () {
            let item = $(this).parent();
            let active = item.mcActive();
            let language = active ? false : item.attr('data-language');
            switch (item.parent().attr('data-source')) {
                case 'article-categories':
                    MCArticles.categories.show(MCArticles.categories.activeID(), language);
                    break;
                case 'articles':
                    let previous_active = articles_content.find('.mc-language-switcher .mc-active');
                    infoPanel('The changes will be lost.', 'alert', () => {
                        let id = item.attr('data-id');
                        if (!id && !active) {
                            MCArticles.clear();
                        } else {
                            MCArticles.show(id && !active ? id : MCArticles.activeID(true));
                        }
                    }, false, false, false, !articles_save_required, () => {
                        item.mcActive(false);
                        previous_active.mcActive(true);
                    });
                    break;
                case 'automations':
                    MCSettings.automations.show(false, language);
                    break;
                case 'settings':
                    let active_language = item.parent().find('[data-language].mc-active');
                    MCSettings.translations.save(item, active ? item.attr('data-language') : (active_language.length ? active_language.attr('data-language') : false));
                    MCSettings.translations.activate(item, language);
                    break;
            }
            item.siblings().mcActive(false);
            item.mcActive(!active);
        });

        $(admin).on('click', '.mc-language-switcher span > i', function () {
            let item = $(this).parent();
            let language = item.attr('data-language');
            infoPanel(mc_('The {T} translation will be deleted.').replace('{T}', mc_(MC_LANGUAGE_CODES[language])), 'alert', () => {
                switch (item.parent().attr('data-source')) {
                    case 'article-categories':
                        MCArticles.categories.translations.delete(language);
                        break;
                    case 'articles':
                        MCArticles.translations.delete(language);
                        break;
                    case 'automations':
                        MCSettings.automations.deleteTranslation(false, language);
                        MCSettings.automations.show();
                        break;
                    case 'settings':
                        MCSettings.translations.delete(item, language);
                        break;
                }
                item.remove();
            });
        });

        $(admin).on('click', '.mc-languages-box [data-language]', function () {
            let box = $(this).parents().eq(1);
            let language = $(this).attr('data-language');
            let hide = true;
            switch (box.attr('data-source')) {
                case 'article-categories':
                    MCArticles.categories.translations.add(language);
                    break;
                case 'articles':
                    infoPanel('The changes will be lost.', 'alert', () => {
                        MCArticles.translations.add(language);
                        admin.mcHideLightbox();
                    }, false, false, false, !articles_save_required);
                    hide = false;
                    break;
                case 'automations':
                    MCSettings.automations.addTranslation(false, false, language);
                    MCSettings.automations.show(false, language);
                    break;
                case 'settings':
                    MCSettings.translations.add(language);
                    break;
            }
            if (hide) {
                admin.mcHideLightbox();
            }
        });

        // Lightbox
        $(admin).on('click', '.mc-lightbox .mc-top-bar .mc-close', function () {
            admin.mcHideLightbox();
        });

        $(admin).on('click', '.mc-lightbox .mc-info', function () {
            $(this).mcActive(false);
        });

        // Alert and information box
        $(admin).on('click', '.mc-dialog-box a', function () {
            let lightbox = $(this).closest('.mc-lightbox');
            if ($(this).hasClass('mc-confirm')) {
                alertOnConfirmation();
            }
            if ($(this).hasClass('mc-cancel') && alertOnCancel) {
                alertOnCancel();
            }
            if (admin.find('.mc-lightbox.mc-active').length == 1) {
                overlay.mcActive(false);
            }
            MCAdmin.open_popup = false;
            lightbox.mcActive(false);
        });

        $(admin).on('click', '.mc-menu-wide li, .mc-nav li', function () {
            $(this).siblings().mcActive(false);
            $(this).mcActive(true);
        });

        $(admin).on('click', '.mc-nav:not(.mc-nav-only) li:not(.mc-tab-nav-title)', function () {
            let area = $(this).closest('.mc-tab');
            let tab = $(area).find(' > .mc-content > div').mcActive(false).eq($(this).parent().find('li:not(.mc-tab-nav-title)').index(this));
            tab.mcActive(true);
            tab.find('textarea').each(function () {
                $(this).autoExpandTextarea();
                $(this).manualExpandTextarea();
            });
            area.find('.mc-scroll-area:not(.mc-nav)').scrollTop(0);
        });

        $(admin).mcInitTooltips();

        $(admin).on('click', '[data-button="toggle"]', function () {
            let show = admin.find('.' + $(this).data('show'));
            let hide = admin.find('.' + $(this).data('hide'));
            show.addClass('mc-show-animation').show();
            hide.hide();
            MCAdmin.open_popup = show.hasClass('mc-lightbox') || show.hasClass('mc-popup') ? show : false;
        });

        $(admin).on('click', '.mc-info-card', function () {
            $(this).mcActive(false);
        });

        $(upload_input).on('change', function () {
            if (upload_function) {
                upload_function();
                upload_function = false;
            } else {
                $(upload_target).mcLoading($(this).prop('files').length);
                $(this).mcUploadFiles((response) => {
                    $(upload_target).mcLoading(false);
                    response = JSON.parse(response);
                    if (response[0] == 'success') {
                        let type = $(upload_target).closest('[data-type]').data('type');
                        if (type == 'upload-image') {
                            if ($(upload_target).attr('data-value')) {
                                MCF.ajax({ function: 'delete-file', path: $(upload_target).attr('data-value') });
                            }
                            $(upload_target).attr('data-value', response[1]).css('background-image', `url("${response[1]}")`);
                        }
                        if (upload_on_success) {
                            upload_on_success(response[1]);
                        }
                    } else {
                        console.log(response[1]);
                    }
                });
            }
        });

        $(admin).on('click', '.mc-accordion > div > span', function (e) {
            let parent = $(this).parent();
            let active = $(parent).mcActive();
            if (!$(e.target).is('span')) return;
            parent.siblings().mcActive(false);
            parent.mcActive(!active);
        });

        $(admin).on('mousedown', function (e) {
            if ($(MCAdmin.open_popup).length) {
                let popup = $(MCAdmin.open_popup);
                if (!popup.is(e.target) && popup.has(e.target).length === 0 && !['mc-btn-saved-replies', 'mc-btn-emoji', 'mc-btn-woocommerce', 'mc-btn-shopify', 'mc-btn-open-ai'].includes($(e.target).attr('class'))) {
                    if (popup.hasClass('mc-popup')) {
                        popup.mcTogglePopup();
                    } else if (popup.hasClass('mc-select')) {
                        popup.find('ul').mcActive(false);
                    } else if (popup.hasClass('mc-menu-mobile')) {
                        popup.find('i').mcActive(false);
                    } else if (popup.hasClass('mc-menu')) {
                        popup.mcActive(false);
                    } else if (!MCAdmin.open_popup || !['mc-embeddings-box'].includes(MCAdmin.open_popup.attr('id'))) {
                        admin.mcHideLightbox();
                    }
                    MCAdmin.open_popup = false;
                }
            }
        });
    });

    function initialization() {
        MCF.ajax({
            function: 'get-conversations'
        }, (response) => {
            if (!response.length) {
                conversations_area.find('.mc-board').addClass('mc-no-conversation');
            }
            MCConversations.populateList(response);
            if (responsive) {
                conversations_area.find('.mc-admin-list').mcActive(true);
            }
            if (MCF.getURL('conversation')) {
                if (!conversations_area.mcActive()) {
                    header.find('.mc-admin-nav #mc-conversations').click();
                }
                MCConversations.openConversation(MCF.getURL('conversation'));
            } else if (!responsive && !MCF.getURL('user') && !MCF.getURL('setting') && !MCF.getURL('report') && (!MCF.getURL('area') || MCF.getURL('area') == 'conversations')) {
                setTimeout(() => {
                    MCConversations.clickFirst();
                }, 100);
            }
            MCConversations.startRealTime();
            MCConversations.datetime_last_conversation = MC_ADMIN_SETTINGS.now_db;
            loadingGlobal(false);
        });
        MCF.serviceWorker.init();
        if (MC_ADMIN_SETTINGS.push_notifications) {
            MCF.serviceWorker.initPushNotifications();
        }
        setInterval(function () {
            MCF.ajax({
                function: 'get-active-user',
                db: true
            }, (response) => {
                if (!response) MCF.reset();
            });
        }, 3600000);
    }
}(jQuery));

// tinyColorPicker v1.1.1 2016-08-30 

!function (a, b) { "object" == typeof exports ? module.exports = b(a) : "function" == typeof define && define.amd ? define("colors", [], function () { return b(a) }) : a.Colors = b(a) }(this, function (a, b) { "use strict"; function c(a, c, d, f, g) { if ("string" == typeof c) { var c = v.txt2color(c); d = c.type, p[d] = c[d], g = g !== b ? g : c.alpha } else if (c) for (var h in c) a[d][h] = k(c[h] / l[d][h][1], 0, 1); return g !== b && (a.alpha = k(+g, 0, 1)), e(d, f ? a : b) } function d(a, b, c) { var d = o.options.grey, e = {}; return e.RGB = { r: a.r, g: a.g, b: a.b }, e.rgb = { r: b.r, g: b.g, b: b.b }, e.alpha = c, e.equivalentGrey = n(d.r * a.r + d.g * a.g + d.b * a.b), e.rgbaMixBlack = i(b, { r: 0, g: 0, b: 0 }, c, 1), e.rgbaMixWhite = i(b, { r: 1, g: 1, b: 1 }, c, 1), e.rgbaMixBlack.luminance = h(e.rgbaMixBlack, !0), e.rgbaMixWhite.luminance = h(e.rgbaMixWhite, !0), o.options.customBG && (e.rgbaMixCustom = i(b, o.options.customBG, c, 1), e.rgbaMixCustom.luminance = h(e.rgbaMixCustom, !0), o.options.customBG.luminance = h(o.options.customBG, !0)), e } function e(a, b) { var c, e, k, q = b || p, r = v, s = o.options, t = l, u = q.RND, w = "", x = "", y = { hsl: "hsv", rgb: a }, z = u.rgb; if ("alpha" !== a) { for (var A in t) if (!t[A][A]) { a !== A && (x = y[A] || "rgb", q[A] = r[x + "2" + A](q[x])), u[A] || (u[A] = {}), c = q[A]; for (w in c) u[A][w] = n(c[w] * t[A][w][1]) } z = u.rgb, q.HEX = r.RGB2HEX(z), q.equivalentGrey = s.grey.r * q.rgb.r + s.grey.g * q.rgb.g + s.grey.b * q.rgb.b, q.webSave = e = f(z, 51), q.webSmart = k = f(z, 17), q.saveColor = z.r === e.r && z.g === e.g && z.b === e.b ? "web save" : z.r === k.r && z.g === k.g && z.b === k.b ? "web smart" : "", q.hueRGB = v.hue2RGB(q.hsv.h), b && (q.background = d(z, q.rgb, q.alpha)) } var B, C, D, E = q.rgb, F = q.alpha, G = "luminance", H = q.background; return B = i(E, { r: 0, g: 0, b: 0 }, F, 1), B[G] = h(B, !0), q.rgbaMixBlack = B, C = i(E, { r: 1, g: 1, b: 1 }, F, 1), C[G] = h(C, !0), q.rgbaMixWhite = C, s.customBG && (D = i(E, H.rgbaMixCustom, F, 1), D[G] = h(D, !0), D.WCAG2Ratio = j(D[G], H.rgbaMixCustom[G]), q.rgbaMixBGMixCustom = D, D.luminanceDelta = m.abs(D[G] - H.rgbaMixCustom[G]), D.hueDelta = g(H.rgbaMixCustom, D, !0)), q.RGBLuminance = h(z), q.HUELuminance = h(q.hueRGB), s.convertCallback && s.convertCallback(q, a), q } function f(a, b) { var c = {}, d = 0, e = b / 2; for (var f in a) d = a[f] % b, c[f] = a[f] + (d > e ? b - d : -d); return c } function g(a, b, c) { return (m.max(a.r - b.r, b.r - a.r) + m.max(a.g - b.g, b.g - a.g) + m.max(a.b - b.b, b.b - a.b)) * (c ? 255 : 1) / 765 } function h(a, b) { for (var c = b ? 1 : 255, d = [a.r / c, a.g / c, a.b / c], e = o.options.luminance, f = d.length; f--;)d[f] = d[f] <= .03928 ? d[f] / 12.92 : m.pow((d[f] + .055) / 1.055, 2.4); return e.r * d[0] + e.g * d[1] + e.b * d[2] } function i(a, c, d, e) { var f = {}, g = d !== b ? d : 1, h = e !== b ? e : 1, i = g + h * (1 - g); for (var j in a) f[j] = (a[j] * g + c[j] * h * (1 - g)) / i; return f.a = i, f } function j(a, b) { var c = 1; return c = a >= b ? (a + .05) / (b + .05) : (b + .05) / (a + .05), n(100 * c) / 100 } function k(a, b, c) { return a > c ? c : b > a ? b : a } var l = { rgb: { r: [0, 255], g: [0, 255], b: [0, 255] }, hsv: { h: [0, 360], s: [0, 100], v: [0, 100] }, hsl: { h: [0, 360], s: [0, 100], l: [0, 100] }, alpha: { alpha: [0, 1] }, HEX: { HEX: [0, 16777215] } }, m = a.Math, n = m.round, o = {}, p = {}, q = { r: .298954, g: .586434, b: .114612 }, r = { r: .2126, g: .7152, b: .0722 }, s = function (a) { this.colors = { RND: {} }, this.options = { color: "rgba(0,0,0,0)", grey: q, luminance: r, valueRanges: l }, t(this, a || {}) }, t = function (a, d) { var e, f = a.options; u(a); for (var g in d) d[g] !== b && (f[g] = d[g]); e = f.customBG, f.customBG = "string" == typeof e ? v.txt2color(e).rgb : e, p = c(a.colors, f.color, b, !0) }, u = function (a) { o !== a && (o = a, p = a.colors) }; s.prototype.setColor = function (a, d, f) { return u(this), a ? c(this.colors, a, d, b, f) : (f !== b && (this.colors.alpha = k(f, 0, 1)), e(d)) }, s.prototype.setCustomBackground = function (a) { return u(this), this.options.customBG = "string" == typeof a ? v.txt2color(a).rgb : a, c(this.colors, b, "rgb") }, s.prototype.saveAsBackground = function () { return u(this), c(this.colors, b, "rgb", !0) }, s.prototype.toString = function (a, b) { return v.color2text((a || "rgb").toLowerCase(), this.colors, b) }; var v = { txt2color: function (a) { var b = {}, c = a.replace(/(?:#|\)|%)/g, "").split("("), d = (c[1] || "").split(/,\s*/), e = c[1] ? c[0].substr(0, 3) : "rgb", f = ""; if (b.type = e, b[e] = {}, c[1]) for (var g = 3; g--;)f = e[g] || e.charAt(g), b[e][f] = +d[g] / l[e][f][1]; else b.rgb = v.HEX2rgb(c[0]); return b.alpha = d[3] ? +d[3] : 1, b }, color2text: function (a, b, c) { var d = c !== !1 && n(100 * b.alpha) / 100, e = "number" == typeof d && c !== !1 && (c || 1 !== d), f = b.RND.rgb, g = b.RND.hsl, h = "hex" === a && e, i = "hex" === a && !h, j = "rgb" === a || h, k = j ? f.r + ", " + f.g + ", " + f.b : i ? "#" + b.HEX : g.h + ", " + g.s + "%, " + g.l + "%"; return i ? k : (h ? "rgb" : a) + (e ? "a" : "") + "(" + k + (e ? ", " + d : "") + ")" }, RGB2HEX: function (a) { return ((a.r < 16 ? "0" : "") + a.r.toString(16) + (a.g < 16 ? "0" : "") + a.g.toString(16) + (a.b < 16 ? "0" : "") + a.b.toString(16)).toUpperCase() }, HEX2rgb: function (a) { return a = a.split(""), { r: +("0x" + a[0] + a[a[3] ? 1 : 0]) / 255, g: +("0x" + a[a[3] ? 2 : 1] + (a[3] || a[1])) / 255, b: +("0x" + (a[4] || a[2]) + (a[5] || a[2])) / 255 } }, hue2RGB: function (a) { var b = 6 * a, c = ~~b % 6, d = 6 === b ? 0 : b - c; return { r: n(255 * [1, 1 - d, 0, 0, d, 1][c]), g: n(255 * [d, 1, 1, 1 - d, 0, 0][c]), b: n(255 * [0, 0, d, 1, 1, 1 - d][c]) } }, rgb2hsv: function (a) { var b, c, d, e = a.r, f = a.g, g = a.b, h = 0; return g > f && (f = g + (g = f, 0), h = -1), c = g, f > e && (e = f + (f = e, 0), h = -2 / 6 - h, c = m.min(f, g)), b = e - c, d = e ? b / e : 0, { h: 1e-15 > d ? p && p.hsl && p.hsl.h || 0 : b ? m.abs(h + (f - g) / (6 * b)) : 0, s: e ? b / e : p && p.hsv && p.hsv.s || 0, v: e } }, hsv2rgb: function (a) { var b = 6 * a.h, c = a.s, d = a.v, e = ~~b, f = b - e, g = d * (1 - c), h = d * (1 - f * c), i = d * (1 - (1 - f) * c), j = e % 6; return { r: [d, h, g, g, i, d][j], g: [i, d, d, h, g, g][j], b: [g, g, i, d, d, h][j] } }, hsv2hsl: function (a) { var b = (2 - a.s) * a.v, c = a.s * a.v; return c = a.s ? 1 > b ? b ? c / b : 0 : c / (2 - b) : 0, { h: a.h, s: a.v || c ? c : p && p.hsl && p.hsl.s || 0, l: b / 2 } }, rgb2hsl: function (a, b) { var c = v.rgb2hsv(a); return v.hsv2hsl(b ? c : p.hsv = c) }, hsl2rgb: function (a) { var b = 6 * a.h, c = a.s, d = a.l, e = .5 > d ? d * (1 + c) : d + c - c * d, f = d + d - e, g = e ? (e - f) / e : 0, h = ~~b, i = b - h, j = e * g * i, k = f + j, l = e - j, m = h % 6; return { r: [e, l, f, f, k, e][m], g: [k, e, e, l, f, f][m], b: [f, f, k, e, e, l][m] } } }; return s }), function (a, b) { "object" == typeof exports ? module.exports = b(a, require("jquery"), require("colors")) : "function" == typeof define && define.amd ? define(["jquery", "colors"], function (c, d) { return b(a, c, d) }) : b(a, a.jQuery, a.Colors) }(this, function (a, b, c, d) { "use strict"; function e(a) { return a.value || a.getAttribute("value") || b(a).css("background-color") || "#FFF" } function f(a) { return a = a.originalEvent && a.originalEvent.touches ? a.originalEvent.touches[0] : a, a.originalEvent ? a.originalEvent : a } function g(a) { return b(a.find(r.doRender)[0] || a[0]) } function h(c) { var d = b(this), f = d.offset(), h = b(a), k = r.gap; c ? (s = g(d), s._colorMode = s.data("colorMode"), p.$trigger = d, (t || i()).css(r.positionCallback.call(p, d) || { left: (t._left = f.left) - ((t._left += t._width - (h.scrollLeft() + h.width())) + k > 0 ? t._left + k : 0), top: (t._top = f.top + d.outerHeight()) - ((t._top += t._height - (h.scrollTop() + h.height())) + k > 0 ? t._top + k : 0) }).show(r.animationSpeed, function () { c !== !0 && (y.toggle(!!r.opacity)._width = y.width(), v._width = v.width(), v._height = v.height(), u._height = u.height(), q.setColor(e(s[0])), n(!0)) }).off(".tcp").on(D, ".cp-xy-slider,.cp-z-slider,.cp-alpha", j)) : p.$trigger && b(t).hide(r.animationSpeed, function () { n(!1), p.$trigger = null }).off(".tcp") } function i() { return b("head")[r.cssPrepend ? "prepend" : "append"]('<style type="text/css" id="tinyColorPickerStyles">' + (r.css || I) + (r.cssAddon || "") + "</style>"), b(H).css({ margin: r.margin }).appendTo("body").show(0, function () { p.$UI = t = b(this), F = r.GPU && t.css("perspective") !== d, u = b(".cp-z-slider", this), v = b(".cp-xy-slider", this), w = b(".cp-xy-cursor", this), x = b(".cp-z-cursor", this), y = b(".cp-alpha", this), z = b(".cp-alpha-cursor", this), r.buildCallback.call(p, t), t.prepend("<div>").children().eq(0).css("width", t.children().eq(0).width()), t._width = this.offsetWidth, t._height = this.offsetHeight }).hide() } function j(a) { var c = this.className.replace(/cp-(.*?)(?:\s*|$)/, "$1").replace("-", "_"); (a.button || a.which) > 1 || (a.preventDefault && a.preventDefault(), a.returnValue = !1, s._offset = b(this).offset(), (c = "xy_slider" === c ? k : "z_slider" === c ? l : m)(a), n(), A.on(E, function () { A.off(".tcp") }).on(C, function (a) { c(a), n() })) } function k(a) { var b = f(a), c = b.pageX - s._offset.left, d = b.pageY - s._offset.top; q.setColor({ s: c / v._width * 100, v: 100 - d / v._height * 100 }, "hsv") } function l(a) { var b = f(a).pageY - s._offset.top; q.setColor({ h: 360 - b / u._height * 360 }, "hsv") } function m(a) { var b = f(a).pageX - s._offset.left, c = b / y._width; q.setColor({}, "rgb", c) } function n(a) { var b = q.colors, c = b.hueRGB, e = (b.RND.rgb, b.RND.hsl, r.dark), f = r.light, g = q.toString(s._colorMode, r.forceAlpha), h = b.HUELuminance > .22 ? e : f, i = b.rgbaMixBlack.luminance > .22 ? e : f, j = (1 - b.hsv.h) * u._height, k = b.hsv.s * v._width, l = (1 - b.hsv.v) * v._height, m = b.alpha * y._width, n = F ? "translate3d" : "", p = s[0].value, t = s[0].hasAttribute("value") && "" === p && a !== d; v._css = { backgroundColor: "rgb(" + c.r + "," + c.g + "," + c.b + ")" }, w._css = { transform: n + "(" + k + "px, " + l + "px, 0)", left: F ? "" : k, top: F ? "" : l, borderColor: b.RGBLuminance > .22 ? e : f }, x._css = { transform: n + "(0, " + j + "px, 0)", top: F ? "" : j, borderColor: "transparent " + h }, y._css = { backgroundColor: "#" + b.HEX }, z._css = { transform: n + "(" + m + "px, 0, 0)", left: F ? "" : m, borderColor: i + " transparent" }, s._css = { backgroundColor: t ? "" : g, color: t ? "" : b.rgbaMixBGMixCustom.luminance > .22 ? e : f }, s.text = t ? "" : p !== g ? g : "", a !== d ? o(a) : G(o) } function o(a) { v.css(v._css), w.css(w._css), x.css(x._css), y.css(y._css), z.css(z._css), r.doRender && s.css(s._css), s.text && s.val(s.text), r.renderCallback.call(p, s, "boolean" == typeof a ? a : d) } var p, q, r, s, t, u, v, w, x, y, z, A = b(document), B = b(), C = "touchmove.tcp mousemove.tcp pointermove.tcp", D = "touchstart.tcp mousedown.tcp pointerdown.tcp", E = "touchend.tcp mouseup.tcp pointerup.tcp", F = !1, G = a.requestAnimationFrame || a.webkitRequestAnimationFrame || function (a) { a() }, H = '<div class="cp-color-picker"><div class="cp-z-slider"><div class="cp-z-cursor"></div></div><div class="cp-xy-slider"><div class="cp-white"></div><div class="cp-xy-cursor"></div></div><div class="cp-alpha"><div class="cp-alpha-cursor"></div></div></div>', I = ".cp-color-picker{position:absolute;overflow:hidden;padding:6px 6px 0;background-color:#444;color:#bbb;font-family:Arial,Helvetica,sans-serif;font-size:12px;font-weight:400;cursor:default;border-radius:5px}.cp-color-picker>div{position:relative;overflow:hidden}.cp-xy-slider{float:left;height:128px;width:128px;margin-bottom:6px;background:linear-gradient(to right,#FFF,rgba(255,255,255,0))}.cp-white{height:100%;width:100%;background:linear-gradient(rgba(0,0,0,0),#000)}.cp-xy-cursor{position:absolute;top:0;width:10px;height:10px;margin:-5px;border:1px solid #fff;border-radius:100%;box-sizing:border-box}.cp-z-slider{float:right;margin-left:6px;height:128px;width:20px;background:linear-gradient(red 0,#f0f 17%,#00f 33%,#0ff 50%,#0f0 67%,#ff0 83%,red 100%)}.cp-z-cursor{position:absolute;margin-top:-4px;width:100%;border:4px solid #fff;border-color:transparent #fff;box-sizing:border-box}.cp-alpha{clear:both;width:100%;height:16px;margin:6px 0;background:linear-gradient(to right,#444,rgba(0,0,0,0))}.cp-alpha-cursor{position:absolute;margin-left:-4px;height:100%;border:4px solid #fff;border-color:#fff transparent;box-sizing:border-box}", J = function (a) { q = this.color = new c(a), r = q.options, p = this }; J.prototype = { render: n, toggle: h }, b.fn.colorPicker = function (c) { var d = this, f = function () { }; return c = b.extend({ animationSpeed: 150, GPU: !0, doRender: !0, customBG: "#FFF", opacity: !0, renderCallback: f, buildCallback: f, positionCallback: f, body: document.body, scrollResize: !0, gap: 4, dark: "#222", light: "#DDD" }, c), !p && c.scrollResize && b(a).on("resize.tcp scroll.tcp", function () { p.$trigger && p.toggle.call(p.$trigger[0], !0) }), B = B.add(this), this.colorPicker = p || new J(c), this.options = c, b(c.body).off(".tcp").on(D, function (a) { -1 === B.add(t).add(b(t).find(a.target)).index(a.target) && h() }), this.on("focusin.tcp click.tcp", function (a) { p.color.options = b.extend(p.color.options, r = d.options), h.call(this, a) }).on("change.tcp", function () { q.setColor(this.value || "#FFF"), d.colorPicker.render(!0) }).each(function () { var a = e(this), d = a.split("("), f = g(b(this)); f.data("colorMode", d[1] ? d[0].substr(0, 3) : "HEX").attr("readonly", r.preventFocus), c.doRender && f.css({ "background-color": a, color: function () { return q.setColor(a).rgbaMixBGMixCustom.luminance > .22 ? c.dark : c.light } }) }) }, b.fn.colorPicker.destroy = function () { b("*").off(".tcp"), p.toggle(!1), B = b() } });