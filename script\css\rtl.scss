
/*
* 
* ==========================================================
* RTL.SCSS
* ==========================================================
*
* Front-end RTL CSS. This file is imported only.
*
*/

.mc-rtl {
    direction: rtl;
    text-align: right;

    textarea, .mc-panel, ul, ul li {
        direction: rtl;
        text-align: right;
    }

    .mc-input.mc-input-btn > div {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
        margin-left: 0;
        margin-right: -3px;
    }

    .mc-input-select-input {
        > div {
            left: auto;
            right: 6px;
            padding-left: 0;
            padding-right: 5px;
        }
    }

    .mc-list .mc-input-select-input input {
        padding: 5px 60px 0 0;
    }

    .mc-btn-text i {
        margin: 0 0 0 15px;
    }

    .mc-search-btn {
        > i,
        > input {
            right: auto;
            left: 0;
        }

        > input {
            padding: 0 15px 0 50px !important;
        }

        &.mc-active > i {
            right: auto;
            left: 15px;
        }
    }

    div ul.mc-menu li,
    .mc-select ul li {
        padding: 6px 12px 6px 25px;
    }

    .mc-select p {
        padding: 0 0 0 20px;

        &:after {
            right: auto;
            left: 0;
        }
    }

    > div {
        .mc-header-agent {
            .mc-profile {
                text-align: right;

                img {
                    margin-right: 0;
                    margin-left: 15px;
                }

                .mc-status {
                    padding-right: 15px;
                    padding-left: 0;

                    &:before {
                        right: 0;
                        left: auto;
                    }
                }

                .mc-status-typing {
                    float: right;
                    padding-right: 0;

                    &:after {
                        right: calc(100% + 5px);
                        left: auto;
                    }
                }
            }

            &:hover .mc-profile {
                margin-right: 60px;
                margin-left: 0;
            }
        }

        .mc-header {
            .mc-dashboard-btn {
                right: -60px !important;
                left: auto;
            }

            &:hover .mc-dashboard-btn {
                right: 20px !important;
                left: auto;
            }
        }

        .mc-scroll-area .mc-header.mc-header-panel {
            text-align: right;
            padding: 5px 30px 5px 60px;

            .mc-dashboard-btn {
                left: 10px;
                right: auto !important;
            }
        }

        .mc-list {
            text-align: right;

            > .mc-right {
                float: left;
                margin: 10px 10px 30px 20px;

                &.mc-thumb-active {
                    margin: 10px 10px 30px 55px;
                }

                .mc-thumb {
                    left: -35px;
                    right: auto;
                }

                .mc-time {
                    left: 0;
                    right: auto;

                    i {
                        padding-left: 0;
                        padding-right: 10px;
                    }
                }
            }

            > div:not(.mc-right) {
                float: right;
                margin: 10px 20px 30px 10px;

                &.mc-thumb-active {
                    margin: 10px 55px 30px 10px;
                }

                .mc-thumb {
                    right: -35px;
                    left: auto;
                }
            }

            > div:first-child {
                margin-top: 20px;
            }

            .mc-time {
                right: 0;
                left: auto;
                flex-direction: row-reverse;

                i {
                    display: none;
                }
            }

            [data-id="sending"] .mc-time {
                padding-left: 20px;

                > i {
                    transform: translateX(-25px);
                }
            }
        }

        .mc-popup.mc-emoji {
            margin-left: -30px;

            &:after {
                left: 33px;
                right: auto;
            }
        }
    }

    .mc-editor {
        .mc-textarea {
            padding: 15px 15px 15px 120px;
        }

        &.mc-disabled-1 .mc-textarea {
            padding-left: 80px;
            padding-right: 15px;
        }

        &.mc-disabled-2 .mc-textarea {
            padding-left: 50px;
            padding-right: 15px;
        }

        &.mc-active {

            &.mc-disabled-2 .mc-textarea {
                padding-right: 15px;
            }

            .mc-textarea {
                padding: 15px 15px 15px 80px
            }
        }

        .mc-submit {
            padding-left: 0;
            padding-right: 13px;
        }

        .mc-loader {
            right: auto;
            left: 15px;
        }

        .mc-bar {
            padding: 15px 0 15px 15px;
            right: auto;
            left: 0;
        }

        .mc-bar-icons > div {

            &:before {
                left: 0px;
                right: 7px;
            }

            &:last-child {
                margin-right: 7px;
                margin-left: 0;
            }
        }

        .mc-attachments > div {
            margin: 5px 0 5px 5px;
            padding-right: 0;
            padding-left: 15px;

            i {
                left: 0px;
                right: 5px;
            }
        }
    }

    .mc-user-conversations > li > div {
        padding-left: 0;
        padding-right: 55px;
        text-align: right;

        img {
            left: auto;
            right: 0;
        }

        div:not(.mc-message) > span:first-child {
            margin: 0 0 0 15px;
        }
    }

    .mc-popup-message {
        .mc-icon-close {
            right: auto;
            left: 15px;
        }
    }

    .mc-articles {
        text-align: right;

        > div > div {
            text-align: right;
        }
    }

    .mc-rich-message {
        text-align: right;

        .mc-input > span {
            left: auto;
            right: 0;
            text-align: right;

            &.mc-active {
                right: 5px;
            }
        }

        .mc-rating .mc-submit + div {
            margin: 0 30px 0 0;
        }

        .mc-text-list-single > div {
            padding-left: 0;
            padding-right: 15px;

            &:before {
                left: auto;
                right: 0;
            }
        }

        .mc-slider {
            > div {
                direction: ltr;
            }

            .mc-card-img + div + .mc-card-extra {
                left: auto;
                right: 15px;
            }
        }



        &.mc-rich-select p:after {
            left: 8px;
        }
    }

    .mc-timetable > span {
        padding: 0 20px 0 0;

        i {
            left: auto;
            right: 0;
        }
    }

    .mc-list table th, .mc-list table td, .mc-init-form .mc-text, .mc-privacy .mc-title, .mc-privacy .mc-buttons {
        text-align: right;
    }

    .mc-lightbox-overlay i {
        left: 10px;
        right: auto;
    }

    .mc-rating > div {
        padding: 0 10px 0 0;
    }

    &.mc-chat {

        .mc-responsive-close-btn {
            left: 0;
            right: auto;
            text-align: left;

            &:before {
                left: 10px;
                right: auto;
            }
        }
    }

    .mc-article-category-links > span {
        margin-right: 0;
        margin-left: 20px;

        & + span:before {
            left: auto;
            right: -10px;
        }
    }

    .mc-label-date-top {
        left: 5px;
        right: 0;
    }

    .mc-select-phone img {
        margin: 0 0 0 10px;
    }

    .mc-select-search input {
        margin: 0 !important;
        padding: 0 10px !important;
    }

    .mc-input-select-input > div.mc-select-phone + input {
        padding: 5px 100px 0 0;
    }

    .mc-close-chat {
        right: auto;
        left: 20px;
    }

    #mc-audio-clip {
        right: 0;
        left: 40px;

        .mc-btn-clip-player.mc-active {
            margin-right: 0;
            margin-left: -15px;
        }
    }

    .mc-departments-list > div span, .mc-agents-list > div span, .mc-channels-list > div span {
        padding: 0 15px 0 0;
    }
}
