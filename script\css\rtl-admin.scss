
/*
* 
* ==========================================================
* RTL-ADMIN.SCSS
* ==========================================================
*
* Back-end RTL CSS. This file is imported only.
*
*/

.mc-rtl {
    direction: rtl;
    text-align: right;

    textarea, .mc-panel, ul, ul li, .mc-table th, .mc-table td {
        direction: rtl;
        text-align: right;
    }

    &.mc-admin {
        padding-left: 0;
        padding-right: 65px;

        > main > div {
            > .mc-tab > .mc-nav > ul {
                padding-left: 0;
            }

            > .mc-top-bar > div:last-child {
                .mc-btn-icon + .mc-btn, .mc-btn + .mc-btn-icon, .mc-btn + .mc-btn {
                    margin-left: 0;
                    margin-right: 15px;
                }
            }
        }
    }

    .codex-editor:after {
        left: auto;
        right: 0;
    }

    .mc-btn:not(.mc-hide) + a, a.mc-btn:not(.mc-hide) + a {
        margin-right: 15px;
        margin-left: 0;
    }

    .mc-board {
        .mc-user-details {
            .mc-user-details-close {
                right: auto;
                left: 15px;
            }

            .mc-profile {
                margin-left: 0;
                margin-right: -10px;
            }
        }

        > .mc-admin-list .mc-scroll-area li {
            .mc-profile, p {
                padding-left: 0;
                padding-right: 58px;
            }
        }
    }

    .mc-area-articles {
        > .mc-tab > .mc-content .mc-article-content {
            padding-left: 0;
            padding-right: 40px;
        }
    }

    .mc-flex .mc-btn + .mc-btn, .mc-flex .mc-btn + .mc-btn-icon {
        margin-left: 0;
        margin-right: 15px;
    }

    .mc-area-chatbot .mc-repeater {
        margin-right: 0;
        margin-left: 30px;

        .mc-repeater {
            margin-left: 0;

            .mc-sub-repeater-close {
                left: 10px;
            }
        }
    }

    [data-id="open-ai-faq-set-data"] .mc-setting + .mc-setting {
        margin-left: 0;
        margin-right: 10px;
    }

    #mc-table-chatbot-files td:last-child i, #mc-table-chatbot-website td:last-child i {
        right: auto;
        left: 5px;
    }

    .mc-playground-info {
        border-right: 1px solid #d4d4d4;
        border-left: none;
    }

    .mc-playground {
        .mc-scroll-area > div {
            margin-left: 15px;
            margin-right: 0;
        }

        .mc-scroll-area > div > div:first-child div {
            right: auto;
            left: 0;
        }
    }

    .mc-menu-wide ul li {
        margin: 0 0 0 30px;
    }

    .mc-table-users th.mc-active:after {
        left: 15px;
        right: auto;
    }

    > .mc-header {
        border-right: none;
        border-left: 1px solid #d4d4d4;
    }

    > .mc-header, > .mc-header > .mc-admin-nav-right {
        left: auto;
        right: 0;
    }

    > .mc-header > .mc-admin-nav-right .mc-account:hover > div {
        left: auto;
        right: 65px;

        &:before {
            left: auto;
            right: -13px;
            bottom: 25px;
            transform: rotate( -90deg);
        }
    }

    .mc-input.mc-input-btn > div {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
        border-top-left-radius: 3px;
        border-bottom-left-radius: 3px;
        margin-left: 0;
        margin-right: -3px;

        &:before {
            transform: rotate(180deg);
            display: inline-block;
        }
    }

    .mc-btn-text i {
        margin: 0 0 0 15px;
    }

    .mc-list-thumbs li > div.mc-image {
        margin-left: 15px;
        margin-right: 0;
    }

    .mc-search-btn, .mc-filter-btn {
        > i {
            right: auto;
            left: 0;
        }
    }

    .mc-search-btn {
        > input {
            right: auto;
            left: 0;
        }

        > input {
            padding: 0 15px 0 50px !important;
        }

        &.mc-active > i {
            right: auto;
            left: 15px;
        }
    }

    .mc-filter-btn {
        &.mc-active > i {
            left: 15px;
        }

        > div .mc-select {
            &:first-child {
                margin-right: 0;
            }

            &:last-child {
                margin-left: 0;
                margin-right: 15px;
            }
        }
    }

    div ul.mc-menu li, .mc-select ul li {
        padding: 6px 12px 6px 25px;
    }

    .mc-select {
        p {
            padding: 0 0 0 20px;
            direction: ltr;

            &:after {
                right: auto !important;
                left: 0;
            }
        }

        &.mc-select-colors > p {
            padding: 0 15px 0 40px;
        }

        & + .mc-select {
            margin-left: 0;
            margin-right: 15px;
        }
    }

    .mc-select-checkbox > div > label {
        margin: 0 15px 0 0 !important;
    }

    > div {
        .mc-list {
            text-align: right;

            > .mc-right {
                float: left;
                margin: 10px 10px 30px 20px;

                &.mc-thumb-active {
                    margin: 10px 10px 30px 65px;
                }

                .mc-thumb {
                    left: -45px;
                    right: auto;
                }

                .mc-time {
                    left: 0;
                    right: auto;

                    &:hover > span + span {
                        padding-right: 0;
                        padding-left: 10px;
                    }
                }
            }

            > div:not(.mc-right) {
                float: right;
                margin: 10px 20px 30px 10px;

                &.mc-thumb-active {
                    margin: 10px 65px 30px 10px;
                }

                .mc-thumb {
                    right: -45px;
                    left: auto;
                }
            }

            > div:first-child {
                margin-top: 20px;
            }

            .mc-time {
                left: auto;
                right: 0;

                &:hover > span + span {
                    padding-right: 10px;
                    padding-left: 0;
                }
            }

            [data-id="sending"] .mc-time {
                padding-left: 20px;

                > i {
                    transform: translateX(-25px);
                }
            }
        }

        .mc-popup.mc-emoji {
            margin-left: -30px;

            &:after {
                left: 33px;
                right: auto;
            }
        }
    }

    .mc-editor {
        .mc-textarea {
            padding: 15px 15px 15px 85px;
        }

        .mc-submit {
            padding-left: 0;
            padding-right: 13px;
        }

        .mc-loader {
            right: auto;
            left: 15px;
        }

        .mc-bar {
            right: auto;
            left: 0;
        }

        .mc-bar-icons > div {
            margin-right: 0;
            margin-left: 7px;

            &:before {
                left: 0px;
                right: 7px;
            }
        }

        .mc-attachments > div {
            margin: 5px 0 5px 5px;
            padding-right: 0;
            padding-left: 15px;

            i {
                left: 0px;
                right: 5px;
            }
        }
    }

    .mc-user-conversations > li > div {
        padding-left: 0 !important;
        padding-right: 55px;
        text-align: right;

        img {
            left: auto;
            right: 0;
        }

        div:not(.mc-message) > span:first-child {
            margin: 0 0 0 15px;
        }
    }

    .mc-popup-message {
        .mc-icon-close {
            right: auto;
            left: 15px;
        }
    }

    .mc-articles {
        text-align: right;

        > div > div {
            text-align: right;
        }
    }

    .mc-rich-message {
        text-align: right;

        .mc-input > span {
            left: auto;
            right: 0;
            text-align: right;

            &.mc-active {
                right: 5px;
            }
        }

        .mc-rating .mc-submit + div {
            margin: 0 30px 0 0;
        }

        .mc-text-list-single > div {
            padding-left: 0;
            padding-right: 15px;

            &:before {
                left: auto;
                right: 0;
            }
        }

        .mc-slider {
            > div {
                direction: ltr;
            }

            .mc-card-img + div + .mc-card-extra {
                left: auto;
                right: 15px;
            }
        }

        &.mc-rich-select p:after {
            left: 8px;
        }
    }

    .mc-timetable > span {
        padding: 0 20px 0 0;

        i {
            left: auto;
            right: 0;
        }
    }

    .mc-list {
        table th, table td {
            text-align: right;
        }

        .mc-time {
            flex-direction: row-reverse;
        }

        [data-id="sending"].mc-right .mc-time, [data-id="sending"] .mc-time {
            right: 0;

            > i:after {
                right: calc(100% + 5px);
                left: auto;
            }
        }
    }

    .mc-lightbox .mc-top-bar {
        a + a {
            margin-left: 0;
            margin-right: 15px;
        }

        > div:first-child {
            margin-right: 0;
            margin-left: 15px;
        }
    }

    .mc-lightbox-overlay i {
        left: 10px;
        right: auto;
    }

    .mc-rating > div {
        padding: 0 10px 0 0;
    }

    .mc-profile {
        padding-left: 0;
        padding-right: 45px;

        img {
            left: auto;
            right: 0;
        }
    }

    .mc-admin-list {
        border-right: none;
        border-left: 1px solid #d4d4d4;

        .mc-scroll-area li {
            border-left: none;
            border-right: 2px solid rgba(255,255,255,0);

            .mc-profile {
                .mc-name {
                    padding-right: 0;
                    padding-left: 10px;
                }

                .mc-time {
                    margin-right: auto;
                    margin-left: 0;
                }
            }

            > span {
                right: auto;
                left: 10px;
            }

            &:before {
                left: auto;
                right: 0;
            }
        }
    }

    .mc-tags-area {
        padding-right: 0;
        padding-left: 10px;
    }

    .mc-menu-mobile {
        left: 15px;
        right: auto !important;
    }

    .mc-conversation > .mc-top > a {
        padding: 0 0 0 15px;
    }

    .mc-user-details {
        border-left: none;
        border-right: 1px solid #d4d4d4;

        .mc-inline {
            &.mc-inline-departments, &.mc-inline-agents {
                padding: 0 15px 0 0;
            }

            h3 {
                margin-right: 0;
                margin-left: 15px;
            }
        }

        .mc-select p:after {
            right: auto !important;
            left: 15px;
        }
    }

    .mc-panel-details {
        > i {
            right: auto;
            left: 15px;
        }

        .mc-title {
            padding-left: 10px;
            padding-right: 0;
        }

        > .mc-title {
            padding: 15px 15px 0 15px;
        }

        > div > .mc-title > i {
            right: auto;
            left: 12px;
        }

        .mc-split {
            flex-direction: row-reverse;

            > div + div {
                margin-left: 15px;
            }
        }

        .mc-list-links > p {
            padding: 0 15px 0 0;
        }

        &.mc-collapse > .mc-collapse-btn {
            margin-left: 0;
            margin-right: 15px;
        }

        .mc-woocommerce-cart > a {
            padding-right: 15px;
            padding-left: 35px;
        }
    }

    .mc-admin-nav-right [data-value="status"], td.mc-online, td.mc-offline {
        &:before {
            right: auto;
            left: 3px;
        }

        &:after {
            right: auto;
            left: 0;
        }
    }

    .mc-panel-notes > div > div > span:first-child i, .mc-list-links > a i {
        right: auto;
        left: 0;
        text-align: right;
    }

    .mc-profile-list {

        > ul > li {
            padding-left: 0;
            padding-right: 30px;
            display: flex;
            flex-direction: row;
            line-height: 28px;

            .mc-icon, > img {
                left: auto;
                right: 0;
            }
        }

        > ul > li > span {
            padding-left: 10px;
            padding-right: 0;
        }
    }

    .mc-header {
        > .mc-admin-nav-right .mc-account {
            .mc-menu [data-value="status"] {

                &:before {
                    left: 0;
                    right: auto;
                }

                &:after {
                    left: -3px;
                    right: auto;
                }
            }

            .mc-profile img {
                left: auto;
                right: 20px;
            }

            .mc-profile {
                padding: 20px 65px 20px 20px;
            }
        }
    }

    .mc-panel-aecommerce .mc-list-items > a > span:first-child {
        margin-right: 0;
        margin-left: 5px;
    }

    .mc-panel-aecommerce .mc-aecommerce-orders > a > span:last-child {
        margin-right: 15px;
        margin-left: 0;
    }

    .mc-profile-box {
        .mc-top-bar .mc-profile span {
            margin-right: 20px;
            margin-left: 0;
        }

        .mc-profile-list {
            padding-right: 0;
            padding-left: 30px;
        }
    }

    .mc-profile-edit-box {
        .mc-main > div + div {
            margin-left: 0;
            margin-right: 30px;
        }

        .mc-top-bar .mc-profile {
            padding-right: 65px;
            padding-left: 15px;
        }
    }

    > main > div > .mc-top-bar {
        > div h2 {
            margin-right: 0;
            margin-left: 60px;
        }

        > div:last-child .mc-search-btn {
            margin-right: 0;
            margin-left: 30px;
        }

        > div:first-child {
            padding-right: 0;
            padding-left: 30px;
        }
    }

    .mc-area-users {
        .mc-scroll-area {
            margin: 15px 15px 0 0;
            padding-right: 0;
            padding-left: 15px;
        }

        .mc-filter-btn.mc-active i {
            right: auto;
            left: 0;
        }
    }

    > .mc-header > .mc-admin-nav > div {
        > a > span, > .mc-header > .mc-admin-nav-right .mc-account > div {
            left: auto;
            right: 75px;
        }

        > a:hover span {
            left: auto;
            right: 65px;
        }

        > a > span:before, > .mc-header > .mc-admin-nav-right .mc-account > div:before {
            left: auto;
            right: -13px;
            transform: rotate( -90deg );
        }

        > a > span:after, > .mc-header > .mc-admin-nav-right .mc-account > div:after {
            left: auto;
            right: 0;
        }
    }

    .mc-setting {

        .mc-repeater-add + .mc-btn-icon, .mc-repeater-add + .mc-btn-icon {
            transform: translate(-11px, 13px);
        }

        .mc-language-switcher-cnt > label {
            margin: 0 15px 0 0;
        }

        > .mc-setting-content, .mc-setting > .mc-setting-content {
            padding-right: 0;
            padding-left: 60px;
        }

        label, .mc-setting label {
            margin: 0 0 15px 30px;
        }

        input[type="number"], .mc-setting input[type="number"] {
            padding-left: 0;
            padding-right: 10px;
        }

        p .mc-icon-help, .mc-setting p .mc-icon-help {
            margin: 0 5px 0 0;
        }

        .repeater-item > i, .mc-setting .repeater-item > i {
            right: auto;
            left: -30px;
        }

        .repeater-item > div label {
            margin-right: 0;
            margin-left: 15px;
        }

        [data-type="upload-file"] .mc-btn {
            margin-left: 0;
            margin-right: 5px;
        }

        .mc-icon-help {
            margin: 0 5px 0 0;
        }

        .repeater-item .mc-enlarger:before {
            right: 0;
        }

        &.mc-type-input-button .input a, .mc-setting.mc-type-input-button .input a {
            margin-left: 0;
            margin-right: 15px;
        }

        &.mc-type-color .input:after, .mc-setting.mc-type-color .input:after {
            right: auto;
            left: 1px;
        }

        &.mc-type-color .input i, .mc-setting.mc-type-color .input i {
            left: auto;
            right: 12px;
        }
    }

    .mc-type-multi-input > .input > div:not(.multi-input-textarea) > label {
        margin: 0 0 0 15px;
    }

    .mc-timetable > div > div > div:after {
        right: auto;
        left: 8px;
    }

    .mc-timetable > div > div > div {
        padding: 0 7px 0 0;
    }

    .mc-language-switcher > i {
        margin-left: 0;
        margin-right: 10px;
    }

    .mc-languages-box .mc-main > div > img {
        margin-right: 0;
        margin-left: 15px;
    }

    #departments .repeater-item > div + div {
        padding-left: 0;
        padding-right: 15px;
    }

    .mc-apps > div {
        padding: 30px 130px 30px 30px;

        img {
            left: auto;
            right: 30px;
        }

        i {
            right: auto;
            left: 30px;
        }
    }

    .mc-tab {

        > .mc-nav {
            border-right: none;
            border-left: 1px solid #d4d4d4;
        }
    }

    .mc-area-settings {

        > .mc-tab > .mc-content {
            padding-right: 30px;
            padding-left: 15px;
        }
    }

    .mc-inner-tab .mc-nav > ul li {
        padding-right: 0;
        padding-left: 25px;
        margin-left: 0;

        i {
            right: auto;
            left: -5px;
        }

        span {
            right: auto;
            left: 32px;
        }
    }

    .mc-translations .mc-nav li {
        padding-left: 0;
        padding-right: 30px;

        img {
            left: auto;
            right: 0;
        }
    }

    .mc-area-reports {
        > .mc-tab {
            > .mc-content {
                padding-right: 0;
                padding-left: 15px;

                > .mc-reports-sidebar {
                    padding-left: 0;
                    padding-right: 15px;
                }

                > .mc-reports-chart {
                    padding-right: 0;
                    padding-left: 20px;
                }
            }
        }

        .mc-report-export {
            margin-left: 0;
            margin-right: 5px;
        }

        td:first-child > div {
            margin: 0 0 0 15px;
        }
    }

    .mc-report-agents-ratings td .mc-icon-check, .mc-report-agents-ratings td .mc-icon-like, .mc-report-articles-ratings td .mc-icon-check, .mc-report-articles-ratings td .mc-icon-like, .mc-area-reports td img {
        margin: 0 0 0 10px;
    }

    .mc-popup.mc-replies .mc-replies-list ul li {
        margin-right: 0;
        margin-left: 15px;

        div:first-child {
            margin-right: 0;
            margin-left: 15px;
            padding-left: 0;
            padding-right: 15px;
        }

        div:first-child:before {
            left: auto;
            right: 0;
        }
    }

    .mc-area-conversations {
        > .mc-btn-collapse.mc-left {
            transform: none;
            left: auto;
            right: 67px;
        }

        > .mc-btn-collapse.mc-left.mc-active {
            transform: rotate(180deg);
        }

        > .mc-btn-collapse.mc-right {
            left: 2px;
            right: auto;
            transform: none;
        }

        > .mc-btn-collapse.mc-right.mc-active {
            transform: rotate(180deg);
        }
    }

    .mc-user-details .mc-profile {
        margin-left: 0;
        margin-right: 10px;
    }

    .mc-search-dropdown {
        .mc-search-btn {
            margin-left: 30px !important;
            margin-right: auto !important;

            &.mc-active i {
                top: 12px;
            }
        }

        .mc-search-dropdown-items {
            left: 30px;
            right: -1px
        }
    }

    #tags {
        .repeater-item div + div {
            right: auto;
            left: 0;
        }

        [data-id="tag-name"] {
            margin-left: 50px;
            margin-right: auto;
        }
    }

    .codex-editor.codex-editor--rtl .ce-toolbar__actions {
        right: -60px;
        left: auto;
    }

    .ce-toolbar__actions {
        padding-right: 0;
        padding-left: 15px;
    }

    .ce-popover__item-icon {
        margin-left: 10px;
        margin-right: 0;
    }
}

.rtl.daterangepicker {
    direction: rtl;
    text-align: right;
    right: auto !important;
    left: 20px !important;
}

@media (min-width: 465px) and (max-width: 912px) {
    .mc-board {
        .mc-conversation > .mc-top {
            padding-left: 20px;
            padding-right: 45px;
        }

        > .mc-admin-list {
            left: auto;
            right: 65px;
        }

        > .mc-user-details {
            right: auto;
            left: 0;
        }

        > .mc-admin-list .mc-top {
            padding-left: 20px;
            padding-right: 45px;
        }
    }
}

@media (max-width: 464px) {
    .mc-rtl {

        .mc-menu-wide > ul li, .mc-nav > ul li {
            padding: 10px 15px 10px 25px;
        }

        .mc-menu-mobile {
            right: auto !important;
            left: 0 !important;
        }

        .mc-menu-mobile > div, .mc-menu-mobile > ul {
            right: auto;
            left: 10px;
        }

        .mc-select p, .mc-board > .mc-admin-list .mc-scroll-area li p {
            padding: 0 0 0 20px;
        }

        .mc-area-settings > .mc-tab > .mc-nav > ul {
            padding-right: 0 !important;
            margin: 0;
        }

        .mc-area-settings > .mc-tab > .mc-nav, .mc-area-reports > .mc-tab > .mc-nav {
            left: auto;
            right: 15px;
            border: none;
        }

        .mc-playground .mc-scroll-area > div {
            margin-left: 0;
        }

        #mc-chatbot-qea .mc-repeater-add {
            margin-right: 0;
            margin-left: 30px;
        }

        .mc-search-btn.mc-active > i {
            left: 5px;
        }

        .mc-board {
            > .mc-admin-list .mc-scroll-area li {
                > div, p {
                    padding-left: 0;
                    padding-right: 65px;
                }

                .mc-notification-counter {
                    right: auto;
                    left: 14px;

                    & + div + p {
                        margin-right: 0;
                        margin-left: 30px;
                    }
                }
            }

            .mc-user-details .mc-profile {
                margin-right: 10px;
            }

            .mc-conversation .mc-top .mc-btn-back:before {
                content: "\75";
            }

            > .mc-admin-list > .mc-top {
                .mc-search-btn:not(.mc-active) {
                    margin-right: 0;
                    margin-left: 5px;
                }

                > .mc-select p {
                    padding: 0 15px 0 20px;
                }

                .mc-filter-btn .mc-select:first-child {
                    margin-right: 30px;
                }
            }
        }

        .mc-lightbox {
            .mc-top-bar a + a {
                margin-left: 2px;
                margin-right: 0;
            }

            .mc-main > .mc-bottom .mc-btn, .mc-main > .mc-bottom .mc-btn-text {
                text-align: right;
            }

            .mc-top-bar .mc-close, .mc-admin > main > div > .mc-top-bar .mc-close {
                margin-right: auto;
                margin-left: 2px;
            }
        }

        .mc-lightbox .mc-top-bar .mc-profile, > main > div > .mc-top-bar .mc-profile {
            padding: 0 45px 0 0 !important;
        }

        .mc-popup .mc-header .mc-search-btn:not(.mc-active) > i {
            right: auto;
            left: -10px;
        }

        .mc-popup.mc-replies:after, .mc-popup.mc-woocommerce-products:after {
            left: auto;
            right: 56px;
        }

        .mc-dialogflow-intent-box .mc-intent-add i {
            margin-left: 0;
            margin-right: 15px;
        }

        .mc-top-bar {
            .mc-menu-mobile {
                left: 5px !important;
                right: auto !important;
            }

            .mc-btn.mc-icon {
                margin-right: 0;
                margin-left: 5px !important;
            }
        }

        .mc-area-users {

            .mc-top-bar > div:last-child {
                padding-right: 0;
                padding-left: 45px;
            }

            .mc-table-users td:first-child {
                padding-right: 15px;
                padding-left: 0;
            }
        }

        .mc-profile-box .mc-top-bar .mc-profile span {
            margin-right: 0;
        }

        .mc-profile-edit-box .mc-top-bar .mc-profile {
            padding-right: 0;
            padding-left: 15px;
        }

        .mc-profile-list > ul > li {
            line-height: 35px;
        }

        .mc-menu-wide > div:after, .mc-table-users th:first-child:after, .mc-nav > div:after {
            right: auto;
            left: 0;
        }

        .mc-menu-wide > div:not(.mc-btn), .mc-nav > div:not(.mc-btn) {
            padding-right: 0;
            padding-left: 20px;
        }

        .mc-direct-message-box .mc-bottom .mc-btn-text {
            margin-left: 0 !important;
            margin-right: 15px !important;
        }

        .mc-type-multi-input > .input > div:not(.multi-input-textarea) > label {
            margin: 0 0 10px 15px;
        }

        .mc-inner-tab .mc-nav > ul, .mc-inner-tab .mc-nav > ul li {
            margin-left: 0;
            padding-left: 0;
        }

        .mc-automations-area > .mc-select > p {
            padding-right: 0;
        }

        .mc-area-reports {
            #mc-date-picker {
                margin-right: 0;
                margin-left: 5px;
            }

            > .mc-tab > .mc-content {
                padding: 0;

                > .mc-reports-chart {
                    padding: 15px;
                }

                > .mc-reports-sidebar {
                    padding-left: 15px;
                }
            }
        }

        .mc-filter-btn > div {
            justify-content: flex-end;
        }

        .ce-toolbar__settings-btn {
            margin-left: 0;
            margin-right: 5px;
        }

        .codex-editor.codex-editor--rtl .ce-toolbar__actions {
            right: 0;
        }

        .mc-area-articles > .mc-tab > .mc-content .mc-article-content {
            padding-right: 0;
        }

        .ce-inline-toolbar__dropdown {
            display: none;
        }
    }
}

.mc-flow-scroll {
    right: auto;
    left: 15px;

    &.mc-flow-scroll.mc-icon-arrow-right {
        left: 60px;
    }
}

.mc-flow-block-cnt-name {
    left: 0;
    right: auto;
}

@media (min-width: 1301px) {
    .mc-repeater-block-data .mc-setting + .mc-setting, .mc-repeater-block-actions .mc-setting + .mc-setting, .mc-repeater-block-rest-api .mc-setting + .mc-setting {
        margin: 0 15px 0 0;
    }
}

@keyframes mc-open {
    0% {
        transform: scale(0);
        transform-origin: top left;
    }

    100% {
        transform: scale(1);
        transform-origin: top left;
    }
}

@media (min-width: 651px) {
    .mc-rtl {
        .ce-toolbox {
            left: auto;
            right: 0;
        }

        .cdx-search-field__icon .icon {
            margin-left: 10px;
            margin-right: 0;
        }

        .codex-editor.codex-editor--rtl .ce-settings {
            left: auto;
            right: 5px;
        }
    }
}
