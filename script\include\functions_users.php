<?php

/*
 * ==========================================================
 * FUNCTIONS_USERS.PHP
 * ==========================================================
 *
 * Users functions file. © 2017-2025 masichat.com. All rights reserved.
 *
 * -----------------------------------------------------------
 * LOGIN AND ACCOUNT
 * -----------------------------------------------------------
 *
 * 1. Check if the login details are corrects and if yes set the login
 * 2. Update details of the login cookie
 * 3. Logout a user
 * 4. Return the logged in user information
 * 5. Set the login cookie
 * 6. Get the login cookie
 * 7. Verify the login password
 * 8. Check the the active user is an admin, bot, or agent
 * 9. Return the department of the active agent
 * 10. Check the the active user it the supervisor
 * 11. Envato purchase code validation
 * 12. Email verification
 *
 */

function mc_login($email = '', $password = '', $user_id = '', $user_token = '') {
    global $MC_LOGIN;
    $valid_login = false;
    $result = null;
    $query = 'SELECT id, profile_image, first_name, last_name, email, password, user_type, token, department FROM mc_users ';
    $ip = isset($_SERVER['HTTP_CF_CONNECTING_IP']) && substr_count($_SERVER['HTTP_CF_CONNECTING_IP'], '.') == 3 ? $_SERVER['HTTP_CF_CONNECTING_IP'] : $_SERVER['REMOTE_ADDR'];
    $ips = mc_get_external_setting('ip-ban', []);
    if (isset($ips[$ip]) && $ips[$ip][0] > 10) {
        if ($ips[$ip][1] > time() - 3600) {
            return 'ip-ban';
        }
        unset($ips[$ip]);
        mc_save_external_setting('ip-ban', $ips);
    }
    if ($email && $password) {

        // Login for registered users and agents
        $result = mc_db_get($query . 'WHERE email = "' . mc_db_escape($email) . '" LIMIT 1');
        if (mc_is_error($result)) {
            return $result;
        }
        $valid_login = mc_isset($result, 'password') && isset($result['user_type']) && mc_password_verify($password, $result['password']);
        if (!$valid_login) {
            $verification_url = mc_get_setting('login-verification-url');
            if ($verification_url) {
                $response = mc_curl($verification_url . (strpos($verification_url, '?') ? '&' : '?') . 'email=' . $email . '&password=' . urlencode($password));
                if (isset($response['first_name'])) {
                    unset($response['user_type']);
                    $result = mc_db_get($query . 'WHERE email = "' . mc_db_escape($email) . '"');
                    if ($result) {
                        $valid_login = true;
                    } else {
                        $response = mc_add_user($response, $response['details'], false);
                        if (!mc_is_validation_error($response) && is_numeric($response)) {
                            $result = mc_db_get($query . 'WHERE id = ' . mc_db_escape($response, true));
                            $valid_login = true;
                        }
                    }
                }
            }
        }
        if ($valid_login && $MC_LOGIN && $MC_LOGIN['id'] != $result['id']) {
            mc_db_query('UPDATE mc_conversations SET user_id = ' . $result['id'] . ' WHERE user_id = ' . $MC_LOGIN['id']);
        }
    } else if ($user_id && $user_token) {

        // Login for visitors
        $result = mc_db_get($query . 'WHERE id = ' . mc_db_escape($user_id, true) . ' AND token = "' . mc_db_escape($user_token) . '"');
        if (mc_is_error($result)) {
            return $result;
        }
        if (isset($result['user_type']) && isset($result['token'])) {
            $valid_login = true;
        }
    }
    if ($valid_login) {
        $settings = ['id' => $result['id'], 'profile_image' => $result['profile_image'], 'first_name' => $result['first_name'], 'last_name' => $result['last_name'], 'email' => $result['email'], 'user_type' => $result['user_type'], 'token' => $result['token'], 'url' => MC_URL, 'password' => $result['password']];
        if (isset($result['department'])) {
            $settings['department'] = $result['department'];
        }
        mc_set_cookie_login($settings);
        $MC_LOGIN = $settings;
        return [$settings, mc_encryption(json_encode($settings))];
    }
    $ips[$ip] = empty($ips[$ip]) ? [1, time()] : [$ips[$ip][0] + 1, $ips[$ip][1]];
    mc_save_external_setting('ip-ban', $ips);
    return false;
}

function mc_update_login($profile_image, $first_name, $last_name, $email, $department = '', $user_type = false, $user_id = false) {
    global $MC_LOGIN;
    $settings = mc_get_cookie_login();
    if (empty($settings)) {
        $settings = [];
    }
    if ($user_id) {
        $settings['id'] = $user_id;
    }
    $settings['profile_image'] = $profile_image;
    $settings['first_name'] = $first_name;
    $settings['last_name'] = $last_name;
    $settings['email'] = $email;
    $settings['department'] = $department == 'NULL' || !$department || $department === false ? null : $department;
    if ($user_type) {
        $settings['user_type'] = $user_type;
    }
    mc_set_cookie_login($settings);
    $MC_LOGIN = $settings;
    return [$settings, mc_encryption(json_encode($settings))];
}

function mc_logout() {
    global $MC_LOGIN;
    if (!headers_sent()) {
        $time = time() - 3600;
        setcookie('mc-login', '', $time);
    }
    $MC_LOGIN = null;
    return true;
}

function mc_get_active_user($login_data = false, $database = false, $login_app = false, $user_token = false) {
    global $MC_LOGIN;
    $return = false;
    if ($MC_LOGIN) {
        $return = $MC_LOGIN;
    } else if (!empty($login_data)) {
        $return = json_decode(mc_encryption($login_data, false), true);
    }
    if ($return === false) {
        $return = mc_get_cookie_login();
    }
    if ($login_app !== false) {
        if (!is_array($login_app)) {
            $login_app = json_decode($login_app, true);
        }
        $app = $login_app[1];
        $login_app_data = $login_app[0];
        if (defined('MC_WP') && $app == 'wp') {
            if ($return === false || !isset($return['email'])) {
                $return = mc_wp_get_active_user($login_app_data);
                if (isset($return[1])) {
                    $return = array_merge($return[0], ['cookie' => $return[1]]);
                }
            } else {
                $wp_user = mc_wp_get_user($login_app_data[0]);
                if (isset($wp_user['email']) && $wp_user['email'] != $return['email']) {
                    $return = mc_wp_get_active_user($login_app_data);
                }
            }
        } else if ($app == 'default' && $login_app_data && (!$return || mc_isset($return, 'email') != mc_isset($login_app_data, 'email'))) {
            if ($login_data && $return && mc_isset($return, 'first_name') == mc_isset($login_app_data, 'first_name') && mc_isset($return, 'last_name') == mc_isset($login_app_data, 'last_name')) {
                return $return;
            }
            $return = mc_add_user_and_login($login_app_data, mc_isset($login_app_data, 'extra', []), false);
            if (mc_is_validation_error($return) && $return->error == 'duplicate-email' && !empty($login_app_data['password'])) {
                $active_user = mc_db_get('SELECT id, token FROM mc_users WHERE password = "' . mc_db_escape($login_app_data['password']) . '" AND email = "' . mc_isset($login_app_data, 'email', '') . '" LIMIT 1');
                $return = $active_user ? mc_login('', '', $active_user['id'], $active_user['token']) : false;
            }
            $return = is_array($return) ? array_merge($return[0], ['cookie' => $return[1]]) : false;
        } else if (defined('MC_PERFEX') && $app == 'perfex') {
            $return = mc_perfex_get_active_user_function($return, $login_app_data);
        } else if (defined('MC_WHMCS') && $app == 'whmcs') {
            $return = mc_whmcs_get_active_user_function($return, $login_app_data);
        } else if (mc_is_cloud() && $app == 'shopify') {
            require_once(MC_CLOUD_PATH . '/account/functions.php');
            $return = shopify_get_active_user($login_app_data);
            $return = is_array($return) ? array_merge($return[0], ['cookie' => $return[1]]) : false;
        } else if (defined('MC_AECOMMERCE') && $app == 'aecommerce') {
            $return = mc_aecommerce_get_active_user_function($return, $login_app_data);
        }
    }
    if (($database && $return && isset($return['id'])) || $user_token) {
        $keys = ['id', 'profile_image', 'first_name', 'last_name', 'email', 'password', 'user_type'];
        $active_user = mc_db_get('SELECT ' . implode(',', $keys) . ' FROM mc_users WHERE ' . ($user_token ? ('token = "' . mc_db_escape($user_token) . '"') : ('id = ' . $return['id'])));
        if ($active_user && (empty($return['password']) || empty($active_user['password']) || $return['password'] == $active_user['password']) && (!mc_is_agent($active_user['user_type']) || $active_user['user_type'] == $return['user_type'])) {
            for ($i = 0; $i < count($keys); $i++) {
                $return[$keys[$i]] = $active_user[$keys[$i]];
            }
            $return['phone'] = mc_get_user_extra($return['id'], 'phone');
            $return['cookie'] = mc_encryption(json_encode($return));
        } else if ($login_data !== false && $login_app !== false) {
            unset($_COOKIE['mc-login']);
            $MC_LOGIN = false;
            return mc_get_active_user(false, $database, $login_app);
        } else {
            $return = false;
        }
    }
    if ($return !== false) {
        if (!$MC_LOGIN) {
            $MC_LOGIN = $return;
        } else {
            $old_user_id = false;
            if ($MC_LOGIN['id'] != $return['id']) {
                $old_user_id = $MC_LOGIN['id'];
            } else if ($login_data && $database) {
                $active_user = json_decode(mc_encryption($login_data, false), true);
                if ($active_user && $return['id'] != $active_user['id']) {
                    $old_user_id = $active_user['id'];
                }
            }
            if ($old_user_id) {
                mc_db_query('UPDATE mc_conversations SET user_id = ' . $return['id'] . ' WHERE user_id = ' . $old_user_id);
                mc_db_query('UPDATE mc_messages SET user_id = ' . $return['id'] . ' WHERE user_id = ' . $old_user_id);
                mc_delete_user($old_user_id);
            }
        }
    }
    return $return;
}

function mc_set_cookie_login($value) {
    if (!headers_sent()) {
        setcookie('mc-login', mc_encryption(json_encode($value)), time() + 315569260, '/', mc_get_setting('cookie-domain', ''));
    }
}

function mc_get_cookie_login() {
    $cookie = isset($_COOKIE['mc-login']) ? $_COOKIE['mc-login'] : mc_isset($_POST, 'login-cookie');
    if ($cookie) {
        $response = json_decode(mc_encryption($cookie, false), true);
        return empty($response) ? false : $response;
    }
    return false;
}

function mc_password_verify($password, $hash) {
    $success = password_verify($password, $hash);
    if (!$success && defined('MC_WP')) {
        $wp_hasher = new MCPasswordHash(8, true);
        $success = $wp_hasher->CheckPassword($password, $hash);
    }
    return $success;
}

function mc_is_agent($user = false, $exclude_bot = false, $only_admin = false, $only_agent = false) {
    if ($user === '') {
        return false;
    }
    $user = $user === false ? mc_get_active_user() : (is_string($user) ? ['user_type' => $user] : $user);
    if (!$user) {
        return !empty($GLOBALS['MC_FORCE_ADMIN']);
    }
    return (!$only_admin && $user['user_type'] == 'agent') || (!$only_agent && $user['user_type'] == 'admin') || (!$exclude_bot && $user['user_type'] == 'bot');
}

function mc_get_agent_department() {
    if (mc_is_agent() && !defined('MC_API')) {
        $user = mc_get_active_user();
        return mc_isset($user, 'department');
    }
    return false;
}

function mc_supervisor() {
    $supervisors = mc_get_setting('supervisors', []);
    $active_user_id = mc_get_active_user_ID();
    $deprecated = mc_get_setting('supervisor'); // Deprecated
    if ($deprecated && !empty($deprecated['supervisor-id'])) { // Deprecated
        $supervisors[] = $deprecated; // Deprecated
    } // Deprecated
    for ($i = 0; $i < count($supervisors); $i++) {
        if (in_array($active_user_id, explode(',', str_replace(' ', '', $supervisors[$i]['supervisor-id'])))) {
            return $supervisors[$i];
        }
    }
    return false;
}

function mc_envato_purchase_code_validation($purchase_code, $full_details = false) {
    // Envato purchase code validation removed - always return true
    if ($full_details) {
        return ['purchase_code' => $purchase_code, 'item' => ['name' => 'Masi Chat'], 'license' => 'Extended License'];
    }
    return true;
}

function mc_otp($email = false, $otp = false) {
    if ($otp) {
        $otp_decrypted = json_decode(mc_encryption($otp[0], false), true);
        return $otp_decrypted && $otp_decrypted[0] == $otp[1] && $email == $otp_decrypted[1];
    }
    $otp = rand(99999, 999999);
    if ($email) {
        $email_content = mc_get_external_setting('email-otp');
        $body = '';
        $subject = '';
        if ($email_content) {
            $email_content = $email_content['email-otp'][0];
            $body = trim(mc_isset($email_content, 'email-otp-content', [''])[0]);
            $subject = mc_isset($email_content, 'email-otp-subject', [''])[0];
            if (empty($subject)) {
                $subject = 'One-Time Code';
            }
            if (empty($body)) {
                $body = 'Your one-time code is {code}.';
            }
        }
        $response = mc_email_send($email, $subject, str_replace('{code}', $otp, $body));
        return $response ? mc_encryption(json_encode([$otp, $email])) : false;
    }
    return false;
}

/*
 * -----------------------------------------------------------
 * USERS
 * -----------------------------------------------------------
 *
 * 1. Add a new user or agent.
 * 2. Add a new user extra details
 * 3. Add a new user and login it
 * 4. Delete a user and all the related information (conversations, messages)
 * 5. Delete multiple users and all the related information (conversations, messages)
 * 6. Delete all leads
 * 7. Update a user or agent.
 * 8. Update a user or agent detail or extra detail.
 * 9. Update a visitor to lead
 * 10. Update the current user and a conversation message
 * 11. Return the user with the given id
 * 12. Return all users, Agents
 * 13. Return the users registered after the given date
 * 14. Search users based on the gived keyword
 * 15. Return the users count grouped by user type
 * 16. Return the user additional details
 * 17. Return the agent or admin with the given ID
 * 18. Set the active admin if any and register if required
 * 19. Return the full name of a user
 * 20. Save a CSV file with all users details
 * 21. Save automatic information from the user: IP, Country, OS, Browser
 * 22. Set and get the current page URL of a user
 * 23. Create or update the bot
 * 24. Return the bot ID
 * 25. Return the user or the last agent of a conversation
 * 26. Return an array with the agents ids
 * 27. Generate the profile picture of the user from its name
 * 28. Return the users who have the requested details
 * 29. Return the ID of the active user
 * 30. Get a user from a detail
 * 31. Check if the user is typing on the chat
 * 32. Check if an agent is typing in a conversation
 * 33. Set the user typing status
 * 34. Set agent raring
 * 35. Get agent rating
 * 36. Split a full name into first name and last name
 * 37. Get the IP information
 * 38. Return the user detail fields
 * 39. Import users
 *
 */

function mc_add_user($settings = [], $settings_extra = [], $hash_password = true, $skip_otp = true) {
    $keys = ['profile_image', 'first_name', 'last_name', 'email', 'user_type', 'password', 'department'];
    for ($i = 0; $i < count($keys); $i++) {
        $settings[$keys[$i]] = mc_isset($settings, $keys[$i], '');
        if (!is_string($settings[$keys[$i]])) {
            $settings[$keys[$i]] = trim($settings[$keys[$i]][0]);
        }
    }
    $password = $settings['password'];
    $email = $settings['email'];
    if ($skip_otp) {
        $settings['otp'] = false;
    }
    if (!empty($email)) {
        $email = mc_db_escape($email);
        $existing_user = mc_db_get('SELECT id, user_type, token FROM mc_users WHERE email = "' . $email . '" LIMIT 1');
        if ($existing_user) {
            if (empty($settings['otp'])) {
                return new MCValidationError('duplicate-email');
            } else {
                if (mc_otp($email, mc_isset($settings, 'otp')) !== true) {
                    return new MCValidationError('invalid-otp');
                }
                return mc_is_agent($existing_user) ? 'admin-user-error' : mc_login(false, false, $existing_user['id'], $existing_user['token']);
            }
        }
    }
    if (!empty($settings_extra['phone']) && mc_get_user_by('phone', $settings_extra['phone'][0])) {
        return new MCValidationError('duplicate-phone');
    }
    if (!$skip_otp && !empty($email) && mc_get_setting('registration-otp') && mc_otp($email, mc_isset($settings, 'otp')) !== true) {
        return new MCValidationError('invalid-otp');
    }
    if (empty($settings['profile_image'])) {
        $settings['profile_image'] = mc_get_avatar($settings['first_name'], $settings['last_name']);
    }
    if (empty($settings['first_name'])) {
        $name = mc_get_setting('visitor-prefix');
        $settings['first_name'] = $name ? $name : 'User';
        $settings['last_name'] = '#' . rand(0, 99999);
    }
    if (empty($settings['user_type'])) {
        $settings['user_type'] = empty($email) ? 'visitor' : 'user';
    } else if (!in_array($settings['user_type'], ['visitor', 'user', 'lead', 'agent', 'admin', 'bot'])) {
        return new MCValidationError('invalid-user-type');
    }
    if ($settings['user_type'] == 'user') {
        if (!empty($settings['first_name']) && (empty($settings['last_name']) || substr($settings['last_name'], 0, 1) == '#')) {
            $split_name = mc_split_name($settings['first_name']);
            $settings['first_name'] = $split_name[0];
            $settings['last_name'] = $split_name[1];
        }
    }
    if (mc_is_agent($settings) && !mc_is_agent(false, true, true)) {
        return mc_error('security-error', 'mc_add_user');
    }
    if (!empty($password) && $hash_password) {
        $password = password_hash($password, PASSWORD_DEFAULT);
    }
    if (empty($settings['department'])) {
        $settings['department'] = mc_is_agent() && mc_isset(mc_get_active_user(), 'department') ? mc_get_active_user()['department'] : 'NULL';
    }
    // Envato purchase code validation removed
    $now = mc_gmt_now();
    $token = bin2hex(openssl_random_pseudo_bytes(20));
    $query = 'INSERT INTO mc_users(first_name, last_name, password, email, profile_image, user_type, creation_time, token, department, last_activity) VALUES ("' . mc_db_escape($settings['first_name']) . '", "' . mc_db_escape($settings['last_name']) . '", "' . mc_db_escape($password) . '", ' . ($settings['email'] ? '"' . $settings['email'] . '"' : 'NULL') . ', "' . mc_db_escape($settings['profile_image']) . '", "' . $settings['user_type'] . '", "' . $now . '", "' . $token . '", ' . mc_db_escape($settings['department']) . ', "' . $now . '")';
    $user_id = mc_db_query($query, true);
    if (!mc_is_error($user_id) && is_numeric($user_id) && $user_id > 0 && !empty($settings_extra)) {
        mc_add_new_user_extra($user_id, $settings_extra);
    }
    if (!mc_is_error($user_id) && !mc_is_agent() && ($settings['user_type'] == 'user' || $settings['user_type'] == 'lead' || mc_get_setting('visitor-autodata'))) {
        mc_user_autodata($user_id);
    }
    if ($settings['user_type'] == 'visitor') {
        mc_reports_update('visitors');
    }
    if (isset($_POST['payload']) && isset($_POST['payload']['rich-messages']) && isset($_POST['payload']['rich-messages']['registration'])) {
        mc_reports_update('registrations');
    }
    if (!empty($email)) {
        mc_newsletter($email, $settings['first_name'], $settings['last_name']);
        if (mc_is_cloud() && mc_is_agent($settings)) {
            mc_cloud_set_agent($email);
        }
    }
    if (mc_is_agent(false, true, false, true) && (mc_routing_is_active() || mc_get_multi_setting('agent-hide-conversations', 'agent-hide-conversations-active'))) {
        mc_new_conversation($user_id, 3, '', mc_get_agent_department(), mc_get_active_user_ID());
    }
    return $user_id;
}

function mc_add_new_user_extra($user_id, $settings) {
    $query = '';
    $user_id = mc_db_escape($user_id, true);
    foreach ($settings as $key => $setting) {
        if (!is_array($setting)) {
            $setting = [$setting, mc_string_slug($key, 'string')];
        }
        if ($setting[0] && $setting[0] != 'null') {
            $query .= '("' . $user_id . '", "' . mc_db_escape($key) . '", "' . mc_db_escape($setting[1]) . '", "' . mc_db_escape($setting[0]) . '"),';
        }
    }
    if ($query) {
        $query = 'INSERT IGNORE INTO mc_users_data(user_id, slug, name, value) VALUES ' . substr($query, 0, -1);
        return mc_db_query($query);
    }
    return false;
}

function mc_add_user_and_login($settings, $settings_extra, $hash_password = true) {
    $response = mc_add_user($settings, $settings_extra, $hash_password, false);
    if (is_numeric($response)) {
        $token = mc_db_get('SELECT token FROM mc_users WHERE id = ' . $response);
        return mc_login('', '', $response, $token['token']);
    }
    return $response;
}

function mc_delete_user($user_id) {
    return mc_delete_users([$user_id]);
}

function mc_delete_users($user_ids) {
    $query = '';
    $log_text = mc_get_setting('logs') ? 'Agent ' . mc_get_user_name() . ' #' . mc_get_active_user_ID() . ' deleted the user #' : false;
    $cloud = mc_is_cloud();
    for ($i = 0; $i < count($user_ids); $i++) {
        $user_id = mc_db_escape($user_ids[$i], true);
        $query .= $user_id . ',';
        if ($log_text) {
            mc_logs($log_text . $user_id);
        }
        if ($cloud) {
            $user = mc_get_user($user_id);
            if ($user && mc_is_agent($user)) {
                mc_cloud_set_agent($user['email'], 'delete');
            }
        }
    }
    $query = substr($query, 0, -1);
    $ids = array_column(mc_db_get('SELECT id FROM mc_conversations WHERE user_id IN (' . $query . ')', false), 'id');
    $profile_images = mc_db_get('SELECT profile_image FROM mc_users WHERE id IN (' . $query . ')', false);
    for ($i = 0; $i < count($ids); $i++) {
        mc_delete_attachments($ids[$i]);
    }
    for ($i = 0; $i < count($profile_images); $i++) {
        mc_file_delete($profile_images[$i]['profile_image']);
    }
    if (!empty($ids)) {
        mc_db_query('DELETE FROM mc_settings WHERE name IN (' . implode(', ', array_map(function ($e) {
            return '"notes-' . $e . '"';
        }, $ids)) . ')');
    }
    if (defined('SHOPIFY_CLIENT_ID')) {
        mc_db_query('DELETE FROM mc_settings WHERE name = "shopify_cart_' . $user_id . '" LIMIT 1');
    }
    $agent_ids = mc_get_agents_ids();
    $is_agent = false;
    $agent_id_to_assign = false;
    foreach ($agent_ids as $agent_id) {
        if (in_array($agent_id, $user_ids)) {
            $is_agent = true;
        } else if (!$agent_id_to_assign) {
            $agent_id_to_assign = $agent_id;
        }
    }
    if ($is_agent && $agent_id_to_assign) {
        $agent_ids = implode(', ', $agent_ids);
        mc_db_query('UPDATE mc_conversations SET agent_id = NULL WHERE agent_id IN (' . $agent_ids . ')');
        mc_db_query('UPDATE mc_messages SET user_id = ' . $agent_id_to_assign . ' WHERE user_id IN (' . $agent_ids . ')');
    }

    return mc_db_query('DELETE FROM mc_users WHERE id IN (' . $query . ')');
}

function mc_delete_leads() {
    return mc_db_query('DELETE FROM mc_users WHERE user_type = "lead"');
}

function mc_update_user($user_id, $settings, $settings_extra = [], $hash_password = true, $skip_otp = false) {
    $result = false;
    $user_id = mc_db_escape($user_id, true);
    $keys = ['profile_image', 'first_name', 'last_name', 'email', 'user_type', 'password', 'department'];
    $profile_image = mc_isset($settings, 'profile_image');
    $first_name = trim(mc_isset($settings, 'first_name'));
    $last_name = trim(mc_isset($settings, 'last_name'));
    $password = isset($settings['password']) && $settings['password'] != '********' ? $settings['password'] : '';
    $department = isset($settings['department']) ? mc_isset($settings, 'department', 'NULL') : false;
    $user_type = mc_isset($settings, 'user_type');
    $email = mc_isset($settings, 'email');
    $active_user = mc_get_active_user();
    $is_active_user_agent = mc_is_agent($active_user);
    $query = '';
    if ($user_type && mc_is_agent($user_type) && !mc_is_agent(false, true, true)) {
        return mc_error('security-error', 'mc_update_user');
    }
    if ($first_name && (strpos($profile_image, 'media/user.svg') || (!$profile_image && strpos(mc_isset(mc_db_get('SELECT profile_image FROM mc_users WHERE id = ' . $user_id), 'email'), 'media/user.svg')))) {
        $profile_image = mc_get_avatar($first_name, $last_name);
    }
    if ($email) {
        $email = trim(mc_db_escape($email));
        $existing_user = mc_db_get('SELECT id, user_type, email, token FROM mc_users WHERE email = "' . $email . '" AND id <> ' . $user_id);
        if ($existing_user) {
            if (empty($settings['otp'])) {
                return new MCValidationError('duplicate-email');
            } else {
                if (mc_otp($email, mc_isset($settings, 'otp')) !== true) {
                    return new MCValidationError('invalid-otp');
                }
                return mc_is_agent($existing_user) ? 'admin-user-error' : mc_login(false, false, $existing_user['id'], $existing_user['token']);
            }
        } else {
            $query .= ', email = "' . $email . '"';
        }
        if (mc_is_cloud() && mc_is_agent($user_type)) {
            $old_email = mc_isset(mc_db_get('SELECT email FROM mc_users WHERE id = ' . $user_id), 'email');
            if ($old_email && $old_email != $email) {
                mc_cloud_set_agent($old_email, 'update', $email);
            }
        }
    }
    if (!empty($settings_extra['phone']) && intval(mc_db_get('SELECT COUNT(*) as count FROM mc_users_data WHERE slug = "phone" AND (value = "' . $settings_extra['phone'][0] . '"' . (strpos($settings_extra['phone'][0], '+') !== false ? (' OR value = "' . str_replace('+', '00', $settings_extra['phone'][0]) . '"') : '') . ') AND user_id <> ' . mc_db_escape($user_id, true))['count']) > 0) {
        return new MCValidationError('duplicate-phone');
    }
    if (!$is_active_user_agent && !$skip_otp && $email && mc_otp($email, mc_isset($settings, 'otp')) !== true && mc_get_setting('registration-otp')) {
        return new MCValidationError('invalid-otp');
    }
    if (!$is_active_user_agent && (!$user_type || !mc_is_agent($user_type))) {
        $user_type = $email || $active_user['email'] ? 'user' : (intval(mc_db_get('SELECT COUNT(*) AS count FROM mc_conversations WHERE user_id = ' . $user_id)['count']) > 0 ? 'lead' : 'visitor');
    }
    if ($user_type) {
        $query .= ', user_type = "' . mc_db_escape($user_type) . '"';
    }
    if ($profile_image) {
        $query .= ', profile_image = "' . mc_db_escape($profile_image) . '"';
    }
    if ($first_name) {
        if (empty($last_name) || substr($last_name, 0, 1) == '#') {
            $split_name = mc_split_name($first_name);
            $first_name = $split_name[0];
            $last_name = mc_isset($split_name, 1, 'NULL');
        }
        $query .= ', first_name = "' . mc_db_escape(ucfirst($first_name)) . '"';
    }
    if ($last_name || $last_name == 'NULL') {
        $query .= ', last_name = "' . ($last_name == 'NULL' ? '' : mc_db_escape(ucfirst($last_name))) . '"';
    }
    if ($password) {
        if ($hash_password) {
            $password = password_hash($password, PASSWORD_DEFAULT);
        }
        $query .= ', password = "' . mc_db_escape($password) . '"';
    }
    if ($department || $department == 'NULL') {
        $query .= ', department = ' . ($department == 'NULL' ? 'NULL' : mc_db_escape($department, true));
    }
    if ($query) {
        $result = mc_db_query('UPDATE mc_users SET ' . substr($query, 1) . ' WHERE id = ' . $user_id);
    }

    // Extra user details
    if ($active_user && $active_user['id'] == $user_id) {
        $user = mc_get_user($user_id);
        if ($user) {
            $result = mc_update_login($user['profile_image'], $user['first_name'], $user['last_name'], $user['email'], $user['department'], $user['user_type'], $user_id);
            mc_user_autodata($user_id);
        }
    }
    if (isset($settings_extra['language']) && !empty($settings_extra['language'][0])) {
        $settings_extra['browser_language'] = ['', ''];
    }
    foreach ($settings_extra as $key => $setting) {
        if (!in_array($key, $keys)) {
            if (!is_array($setting)) {
                $setting = [$setting, mc_string_slug($key, 'string')];
            }
            mc_db_query('REPLACE INTO mc_users_data SET name = "' . mc_db_escape($setting[1]) . '", value = "' . mc_db_escape($setting[0]) . '", slug = "' . mc_db_escape($key) . '", user_id = ' . $user_id);
        }
    }
    mc_db_query('DELETE FROM mc_users_data WHERE user_id = ' . $user_id . ' AND value = ""');
    if (defined('MC_SLACK') && $first_name && $last_name && mc_get_setting('slack-active')) {
        mc_slack_rename_channel($user_id, trim($first_name . '_' . $last_name));
    }
    if ($email) {
        mc_newsletter($email, $first_name, $last_name);
    }

    // More
    if ($is_active_user_agent && mc_get_setting('logs')) {
        mc_logs('updated the user details of the user #' . $user_id);
    }
    return $result;
}

function mc_update_user_value($user_id, $slug, $value, $name = false) {
    $user_id = mc_db_escape($user_id, true);
    if (!mc_is_agent(false, true, true) && ((mc_is_agent() && mc_isset(mc_db_get('SELECT user_type FROM mc_users WHERE id = ' . $user_id), 'user_type') == 'admin') || ($slug == 'user_type' && mc_is_agent($value, true)))) {
        return mc_error('security-error', 'mc_update_user_value');
    }
    if (empty($value)) {
        return mc_db_query('DELETE FROM mc_users_data WHERE user_id = ' . $user_id . ' AND slug = "' . mc_db_escape($slug) . '"');
    }
    if (in_array($slug, ['profile_image', 'first_name', 'last_name', 'email', 'password', 'department', 'user_type', 'last_activity', 'typing'])) {
        if ($slug == 'password')
            $value = password_hash($value, PASSWORD_DEFAULT);
        if ($slug == 'email') {
            mc_newsletter($value);
        }
        if ($user_id == mc_get_active_user_ID()) {
            $GLOBALS['MC_LOGIN'][$slug] = $value;
        }
        return mc_db_query('UPDATE mc_users SET ' . mc_db_escape($slug) . ' = "' . mc_db_escape($value) . '" WHERE id = ' . $user_id);
    }
    return mc_db_query('REPLACE INTO mc_users_data SET name = "' . mc_db_escape($name ? $name : mc_string_slug($slug, 'string')) . '", value = "' . mc_db_escape($value) . '", slug = "' . mc_db_escape($slug) . '", user_id = ' . $user_id);
}

function mc_update_user_to_lead($user_id) {
    mc_user_autodata($user_id);
    return mc_update_user_value($user_id, 'user_type', 'lead');
}

function mc_update_user_and_message($user_id, $settings, $settings_extra = [], $message_id = false, $message = false, $payload = false, $skip_otp = false) {
    $result = mc_update_user($user_id, $settings, $settings_extra, true, $skip_otp);
    $rich_message = mc_isset($payload, 'rich-messages');
    if (mc_is_validation_error($result) && $result->code() == 'duplicate-email') {
        return $result;
    }
    if ($message_id) {
        if ($message) {
            mc_update_message($message_id, $message, false, $payload);
        }
        $message = '';
        foreach ($settings as $key => $setting) {
            if ($setting) {
                $message .= mc_string_slug($key, 'string') . ': ' . $setting . PHP_EOL;
            }
        }
        foreach ($settings_extra as $key => $setting) {
            $message .= mc_string_slug($key, 'string') . ': ' . $setting[0] . PHP_EOL;
        }
        mc_send_agents_notifications($message, false, mc_db_get('SELECT conversation_id FROM mc_messages WHERE id = ' . mc_db_escape($message_id, true))['conversation_id']);
    }
    if ($rich_message) {
        if (isset($rich_message['mc-follow-up-form'])) {
            mc_reports_update('follow-up');
        }
        if (isset($rich_message['registration'])) {
            mc_reports_update('registrations');
        }
    }
    return $result;
}

function mc_get_user($user_id, $extra = false) {
    if ($user_id) {
        $user = mc_db_get(SELECT_FROM_USERS . ', password FROM mc_users WHERE id = ' . mc_db_escape($user_id, true));
        if (isset($user) && is_array($user)) {
            if ($extra) {
                $user['details'] = mc_get_user_extra($user_id);
            }
            return $user;
        }
    }
    return false;
}

function mc_get_users($sorting = ['creation_time', 'DESC'], $user_types = [], $search = '', $pagination = 0, $extra = false, $user_ids = false, $department = false, $tag = false, $source = false) {
    $query = '';
    $query_search = '';
    $count = count($user_types);
    $sorting_field = $sorting[0];
    $main_field_sorting = in_array($sorting_field, ['id', 'first_name', 'last_name', 'email', 'profile_image', 'user_type', 'creation_time', 'last_activity', 'department']);
    if ($count) {
        for ($i = 0; $i < $count; $i++) {
            $query .= 'user_type = "' . mc_db_escape($user_types[$i]) . '" OR ';
        }
        $query = '(' . substr($query, 0, strlen($query) - 4) . ')';
    }
    if ($user_ids) {
        $count_user_ids = count($user_ids);
        if ($count_user_ids) {
            $query .= ($query ? ' AND ' : '') . ' mc_users.id IN (' . mc_db_escape(implode(',', $user_ids)) . ')';
        }
    }
    if ($department || $tag || $source) {
        $query .= ($query ? ' AND ' : '') . ' mc_users.id IN (SELECT A.id FROM mc_users A, mc_conversations B WHERE A.id = B.user_id' . ($department ? ' AND B.department = "' . mc_db_escape($department, true) . '"' : '') . ($tag ? ' AND B.tags LIKE "%' . mc_db_escape($tag) . '%"' : '') . ($source ? ' AND ' . ($source == 'chat' ? '(B.source = "" OR B.source IS NULL)' : 'B.source = "' . mc_db_escape($source) . '"') : '') . ')';
    }
    if ($search) {
        $searched_users = mc_search_users($search);
        $count_search = count($searched_users);
        if ($count_search > 0) {
            for ($i = 0; $i < $count_search; $i++) {
                $query_search .= $searched_users[$i]['id'] . ',';
            }
            $query .= ($query ? ' AND ' : '') . 'mc_users.id IN (' . substr($query_search, 0, -1) . ')';
        } else {
            return [];
        }
    }
    if ($query) {
        $query = ' WHERE user_type <> "bot" AND ' . $query;
    } else {
        $query = ' WHERE user_type <> "bot"';
    }
    $users = mc_db_get(SELECT_FROM_USERS . ' FROM mc_users ' . $query . mc_routing_and_department_db('mc_conversations', true) . ($main_field_sorting ? (' ORDER BY ' . mc_db_escape($sorting_field) . ' ' . mc_db_escape($sorting[1])) : '') . ' LIMIT ' . (intval(mc_db_escape($pagination, true)) * 100) . ',100', false);
    $users_count = count($users);
    if (!$users_count) {
        return [];
    }
    if (isset($users) && is_array($users)) {
        $is_array = is_array($extra);
        if ($extra && (!$is_array || count($extra))) {
            $query = '';
            $query_extra = '';
            for ($i = 0; $i < $users_count; $i++) {
                $query .= $users[$i]['id'] . ',';
                $users[$i]['extra'] = [];
            }
            if ($is_array) {
                for ($i = 0; $i < count($extra); $i++) {
                    $query_extra .= 'slug = "' . $extra[$i] . '" OR ';
                }
                if ($query_extra) {
                    $query_extra = ' AND (' . substr($query_extra, 0, -4) . ')';
                }
            }
            $users_extra = mc_db_get('SELECT user_id, slug, value FROM mc_users_data WHERE user_id IN (' . substr($query, 0, -1) . ')' . $query_extra . ' ORDER BY user_id', false);
            for ($i = 0; $i < count($users_extra); $i++) {
                $user_id = $users_extra[$i]['user_id'];
                $slug = $users_extra[$i]['slug'];
                $value = $users_extra[$i]['value'];
                for ($j = 0; $j < $users_count; $j++) {
                    if ($users[$j]['id'] == $user_id) {
                        $users[$j]['extra'][$slug] = $value;
                        break;
                    }
                }
            }
        }
        if (!$main_field_sorting) {
            if ($sorting[1] == 'ASC') {
                usort($users, function ($a, $b) use ($sorting_field) {
                    return isset($a['extra'][$sorting_field]) ? $a['extra'][$sorting_field] <=> (isset($b['extra'][$sorting_field]) ? $b['extra'][$sorting_field] : '') : -1;
                });
            } else {
                usort($users, function ($a, $b) use ($sorting_field) {
                    return isset($b['extra'][$sorting_field]) ? $b['extra'][$sorting_field] <=> (isset($a['extra'][$sorting_field]) ? $a['extra'][$sorting_field] : '') : -1;
                });
            }
        }
        return $users;
    } else {
        return mc_error('db-error', 'mc_get_users', $users);
    }
}

function mc_get_new_users($datetime) {
    $datetime = mc_db_escape($datetime);
    $users = mc_db_get(SELECT_FROM_USERS . ' FROM mc_users WHERE user_type <> "bot" AND ' . (is_numeric($datetime) ? ('id > ' . $datetime) : ('creation_time > "' . $datetime . '"')) . mc_routing_and_department_db('mc_conversations', true) . ' ORDER BY id DESC', false);
    if (isset($users) && is_array($users)) {
        return $users;
    } else {
        return mc_error('db-error', 'mc_get_new_users', $users);
    }
}

function mc_search_users($search) {
    $search = trim(mc_db_escape($search));
    $query = '';
    if (strpos($search, ' ') > 0) {
        $search = explode(' ', $search);
    } else {
        $search = [$search];
    }
    for ($i = 0; $i < count($search); $i++) {
        $search[$i] = mc_db_escape($search[$i]);
        $query .= 'first_name LIKE "%' . $search[$i] . '%" OR last_name LIKE "%' . $search[$i] . '%" OR ';
    }
    $result = mc_db_get('SELECT * FROM mc_users WHERE user_type <> "bot" AND (' . $query . ' email LIKE "%' . $search[0] . '%" OR id IN (SELECT user_id FROM mc_users_data WHERE value LIKE "%' . $search[0] . '%")) ' . mc_routing_and_department_db('mc_conversations', true) . ' GROUP BY mc_users.id;', false);
    if (isset($result) && is_array($result)) {
        return $result;
    } else {
        return mc_error('db-error', 'mc_search_users', $result);
    }
}

function mc_count_users() {
    $query = mc_routing_and_department_db('mc_conversations', true);
    if ($query) {
        $users = mc_db_get(substr($query, strpos($query, '(SE') + 1, -2), false);
        $count = count($users);
        if (!$count) {
            return ['all' => 0, 'lead' => 0, 'user' => 0, 'visitor' => 0];
        }
        $query = '';
        for ($i = 0; $i < $count; $i++) {
            $query .= $users[$i]['user_id'] . ',';
        }
        if ($query) {
            $query = 'AND id IN (' . substr($query, 0, -1) . ')';
        }
    }
    return mc_db_get('SELECT SUM(CASE WHEN user_type <> "bot" ' . $query . ' THEN 1 ELSE 0 END) AS `all`, SUM(CASE WHEN user_type = "lead"' . $query . ' THEN 1 ELSE 0 END) AS `lead`, SUM(CASE WHEN user_type = "user"' . $query . ' THEN 1 ELSE 0 END) AS `user`, SUM(CASE WHEN user_type = "visitor"' . $query . ' THEN 1 ELSE 0 END) AS `visitor` FROM mc_users');
}

function mc_get_user_extra($user_id, $slug = false, $default = false) {
    if (empty($user_id)) {
        return false;
    }
    $response = mc_db_get('SELECT slug, name, value FROM mc_users_data WHERE user_id = ' . mc_db_escape($user_id, true) . ($slug ? ' AND slug = "' . mc_db_escape($slug) . '" LIMIT 1' : ''), $slug);
    return $slug ? mc_isset($response, 'value', $default) : $response;
}

function mc_get_agent($agent_id) {
    $user = mc_db_get('SELECT id, first_name, last_name, profile_image, department FROM mc_users WHERE (user_type = "admin" OR user_type = "agent" OR user_type = "bot") AND id = ' . mc_db_escape($agent_id, true));
    if (isset($user) && is_array($user)) {
        $user['details'] = mc_get_user_extra($agent_id);
        for ($i = 0; $i < count($user['details']); $i++) {
            if ($user['details'][$i]['slug'] == 'country') {
                $country = $user['details'][$i]['value'];
                $countries = mc_get_json_resource('json/countries.json');
                $user['country_code'] = $countries[$country];
                if (isset($countries[$country]) && file_exists(MC_PATH . '/media/flags/' . strtolower($countries[$country]) . '.png')) {
                    $user['flag'] = strtolower($countries[$country]) . '.png';
                }
                break;
            }
        }
        return $user;
    } else {
        return false;
    }
}

function mc_set_external_active_admin($external_user) {
    $active_user = mc_get_active_user();
    if (!$external_user || $external_user['user_type'] != 'admin') {
        return false;
    }
    if (!mc_is_agent($active_user) || empty($active_user['url']) || $active_user['url'] != MC_URL || empty($external_user['email']) || $external_user['email'] != $active_user['email']) {
        $settings = false;
        $db_user = mc_db_get('SELECT * FROM mc_users WHERE email = "' . mc_db_escape($external_user['email']) . '" LIMIT 1');
        if (!empty($db_user) && isset($db_user['password']) && $external_user['password'] == $db_user['password']) {
            if (!mc_is_agent($db_user)) {
                $db_user['user_type'] = 'agent';
                mc_db_query('UPDATE mc_users SET user_type = "agent" WHERE email = "' . mc_db_escape($external_user['email']) . '"');
            }
            $settings = ['id' => $db_user['id'], 'profile_image' => $db_user['profile_image'], 'first_name' => $db_user['first_name'], 'last_name' => $db_user['last_name'], 'email' => $db_user['email'], 'user_type' => $db_user['user_type'], 'token' => $db_user['token']];
        } else if (empty($db_user)) {
            $settings = ['id' => mc_isset($external_user, 'id'), 'profile_image' => mc_isset($external_user, 'profile_image', ''), 'first_name' => $external_user['first_name'], 'last_name' => $external_user['last_name'], 'password' => $external_user['password'], 'email' => $external_user['email'], 'user_type' => 'admin'];
            if (!mc_is_agent($active_user)) {
                global $MC_LOGIN;
                $MC_LOGIN = $settings;
            }
            $settings['id'] = mc_add_user($settings, mc_isset($external_user, 'extra', []), false);
        } else {
            mc_logout();
            return 'logout';
        }
        if ($settings) {
            unset($settings['password']);
            global $MC_LOGIN;
            $settings['url'] = MC_URL;
            if (!headers_sent()) {
                mc_set_cookie_login($settings);
                $MC_LOGIN = $settings;
            }
            return true;
        }
        return false;
    }
    return true;
}

function mc_get_user_name($user = false) {
    $user = $user === false ? mc_get_active_user() : $user;
    $name = trim(mc_isset($user, 'first_name', '') . ' ' . mc_isset($user, 'last_name', ''));
    return substr(mc_isset($user, 'last_name', '-'), 0, 1) != '#' ? $name : mc_get_setting('visitor-default-name', $name);
}

function mc_csv_users($user_ids = false) {
    $custom_fields = mc_get_setting('user-additional-fields', []);
    $header = ['Birthdate', 'City', 'Company', 'Country', 'Language', 'Phone', 'Website'];
    $users = mc_db_get('SELECT id, first_name, last_name, email, profile_image, user_type, creation_time FROM mc_users WHERE user_type <> "bot"' . mc_routing_and_department_db('mc_conversations', true) . ' ORDER BY first_name', false);
    $users_response = [];
    for ($i = 0; $i < count($custom_fields); $i++) {
        array_push($header, $custom_fields[$i]['extra-field-name']);
    }
    for ($i = 0; $i < count($users); $i++) {
        $user = $users[$i];
        if ($user_ids && !in_array($user['id'], $user_ids)) {
            continue;
        }
        if ($user['user_type'] != 'visitor' && $user['user_type'] != 'lead') {
            $user_extra = mc_db_get('SELECT * FROM mc_users_data WHERE user_id = ' . $user['id'], false);
            for ($j = 0; $j < count($header); $j++) {
                $key = $header[$j];
                $user[$key] = '';
                for ($y = 0; $y < count($user_extra); $y++) {
                    if ($user_extra[$y]['name'] == $key) {
                        $user[$key] = $user_extra[$y]['value'];
                        break;
                    }
                }
            }
        } else {
            for ($j = 0; $j < count($header); $j++) {
                $user[$header[$j]] = '';
            }
        }
        array_push($users_response, $user);
    }
    return mc_csv($users_response, array_merge(['ID', 'First Name', 'Last Name', 'Email', 'Profile Image', 'Type', 'Creation Time'], $header), 'users');
}

function mc_user_autodata($user_id) {
    if (!defined('MC_API') && empty($GLOBALS['MC_FORCE_ADMIN'])) {
        $settings = [];
        $user_agent = mc_isset($_SERVER, 'HTTP_USER_AGENT');

        // IP and related data
        $ip_data = mc_ip_info('status,country,countryCode,city,timezone,currency');

        if ($ip_data) {
            $settings['ip'] = [$ip_data['ip'], 'IP'];
            if (isset($ip_data['city']) && isset($ip_data['country'])) {
                $settings['location'] = [$ip_data['city'] . ', ' . $ip_data['country'], 'Location'];
            }
            if (isset($ip_data['timezone'])) {
                $settings['timezone'] = [$ip_data['timezone'], 'Timezone'];
            }
            if (isset($ip_data['currency'])) {
                $settings['currency'] = [$ip_data['currency'], 'Currency'];
            }
            if (isset($ip_data['countryCode'])) {
                $settings['country_code'] = [$ip_data['countryCode'], 'Country Code'];
            }
        }

        // Browser
        $browser = '';
        $agent = strtolower($user_agent);
        if (strpos($agent, 'safari/') and strpos($agent, 'opr/')) {
            $browser = 'Opera';
        } else if (strpos($agent, 'safari/') and strpos($agent, 'chrome/') and strpos($agent, 'edge/') == false) {
            $browser = 'Chrome';
        } else if (strpos($agent, 'msie')) {
            $browser = 'Internet Explorer';
        } else if (strpos($agent, 'firefox/')) {
            $browser = 'Firefox';
        } else if (strpos($agent, 'edge/')) {
            $browser = 'Microsoft Edge';
        } else if (strpos($agent, 'safari/') and strpos($agent, 'opr/') == false and strpos($agent, 'chrome/') == false) {
            $browser = 'Safari';
        }
        ;
        if ($browser) {
            $settings['browser'] = [$browser, 'Browser'];
        }

        // Browser language
        if (isset($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
            $settings['browser_language'] = [strtoupper(mc_language_code($_SERVER['HTTP_ACCEPT_LANGUAGE'])), 'Language'];
        }

        // OS
        $os = '';
        $os_array = ['/windows nt 10/i' => 'Windows 10', '/windows nt 6.3/i' => 'Windows 8.1', '/windows nt 6.2/i' => 'Windows 8', '/windows nt 6.1/i' => 'Windows 7', '/windows nt 6.0/i' => 'Windows Vista', '/windows nt 5.2/i' => 'Windows Server 2003/XP x64', '/windows xp/i' => 'Windows XP', '/windows nt 5.0/i' => 'Windows 2000', '/windows me/i' => 'Windows ME', '/macintosh|mac os x/i' => 'Mac OS X', '/mac_powerpc/i' => 'Mac OS 9', '/linux/i' => 'Linux', '/ubuntu/i' => 'Ubuntu', '/iphone/i' => 'iPhone', '/ipod/i' => 'iPod', '/ipad/i' => 'iPad', '/android/i' => 'Android', '/blackberry/i' => 'BlackBerry', '/webos/i' => 'Mobile'];
        foreach ($os_array as $regex => $value) {
            if (preg_match($regex, $user_agent)) {
                $os = $value;
            }
        }
        if ($os) {
            $settings['os'] = [$os, 'OS'];
        }

        // Current url
        if (isset($_POST['current_url'])) {
            $settings['current_url'] = [$_POST['current_url'], 'Current URL'];
        } else if (isset($_SERVER['HTTP_REFERER'])) {
            $settings['current_url'] = [$_SERVER['HTTP_REFERER'], 'Current URL'];
        }

        // Save the data
        return mc_add_new_user_extra($user_id, $settings);
    }
    return false;
}

function mc_current_url($user_id = false, $url = false) {
    if (!empty($user_id)) {
        if ($url === false) {
            $url = mc_db_get('SELECT value FROM mc_users_data WHERE user_id ="' . mc_db_escape($user_id, true) . '" and slug = "current_url" LIMIT 1');
            return isset($url['value']) ? $url['value'] : false;
        }
        return mc_update_user_value($user_id, 'current_url', $url, 'Current URL');
    }
    return false;
}

function mc_update_bot($name = '', $profile_image = '') {
    $bot = mc_db_get('SELECT id, profile_image, first_name FROM mc_users WHERE user_type = "bot" LIMIT 1');
    if (!$name) {
        $name = 'Bot';
    }
    if (!$profile_image) {
        $profile_image = MC_URL . '/media/user.svg';
    }
    $settings = ['profile_image' => $profile_image, 'first_name' => $name, 'user_type' => 'bot'];
    if (!$bot) {
        return mc_add_user($settings);
    } else if ($bot['profile_image'] != $profile_image || $bot['first_name'] != $name) {
        return mc_update_user($bot['id'], $settings);
    }
    return false;
}

function mc_get_bot_id() {
    if (isset($GLOBALS['mc-bot-id'])) {
        return $GLOBALS['mc-bot-id'];
    }
    $bot_id = mc_isset(mc_db_get('SELECT id FROM mc_users WHERE user_type = "bot" LIMIT 1'), 'id');
    if (!$bot_id) {
        $bot_id = mc_update_bot();
    }
    $GLOBALS['mc-bot-id'] = $bot_id;
    return $bot_id;
}

function mc_get_user_from_conversation($conversation_id, $agent = false) {
    $conversation_id = mc_db_escape($conversation_id, true);
    $user_id = mc_isset(mc_db_get($agent ? ('SELECT A.id FROM mc_users A, mc_messages B WHERE A.id = B.user_id AND (A.user_type = "admin" OR A.user_type = "agent") AND B.conversation_id = ' . $conversation_id . ' GROUP BY A.id') : ('SELECT A.id, A.email FROM mc_users A, mc_conversations B WHERE A.id = B.user_id AND B.id = ' . $conversation_id)), 'id');
    return $user_id ? mc_get_user($user_id) : false;
}

function mc_get_agents_ids($admins = true) {
    $agents_ids = mc_db_get('SELECT id FROM mc_users WHERE user_type = "agent"' . ($admins ? ' OR user_type = "admin"' : ''), false);
    for ($i = 0; $i < count($agents_ids); $i++) {
        $agents_ids[$i] = intval($agents_ids[$i]['id']);
    }
    return $agents_ids;
}

function mc_get_avatar($first_name, $last_name = '') {
    $picture_url = MC_URL . '/media/user.svg';
    $first_char_last_name = mb_substr($last_name, 0, 1);
    if (!empty($first_name) && $first_char_last_name != '#') {
        $file_name = rand(99, 9999999) . '.png';
        $t = 'https://ui-avatars.com/api/?background=random&size=512&font-size=0.35&name=' . urlencode($first_name) . '+' . urlencode($last_name);
        $picture_url = mc_download_file('https://ui-avatars.com/api/?background=random&size=512&font-size=0.35&name=' . urlencode($first_name) . '+' . urlencode($last_name), $file_name);
        if (!mc_get_multi_setting('amazon-s3', 'amazon-s3-active') && !defined('MC_CLOUD_AWS_S3')) {
            $path = mc_upload_path(false, true) . '/' . $file_name;
            if (!file_exists($path) || filesize($path) < 1000) {
                $picture_url = MC_URL . '/media/user.svg';
            }
        }
    }
    return $picture_url;
}

function mc_get_users_with_details($details, $user_ids = false) {
    $response = [];
    $primary_details = ['first_name', 'last_name', 'email', 'profile_image', 'department'];
    if ($user_ids == 'all') {
        $user_ids = false;
    } else if ($user_ids == 'agents') {
        $user_ids = mc_get_agents_ids();
    } else if ($user_ids) {
        $user_ids = '(' . (is_string($user_ids) ? str_replace(' ', '', mc_db_escape($user_ids)) : mc_db_escape(substr(json_encode($user_ids), 1, -1))) . ')';
    }
    for ($i = 0; $i < count($details); $i++) {
        $detail = mc_db_escape($details[$i]);
        if (empty($response[$detail])) {
            $primary = in_array($detail, $primary_details);
            if ($primary) {
                $query = 'SELECT id, ' . $detail . ' AS `value` FROM mc_users WHERE ' . $detail . ' IS NOT NULL AND ' . $detail . ' <> ""' . ($user_ids ? ' AND id IN ' . $user_ids : '');
            } else {
                $query = 'SELECT user_id AS `id`, value FROM mc_users_data WHERE slug = "' . $detail . '"' . ($user_ids ? ' AND user_id IN ' . $user_ids : '');
            }
            $response[$detail] = mc_db_get($query, false);
        }
    }
    return $response;
}

function mc_get_active_user_ID() {
    $active_user = mc_get_active_user();
    return $active_user ? mc_isset($active_user, 'id') : false;
}

function mc_is_typing($user_id, $conversation_id) {
    $typing = mc_db_get('SELECT COUNT(*) as typing FROM mc_users WHERE id = ' . mc_db_escape($user_id, true) . ' AND typing = "' . mc_db_escape($conversation_id, true) . '"');
    return $typing['typing'] != 0;
}

function mc_is_agent_typing($conversation_id) {
    return mc_db_get('SELECT id, first_name, last_name FROM mc_users WHERE typing = ' . mc_db_escape($conversation_id, true) . ' AND (user_type = "agent" OR user_type = "admin") AND id <> ' . mc_get_active_user_ID());
}

function mc_set_typing($user_id = false, $conversation_id = false, $source = false) {
    if ($source && isset($source[0])) {
        if ($source[0] == 'fb') {
            return mc_messenger_set_typing($source[1], $source[2]);
        }
        if ($source[0] == 'tw') {
            return mc_twitter_set_typing($source[1]);
        }
        return false;
    } else {
        return mc_pusher_active() ? mc_pusher_trigger('private-user-' . $user_id, 'client-typing') : mc_db_query('UPDATE mc_users SET typing = ' . mc_db_escape($conversation_id, true) . ' WHERE id = ' . mc_db_escape($user_id, true));
    }
}

function mc_set_rating($conversation_id, $user_id, $rating, $message = false, $agent_id = false) {
    $payload = ['rating' => $rating];
    if ($agent_id) {
        $payload['agent_id'] = $agent_id;
    }
    if ($message) {
        $payload['message'] = $message;
    }
    $message = '[rating value="' . $rating . '"' . ($message ? ' message="' . mc_rich_value($message) . '"' : '') . ']';
    $ratings = mc_get_external_setting('ratings', []);
    $ratings[$conversation_id] = $payload;
    mc_save_external_setting('ratings', $ratings);
    return mc_db_query('INSERT INTO mc_messages(user_id, message, payload, creation_time, status_code, conversation_id) VALUES(' . mc_db_escape($user_id, true) . ', "' . mc_db_escape($message) . '", "' . mc_db_json_escape($payload) . '", "' . mc_gmt_now() . '", 0, ' . $conversation_id . ')');
}

function mc_get_rating($agent_id) {
    $ratings = mc_get_external_setting('ratings');
    $positive = 0;
    $negative = 0;
    if (!empty($ratings)) {
        foreach ($ratings as $rating) {
            if (mc_isset($rating, 'agent_id', -1) == $agent_id) {
                if ($rating['rating'] == 1) {
                    $positive++;
                } else {
                    $negative++;
                }
            }
        }
    }
    return [$positive, $negative];
}

function mc_split_name($name) {
    $space_in_name = strpos($name, ' ');
    return [$space_in_name ? trim(substr($name, 0, $space_in_name)) : $name, $space_in_name ? trim(substr($name, $space_in_name)) : ''];
}

function mc_ip_info($fields) {
    $ip = isset($_SERVER['HTTP_CF_CONNECTING_IP']) && substr_count($_SERVER['HTTP_CF_CONNECTING_IP'], '.') == 3 ? $_SERVER['HTTP_CF_CONNECTING_IP'] : $_SERVER['REMOTE_ADDR'];
    if (strlen($ip) > 6) {
        $ip_data = json_decode(mc_download('http://ip-api.com/json/' . $ip . '?fields=' . $fields), true);
        if (!empty($ip_data)) {
            $ip_data['ip'] = $ip;
            return $ip_data;
        }
    }
    return false;
}
function mc_users_get_fields() {
    $additional_fields = mc_get_setting('user-additional-fields', []);
    $fields = [
        ['name' => 'Address', 'id' => 'address'],
        ['name' => 'City', 'id' => 'city'],
        ['name' => 'Country', 'id' => 'country'],
        ['name' => 'Postal code', 'id' => 'postal_code'],
        ['name' => 'State', 'id' => 'state'],
        ['name' => 'Phone', 'id' => 'phone'],
        ['name' => 'Language', 'id' => 'language'],
        ['name' => 'Birthdate', 'id' => 'birthdate'],
        ['name' => 'Company', 'id' => 'company'],
        ['name' => 'Website', 'id' => 'website']
    ];
    for ($i = 0; $i < count($additional_fields); $i++) {
        $value = $additional_fields[$i];
        if ($value['extra-field-name']) {
            array_push($fields, ['id' => $value['extra-field-slug'], 'name' => $value['extra-field-name']]);
        }
    }
    return $fields;
}

/*
 * -----------------------------------------------------------
 * ONLINE STATUS
 * -----------------------------------------------------------
 *
 * 1. Update the user last activity date
 * 2. Check if a date is considered online
 * 3. Check if at least one agent or admin is online
 * 4. Return the online users
 * 5. Return an array with the IDs of the online users
 * 6. Check if a user is online
 *
 */

function mc_update_users_last_activity($user_id = -1, $return_user_id = -1, $check_slack = false) {
    $result = $user_id != -1 ? mc_update_user_value($user_id, 'last_activity', mc_gmt_now()) : false;
    if ($return_user_id != -1) {
        $last_activity = mc_db_get('SELECT last_activity FROM mc_users WHERE id = ' . mc_db_escape($return_user_id, true));
        if (!isset($last_activity['last_activity'])) {
            return 'offline';
        }
        if (mc_is_online($last_activity['last_activity'])) {
            return 'online';
        } else {
            return defined('MC_SLACK') && $check_slack ? mc_slack_presence($return_user_id) : 'offline';
        }
    }
    return $result;
}

function mc_is_online($datetime) {
    return strtotime($datetime) > mc_gmt_now(30, true);
}

function mc_agents_online() {
    $online = $online = mc_pusher_active() ? mc_pusher_agents_online() : intval(mc_db_get('SELECT COUNT(*) as count FROM mc_users WHERE (user_type = "agent" OR user_type = "admin") AND last_activity > "' . mc_gmt_now(30) . '"')['count']) > 0;
    return $online ? true : (defined('MC_SLACK') && mc_get_setting('slack-active') ? mc_slack_presence() == 'online' : false);
}

function mc_get_online_users($sorting = 'creation_time', $agents = false) {
    $online_user_ids = mc_get_online_user_ids($agents);
    return empty($online_user_ids) ? [] : mc_get_users([$sorting, 'DESC'], $agents ? ['admin', 'agent'] : [], '', 0, false, $online_user_ids);
}

function mc_get_online_user_ids($agents = false) {
    $user_ids = [];
    $query = 'SELECT id FROM mc_users WHERE (' . ($agents ? ($agents === true ? 'user_type = "admin" OR user_type = "agent"' : 'user_type = "' . $agents . '"') : 'user_type = "visitor" OR user_type = "lead" OR user_type = "user"') . ')';
    if (mc_pusher_active()) {
        $users = mc_db_get($query, false);
        $users_id_check = [];
        $pusher_users = mc_pusher_get_online_users();
        for ($i = 0; $i < count($users); $i++) {
            array_push($users_id_check, $users[$i]['id']);
        }
        for ($i = 0; $i < count($pusher_users); $i++) {
            $id = $pusher_users[$i]->id;
            if (in_array($id, $users_id_check)) {
                array_push($user_ids, $id);
            }
        }
    } else {
        $users = mc_db_get($query . ' AND last_activity > "' . mc_gmt_now(30) . '"', false);
        for ($i = 0; $i < count($users); $i++) {
            array_push($user_ids, $users[$i]['id']);
        }
    }
    return $user_ids;
}

function mc_is_user_online($user_id) {
    if (empty($user_id)) {
        return false;
    }
    if (mc_pusher_active()) {
        $users = mc_pusher_get_online_users();
        for ($i = 0; $i < count($users); $i++) {
            if ($users[$i]->id == $user_id) {
                return true;
            }
        }
    } else {
        $user = mc_db_get('SELECT last_activity, user_type FROM mc_users WHERE id = ' . mc_db_escape($user_id, true));
        if (isset($user['last_activity']) && mc_is_online($user['last_activity'])) {
            return true;
        }
    }
    if (defined('MC_SLACK') && mc_get_setting('slack-active') && isset($user['user_type']) && mc_is_agent($user['user_type'])) {
        if (mc_slack_presence($user_id) == 'online')
            return true;
    }
    return false;
}

function mc_get_user_by($by, $value) {
    $query = SELECT_FROM_USERS . ' FROM mc_users A WHERE ';
    if (empty($value))
        return false;
    $value = mc_db_escape($value);
    switch ($by) {
        case 'email':
            return mc_db_get($query . 'email = "' . $value . '" LIMIT 1');
        case 'first_name':
            return mc_db_get($query . 'first_name = "' . $value . '" LIMIT 1');
        case 'last_name':
            return mc_db_get($query . 'last_name = "' . $value . '" LIMIT 1');
        case 'phone':
            return mc_db_get($query . 'id IN (SELECT user_id FROM mc_users_data WHERE slug = "phone" AND (value = "' . $value . '" OR value = "' . (strpos($value, '+') === false ? ('+' . $value) : (str_replace('+', '00', $value))) . '")) LIMIT 1');
        default:
            return mc_db_get($query . 'id IN (SELECT user_id FROM mc_users_data WHERE slug = "' . mc_db_escape($by) . '" AND value = "' . $value . '") LIMIT 1');
    }
}

function mc_import_users($url) {
    $rows = mc_csv_read($url);
    foreach ($rows as $row) {
        $user_data = [];
        foreach ($row as $key => $value) {
            $slug_key = mc_string_slug($key);
            if (in_array($slug_key, ['first-name', 'last-name', 'profile-image'])) {
                $slug_key = str_replace('-', '_', $slug_key);
            }
            $user_data[$slug_key] = $value;
        }
        mc_add_user($user_data, $user_data);
    }
    return true;
}

/*
 * -----------------------------------------------------------
 * QUEUE AND ROUTING
 * -----------------------------------------------------------
 *
 * 1. Update the queue and return the current queue status
 * 2. Internal function
 * 3. Assign the conversation to an agent
 * 4. Assigne all unassigned conversations to the active agent
 * 5. Route conversations to agents
 * 6. Find the best agent to assign a conversation
 * 7. Check if routing is active
 *
 */

function mc_queue($conversation_id, $department = false, $is_send = true) {
    $position = 0;
    $queue_db = mc_get_external_setting('queue', []);
    $settings = mc_get_setting('queue');
    $queue = [];
    $index = 0;
    $unix_now = time();
    $unix_min = strtotime('-1 minutes');
    $conversation = mc_db_get('SELECT user_id, agent_id, source FROM mc_conversations WHERE id = ' . mc_db_escape($conversation_id, true));
    $show_progress = !mc_execute_bot_message('offline', 'check');
    $message = false;
    if (!empty(mc_isset($conversation, 'agent_id'))) {
        return 0;
    }
    if (!mc_isset_num($department) || $department == -1) {
        $department = false;
    }
    for ($i = 0; $i < count($queue_db); $i++) {
        if ($unix_min < intval($queue_db[$i][1])) {
            if ($queue_db[$i][0] == $conversation_id) {
                array_push($queue, [$conversation_id, $unix_now, $department]);
                $position = $index + 1;
            } else {
                array_push($queue, $queue_db[$i]);
            }
            if (!$department || $department == $queue_db[$i][2]) {
                $index++;
            }
        }
    }
    if (count($queue) == 0 || $position == 1) {
        $agent_id = mc_routing_find_best_agent($department, mc_isset($settings, 'queue-concurrent-chats', 5));
        if ($agent_id !== false) {
            mc_routing_assign_conversation($agent_id, $conversation_id);
            array_shift($queue);
            $position = 0;
            $user_id = $conversation['user_id'];
            $message = mc_t(mc_isset($settings, 'queue-message-success', 'It\'s your turn! An agent will reply to you shortly.'));
            $message = $is_send ? [$message, mc_send_message(mc_get_bot_id(), $conversation_id, $message, [], 2)['id']] : false;
            mc_send_agents_notifications(mc_isset(mc_get_last_message($conversation_id, false, $user_id), 'message'), false, $conversation_id);
        } else if ($position == 0) {
            array_push($queue, [$conversation_id, $unix_now, $department]);
            $position = $index + 1;
        }
    } else if ($position == 0) {
        array_push($queue, [$conversation_id, $unix_now, $department]);
        $position = $index + 1;
    }
    mc_save_external_setting('queue', $queue);
    return [$position, $show_progress, $message];
}

function mc_queue_check_and_run($conversation_id, $department, $source = false) {
    if (mc_get_multi_setting('queue', 'queue-active')) {
        $continue = !defined('MC_DIALOGFLOW') || !mc_chatbot_active(false, true, $source ? 'ig' : 'fb') || !mc_dialogflow_get_human_takeover_settings()['active'];
        if (!$continue) {
            $conversation = mc_db_get('SELECT agent_id, status_code FROM mc_conversations WHERE id = ' . $conversation_id);
            $continue = empty($conversation['agent_id']) && $conversation['status_code'] == 2;
        }
        if ($continue) {
            return mc_queue($conversation_id, $department, false);
        }
    }
    return false;
}

function mc_routing_and_department_db($table_name = 'mc_conversations', $users = false) {
    $hide = mc_get_multi_setting('agent-hide-conversations', 'agent-hide-conversations-active');
    $routing = mc_is_agent(false, true, false, true) && (mc_routing_is_active() || $hide);
    $routing_unassigned = $routing && $hide && mc_get_multi_setting('agent-hide-conversations', 'agent-hide-conversations-view');
    $department = mc_get_agent_department();
    $query = ($routing ? (' AND (' . $table_name . '.agent_id = ' . mc_get_active_user_ID() . ($routing_unassigned ? (' OR (' . $table_name . '.agent_id IS NULL OR ' . $table_name . '.agent_id = 0))') : ')')) : '') . ($department !== false ? ' AND ' . $table_name . '.department = ' . $department : '');
    return $query ? ($users ? ' AND (' . ($department !== false ? 'department = ' . $department . ' OR ' : '') . 'id IN (SELECT user_id FROM ' . $table_name . ' WHERE ' . substr($query, 4) . '))' : $query) : '';
}

function mc_routing_assign_conversation($agent_id, $conversation_id = false) {
    return mc_db_query('UPDATE mc_conversations SET agent_id = ' . (is_null($agent_id) ? 'NULL' : mc_db_escape($agent_id, true)) . ' WHERE id = ' . mc_db_escape($conversation_id, true));
}

function mc_routing_assign_conversations_active_agent($is_queue = false) {
    $active_user = mc_get_active_user();
    if ($active_user && mc_is_agent($active_user, true, false, true)) {
        $department = mc_get_agent_department();
        return mc_db_query('UPDATE mc_conversations SET agent_id = "' . $active_user['id'] . '" WHERE (agent_id = 0 OR agent_id IS NULL)' . ($department !== false && $department !== '' ? ' AND department = ' . $department : '') . ($is_queue ? ' AND source <> "" AND source IS NOT NULL' : ''));
    }
    return false;
}

function mc_routing($conversation_id = false, $department = false, $unassigned = false) {
    $agent_id = mc_routing_find_best_agent($department);
    if ($agent_id) {
        return $conversation_id == -1 || !$conversation_id ? $agent_id : mc_routing_assign_conversation($agent_id, $conversation_id);
    } else if ($unassigned) {
        return $conversation_id ? mc_routing_assign_conversation(null, $conversation_id) : null;
    }
    return false;
}

function mc_routing_find_best_agent($department = false, $is_ignore_count = false) {
    $department = mc_db_escape($department);
    $online_agents_ids = mc_get_multi_setting('routing', 'routing-disable-status-check') ? mc_get_agents_ids(false) : mc_get_online_user_ids('agent');
    $is_queue = mc_get_multi_setting('queue', 'queue-active');
    $concurrent_chats = $is_queue && !$is_ignore_count ? mc_get_multi_setting('queue', 'queue-concurrent-chats') : 9999;
    if (!empty($online_agents_ids)) {
        $best_online_agent = mc_db_get('SELECT u.id, COUNT(c.id) AS count FROM mc_users u LEFT JOIN mc_conversations c ON c.agent_id = u.id  AND c.status_code IN (0, 1, 2)' . ($department ? ' AND c.department = ' . intval($department) : '') . ' WHERE u.id IN (' . implode(', ', $online_agents_ids) . ') GROUP BY u.id ORDER BY count LIMIT 1');
        if (!empty($best_online_agent) && $best_online_agent['count'] < $concurrent_chats) {
            return $best_online_agent['id'];
        }
    }
    return false;
}

function mc_routing_is_active() {
    return mc_get_multi_setting('queue', 'queue-active') || mc_get_multi_setting('routing', 'routing-active'); 
}

?>