﻿
/*
* 
* ===================================================================
* CLOUD FILE FOR MASI CHAT ADMIN AREA
* ===================================================================
*
*/

(function ($) {
    var admin;
    var CLOUD_URL;
    var is_settings_loaded = false;

    var MCCloud = {
        shopify_products_box: false,
        shopify_products_box_ul: false,

        removeAdminID: function (ids) {
            let index = ids.indexOf(MC_ADMIN_SETTINGS.cloud.id);
            if (index != -1) {
                ids.splice(index, 1);
            }
            return ids;
        },

        creditsAlert: function (element, e) {
            let id = $(element).closest('[id]').attr('id');
            if (MC_ADMIN_SETTINGS.credits <= 0 && (((id.includes('google') || id.includes('dialogflow')) && id != 'open-ai-spelling-correction-dialogflow' && admin.find('#google-sync-mode select').val() == 'auto') || (id.includes('open-ai') && admin.find('#open-ai-sync-mode select').val() == 'auto'))) {
                MCAdmin.genericPanel('credits-panel', 'Credits required', '<p>' + mc_('To use the {R} feature in automatic sync mode, credits are required. If you don\'t want to buy credits, switch to manual sync mode and use your own API key.').replace('{R}', '<b>' + $(element).prev().html() + '</b>') + '</p>', [['Buy credits', 'plus']]);
                MCAdmin.settings.input.reset(element);
                e.preventDefault();
                return true;
            }
            return false;
        },

        creditsAlertQuota: function () {
            let docs = admin.find('.mc-docs').attr('href');
            MCAdmin.infoBottom(mc_('You have used all of your credits. Add more credits {R}.').replace('{R}', '<a href="account?tab=membership#credits">' + mc_('here') + '</a>') + (docs ? '<a href="' + docs + '#cloud-credits" target="_blank" class="mc-icon-link"><i class="mc-icon-help"></i></a>' : ''), 'error');
        },

        shopify: {

            conversationPanel: function () {
                let code = '';
                let shopify_id = MCF.activeUser().getExtra('shopify_id');
                if (!this.panel) {
                    this.panel = admin.find('.mc-panel-shopify');
                }
                if ((shopify_id || (MC_ADMIN_SETTINGS.shopify_shop && MCF.activeUser().getExtra('current_url') && MCF.activeUser().getExtra('current_url').value.includes(MC_ADMIN_SETTINGS.shopify_shop))) && !MCAdmin.loading(this.panel)) {
                    MCF.ajax({
                        function: 'shopify-get-conversation-details',
                        shopify_id: shopify_id ? shopify_id.value : false
                    }, (response) => {
                        code = `<i class="mc-icon-refresh"></i><h3>Shopify</h3><div><div class="mc-split"><div><div class="mc-title">${mc_('Number of orders')}</div><span>${response.orders_count} ${mc_('orders')}</span></div><div><div class="mc-title">${mc_('Total spend')}</div><span>${response.total}</span></div></div><div class="mc-title">${mc_('Cart')}</div><div class="mc-list-items mc-list-links mc-shopify-cart">`;
                        if (response.cart.items) {
                            for (var i = 0; i < response.cart.items.length; i++) {
                                let product = response.cart.items[i];
                                code += `<a href="${product.url}" target="_blank" data-id="${product.id}"><span>${product.quantity} x</span><span>${product.title}</span><span>${product.price}</span></a>`;
                            }
                        }
                        code += (response.cart.items && response.cart.items.length ? '' : '<p>' + mc_('The cart is currently empty.') + '</p>') + '</div>';
                        if (response.orders.length) {
                            code += `<div class="mc-title">${mc_('Orders')}</div><div class="mc-list-items mc-shopify-orders mc-accordion">`;
                            for (var i = 0; i < response.orders.length; i++) {
                                let order = response.orders[i];
                                let id = order.id;
                                let items = order.items;
                                code += `<div data-id="${id}"><span><span>#${id}</span><span>${order.price}</span><span>${MCF.beautifyTime(order.date, true)}</span><a href="${order.url}" target="_blank" class="mc-icon-next"></a></span><div>`;
                                for (var j = 0; j < items.length; j++) {
                                    code += `<a data-product-id="${items[j].id}"><span>${items[j].quantity} x</span> <span>${items[j].name}</span></a>`;
                                }
                                for (var j = 0; j < 2; j++) {
                                    let key = j == 0 ? 'shipping' : 'billing';
                                    if (order[key + '_address']) {
                                        code += `<div class="mc-title">${mc_((j == 0 ? 'Shipping' : 'Billing') + ' address')}</div><div class="mc-multiline">${order[key + '_address'].replace(/\n/g, '<br>')}</div>`;
                                    }
                                }
                                code += `<span data-status="${order.status}">${order.status}</span></div></div>`;
                            }
                            code += '</div>';
                        }
                        $(this.panel).html(code).mcLoading(false);
                        MCAdmin.collapse(this.panel, 160);
                    });

                } else {
                    $(this.panel).html(code);
                }
            }
        },
    }

    window.MCCloud = MCCloud;

    function manualSyncSettingsVisibility(setting_name, show = true) {
        let selectors = { google: '#google-client-id, #google-client-secret, #google-refresh-token', 'open-ai': '#open-ai-key', 'whatsapp-cloud': '#whatsapp-twilio-btn, #whatsapp-cloud-key', 'messenger': '#messenger-key, #messenger-path-btn' };
        let selectors_hide = { 'whatsapp-cloud': '#whatsapp-cloud-sync-btn, #whatsapp-cloud-reconnect-btn' };
        let items = admin.find(selectors[setting_name]);
        let items_hide = admin.find(selectors_hide[setting_name]);
        items.mcActive(show);
        items_hide.mcActive(!show);
        if (!show) {
            items.each(function () {
                $(this).find('input').val('');
            });
        }
    }

    function mc_(text) {
        return MC_TRANSLATIONS && text in MC_TRANSLATIONS ? MC_TRANSLATIONS[text] : text;
    }

    function meta_sync(whatsapp = true) {
        let config = whatsapp ? { config_id: MC_CLOUD_WHATSAPP.configuration_id, response_type: 'code', override_default_response_type: true } : { config_id: MC_CLOUD_MESSENGER.configuration_id, response_type: 'token' };
        FB.logout();
        FB.login(function (response) {
            response = response.authResponse ? response.authResponse : false;
            if (response && ((whatsapp && response.code) || (!whatsapp && response.accessToken))) {
                let button = admin.find(whatsapp ? '#whatsapp-cloud-sync-btn a' : '#messenger-sync-btn a');
                if (MCAdmin.loading(button)) {
                    return;
                }
                ajax(whatsapp ? 'whatsapp-sync' : 'messenger-sync', { access_token: response.accessToken, code: response.code }, (response) => {
                    button.mcLoading(false);
                    if (response && ((whatsapp && response.access_token) || (!whatsapp && Array.isArray(response) && response.length))) {
                        let repeater = admin.find(whatsapp ? '#whatsapp-cloud-numbers' : '#messenger-pages');
                        let repeater_items = repeater.find('.repeater-item');
                        let count_start = repeater_items.length;
                        let index = 0;
                        if (count_start == 1 && !repeater_items.eq(0).find('input').eq(0).val()) {
                            count_start = 0;
                        }
                        let count_end = (whatsapp ? response.phone_numbers.length : response.length) + count_start;
                        let existing_items = repeater.find(whatsapp ? '[data-id=whatsapp-cloud-numbers-phone-id]' : '[data-id=messenger-page-id]').map(function () { return $(this).val() }).get();
                        for (var i = count_start; i < count_end; i++) {
                            if (!existing_items.includes(whatsapp ? response.phone_numbers[index] : response[index].page_id)) {
                                if (i >= repeater_items.length) {
                                    repeater.find('.mc-repeater-add').click();
                                    repeater_items = repeater.find('.repeater-item');
                                }
                                let repeater_item = repeater_items.last();
                                if (whatsapp) {
                                    repeater_item.find('[data-id=whatsapp-cloud-numbers-phone-id]').val(response.phone_numbers[index]);
                                    repeater_item.find('[data-id=whatsapp-cloud-numbers-token]').val(response.access_token);
                                    repeater_item.find('[data-id=whatsapp-cloud-numbers-account-id]').val(response.waba_id);
                                } else {
                                    repeater_item.find('[data-id=messenger-page-name]').val(response[index].name);
                                    repeater_item.find('[data-id=messenger-page-id]').val(response[index].page_id);
                                    repeater_item.find('[data-id=messenger-page-token]').val(response[index].access_token);
                                    repeater_item.find('[data-id=messenger-instagram-id]').val(response[index].instagram);
                                }
                            }
                            index++;
                        }
                        MCAdmin.settings.save();
                        MCAdmin.infoBottom('Synchronization completed.');
                    } else {
                        console.error(response);
                    }
                });
            }
        }, config);
    }

    function ajax(function_name, data = {}, onSuccess = false) {
        $.extend(data, { function: function_name });
        $.ajax({
            method: 'POST',
            url: 'account/ajax.php',
            data: data
        }).done((response) => {
            if (onSuccess) {
                onSuccess(response === false ? false : JSON.parse(response));
            }
        });
    }

    function scrollPagination(area, check = false, offset = 0) {
        if (check) return $(area).scrollTop() + $(area).innerHeight() >= ($(area)[0].scrollHeight - 1);
        $(area).scrollTop($(area)[0].scrollHeight - offset);
    }

    $(document).ready(function () {
        admin = $('.mc-admin');
        CLOUD_URL = MC_URL.substring(0, MC_URL.substring(0, MC_URL.length - 2).lastIndexOf('/'));

        // Disable apps for free plan
        if (DISABLE_APPS == 'true' && (!MC_CLOUD_MEMBERSHIP || MC_CLOUD_MEMBERSHIP == '0' || MC_CLOUD_MEMBERSHIP == 'free')) {
            admin.find('#tab-messenger,#tab-whatsapp,#tab-twitter,#tab-telegram,#tab-wechat,#tab-viber,#tab-line').hide();
            admin.find('[data-app="messenger"],[data-app="whatsapp"],[data-app="twitter"],[data-app="telegram"],[data-app="wechat"],[data-app="viber"],[data-app="line"],[data-app="zalo"]').addClass('mc-disabled');
        }

        // Shopify
        if (MC_ADMIN_SETTINGS.shopify_shop) {
            admin.find('.mc-btn-saved-replies').after(`<div class="mc-btn-shopify" data-mc-tooltip="${mc_('Add Shopify product')}"></div>`);
            admin.find('.mc-editor').append(`<div class="mc-popup mc-shopify-products"><div class="mc-header"><div class="mc-select"><p data-value="">${mc_('All')}</p><ul class="mc-scroll-area"></ul></div><div class="mc-search-btn"><i class="mc-icon mc-icon-search"></i><input type="text" placeholder="${mc_('Search ...')}" /></div></div><div class="mc-shopify-products-list mc-list-thumbs mc-scroll-area"><ul class="mc-loading"></ul></div><i class="mc-icon-close mc-popup-close"></i></div>`);
            MCCloud.shopify_products_box = admin.find('.mc-shopify-products');
            MCCloud.shopify_products_box_ul = MCCloud.shopify_products_box.find(' > div > ul');
        }

        $(document).on('MCSettingsLoaded', function (e, settings) {
            is_settings_loaded = true;

            // Credits and sync mode
            let settings_check = [['google', 'client-id'], ['open-ai', 'key'], ['whatsapp-cloud', 'key']];
            for (var i = 0; i < settings_check.length; i++) {
                let key = settings_check[i][0];
                if (settings[key] && ((settings[key][0][key + '-sync-mode'] && settings[key][0][key + '-sync-mode'][0] == 'manual') || settings[key][0][key + '-' + settings_check[i][1]][0])) {
                    manualSyncSettingsVisibility(key);
                    admin.find('#' + key + '-sync-mode select').val('manual');
                }
            }
            for (var key in MC_AUTO_SYNC) {
                if (!MC_AUTO_SYNC[key]) {
                    let element = admin.find('#' + key + '-sync-mode');
                    element.find('select').val('manual');
                    element.addClass('mc-hide');
                    manualSyncSettingsVisibility(key, true);
                }
            }
        });

        // Credits and sync mode
        $(admin).on('change', '#google-sync-mode select, #open-ai-sync-mode select, #whatsapp-cloud-sync-mode select', function () {
            manualSyncSettingsVisibility($(this).parent().attr('id').replace('-sync-mode', ''), $(this).val() == 'manual');
            MCAdmin.infoBottom('Save changes to apply new sync mode.', 'info');
        });

        $(admin).on('click', '#open-ai-active input, #open-ai-spelling-correction input, #open-ai-spelling-correction-dialogflow input, #open-ai-rewrite input, #open-ai-speech-recognition input, #mc-train-chatbot, #dialogflow-sync-btn .mc-btn, #dialogflow-active input, #google-multilingual input, #google-multilingual-translation input, #google-translation input, #google-language-detection input', function (e) {
            if (MCCloud.creditsAlert(this, e)) {
                return false;
            }
        });

        $(admin).on('change', '#open-ai-mode select', function () {
            if ($(this).val() == 'assistant') {
                admin.find('#open-ai-sync-mode select').val('manual');
                admin.find('#open-ai-key').mcActive(true);
            }
        });

        $(admin).on('change', '#open-ai-sync-mode select', function () {
            if ($(this).val() == 'auto') {
                let select = admin.find('#open-ai-mode select');
                if (select.val() == 'assistant') {
                    select.val('');
                }
                admin.find('#open-ai-assistant-id').mcActive(false);
            }
        });

        if (MC_ADMIN_SETTINGS.credits_required) {
            MCCloud.creditsAlertQuota();
        }

        // WhatsApp and Messenger
        let is_meta_sdk_loaded = false;
        $(admin).on('click', '#whatsapp-cloud-sync-btn .mc-btn, #whatsapp-cloud-reconnect-btn .mc-btn, #messenger-sync-btn a', function (e) {
            let id = $(this).parent().attr('id');
            let is_whatsapp = id != 'messenger-sync-btn';
            let reconnect = id == 'whatsapp-cloud-reconnect-btn' ? { scope: 'whatsapp_business_messaging, whatsapp_business_management, business_management' } : false;
            if (is_meta_sdk_loaded) {
                if (reconnect) {
                    FB.login(() => { }, reconnect);
                } else {
                    meta_sync(is_whatsapp);
                }
            } else {
                window.fbAsyncInit = function () {
                    FB.init(is_whatsapp ? { appId: MC_CLOUD_WHATSAPP.app_id, autoLogAppEvents: true, xfbml: true, version: 'v18.0' } : { appId: MC_CLOUD_MESSENGER.app_id, cookie: true, xfbml: true, version: 'v18.0' });
                };
                $.getScript('https://connect.facebook.net/en_US/sdk.js', () => {
                    is_meta_sdk_loaded = true;
                    if (reconnect) {
                        FB.login(() => { }, reconnect);
                    } else {
                        meta_sync(is_whatsapp);
                    }
                });
            }
            e.preventDefault()
            return false;
        });

        // Onboarding
        let URL = document.location.href;
        if ((URL.includes('masichat.com') || URL.includes('masi-chat')) && URL.includes('welcome')) {
            admin.find('#mc-settings').click();
            let items = [
                ['Need help?', 'Not sure what to do? Contact us for help. Log in with your existing email and password. We reply in a few hours.', [], '', 'Contact us', 'https://masichat.com/contact'],
                ['Notifications', 'Activate Push and Email notifications to receive alerts for incoming messages. On iPhone, the mobile app is required.', ['t-qzDPG88Xg', 'enb291Aai5Q'], '#notifications', 'Activate'],
                ['Chatbot', 'Activate the OpenAI chatbot and Human Takeover to transfer the chat to an agent when needed.', ['0p2YWQtsglg'], '#optimal-configuration-ai', 'Activate'],
                ['Try out the chat', 'Try out the chat and send a test message to test notifications and the chatbot functionalities.', ['mxjRevd_8bw'], '#widget-hidden', 'Try now', 'https://app.masichat.com/' + MC_ADMIN_SETTINGS.cloud.chat_id],
                ['Mobile app', 'The admin area is a PWA that can be installed on iPhones, Android, and mobile devices.', ['IhoAlXFywFY'], '#pwa', 'Read more', 'https://masichat.com/knowledge-base/#pwa']
            ];
            let code = '<div>';
            let checks = ['push-notifications-active', 'open-ai-active'];
            for (var i = 0; i < items.length; i++) {
                let id = `id="onboarding-${items[i][3].replace('#', '')}"`;
                let video = '';
                for (var j = 0; j < items[i][2].length; j++) {
                    video += `<a href="https://www.youtube.com/watch?v=${items[i][2][j]}" target="_blank"><img src="account/media/play-video.svg"></a>`;
                }
                code += `<div class="mc-setting"><div><h2>${items[i][0]} ${video}<a href="https://masichat.com/docs/${items[i][3]}" target="_blank"><i class="mc-icon-help"></i></a></h2><p>${items[i][1]}</p></div><div>${items[i][5] ? `<a ${id} href="${items[i][5]}" target="_blank" class="mc-btn">${items[i][4]}</a>` : `<div ${id} class="mc-btn">${items[i][4]}</div>`}</div></div>`;
            }
            setTimeout(() => {
                MCAdmin.genericPanel('onboarding', `Welcome ${admin.find('> .mc-header > .mc-admin-nav-right .mc-account .mc-name').html()} 👋`, '<p>Masi Chat is a powerful tool with many options. Let\'s start by activating the basic functionalities below. Don\'t hesitate to reach out to us if you have any questions.</p>' + code + '</div>', [], '', true);
                for (var i = 0; i < checks.length; i++) {
                    if (admin.find('#' + checks[i] + ' input').prop('checked')) {
                        $('.mc-onboarding-box [id].mc-btn').eq(i).mcActive(true);
                    }
                }
                if (admin.find('.mc-admin-list .mc-scroll-area > ul li').length) {
                    $('#onboarding-widget-hidden').mcActive(true);
                }
            }, 1000);
        }

        $(admin).on('click', '#onboarding-notifications', function () {
            if ($(this).mcActive() || MCAdmin.loading(this)) {
                return
            }
            let ids = ['notify-agent-email', 'notify-user-email', 'push-notifications-active'];
            for (var i = 0; i < ids.length; i++) {
                admin.find('#' + ids[i] + ' input').prop('checked', true);
            }
            if (typeof OneSignal != 'undefined') {
                OneSignal.Slidedown.promptPush({ force: true });
            } else {
                MCF.serviceWorker.initPushNotifications();
            }
            $(document).on('MCPushNotificationSubscription', (e, response) => {
                $(this).mcLoading(false);
                if (response.optedIn) {
                    $(this).mcActive(true);
                }
            });
            MCAdmin.settings.save();
        });

        $(admin).on('click', '#onboarding-optimal-configuration-ai', function () {
            admin.find('#open-ai-active input').prop('checked', true);
            admin.find('#dialogflow-human-takeover-active input').prop('checked', true);
            admin.find('#dialogflow-human-takeover-message textarea').val('I\'m a chatbot. Do you want to get in touch with one of our agents?');
            admin.find('#dialogflow-human-takeover-message-confirmation textarea').val('Alright! We will get in touch soon!');
            admin.find('#dialogflow-human-takeover-confirm input').val('Yes');
            admin.find('#dialogflow-human-takeover-cancel input').val('Cancel');
            MCAdmin.settings.save();
            $(this).mcActive(true);
        });

        $(admin).on('click', '#onboarding-widget-hidden', function () {
            if ($(this).mcActive() || MCAdmin.loading(this)) {
                return
            }
            $(document).on('MCAdminNewConversation', () => {
                $(this).mcLoading(false);
                $(this).mcActive(true);
            });
        });

        $(admin).on('click', '#onboarding-pwa', function () {
            $(this).mcActive(true);
        });

        // Shopify
        $(admin).on('click', '[data-product-id]:not([href])', function () {
            MCF.ajax({
                function: 'shopify-get-product-link',
                product_id: $(this).attr('data-product-id')
            }, (response) => {
                $(this).attr('href', response).attr('target', '_blank');
                window.open(response, '_blank');
            });
        });

        $(admin).on('click', '.mc-panel-shopify > i', function () {
            MCCloud.shopify.conversationPanel();
        });

        $(admin).on('click', '.mc-btn-shopify', function () {
            if (MCCloud.shopify_products_box_ul.mcLoading() || (MC_ADMIN_SETTINGS.languages && MCF.activeUser() && MC_ADMIN_SETTINGS.languages.includes(activeUser().language) && MCF.activeUser().language != MCAdmin.apps.itemsPanel.panel_language)) {
                MCAdmin.apps.itemsPanel.populate('shopify');
            }
            MCCloud.shopify_products_box.find('.mc-search-btn').mcActive(true).find('input').get(0).focus();
            MCCloud.shopify_products_box.mcTogglePopup(this);
        });

        $(MCCloud.shopify_products_box).find('.mc-shopify-products-list').on('scroll', function () {
            if (scrollPagination(this, true)) {
                MCAdmin.apps.itemsPanel.pagination(this, 'shopify');
            }
        });

        $(MCCloud.shopify_products_box).on('click', '.mc-select li', function () {
            MCAdmin.apps.itemsPanel.filter(this, 'shopify');
        });

        $(MCCloud.shopify_products_box).on('input', '.mc-search-btn input', function () {
            MCAdmin.apps.itemsPanel.search(this, 'shopify');
        });

        $(MCCloud.shopify_products_box).on('click', '.mc-search-btn i', function () {
            MCF.searchClear(this, () => { MCAdmin.apps.itemsPanel.search($(this).next(), 'shopify') });
        });

        $(MCCloud.shopify_products_box).on('click', '.mc-shopify-products-list li', function () {
            MCChat.insertText(`{shopify product_id="${$(this).data('id')}"}`);
            MCF.deactivateAll();
            admin.removeClass('mc-popup-active');
        });

        // Miscellaneous
        $(admin).on('click', '.mc-admin-nav-right [data-value="account"], #mc-buy-credits', function () {
            document.location = CLOUD_URL + '/account?tab=membership' + ($(this).attr('id') == 'mc-buy-credits' ? '#credits' : '');
        });

        $(admin).on('click', '.mc-btn-app-disable', function () {
            if (MCAdmin.loading(this)) return;
            MCF.ajax({
                function: 'app-disable',
                app_name: $(this).closest('[data-app]').attr('data-app')
            }, (response) => {
                location.reload();
            });
        });
    });
}(jQuery));