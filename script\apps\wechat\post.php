<?php

/*
 * ==========================================================
 * WECHAT APP POST FILE
 * ==========================================================
 *
 * WeChat app post file to receive messages sent by WeChat. � 2017-2025 masichat.com. All rights reserved.
 *
 */

require('../../include/functions.php');
$response = file_get_contents('php://input');

ob_start();
if (isset($_GET['echostr'])) {
    die($_GET['echostr']);
} else {
    echo 'success';
}
header('Connection: close');
header('Content-Length: ' . ob_get_length());
ob_end_flush();
@ob_flush();
flush();
if (function_exists('fastcgi_finish_request')) {
    fastcgi_finish_request();
}
mc_cloud_load_by_url();

$token = mc_get_multi_setting('wechat', 'wechat-token');
$signature = check_signature($token);
if (!$signature) {
    die();
}
$response = simplexml_load_string($response) or die();
$wechat_open_id = (string) $response->FromUserName;
$message_type = trim((string) $response->MsgType);
$message_id = (string) $response->MsgId;
$message = trim((string) $response->Content);
$user_id = false;
if (!$message_id) {
    die('missing-msgid');
}
$GLOBALS['MC_FORCE_ADMIN'] = true;
$user = mc_get_user_by('wechat-id', $wechat_open_id);
if (!$user) {
    $keys = ['city', 'province', 'country'];
    $extra = ['wechat-id' => [$wechat_open_id, 'WeChat ID']];
    $user_details = mc_get('https://api.weixin.qq.com/cgi-bin/user/info?access_token=' . mc_wechat_get_access_token() . '&openid=' . $wechat_open_id, true);
    if (!empty($user_details['headimgurl'])) {
        $user_details['headimgurl'] = mc_download_file($user_details['headimgurl'], $wechat_open_id . '.png');
    }
    if (defined('MC_DIALOGFLOW')) {
        $extra['language'] = mc_google_language_detection_get_user_extra($message);
    }
    for ($i = 0; $i < count($keys); $i++) {
        $key = $keys[$i];
        if (!empty($user_details[$key])) {
            $extra[$key] = [$user_details[$key], mc_string_slug($key, 'string')];
        }
    }
    $user_id = mc_add_user(['first_name' => mc_isset($user_details, 'nickname', ''), 'last_name' => '', 'profile_image' => mc_isset($user_details, 'headimgurl', ''), 'user_type' => 'lead'], $extra);
    $user = mc_get_user($user_id);
} else {
    $user_id = $user['id'];
    $conversation_id = mc_isset(mc_db_get('SELECT id FROM mc_conversations WHERE source = "wc" AND user_id = ' . $user_id . ' ORDER BY id DESC LIMIT 1'), 'id');
}
$GLOBALS['MC_LOGIN'] = $user;
$is_routing = mc_routing_is_active();
if (!$conversation_id) {
    $department = mc_get_setting('wechat-department');
    $conversation_id = mc_isset(mc_new_conversation($user_id, 2, '', $department, $is_routing ? mc_routing_find_best_agent($department) : -1, 'wc'), 'details', [])['id'];
} else if ($is_routing && mc_isset(mc_db_get('SELECT status_code FROM mc_conversations WHERE id = ' . $conversation_id), 'status_code') == 3) {
    mc_update_conversation_agent($conversation_id, mc_routing_find_best_agent($department));
}

// Emoji
if (strpos($message, '/:') !== false) {
    $emojis = json_decode(file_get_contents(MC_PATH . '/apps/wechat/emoji.json'), true);
    for ($i = 0; $i < count($emojis); $i++) {
        if (strpos($message, $emojis[$i][0]) !== false) {
            $message = str_replace($emojis[$i][0], $emojis[$i][1], $message);
            if (strpos($message, '/:') === false)
                break;
        }
    }
}

// Attachments
$attachments = [];
$attachment_url = false;
switch ($message_type) {
    case 'image':
        $attachment_url = mc_download_file($response->PicUrl, $message_id, true);
        break;
    case 'video':
        $attachment_url = mc_download_file('https://api.weixin.qq.com/cgi-bin/media/get?access_token=' . mc_wechat_get_access_token() . '&media_id=' . $response->MediaId, $message_id, true);
        break;
}
if ($attachment_url) {
    array_push($attachments, [basename($attachment_url), $attachment_url]);
}

// Send message
$response = mc_send_message($user_id, $conversation_id, $message, $attachments, false, ['id' => $message_id]);

// Dialogflow, Notifications, Bot messages
$response_extarnal = mc_messaging_platforms_functions($conversation_id, $message, $attachments, $user, ['source' => 'wc', 'platform_value' => $wechat_open_id]);

// Queue
mc_queue_check_and_run($conversation_id, $department, 'wc');

// Online status
mc_update_users_last_activity($user_id);

$GLOBALS['MC_FORCE_ADMIN'] = false;
die();

function check_signature($token) {
    $signature = $_GET['signature'];
    $timestamp = $_GET['timestamp'];
    $nonce = $_GET['nonce'];
    $check = [$token, $timestamp, $nonce];
    sort($check, SORT_STRING);
    $check = implode($check);
    $check = sha1($check);
    return $check == $signature;
}

?>