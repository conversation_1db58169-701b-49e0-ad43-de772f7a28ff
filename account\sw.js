'use strict';

/*
 * ==========================================================
 * SERVICE WORKER
 * ==========================================================
 *
 * Service Worker of Masi Chat admin area
 *
*/

const MC_CACHE_NAME = 'mc-3-3-9';
const MC_OFFLINE = 'resources/pwa/offline.html';
var mc_push_link;
var mc_push_conversation_id;
var mc_push_user_conversation_id;

importScripts('https://js.pusher.com/beams/service-worker.js');

self.addEventListener('install', (event) => {
    event.waitUntil((async () => {
        const cache = await caches.open(MC_CACHE_NAME);
        await cache.add(new Request(MC_OFFLINE, { cache: 'reload' }));
    })());
});

self.addEventListener('activate', (event) => {
    event.waitUntil((async () => {
        if ('navigationPreload' in self.registration) {
            await self.registration.navigationPreload.enable();
        }
    })());
    self.clients.claim();
});

self.addEventListener('fetch', (event) => {
    if (event.request.mode === 'navigate') {
        event.respondWith((async () => {
            try {
                const preloadResponse = await event.preloadResponse;
                if (preloadResponse) {
                    return preloadResponse;
                }
                const networkResponse = await fetch(event.request);
                return networkResponse;
            } catch (error) {
                const cache = await caches.open(MC_CACHE_NAME);
                const cachedResponse = await cache.match(MC_OFFLINE);
                return cachedResponse;
            }
        })());
    }
});

// Pusher
PusherPushNotifications.onNotificationReceived = ({ pushEvent, payload }) => {
    mc_push_link = payload.notification.deep_link;
    mc_push_conversation_id = payload.data.conversation_id;
    mc_push_user_conversation_id = payload.data.user_id;
    pushEvent.waitUntil(self.registration.showNotification(payload.notification.title, { body: payload.notification.body, icon: payload.notification.icon, data: payload.data }));
};

self.addEventListener('notificationclick', function (event) {
    event.notification.close();
    event.waitUntil(clients.matchAll({
        type: 'window',
        includeUncontrolled: true
    }).then((clientList) => {
        for (var i = 0; i < clientList.length; i++) {
            if (clientList[i].url.split('?')[0] == mc_push_link) {
                clientList[i].postMessage({ 'conversation_id': mc_push_conversation_id, 'user_id': mc_push_user_conversation_id });
                return clientList[i].focus();
            }
        }
        if (mc_push_link && clients.openWindow) return clients.openWindow(mc_push_link + '?conversation=' + mc_push_conversation_id);
    }));
});
