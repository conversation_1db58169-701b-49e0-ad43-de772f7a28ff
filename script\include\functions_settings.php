<?php
use UI\Draw\Color;

/*
 * ==========================================================
 * FUNCTIONS_SETTINGS.PHP
 * ==========================================================
 *
 * Settings functions file. © 2017-2025 masichat.com. All rights reserved.
 *
 * -----------------------------------------------------------
 * SETTINGS
 * -----------------------------------------------------------
 * 1. Return the JS settings of the front-end
 * 2. Return the JS settings of admin area
 * 3. Return the JS settings shared by both the admin and the front-end
 * 4. Return the JS settings of block message
 * 5. Populate the admin area with the settings of the file /resources/json/settings.json
 * 6. Pupulate the admin area of the apps
 * 7. Return the HTML code of a setting element
 * 8. Save the all settings and external settings
 * 9. Save an external setting
 * 10. Return the settings array
 * 11. Return all settings and external settings
 * 12. Return the setting with the given name
 * 13. Return a single setting of a multi values setting
 * 14. Return the external setting with the given name
 * 15. Return a multilingual external setting
 * 16. Return the HTML code of the color palette
 * 17. Export all settings and external settings
 * 18. Import all settings and external settings
 * 19. Return the departments array
 * 20. Echo the departments list
 * 21. Check if the current time is within the office hours
 * 22. Generate the CSS with values setted in the settings area
 * 23. Check the system for requirements and issues
 * 24. Countries list
 * 25. Langauges list
 * 26. Phone codes list
 * 27. Get config file settings
 * 28. Update the service worker file
 *
 */

function mc_get_front_settings() {
    global $MC_LANGUAGE;
    mc_updates_validation();
    $active_user = mc_get_active_user();
    $is_office_hours = mc_office_hours();
    if (mc_get_setting('front-auto-translations') && mc_get_active_user()) {
        $MC_LANGUAGE = [mc_get_user_language(mc_get_active_user_id()), 'front'];
    }
    $return = [
        'language' => mc_get_setting('front-auto-translations'),
        'translations' => mc_get_current_translations(),
        'registration_required' => mc_get_setting('registration-required'),
        'registration_otp' => mc_get_setting('registration-otp'),
        'registration_timetable' => mc_get_setting('registration-timetable'),
        'registration_offline' => mc_get_setting('registration-offline'),
        'registration_link' => mc_get_setting('registration-link', ''),
        'registration_details' => mc_get_setting('registration-user-details-success'),
        'visitors_registration' => mc_get_setting('visitors-registration') || mc_get_setting('online-users-notification'),
        'privacy' => mc_get_multi_setting('privacy', 'privacy-active'),
        'popup' => empty($_POST['popup']) ? false : mc_get_block_setting('popup'),
        'follow' => mc_get_multi_setting('follow-message', 'follow-active') && ($is_office_hours || !mc_get_multi_setting('follow-message', 'follow-disable-office-hours')) ? mc_get_multi_setting('follow-message', 'follow-delay', true) : false,
        'popup_mobile_hidden' => mc_get_multi_setting('popup-message', 'popup-mobile-hidden'),
        'welcome' => mc_get_multi_setting('welcome-message', 'welcome-active'),
        'chat_manual_init' => mc_get_setting('chat-manual-init'),
        'chat_login_init' => mc_get_setting('chat-login-init'),
        'sound' => mc_get_multi_setting('sound-settings', 'sound-settings-active') ? ['volume' => mc_get_multi_setting('sound-settings', 'sound-settings-volume', 0.6), 'repeat' => mc_get_multi_setting('sound-settings', 'sound-settings-repeat')] : false,
        'header_name' => mc_get_setting('header-name', ''),
        'desktop_notifications' => mc_get_setting('desktop-notifications') && !mc_get_multi_setting('push-notifications', 'push-notifications-active'),
        'flash_notifications' => mc_get_setting('flash-notifications'),
        'push_notifications_users' => mc_get_multi_setting('push-notifications', 'push-notifications-users-active'),
        'notifications_icon' => mc_is_cloud() ? MC_CLOUD_BRAND_ICON_PNG : mc_get_setting('notifications-icon', MC_URL . '/media/icon.png'),
        'notify_email_cron' => mc_get_setting('notify-email-cron'),
        'bot_id' => mc_get_bot_id(),
        'bot_name' => mc_get_setting('bot-name', 'Bot'),
        'bot_image' => mc_get_setting('bot-image', MC_URL . '/media/user.png'),
        'bot_delay' => mc_get_setting('dialogflow-bot-delay', 3000),
        'dialogflow_active' => mc_chatbot_active(true, false),
        'open_ai_active' => mc_chatbot_active(false, true),
        'slack_active' => defined('MC_SLACK') && mc_get_setting('slack-active'),
        'rich_messages' => mc_get_rich_messages_ids(),
        'display_users_thumb' => mc_get_setting('display-users-thumb'),
        'hide_agents_thumb' => mc_get_setting('hide-agents-thumb'),
        'auto_open' => mc_get_setting('auto-open'),
        'office_hours' => $is_office_hours,
        'disable_office_hours' => mc_get_setting('chat-timetable-disable'),
        'disable_offline' => mc_get_setting('chat-offline-disable'),
        'timetable' => mc_get_multi_setting('chat-timetable', 'chat-timetable-active'),
        'articles' => mc_get_setting('articles-active'),
        'articles_url_rewrite' => mc_is_articles_url_rewrite() ? mc_get_articles_page_url() : false,
        'init_dashboard' => mc_get_setting('init-dashboard') && !mc_get_setting('disable-dashboard'),
        'disable_dashboard' => mc_get_setting('disable-dashboard'),
        'queue' => mc_get_multi_setting('queue', 'queue-active'),
        'hide_conversations_routing' => !mc_get_multi_setting('queue', 'queue-active') && mc_get_multi_setting('agent-hide-conversations', 'agent-hide-conversations-active') && mc_get_multi_setting('agent-hide-conversations', 'agent-hide-conversations-routing'),
        'webhooks' => mc_get_multi_setting('webhooks', 'webhooks-active') ? mc_get_multi_setting('webhooks', 'webhooks-allowed', true) : false,
        'agents_online' => mc_agents_online(),
        'cron' => date('H') != mc_get_external_setting('cron'),
        'cron_email_piping' => mc_email_piping_is_active() && (!mc_is_cloud() && !mc_get_multi_setting('email-piping', 'email-piping-disable-cron')) && date('i') != mc_get_external_setting('cron-email-piping'),
        'cron_email_piping_active' => mc_email_piping_is_active(),
        'wp' => defined('MC_WP'),
        'perfex' => defined('MC_PERFEX'),
        'whmcs' => defined('MC_WHMCS'),
        'aecommerce' => defined('MC_AECOMMERCE'),
        'martfury' => defined('MC_MARTFURY') && mc_get_setting('martfury-private') ? mc_get_setting('martfury-linking') : false,
        'messenger' => defined('MC_MESSENGER'),
        'pusher' => mc_pusher_active(),
        'cookie_domain' => mc_get_setting('cookie-domain'),
        'visitor_default_name' => mc_get_setting('visitor-default-name'),
        'sms_active_agents' => mc_get_multi_setting('sms', 'sms-active-agents'),
        'language_detection' => false,
        'cloud' => mc_is_cloud() ? ['cloud_user_id' => json_decode(mc_encryption($_POST['cloud'], false), true)['user_id']] : false,
        'automations' => mc_automations_run_all(),
        'close_chat' => mc_get_setting('close-chat'),
        'sender_name' => mc_get_setting('sender-name'),
        'tickets' => defined('MC_TICKETS') && !empty($_POST['tickets']) && $_POST['tickets'] != 'false',
        'max_file_size' => mc_get_server_max_file_size(),
        'tickets_hide' => mc_get_setting('tickets-hide'),
        'rating' => mc_get_multi_setting('rating-message', 'rating-active')
    ];
    if ($return['registration_required'] == 'registration-login' && !mc_isset(mc_get_setting('registration-fields'), 'reg-email')) {
        $return['registration_required'] = 'registration';
    }
    if ($return['rating']) {
        $return['rating_message'] = mc_get_multi_setting('rating-message', 'rating-message-area');
    }
    if ($return['articles']) {
        $return['articles_title'] = mc_get_setting('articles-title', '');
        $return['articles_categories'] = mc_get_setting('articles-categories');
    }
    if ($return['welcome']) {
        $return['welcome_trigger'] = mc_get_multi_setting('welcome-message', 'welcome-trigger', 'load');
        $return['welcome_delay'] = mc_get_multi_setting('welcome-message', 'welcome-delay', 2000);
        $return['welcome_disable_office_hours'] = mc_get_multi_setting('welcome-message', 'welcome-disable-office-hours');
    }
    if ($return['queue']) {
        $return['queue_message'] = mc_get_multi_setting('queue', 'queue-message', '');
        $return['queue_response_time'] = mc_get_multi_setting('queue', 'queue-response-time', 5);
        $return['queue_sound'] = mc_get_multi_setting('queue', 'queue-sound');
    }
    if ($return['timetable']) {
        $return['timetable_type'] = mc_get_multi_setting('chat-timetable', 'chat-timetable-type');
        $return['timetable_hide'] = mc_get_multi_setting('chat-timetable', 'chat-timetable-hide');
        $return['timetable_disable_agents'] = mc_get_multi_setting('chat-timetable', 'chat-timetable-agents');
    }
    if ($return['wp']) {
        $return['wp_users_system'] = mc_get_setting('wp-users-system', 'mc');
        $return['wp_registration'] = mc_get_setting('wp-registration');
    }
    if ($return['push_notifications_users']) {
        $return['push_notifications_provider'] = mc_is_cloud() ? 'onesignal' : mc_get_multi_setting('push-notifications', 'push-notifications-provider', 'pusher');
        $return['push_notifications_id'] = mc_is_cloud() ? ONESIGNAL_APP_ID : mc_get_multi_setting('push-notifications', $return['push_notifications_provider'] == 'onesignal' ? 'push-notifications-onesignal-app-id' : 'push-notifications-id');
        $return['push_notifications_url'] = mc_get_multi_setting('push-notifications', 'push-notifications-sw-url');
    }
    if ($return['pusher']) {
        $return['pusher_key'] = mc_pusher_get_details()[0];
        $return['pusher_cluster'] = mc_pusher_get_details()[3];
    }
    if (!empty($return['timetable_hide']) || !empty($return['timetable_type'])) {
        $return['timetable_message'] = [mc_t(mc_get_multi_setting('chat-timetable', 'chat-timetable-title')), mc_t(mc_get_multi_setting('chat-timetable', 'chat-timetable-msg'))];
    }
    if ($return['tickets']) {
        $return['tickets_registration_required'] = mc_get_setting('tickets-registration-required');
        $return['tickets_registration_redirect'] = mc_get_setting('tickets-registration-redirect', '');
        $return['tickets_default_form'] = mc_get_setting('tickets-registration-disable-password') ? 'registration' : mc_get_setting('tickets-default-form', 'login');
        $return['tickets_conversations_title_user'] = mc_get_setting('tickets-conversations-title-user');
        $return['tickets_welcome_message'] = mc_get_multi_setting('tickets-welcome-message', 'tickets-welcome-message-active') ? mc_merge_fields(mc_t(mc_get_multi_setting('tickets-welcome-message', 'tickets-welcome-message-msg'))) : false;
        $return['tickets_conversation_name'] = mc_get_setting('tickets-conversation-name', '');
        $return['tickets_enter_button'] = mc_get_setting('tickets-enter-button');
        $return['tickets_manual_init'] = mc_get_setting('tickets-manual-init');
        $return['tickets_default_department'] = mc_get_setting('tickets-default-department');
        $return['tickets_names'] = mc_get_setting('tickets-names');
        $return['tickets_recaptcha'] = mc_get_multi_setting('tickets-recaptcha', 'tickets-recaptcha-active') ? mc_get_multi_setting('tickets-recaptcha', 'tickets-recaptcha-key') : false;
        $return['tickets_disable_first'] = mc_get_multi_setting('tickets-disable-features', 'tickets-first-ticket');
        $return['tickets_close'] = mc_get_setting('close-ticket');
    }
    if (defined('MC_WOOCOMMERCE')) {
        $return['woocommerce'] = true;
        $return['woocommerce_returning_'] = !in_array(mc_isset($active_user, 'user_type'), ['user', 'agent', 'admin']) && mc_get_multi_setting('wc-returning-visitor', 'wc-returning-visitor-active');
    }
    if ($return['dialogflow_active'] || $return['open_ai_active']) {
        $return['dialogflow_human_takeover'] = mc_get_multi_setting('dialogflow-human-takeover', 'dialogflow-human-takeover-active');
        $return['dialogflow_human_takeover_disable_chatbot'] = mc_get_multi_setting('dialogflow-human-takeover', 'dialogflow-human-takeover-disable-chatbot');
        $return['dialogflow_welcome'] = mc_get_setting('dialogflow-welcome') || mc_get_multi_setting('google', 'dialogflow-welcome'); // Deprecated: mc_get_setting('dialogflow-welcome')
        $return['dialogflow_send_user_details'] = mc_get_setting('dialogflow-send-user-details') || mc_get_multi_setting('google', 'dialogflow-send-user-details'); // Deprecated: mc_get_setting('dialogflow-send-user-details')
        $return['dialogflow_departments'] = mc_get_setting('dialogflow-departments');
        $return['dialogflow_disable_tickets'] = mc_get_setting('dialogflow-disable-tickets');
        $return['dialogflow_office_hours'] = mc_get_setting('dialogflow-timetable');
        $return['flow_on_load'] = mc_flows_on_conversation_start_or_load(false, mc_get_user_language(), false, true);
        $return['open_ai_context_awareness'] = mc_get_multi_setting('open-ai', 'open-ai-context-awareness');
        if ($return['queue'] && $return['dialogflow_human_takeover']) {
            $return['queue'] = false;
            $return['queue_human_takeover'] = true;
        }
        if (mc_get_multi_setting('chatbot-usage-limit', 'chatbot-usage-limit-quota')) {
            $return['chatbot_limit'] = ['quota' => intval(mc_get_multi_setting('chatbot-usage-limit', 'chatbot-usage-limit-quota')), 'interval' => intval(mc_get_multi_setting('chatbot-usage-limit', 'chatbot-usage-limit-interval')), 'message' => mc_get_multi_setting('chatbot-usage-limit', 'chatbot-usage-limit-message')];
        }
    } else if (defined('MC_DIALOGFLOW')) {
        $return['language_detection'] = mc_get_multi_setting('google', 'google-language-detection') || mc_get_multi_setting('dialogflow-language-detection', 'dialogflow-language-detection-active'); // Deprecated: mc_get_multi_setting('dialogflow-language-detection', 'dialogflow-language-detection-active')
        $return['speech_recognition'] = mc_get_multi_setting('open-ai', 'open-ai-speech-recognition');
    }
    if ($active_user) {
        $user_id = $active_user['id'];
        $current_url = false;
        if (!mc_is_agent($active_user)) {
            try {
                $current_url = isset($_POST['current_url']) ? $_POST['current_url'] : $_SERVER['HTTP_REFERER'];
                if ($current_url) {
                    mc_current_url($user_id, $current_url);
                }
            } catch (Exception $e) {
            }
            if ($return['pusher']) {
                mc_pusher_trigger('private-user-' . $user_id, 'init', ['current_url' => $current_url]);
            }
        }
        mc_update_users_last_activity($user_id);
    }
    return $return;
}

function mc_js_admin() {
    $is_cloud = mc_is_cloud();
    $active_user = mc_get_active_user();
    $active_user_type = mc_isset($active_user, 'user_type');
    $is_agent = mc_is_agent($active_user_type, true, false, true);
    $language = mc_get_admin_language();
    $routing_type = mc_get_multi_setting('queue', 'queue-active') ? 'queue' : (mc_get_multi_setting('routing', 'routing-active') ? 'routing' : (mc_get_multi_setting('agent-hide-conversations', 'agent-hide-conversations-active') ? 'hide-conversations' : false));
    $settings = [
        'bot_id' => mc_get_bot_id(),
        'close_message' => mc_get_multi_setting('close-message', 'close-active'),
        'close_message_transcript' => mc_get_multi_setting('close-message', 'close-transcript'),
        'routing' => (!$active_user || $is_agent) && $routing_type ? $routing_type : false,
        'desktop_notifications' => mc_get_setting('desktop-notifications'),
        'push_notifications' => mc_get_multi_setting('push-notifications', 'push-notifications-active'),
        'push_notifications_users' => mc_get_multi_setting('push-notifications', 'push-notifications-users-active'),
        'flash_notifications' => mc_get_setting('flash-notifications'),
        'notifications_icon' => $is_cloud ? MC_CLOUD_BRAND_ICON_PNG : mc_get_setting('notifications-icon', MC_URL . '/media/icon.png'),
        'auto_updates' => mc_get_setting('auto-updates'),
        'sound' => mc_get_multi_setting('sound-settings', 'sound-settings-active-admin') ? ['volume' => mc_get_multi_setting('sound-settings', 'sound-settings-volume-admin', 0.6), 'repeat' => mc_get_multi_setting('sound-settings', 'sound-settings-repeat-admin')] : false,
        'pusher' => mc_pusher_active(),
        'notify_user_email' => mc_get_setting('notify-user-email') || mc_email_piping_is_active(),
        'assign_conversation_to_agent' => $is_agent && mc_get_multi_setting('agent-hide-conversations', 'agent-hide-conversations-active') && mc_get_multi_setting('agent-hide-conversations', 'agent-hide-conversations-view'),
        'allow_agent_delete_message' => $active_user_type == 'admin' || mc_get_multi_setting('agents', 'agents-delete-message'),
        'supervisor' => mc_supervisor(),
        'sms_active_users' => mc_get_multi_setting('sms', 'sms-active-users'),
        'sms' => mc_get_multi_setting('sms', 'sms-user'),
        'now_db' => mc_gmt_now(),
        'login_time' => time(),
        'single_agent' => intval(mc_db_get('SELECT COUNT(*) as count FROM mc_users WHERE user_type = "agent" OR user_type = "admin"')['count']) == 1,
        'slack_active' => mc_get_setting('slack-active'),
        'zendesk_active' => mc_get_setting('zendesk-active'),
        'active_agent_language' => mc_get_user_language(mc_get_active_user_ID()),
        'transcript_message' => mc_get_multi_setting('transcript', 'transcript-message', ''),
        'cookie_domain' => mc_get_setting('cookie-domain'),
        'cloud' => $is_cloud,
        'online_users_notification' => mc_get_setting('online-users-notification') ? mc_('New user online') : false,
        'webhooks' => mc_get_multi_setting('webhooks', 'webhooks-active') ? mc_get_multi_setting('webhooks', 'webhooks-allowed', true) : false,
        'show_profile_images' => mc_get_setting('show-profile-images-admin'),
        'sender_name' => mc_get_setting('sender-name'),
        'notify_email_cron' => mc_get_setting('notify-email-cron'),
        'order_by_date' => mc_get_setting('order-by-date'),
        'max_file_size' => mc_get_server_max_file_size(),
        'reports_disabled' => mc_get_multi_setting('performance', 'performance-reports'),
        'rich_messages' => mc_get_rich_messages_ids(),
        'color' => mc_get_setting('color-admin-1'),
        'away_mode' => mc_get_setting('away-mode'),
        'chatbot_features' => mc_get_multi_setting('open-ai', 'open-ai-active') || mc_get_multi_setting('google', 'dialogflow-active') || mc_get_setting('ai-smart-reply'),
        'tags' => mc_get_setting('tags', []),
        'tags_show' => mc_get_multi_setting('tags-settings', 'tags-show'),
        'departments' => mc_get_setting('departments'),
        'departments_show' => mc_get_multi_setting('departments-settings', 'departments-show-list'),
        'notes_hide_information' => mc_get_multi_setting('notes-settings', 'notes-hide-name'),
        'visitor_default_name' => mc_get_setting('visitor-default-name'),
        'hide_conversation_details' => mc_get_setting('hide-conversation-details'),
        'visitors_registration' => mc_get_setting('visitors-registration') || mc_get_setting('online-users-notification')
    ];
    $code = '<script>';
    if (defined('MC_DIALOGFLOW')) {
        $settings['dialogflow'] = mc_get_multi_setting('google', 'dialogflow-active');
        $settings['open_ai_user_expressions'] = mc_get_multi_setting('open-ai', 'open-ai-user-expressions');
        $settings['open_ai_prompt_rewrite'] = mc_get_multi_setting('open-ai', 'open-ai-prompt-message-rewrite');
        $settings['smart_reply'] = mc_get_multi_setting('dialogflow-smart-reply', 'dialogflow-smart-reply-active') || mc_get_setting('ai-smart-reply'); // Deprecated: mc_get_multi_setting('dialogflow-smart-reply', 'dialogflow-smart-reply-active')
        $settings['open_ai_model'] = function_exists('mc_open_ai_get_gpt_model') ? mc_open_ai_get_gpt_model() : 'gpt-3.5-turbo'; // Deprecated: function_exists('mc_open_ai_get_gpt_model') ? mc_open_ai_get_gpt_model() : 'gpt-3.5-turbo';
        $settings['translation'] = mc_get_setting('google-translation') || mc_get_multi_setting('google', 'google-translation'); // Deprecated: mc_get_setting('google-translation')
        $settings['multilingual_translation'] = mc_get_multi_setting('google', 'google-multilingual-translation');
        $settings['speech_recognition'] = mc_get_multi_setting('open-ai', 'open-ai-speech-recognition');
        $settings['note_data_scrape'] = mc_get_multi_setting('open-ai', 'open-ai-note-scraping') ? mc_open_ai_data_scraping_get_prompts('name') : false;
        $settings['open_ai_chatbot_status'] = mc_get_multi_setting('open-ai', 'open-ai-active') ? (in_array(mc_get_multi_setting('open-ai', 'open-ai-mode'), ['assistant', 'general']) ? 'mode' : ((!mc_is_cloud() || mc_get_multi_setting('open-ai', 'open-ai-sync-mode') == 'manual') && empty(trim(mc_get_multi_setting('open-ai', 'open-ai-key'))) ? 'key' : true)) : 'inactive';
    }
    if (defined('MC_WOOCOMMERCE')) {
        $settings['currency'] = mc_get_setting('wc-currency-symbol');
        $settings['languages'] = json_encode(mc_isset(mc_wp_language_settings(), 'languages', []));
    }
    if (defined('MC_PERFEX')) {
        $settings['perfex_url'] = mc_get_setting('perfex-url');
    }
    if (defined('MC_WHMCS')) {
        $settings['whmcs_url'] = mc_get_setting('whmcs-url');
    }
    if (defined('MC_AECOMMERCE')) {
        $settings['aecommerce_panel_title'] = mc_get_setting('aecommerce-panel-title', 'Active eCommerce');
    }
    if ($settings['pusher']) {
        $settings['pusher_key'] = mc_pusher_get_details()[0];
        $settings['pusher_cluster'] = mc_pusher_get_details()[3];
    }
    if ($settings['push_notifications'] || $settings['push_notifications_users'] || mc_is_cloud()) {
        $settings['push_notifications_provider'] = mc_is_cloud() ? 'onesignal' : mc_get_multi_setting('push-notifications', 'push-notifications-provider');
        $settings['push_notifications_id'] = mc_is_cloud() ? ONESIGNAL_APP_ID : mc_get_multi_setting('push-notifications', $settings['push_notifications_provider'] == 'onesignal' ? 'push-notifications-onesignal-app-id' : 'push-notifications-id');
        $settings['push_notifications_url'] = mc_get_multi_setting('push-notifications', 'push-notifications-sw-url');
    }
    if ($settings['supervisor']) {
        $settings['allow_supervisor_delete_message'] = $settings['supervisor']['supervisor-delete-message'];
        $settings['supervisor'] = true;
    }
    if ($active_user) {
        if (empty($active_user['url']) || $active_user['url'] == MC_URL) {
            $code .= 'var MC_ACTIVE_AGENT = { id: "' . $active_user['id'] . '", email: "' . $active_user['email'] . '", full_name: "' . mc_get_user_name($active_user) . '", user_type: "' . $active_user_type . '", profile_image: "' . $active_user['profile_image'] . '", department: "' . mc_isset($active_user, 'department', '') . '"};';
        } else {
            $code .= 'MCF.reset();';
        }
    } else {
        $code .= 'var MC_ACTIVE_AGENT = { id: "", full_name: "", user_type: "", profile_image: "", email: "" };';
    }
    if ($active_user && $is_agent && ($routing_type == 'queue' || $routing_type == 'routing')) {
        mc_routing_assign_conversations_active_agent($routing_type == 'queue');
    }
    if (defined('MC_WP')) {
        $code .= 'var MC_WP = true;';
    }
    if ($is_cloud) {
        $cookie_cloud = json_decode(mc_encryption($_POST['cloud'], false), true);
        $settings['cloud'] = $cookie_cloud && isset($cookie_cloud['email']) ? ['email' => $cookie_cloud['email'], 'cloud_user_id' => $cookie_cloud['user_id'], 'token' => $cookie_cloud['token'], 'chat_id' => account_chat_id($cookie_cloud['user_id'])] : [];
        $settings['credits'] = membership_get_active()['credits'];
        $settings['google_client_id'] = mc_defined('GOOGLE_CLIENT_ID');
        $settings['shopify_shop'] = shopify_get_shop_name();
        if ($settings['credits'] <= 0 && (((mc_get_multi_setting('open-ai', 'open-ai-active') || mc_get_multi_setting('open-ai', 'open-ai-spelling-correction') || mc_get_multi_setting('open-ai', 'open-ai-rewrite')) && mc_get_multi_setting('open-ai', 'open-ai-sync-mode', 'manual') != 'manual') || ((mc_get_multi_setting('google', 'dialogflow-active') || mc_get_multi_setting('google', 'google-multilingual-translation') || mc_get_multi_setting('google', 'google-translation') || mc_get_multi_setting('google', 'google-language-detection')) && mc_get_multi_setting('google', 'google-sync-mode', 'manual') != 'manual'))) { // Deprecated: remove , 'manual') default value.
            $settings['credits_required'] = true;
        }
    }
    $file_path = MC_PATH . '/resources/languages/admin/js/' . $language . '.json';
    $translations = $language && $language != 'en' && file_exists($file_path) ? file_get_contents($file_path) : '[]';
    $code .= 'var MC_LANGUAGE_CODES = ' . file_get_contents(MC_PATH . '/resources/languages/language-codes.json') . ';';
    $code .= 'var MC_ADMIN_SETTINGS = ' . json_encode($settings) . ';';
    $code .= 'var MC_TRANSLATIONS = ' . ($translations ? $translations : '[]') . ';';
    $code .= 'var MC_VERSIONS = ' . json_encode(array_merge(['mc' => MC_VERSION], mc_get_installed_apps_version())) . ';';
    $code .= '</script>';
    echo $code;
}

function mc_js_global() {
    global $MC_LANGUAGE;
    if (!isset($MC_LANGUAGE)) {
        mc_init_translations();
    }
    $ajax_url = str_replace('//include', '/include', MC_URL . '/include/ajax.php');
    $code = '<script data-cfasync="false">';
    $code .= 'var MC_AJAX_URL = "' . $ajax_url . '";';
    $code .= 'var MC_URL = "' . MC_URL . '";';
    $code .= 'var MC_LANG = ' . ($MC_LANGUAGE ? json_encode($MC_LANGUAGE) : 'false') . ';';
    $code .= '</script>';
    echo $code;
}

function mc_get_block_setting($value) {
    switch ($value) {
        case 'privacy':
            $settings = mc_get_setting('privacy');
            return $settings && $settings['privacy-active'] ? ['title' => mc_rich_value($settings['privacy-title']), 'message' => mc_rich_value($settings['privacy-msg']), 'decline' => mc_rich_value($settings['privacy-msg-decline']), 'link' => $settings['privacy-link'], 'link-name' => mc_rich_value(mc_isset($settings, 'privacy-link-text', ''), false), 'btn-approve' => mc_rich_value(mc_isset($settings, 'privacy-btn-approve', 'Yes'), false), 'btn-decline' => mc_rich_value(mc_isset($settings, 'privacy-btn-decline', 'Cancel'), false)] : false;
        case 'popup':
            $settings = mc_get_setting('popup-message');
            return $settings && $settings['popup-active'] ? ['title' => mc_rich_value($settings['popup-title']), 'message' => mc_rich_value(nl2br($settings['popup-msg'])), 'image' => $settings['popup-image']] : false;
        case 'welcome':
            $settings = mc_get_setting('welcome-message');
            return $settings && $settings['welcome-active'] ? ['message' => mc_rich_value($settings['welcome-msg'], true, true, true), 'open' => $settings['welcome-open'], 'sound' => $settings['welcome-sound']] : false;
        case 'follow':
            $settings = mc_get_setting('follow-message');
            return $settings && $settings['follow-active'] ? ['title' => mc_rich_value($settings['follow-title']), 'message' => mc_rich_value($settings['follow-msg'], false, true), 'sound' => $settings['follow-sound'], 'name' => $settings['follow-name'] ? 'true' : 'false', 'last-name' => mc_isset($settings, 'follow-last-name') ? 'true' : 'false', 'phone' => mc_isset($settings, 'follow-phone') ? 'true' : 'false', 'phone-required' => mc_isset($settings, 'follow-phone-required') ? 'true' : 'false', 'success' => mc_rich_value(str_replace('{user_name}', '{user_name_}', $settings['follow-success'])), 'placeholder' => mc_rich_value(mc_isset($settings, 'follow-placeholder', 'Email')), 'delay' => mc_isset($settings, 'follow-delay'), 'disable-office-hours' => mc_isset($settings, 'follow-disable-office-hours')] : false;
    }
    return false;
}

function mc_populate_settings($category, $settings, $echo = true) {
    if (!isset($settings) && file_exists(MC_PATH . '/resources/json/settings.json')) {
        $settings = mc_get_json_resource('json/settings.json');
    }
    $settings = $settings[$category];
    $code = '';
    for ($i = 0; $i < count($settings); $i++) {
        $code .= mc_get_setting_code($settings[$i]);
    }
    if ($echo) {
        echo $code;
        return true;
    } else {
        return $code;
    }
}

function mc_populate_app_settings($app_name) {
    $file = MC_PATH . '/apps/' . $app_name . '/settings.json';
    $settings = [$app_name => []];
    if (file_exists($file)) {
        $settings[$app_name] = json_decode(file_get_contents($file), true);
        if (mc_is_cloud()) {
            $settings = mc_cloud_merge_settings($settings);
        }
    }
    return mc_populate_settings($app_name, $settings, false);
}

function mc_get_setting_code($setting) {
    if (isset($setting)) {
        $id = $setting['id'];
        $type = $setting['type'];
        $disable_translations = mc_get_setting('admin-disable-settings-translations');
        $keywords = mc_isset($setting, 'keywords');
        $content = '<div id="' . $id . '" data-type="' . $type . '"' . ($keywords ? ' data-keywords="' . $keywords . '"' : '') . (isset($setting['setting']) ? ' data-setting="' . $setting['setting'] . '"' : '') . ' class="mc-setting mc-type-' . $type . '"><div class="mc-setting-content"><h2>' . mc_s($setting['title'], $disable_translations) . '</h2><p>' . mc_s($setting['content'], $disable_translations) . mc_get_setting_code_help($setting) . '</p></div><div class="input">';
        switch ($type) {
            case 'color':
                $content .= '<input type="text"><i class="mc-close mc-icon-close"></i>';
                break;
            case 'text':
                $content .= '<input type="text">';
                break;
            case 'password':
                $content .= '<input type="password">';
                break;
            case 'textarea':
                $content .= '<textarea></textarea>';
                break;
            case 'select':
                $values = $setting['value'];
                $content .= '<select>';
                for ($i = 0; $i < count($values); $i++) {
                    $content .= '<option value="' . $values[$i][0] . '">' . mc_s($values[$i][1], $disable_translations) . '</option>';
                }
                $content .= '</select>';
                break;
            case 'checkbox':
                $content .= '<input type="checkbox">';
                break;
            case 'radio':
                $values = $setting['value'];
                for ($i = 0; $i < count($values); $i++) {
                    $content .= '<div><input type="radio" name="' . $id . '" value="' . strtolower(str_replace(' ', '-', $values[$i])) . '"><label>' . $setting["value"][$i] . '</label></div>';
                }
                break;
            case 'number':
                $content .= '<input type="number">' . (isset($setting['unit']) ? '<label>' . $setting['unit'] . '</label>' : '');
                break;
            case 'upload':
                $content .= (empty($setting['text-field']) ? '' : '<input type="url">') . '<a class="mc-btn">' . mc_(mc_isset($setting, 'button-text', 'Choose file')) . '</a>';
                break;
            case 'upload-image':
                $content .= '<div class="image"' . (isset($setting['background-size']) ? ' style="background-size: ' . $setting['background-size'] . '"' : '') . '><i class="mc-icon-close"></i></div>';
                break;
            case 'input-button':
                $content .= '<input type="text"><a class="mc-btn">' . mc_s($setting['button-text'], $disable_translations) . '</a>';
                break;
            case 'button':
                $content .= '<a class="mc-btn" href="' . $setting['button-url'] . '" target="_blank">' . mc_s($setting['button-text'], $disable_translations) . '</a>';
                break;
            case 'multi-input':
                $values = $setting['value'];
                for ($i = 0; $i < count($values); $i++) {
                    $sub_type = $values[$i]['type'];
                    $content .= '<div id="' . $values[$i]['id'] . '" data-type="' . $sub_type . '" class="multi-input-' . $sub_type . '"><label>' . mc_s($values[$i]['title'], $disable_translations) . mc_get_setting_code_help($values[$i]) . '</label>';
                    switch ($sub_type) {
                        case 'text':
                            $content .= '<input type="text">';
                            break;
                        case 'password':
                            $content .= '<input type="password">';
                            break;
                        case 'number':
                            $content .= '<input type="number">';
                            break;
                        case 'textarea':
                            $content .= '<textarea></textarea>';
                            break;
                        case 'upload':
                            $content .= '<input type="url"><button type="button">' . mc_('Choose file') . '</button>';
                            break;
                        case 'upload-image':
                            $content .= '<div class="image"><i class="mc-icon-close"></i></div>';
                            break;
                        case 'checkbox':
                            $content .= '<input type="checkbox">';
                            break;
                        case 'select':
                            $content .= '<select>';
                            $items = $values[$i]['value'];
                            for ($j = 0; $j < count($items); $j++) {
                                $content .= '<option value="' . $items[$j][0] . '">' . mc_s($items[$j][1], $disable_translations) . '</option>';
                            }
                            $content .= '</select>';
                            break;
                        case 'button':
                            $content .= '<a class="mc-btn" href="' . $values[$i]['button-url'] . '" target="_blank">' . mc_s($values[$i]['button-text'], $disable_translations) . '</a>';
                            break;
                        case 'select-checkbox':
                            $items = $values[$i]['value'];
                            $content .= '<input type="text" class="mc-select-checkbox-input" readonly><div class="mc-select-checkbox">';
                            for ($i = 0; $i < count($items); $i++) {
                                $content .= '<div class="multi-input-checkbox"><input id="' . $items[$i][0] . '" type="checkbox"><label>' . mc_s($items[$i][1], $disable_translations) . '</label></div>';
                            }
                            $content .= '</div>';
                            break;
                    }
                    $content .= '</div>';
                }
                break;
            case 'range':
                $range = (key_exists('range', $setting) ? $setting['range'] : array(0, 100));
                $unit = (key_exists('unit', $setting) ? '<label>' . $setting['unit'] . '</label>' : '');
                $content .= '<label class="range-value">' . $range[0] . '</label><input type="range" min="' . $range[0] . '" max="' . $range[1] . '" value="' . $range[0] . '" />' . $unit;
                break;
            case 'repeater':
                $content .= '<div class="mc-repeater"><div class="repeater-item">';
                for ($i = 0; $i < count($setting['items']); $i++) {
                    $item = $setting['items'][$i];
                    $content .= '<div>' . (isset($item['name']) ? '<label>' . mc_s($item['name'], $disable_translations) . '</label>' : '');
                    switch ($item['type']) {
                        case 'url':
                        case 'text':
                        case 'number':
                        case 'password':
                            $content .= '<input data-id="' . $item['id'] . '" type="' . $item['type'] . '">';
                            break;
                        case 'textarea':
                            $content .= '<textarea data-id="' . $item['id'] . '"></textarea>';
                            break;
                        case 'checkbox':
                            $content .= '<input data-id="' . $item['id'] . '" type="checkbox">';
                            break;
                        case 'auto-id':
                            $content .= '<input data-type="auto-id" data-id="' . $item['id'] . '" value="1" type="text" readonly="true">';
                            break;
                        case 'hidden':
                            $content .= '<input data-id="' . $item['id'] . '" type="hidden">';
                            break;
                        case 'color-palette':
                            $content .= mc_color_palette($item['id']);
                            break;
                        case 'upload-image':
                            $content .= '<div data-type="upload-image"><div data-id="' . $item['id'] . '" class="image"><i class="mc-icon-close"></i></div></div>';
                            break;
                        case 'upload-file':
                            $content .= '<div data-type="upload-file" class="mc-flex"><input type="url" data-id="' . $item['id'] . '" disabled><a class="mc-btn">' . mc_('Choose file') . '</a></div>';
                            break;
                        case 'button':
                            $content .= '<a data-id="' . $item['id'] . '" href="' . $item['button-url'] . '" class="mc-btn" target="_blank">' . mc_s($item['button-text'], $disable_translations) . '</a>';
                            break;
                        case 'select':
                            $values = $item['value'];
                            $content .= '<select data-id="' . $item['id'] . '">';
                            for ($i = 0; $i < count($values); $i++) {
                                $content .= '<option value="' . $values[$i][0] . '">' . mc_s($values[$i][1], $disable_translations) . '</option>';
                            }
                            $content .= '</select>';
                            break;
                    }
                    $content .= '</div>';
                }
                $content .= '<i class="mc-icon-close"></i></div></div><a class="mc-btn mc-repeater-add">' . mc_('Add new item') . '</a>';
                break;
            case 'timetable':
                $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                $hours = [['', ''], ['00:00', '12:00 am'], ['00:30', '12:30 am'], ['01:00', '1:00 am'], ['01:30', '1:30 am'], ['02:00', '2:00 am'], ['02:30', '2:30 am'], ['03:00', '3:00 am'], ['03:30', '3:30 am'], ['04:00', '4:00 am'], ['04:30', '4:30 am'], ['05:00', '5:00 am'], ['05:30', '5:30 am'], ['06:00', '6:00 am'], ['06:30', '6:30 am'], ['07:00', '7:00 am'], ['07:30', '7:30 am'], ['08:00', '8:00 am'], ['08:30', '8:30 am'], ['09:00', '9:00 am'], ['09:30', '9:30 am'], ['10:00', '10:00 am'], ['10:30', '10:30 am'], ['11:00', '11:00 am'], ['11:30', '11:30 am'], ['12:00', '12:00 pm'], ['12:30', '12:30 pm'], ['13:00', '1:00 pm'], ['13:30', '1:30 pm'], ['14:00', '2:00 pm'], ['14:30', '2:30 pm'], ['15:00', '3:00 pm'], ['15:30', '3:30 pm'], ['16:00', '4:00 pm'], ['16:30', '4:30 pm'], ['17:00', '5:00 pm'], ['17:30', '5:30 pm'], ['18:00', '6:00 pm'], ['18:30', '6:30 pm'], ['19:00', '7:00 pm'], ['19:30', '7:30 pm'], ['20:00', '8:00 pm'], ['20:30', '8:30 pm'], ['21:00', '9:00 pm'], ['21:30', '9:30 pm'], ['22:00', '10:00 pm'], ['22:30', '10:30 pm'], ['23:00', '11:00 pm'], ['23:30', '11:30 pm'], ['closed', mc_('Closed')]];
                $select = '<div class="mc-custom-select">';
                for ($i = 0; $i < count($hours); $i++) {
                    $select .= '<span data-value="' . $hours[$i][0] . '">' . $hours[$i][1] . '</span>';
                }
                $content .= '<div class="mc-timetable">';
                for ($i = 0; $i < 7; $i++) {
                    $content .= '<div data-day="' . strtolower($days[$i]) . '"><label>' . mc_($days[$i]) . '</label><div><div></div><span>' . mc_('To') . '</span><div></div><span>' . mc_('And') . '</span><div></div><span>' . mc_('To') . '</span><div></div></div></div>';
                }
                $content .= $select . '</div></div>';
                break;
            case 'select-images':
                $content .= '<div class="mc-icon-close"></div>';
                for ($i = 0; $i < count($setting['images']); $i++) {
                    $content .= '<div data-value="' . $setting['images'][$i] . '" style="background-image: url(\'' . MC_URL . '/media/' . $setting['images'][$i] . '\')"></div>';
                }
                break;
            case 'select-checkbox':
                $values = $setting['value'];
                $content .= '<select disabled><option>AA</option></select><div class="mc-select-checkbox">';
                for ($i = 0; $i < count($values); $i++) {
                    $content .= '<div id="' . $values[$i]['id'] . '" data-type="checkbox" class="multi-input-checkbox"><input type="checkbox"><label>' . mc_s($values[$i]['title'], $disable_translations) . '</label></div>';
                }
                $content .= '</div>';
                break;
        }
        if (isset($setting['setting']) && ($type == 'multi-input' || !empty($setting['multilingual']))) {
            $content .= '<div class="mc-language-switcher-cnt"><label>' . mc_('Languages') . '</label></div>';
        }
        return $content . '</div></div>';
    }
    return '';
}

function mc_get_setting_code_help($setting) {
    return isset($setting['help']) && (!mc_is_cloud() || defined('MC_CLOUD_DOCS')) ? '<a href="' . (defined('MC_CLOUD_DOCS') ? (MC_CLOUD_DOCS . substr($setting['help'], strpos($setting['help'], '#'))) : $setting['help']) . '" target="_blank" class="mc-icon-help"></a>' : '';
}

function mc_save_settings($settings, $external_settings = [], $external_settings_translations = []) {
    if (isset($settings)) {
        global $MC_SETTINGS;
        if (is_string($settings)) {
            $settings = json_decode($settings, true);
        }
        $settings_encoded = mc_db_json_escape($settings);
        if (isset($settings_encoded) && is_string($settings_encoded)) {

            // Save main settings
            $query = 'INSERT INTO mc_settings(name, value) VALUES (\'settings\', \'' . $settings_encoded . '\') ON DUPLICATE KEY UPDATE value = \'' . $settings_encoded . '\'';
            $result = mc_db_query($query);
            if (mc_is_error($result)) {
                return $result;
            }

            // Save external settings
            foreach ($external_settings as $key => $value) {
                mc_save_external_setting($key, $value);
            }

            // Save external settings translations
            $db = '';
            foreach ($external_settings_translations as $key => $value) {
                $name = 'external-settings-translations-' . $key;
                mc_save_external_setting($name, $value);
                $db .= '"' . $name . '",';
            }
            if ($db) {
                mc_db_query('DELETE FROM mc_settings WHERE name LIKE "external-settings-translations-%" AND name NOT IN (' . substr($db, 0, -1) . ')');
            }

            // Update bot
            mc_update_bot($settings['bot-name'][0], $settings['bot-image'][0]);

            // Cloud
            if (mc_is_cloud()) {
                require_once(MC_CLOUD_PATH . '/account/functions.php');
                mc_cloud_save_settings($settings);
            }

            $MC_SETTINGS = $settings;
            return true;
        } else {
            return mc_error('json-encode-error', 'mc_save_settings');
        }
    } else {
        return mc_error('settings-not-found', 'mc_save_settings');
    }
}

function mc_save_external_setting($name, $value) {
    $settings_encoded = mc_db_json_escape($value);
    return JSON_ERROR_NONE !== json_last_error() ? json_last_error_msg() : mc_db_query('INSERT INTO mc_settings(name, value) VALUES (\'' . mc_db_escape($name) . '\', \'' . $settings_encoded . '\') ON DUPLICATE KEY UPDATE value = \'' . $settings_encoded . '\'');
}

function mc_get_settings() {
    global $MC_SETTINGS;
    if (!isset($MC_SETTINGS)) {
        $MC_SETTINGS = mc_get_external_setting('settings', []);
        if (isset($GLOBALS['MC_LOCAL_SETTINGS'])) {
            $MC_SETTINGS = array_merge($MC_SETTINGS, $GLOBALS['MC_LOCAL_SETTINGS']);
        }
    }
    return $MC_SETTINGS;
}

function mc_get_all_settings() {
    $translations = [];
    $settings = [];
    $rows = mc_db_get('SELECT value FROM mc_settings WHERE name="emails" || name="rich-messages" || name="wc-emails"', false);
    for ($i = 0; $i < count($rows); $i++) {
        $settings = array_merge($settings, json_decode($rows[$i]['value'], true));
    }
    $rows = mc_db_get('SELECT name, value FROM mc_settings WHERE name LIKE "external-settings-translations-%"', false);
    for ($i = 0; $i < count($rows); $i++) {
        $translations[substr($rows[$i]['name'], -2)] = json_decode($rows[$i]['value'], true);
    }
    return array_merge(mc_get_settings(), $settings, ['external-settings-translations' => $translations]);
}

function mc_get_setting($id, $default = false) {
    $settings = mc_get_settings();
    if (!mc_is_error($settings)) {
        if (isset($settings[$id]) && !empty($settings[$id][0])) {
            $setting = $settings[$id][0];
            if (is_array($setting) && !isset($setting[0])) {
                $settings_result = [];
                foreach ($setting as $key => $value) {
                    $settings_result[$key] = $value[0];
                }
                return $settings_result;
            } else {
                return $setting;
            }
        } else {
            return $default;
        }
    } else {
        return $settings;
    }
}

function mc_get_multi_setting($id, $sub_id, $default = false) {
    $setting = mc_get_setting($id);
    if ($setting && !empty($setting[$sub_id])) {
        return $setting[$sub_id];
    }
    return $default;
}

function mc_get_external_setting($name, $default = false) {
    $result = mc_db_get('SELECT value FROM mc_settings WHERE name = "' . mc_db_escape($name) . '"', false);
    $settings = [];
    if (empty($result)) {
        return $default;
    }
    if (mc_is_error($settings)) {
        return $settings;
    }
    if (!is_array($result)) {
        return $result;
    }
    if (count($result) == 1) {
        $result = $result[0]['value'];
        return empty($result) && is_string($result) ? $default : json_decode($result, true);
    }
    for ($i = 0; $i < count($result); $i++) {
        $settings = array_merge($settings, json_decode($result[$i]['value'], true));
    }
    return $settings;
}

function mc_get_multilingual_setting($name, $sub_name, $language = false) {
    $language = $language ? $language : mc_get_user_language();
    $value = $language && $language != 'en' ? mc_isset(mc_get_external_setting('external-settings-translations-' . $language), $sub_name) : false;
    if ($value)
        return $value;
    $value = mc_isset(mc_get_external_setting($name), $sub_name);
    if ($value && is_array($value)) {
        $value = $value[0];
        if (!empty($value) && !is_string($value) && array() !== $value) {
            foreach ($value as $key => $setting) {
                $value[$key] = $setting[0];
            }
        }
    }
    return $value;
}

function mc_color_palette($id = '') {
    return '<div data-type="color-palette" data-value="" data-id="' . $id . '" class="mc-color-palette"><span></span><ul><li data-value=""></li><li data-value="red"></li><li data-value="yellow"></li><li data-value="green"></li><li data-value="pink"></li><li data-value="gray"></li><li data-value="blue"></li></ul></div>';
}

function mc_export_settings() {
    $setting_keys = ['automations', 'emails', 'rich-messages', 'settings', 'app-keys', 'articles', 'articles-categories', 'dialogflow-knowledge', 'open-ai-intents-history', 'slack-channels'];
    $settings = [];
    for ($i = 0; $i < count($setting_keys); $i++) {
        $value = mc_isset(mc_db_get('SELECT value FROM mc_settings WHERE name = "' . $setting_keys[$i] . '"'), 'value');
        if ($value) {
            $value = json_decode($value, true);
            if ($value)
                $settings[$setting_keys[$i]] = $value;
        }
    }
    $settings = json_encode($settings, JSON_INVALID_UTF8_IGNORE);
    if ($settings) {
        $name = 'settings' . '_' . rand(100000, 999999999) . '.json';
        $response = mc_file(MC_PATH . '/uploads/' . $name, $settings);
        return $response ? (MC_URL . '/uploads/' . $name) : $response;
    }
    return JSON_ERROR_NONE !== json_last_error() ? json_last_error_msg() : false;
}

function mc_import_settings($file_url) {
    $settings = json_decode(mc_download($file_url), true);
    if ($settings) {
        foreach ($settings as $key => $setting) {
            mc_save_external_setting($key, $setting);
        }
        mc_file_delete(MC_PATH . substr($file_url, strpos($file_url, '/uploads/')));
        return true;
    }
    return JSON_ERROR_NONE !== json_last_error() ? json_last_error_msg() : false;
}

function mc_get_departments() {
    $items = mc_get_setting('departments');
    $count = is_array($items) ? count($items) : 0;
    $departments = [];
    for ($i = 0; $i < $count; $i++) {
        $departments[$items[$i]['department-id']] = ['name' => mc_($items[$i]['department-name']), 'color' => $items[$i]['department-color'], 'image' => mc_isset($items[$i], 'department-image', '')];
    }
    return $departments;
}

function mc_departments($type) {
    $items = mc_get_setting('departments');
    $count = is_array($items) ? count($items) : 0;
    if ($count) {
        switch ($type) {
            case 'select':
                $code = '<div id="department" data-type="select" class="mc-input mc-input-select"><span>' . mc_('Department') . '</span><select><option value=""></option>';
                for ($i = 0; $i < $count; $i++) {
                    $code .= '<option value="' . $items[$i]['department-id'] . '">' . ucfirst(mc_($items[$i]['department-name'])) . '</option>';
                }
                echo $code . '</select></div>';
                break;
            case 'custom-select':
                $code = '<div class="mc-inline mc-inline-departments"><h3>' . mc_('Department') . '</h3><div id="conversation-department" class="mc-select mc-select-colors' . (!mc_is_agent(false, true, true) && !mc_get_multi_setting('agents', 'agents-update-department') ? ' mc-disabled' : '') . '"><p>' . mc_('None') . '</p><ul><li data-id="" data-value="">' . mc_('None') . '</li>';
                for ($i = 0; $i < $count; $i++) {
                    $id = $items[$i]['department-id'];
                    $code .= '<li data-id="' . $id . '" data-value="' . mc_isset($items[$i], 'department-color', $id) . '">' . ucfirst(mc_($items[$i]['department-name'])) . '</li>';
                }
                echo $code . '</ul></div></div>';
                break;
            case 'dashboard':
                $settings = mc_get_setting('departments-settings');
                if ($settings) {
                    $is_image = mc_isset($settings, 'departments-images') && mc_isset($items[0], 'department-image');
                    $code = '<div class="mc-dashboard-departments"><div class="mc-title">' . mc_(mc_isset($settings, 'departments-title', 'Departments')) . '</div><div class="mc-departments-list"' . (mc_isset($settings, 'departments-force-one') ? ' data-force-one="true"' : '') . '>';
                    for ($i = 0; $i < $count; $i++) {
                        $code .= '<div data-id="' . $items[$i]['department-id'] . '">' . ($is_image ? '<img src="' . $items[$i]['department-image'] . '">' : '<div data-color="' . mc_isset($items[$i], 'department-color') . '"></div>') . '<span>' . mc_($items[$i]['department-name']) . '</span></div>';
                    }
                    echo $code . '</div></div>';
                    break;
                }
        }
    }
}

function mc_office_hours() {
    $settings = mc_get_settings();
    $timetable = mc_isset($settings, 'timetable', [[]])[0];
    $now = time();
    $utc_offset = intval(mc_get_setting('timetable-utc', 0));
    $offset = $now - ($utc_offset * 3600);
    $today = strtolower(gmdate('l', $offset));
    $today_array = explode('-', gmdate('m-d-y', $offset));
    $today_array = [intval($today_array[0]), intval($today_array[1]), intval($today_array[2])];
    if (isset($timetable[$today]) && !empty($timetable[$today][0][0])) {
        $status = false;
        for ($i = 0; $i < 3; $i += 2) {
            if (!empty($timetable[$today][$i][0]) && $timetable[$today][$i][0] != 'closed') {
                $start = explode(':', $timetable[$today][$i][0]);
                $end = explode(':', $timetable[$today][$i + 1][0]);
                $office_hours_start = gmmktime(intval($start[0]) + $utc_offset, intval($start[1]), 0, $today_array[0], $today_array[1], $today_array[2]);
                $office_hours_end = gmmktime(intval($end[0]) + $utc_offset, intval($end[1]), 0, $today_array[0], $today_array[1], $today_array[2]);
                if ($now >= $office_hours_start && $now <= $office_hours_end) {
                    $status = true;
                }
            }
        }
        return $status;
    }
    return true;
}

function mc_css($color_1 = false, $color_2 = false, $color_3 = false, $return = false) {
    $css = '';
    $color_1 = $color_1 ? $color_1 : mc_get_setting('color-1');
    $color_2 = $color_2 ? $color_2 : mc_get_setting('color-2');
    $color_3 = $color_3 ? $color_3 : mc_get_setting('color-3');
    $chat_button_offset_top = mc_get_multi_setting('chat-button-offset', 'chat-button-offset-top');
    $chat_button_offset_bottom = mc_get_multi_setting('chat-button-offset', 'chat-button-offset-bottom');
    $chat_button_offset_right = mc_get_multi_setting('chat-button-offset', 'chat-button-offset-right');
    $chat_button_offset_left = mc_get_multi_setting('chat-button-offset', 'chat-button-offset-left');
    $chat_button_offset_left_mobile = mc_get_multi_setting('chat-button-offset', 'chat-button-offset-mobile');
    $chat_button_offset_left_mobile = $chat_button_offset_left_mobile == 'desktop' ? ['@media (min-width: 768px) {', '}'] : ($chat_button_offset_left_mobile == 'mobile' ? ['@media (max-width: 768px) {', '}'] : ['', '']);
    if ($color_1) {
        $css .= '.mc-chat-btn, .mc-chat>div>.mc-header,.mc-chat .mc-dashboard>div>.mc-btn:hover,.mc-chat .mc-scroll-area .mc-header,.mc-input.mc-input-btn>div,div ul.mc-menu li:hover,
                 .mc-select ul li:hover,.mc-popup.mc-emoji .mc-emoji-bar>div.mc-active, .mc-popup.mc-emoji .mc-emoji-bar>div:hover,.mc-btn,a.mc-btn,.mc-rich-message[disabled] .mc-buttons .mc-btn,
                 .mc-ul>span:before,.mc-article-category-links>span+span:before { background-color: ' . $color_1 . '; }';
        $css .= '.mc-chat .mc-dashboard>div>.mc-btn,.mc-search-btn>input,.mc-input>input:focus, .mc-input>select:focus, .mc-input>textarea:focus,
                 .mc-input.mc-input-image .image:hover { border-color: ' . $color_1 . '; }';
        $css .= '.mc-chat .mc-dashboard>div>.mc-btn,.mc-editor .mc-bar-icons>div:hover:before,.mc-articles>div:hover>div,.mc-main .mc-btn-text:hover,.mc-editor .mc-submit,.mc-table input[type="checkbox"]:checked:before,
                 .mc-select p:hover,div ul.mc-menu li.mc-active, .mc-select ul li.mc-active,.mc-search-btn>i:hover,.mc-search-btn.mc-active i,.mc-rich-message .mc-input>span.mc-active:not(.mc-filled),
                 .mc-input.mc-input-image .image:hover:before,.mc-rich-message .mc-card .mc-card-btn,.mc-slider-arrow:hover,.mc-loading:not(.mc-btn):before,.mc-articles>div.mc-title,.mc-article-categories>div:hover, .mc-article-categories>div.mc-active,
                 .mc-article-categories>div span:hover,.mc-article-categories>div span.mc-active,.mc-btn-text:hover,.mc-player > div:hover,.mc-loader:before { color: ' . $color_1 . '; }';
        $css .= '.mc-search-btn>input:focus,.mc-input>input:focus, .mc-input>select:focus, .mc-input>textarea:focus,.mc-input.mc-input-image .image:hover { box-shadow: 0 0 5px rgba(104, 104, 104, 0.2); }';
        $css .= '.mc-list>div.mc-rich-cnt { border-top-color: ' . $color_1 . '; }';
        $css .= '.mc-list>div.mc-right .mc-message, .mc-list>div.mc-right .mc-message a { color: #566069; } .mc-list>div.mc-right,.mc-right .mc-player>div { background-color: #f0f0f0; }';
    }
    if ($color_2) {
        $css .= '.mc-chat-btn:hover,.mc-input.mc-input-btn>div:hover,.mc-btn:hover,a.mc-btn:hover,.mc-rich-message .mc-card .mc-card-btn:hover { background-color: ' . $color_2 . '; }';
        $css .= '.mc-list>.mc-right .mc-message, .mc-list>.mc-right .mc-message a,.mc-editor .mc-submit:hover { color: ' . $color_2 . '; }';
    }
    if ($color_3) {
        $css .= '.mc-list>.mc-right,.mc-user-conversations>li:hover { background-color: ' . $color_3 . '; }';
    }
    if ($chat_button_offset_top) {
        $css .= $chat_button_offset_left_mobile[0] . '.mc-chat-btn { top: ' . $chat_button_offset_top . 'px; }' . $chat_button_offset_left_mobile[1];
    }
    if ($chat_button_offset_bottom) {
        $css .= $chat_button_offset_left_mobile[0] . '.mc-chat-btn { bottom: ' . $chat_button_offset_bottom . 'px; }' . $chat_button_offset_left_mobile[1];
    }
    if ($chat_button_offset_right) {
        $css .= $chat_button_offset_left_mobile[0] . '.mc-chat-btn { right: ' . $chat_button_offset_right . 'px; }' . $chat_button_offset_left_mobile[1];
    }
    if ($chat_button_offset_left) {
        $css .= $chat_button_offset_left_mobile[0] . '.mc-chat-btn { left: ' . $chat_button_offset_left . 'px; }' . $chat_button_offset_left_mobile[1];
    }
    if ($return)
        return $css;
    if ($css) {
        echo '<style>' . $css . '</style>';
    }
    return false;
}

function mc_system_requirements() {
    $checks = [];

    // PHP version
    $checks['php-version'] = version_compare(PHP_VERSION, '7.2.0') >= 0;

    // ZipArchive
    $checks['zip-archive'] = class_exists('ZipArchive');

    // File permissions
    $permissions = [['plugin', MC_PATH], ['uploads', mc_upload_path()], ['apps', MC_PATH . '/apps'], ['languages', MC_PATH . '/resources/languages']];
    for ($i = 0; $i < count($permissions); $i++) {
        $path = $permissions[$i][1] . '/mc-permissions-check.txt';
        mc_file($path, 'permissions-check');
        $checks[$permissions[$i][0] . '-folder'] = file_exists($path) && strpos(file_get_contents($path), 'permissions-check');
        if (file_exists($path)) {
            unlink($path);
        }
    }

    // AJAX file
    $checks['ajax'] = function_exists('curl_init') && mc_download(MC_URL . '/include/ajax.php') == 'true';

    // cURL
    $checks['curl'] = function_exists('curl_version') && is_array(mc_get_versions());

    // MySQL UTF8MB4 support
    $checks['UTF8mb4'] = !mc_is_error(mc_db_query('SET NAMES UTF8mb4'));

    return $checks;
}

function mc_select_html($type) {
    $code = '<select><option value=""></option>';
    $is_countries = $type == 'countries';
    $items = mc_get_json_resource($is_countries ? 'json/countries.json' : 'languages/language-codes.json');
    foreach ($items as $key => $value) {
        $code .= '<option value="' . ($is_countries ? $value : $key) . '">' . mc_($is_countries ? $key : $value) . '</option>';
    }
    return $code . '</select>';
}

function mc_select_phone() {
    $single = mc_get_setting('phone-code');
    if ($single) {
        return $single;
    } else {
        $phones = mc_get_json_resource('json/phone.json');
        $country_code_ip = strtoupper(mc_isset(mc_ip_info('countryCode'), 'countryCode'));
        $phone_prefix_ip = mc_isset($phones, $country_code_ip);
        $code = '<div class="mc-select"><p data-value="' . ($phone_prefix_ip ? '+' . $phone_prefix_ip : '') . '">' . ($phone_prefix_ip ? '<img src="' . MC_URL . '/media/flags/' . strtolower($country_code_ip) . '.png" alt="' . $country_code_ip . '" loading="lazy" />+' . $phone_prefix_ip : '+00') . '</p><div class="mc-select-search"><input type="text" placeholder="' . mc_('Search ...') . '" /></div><ul class="mc-scroll-area">';
        foreach ($phones as $country_code => $phone_prefix) {
            $country_code = strtolower($country_code);
            $code .= ' <li data-value="+' . $phone_prefix . '" data-country="' . $country_code . '"' . ($phone_prefix_ip == $phone_prefix ? ' class="mc-active"' : '') . '><img src="' . MC_URL . '/media/flags/' . $country_code . '.png" alt="' . $country_code . '" loading="lazy" />+' . $phone_prefix . '</li>';
        }
        return $code . '</ul></div>';
    }
}

function mc_get_config_details($path) {
    $details = [];
    $slugs = ['MC_URL', 'MC_DB_NAME', 'MC_DB_USER', 'MC_DB_PASSWORD', 'MC_DB_HOST', 'MC_DB_PORT'];
    $lines = preg_split("/\r\n|\n|\r/", file_get_contents($path));
    for ($i = 0; $i < count($lines); $i++) {
        $line = $lines[$i];
        for ($j = 0; $j < count($slugs); $j++) {
            if (strpos($line, $slugs[$j])) {
                $details[$slugs[$j]] = str_replace(['define(\'' . $slugs[$j] . '\', \'', '\');'], '', $line);
            }
        }
    }
    return $details;
}

function mc_update_sw($url) {
    $path = MC_PATH . '/sw.js';
    if (!file_exists($path)) {
        copy(MC_PATH . '/resources/sw.js', $path);
    }
    $lines = preg_split("/\r\n|\n|\r/", file_get_contents($path));
    $code = '';
    $url = str_replace(['importScripts(', ');', '\'', '"'], '', $url);
    for ($i = 0; $i < count($lines); $i++) {
        if (strpos($lines[$i], 'importScripts') !== false) {
            $lines[$i] = 'importScripts(\'' . $url . '\');';
        }
        $code .= $lines[$i] . "\n";
    }
    return mc_file(MC_PATH . '/sw.js', $code);
}

/*
 * -----------------------------------------------------------
 * ARTICLES
 * -----------------------------------------------------------
 *
 * 1. Save all articles
 * 2. Save all articles categories
 * 3. Returns all articles
 * 4. Returns all articles categories
 * 5. Returns a single article category
 * 6. Article search
 * 7. Article ratings
 * 8. Inits articles for the admin area
 * 9. Generates an excerpt of the article contents
 * 10. Returns the article.php
 * 11. Returns the article URL
 * 12. Returns the articles page URL
 * 13. Checks if the article URL rewriting is enabled
 *
 */

// Deprecated
function mc_temp_deprecated_articles_migration() {
    $articles = mc_get_external_setting('articles');
    $articles_translations = mc_db_get('SELECT name, value FROM mc_settings WHERE name LIKE "articles-translations-%"', false);
    $now = date('Y-m-d H:i:s');
    $ids = [];
    if ($articles) {
        for ($i = 0; $i < count($articles); $i++) {
            $categories = mc_isset($articles[$i], 'categories');
            $ids[$articles[$i]['id']] = mc_db_query('INSERT INTO mc_articles (title, content, editor_js, nav, link, category, parent_category, language, slug, update_time) VALUES ( "' . mc_db_escape(mc_sanatize_string($articles[$i]['title'])) . '", "' . str_replace(['\"', '"'], ['"', '\"'], mc_sanatize_string($articles[$i]['content'])) . '", "' . mc_db_escape(mc_sanatize_string(json_encode($articles[$i]['editor_js'], JSON_INVALID_UTF8_IGNORE | JSON_UNESCAPED_UNICODE))) . '", "", "' . mc_db_escape(mc_sanatize_string($articles[$i]['link'])) . '", "' . (empty($categories[0]) ? '' : mc_db_escape(mc_sanatize_string($categories[0]))) . '", "' . mc_db_escape(mc_sanatize_string($articles[$i]['parent_category'])) . '", "", "' . mc_db_escape(mc_sanatize_string(mc_string_slug($articles[$i]['title']))) . '", "' . $now . '")', true);
        }
        for ($j = 0; $j < count($articles_translations); $j++) {
            $articles = json_decode($articles_translations[$j]['value'], true);
            for ($i = 0; $i < count($articles); $i++) {
                $categories = mc_isset($articles[$i], 'categories');
                mc_db_query('INSERT INTO mc_articles (title, content, editor_js, nav, link, category, parent_category, language, parent_id, slug, update_time) VALUES ("' . mc_db_escape(mc_sanatize_string($articles[$i]['title'])) . '", "' . str_replace(['\"', '"'], ['"', '\"'], mc_sanatize_string($articles[$i]['content'])) . '", "' . mc_db_escape(mc_sanatize_string(json_encode($articles[$i]['editor_js'], JSON_INVALID_UTF8_IGNORE | JSON_UNESCAPED_UNICODE))) . '", "", "' . mc_db_escape(mc_sanatize_string($articles[$i]['link'])) . '", "' . (empty($categories[0]) ? '' : mc_db_escape(mc_sanatize_string($categories[0]))) . '", "' . mc_db_escape(mc_sanatize_string(mc_isset($articles[$i], 'parent_category', ''))) . '", "' . mc_db_escape(mc_sanatize_string(str_replace('articles-translations-', '', $articles_translations[$j]['name']))) . '", "' . $ids[$articles[$i]['id']] . '", "' . mc_db_escape(mc_sanatize_string(mc_string_slug($articles[$i]['title']))) . '", "' . $now . '")');
            }
        }
        $parent_categories = array_column(mc_db_get('SELECT parent_category FROM mc_articles WHERE parent_category <> "" GROUP BY parent_category', false), 'parent_category');
        $categories = mc_get_articles_categories();
        for ($i = 0; $i < count($categories); $i++) {
            for ($j = 0; $j < count($parent_categories); $j++) {
                if (strtolower($categories[$i]['id']) == strtolower($parent_categories[$j])) {
                    $categories[$i]['parent'] = true;
                    break;
                }
            }
            $category_new_id = mc_string_slug($categories[$i]['title']);
            mc_db_query('UPDATE mc_articles SET category = "' . $category_new_id . '" WHERE category = "' . $categories[$i]['id'] . '"');
            mc_db_query('UPDATE mc_articles SET parent_category = "' . $category_new_id . '" WHERE parent_category = "' . $categories[$i]['id'] . '"');
            $categories[$i]['id'] = $category_new_id;
        }
        mc_save_articles_categories($categories);
        mc_db_query('DELETE FROM mc_settings WHERE name = "articles"');
        mc_db_query('DELETE FROM mc_settings WHERE name LIKE "articles-translations-%"');
    }
}
// Deprecated

function mc_save_article($article) {
    if (is_string($article)) {
        $article = json_decode($article, true);
    }
    $article_id = mc_db_escape(mc_isset($article, 'id'), true);
    if (mc_isset($article, 'delete')) {
        return mc_db_query('DELETE FROM mc_articles WHERE id = ' . $article_id);
    }
    $article_title = mc_db_escape(mc_sanatize_string($article['title']));
    $article_content = str_replace(['\"', '"'], ['"', '\"'], mc_sanatize_string(mc_isset($article, 'content')));
    $article_editor_js = mc_isset($article, 'editor_js', '');
    $article_link = trim(mc_db_escape(mc_sanatize_string(mc_isset($article, 'link'))));
    $article_category = mc_db_escape(mc_sanatize_string(mc_isset($article, 'category', '')));
    $article_parent_category = mc_db_escape(mc_sanatize_string(mc_isset($article, 'parent_category', '')));
    $article_language = mc_db_escape(mc_sanatize_string(mc_isset($article, 'language', '')));
    $article_parent_id = mc_db_escape(mc_isset($article, 'parent_id', 'NULL'));
    $article_slug = mc_db_escape(mc_sanatize_string(mc_string_slug($article_title)));
    preg_match_all('/<code>(.*?)<\/code>/s', $article_content, $matches);
    foreach ($matches[1] as $code) {
        $article_content = str_replace($code, htmlspecialchars($code), $article_content);
    }
    if ($article_editor_js) {
        $article_editor_js = mc_db_json_escape($article_editor_js);
    }
    if (mc_db_get('SELECT slug FROM mc_articles WHERE slug = "' . $article_slug . '"' . ($article_id ? ' AND id <> ' . $article_id : ''))) {
        $random = rand(1000, 9999);
        if ($article_id) {
            $saved_slug = mc_db_get('SELECT slug FROM mc_articles WHERE id = ' . $article_id)['slug'];
            $saved_random = str_replace($article_slug . '-', '', $saved_slug);
            if (is_numeric($saved_random)) {
                $random = $saved_random;
            }
            $article_slug .= '-' . $random;
        }
    }
    if (empty($article_title)) {
        $article_title = '#' . $article_id;
    }
    if (!$article_id) {
        $response = mc_db_query('INSERT INTO mc_articles (title, content, editor_js, nav, link, category, parent_category, language, parent_id, slug, update_time) VALUES ("' . $article_title . '", "' . $article_content . '", "' . $article_editor_js . '", "", "' . $article_link . '", "' . $article_category . '", "' . $article_parent_category . '", "' . $article_language . '", ' . $article_parent_id . ', "' . $article_slug . '", "' . date('Y-m-d H:i:s') . '")', true);
    } else {
        mc_db_query('UPDATE mc_articles SET category = "' . $article_category . '", parent_category = "' . $article_parent_category . '" WHERE parent_id = ' . $article_id);
        $response = mc_db_query('UPDATE mc_articles SET title = "' . $article_title . '", content = "' . $article_content . '", editor_js = "' . $article_editor_js . '", link = "' . $article_link . '", category = "' . $article_category . '", parent_category = "' . $article_parent_category . '", language = "' . $article_language . '", parent_id = ' . $article_parent_id . ', slug = "' . $article_slug . '", update_time = "' . date('Y-m-d H:i:s') . '" WHERE id = ' . $article_id);
    }
    return $response;
}

function mc_save_articles_categories($categories) {
    if (is_string($categories)) {
        $categories = json_decode($categories, true);
    }
    $previous_categories = mc_get_articles_categories();
    $response = mc_save_external_setting('articles-categories', $categories);
    if ($response) {
        $query_categories = ['', ''];
        for ($i = 0; $i < count($categories); $i++) {
            $id = $categories[$i]['id'];
            $is_sub_category = empty($categories[$i]['parent']);
            $query_categories[$is_sub_category] .= '"' . $id . '",';
            if (isset($previous_categories[$i]) && $previous_categories[$i]['id'] != $id) {
                $response = mc_db_query('UPDATE mc_articles SET ' . ($is_sub_category ? 'category' : 'parent_category') . ' = "' . $id . '" WHERE ' . ($is_sub_category ? 'category' : 'parent_category') . ' = "' . $previous_categories[$i]['id'] . '"');
            }
        }
        mc_db_query('UPDATE mc_articles SET parent_category = ""' . ($query_categories[0] ? ' WHERE parent_category NOT IN (' . substr($query_categories[0], 0, -1) . ')' : ''));
        mc_db_query('UPDATE mc_articles SET category = ""' . ($query_categories[1] ? ' WHERE category NOT IN (' . substr($query_categories[1], 0, -1) . ')' : ''));
    }
    return $response;
}

function mc_get_articles($article_id = false, $count = false, $full = false, $categories = false, $language = false, $skip_language = false) {
    $query_part = '';
    if (is_array($language)) {
        $language = $language[0];
    }
    if ($language == 'en' || ($skip_language && !mc_get_setting('front-auto-translations'))) {
        $language = false;
    }
    if ($article_id) {
        $article_id = is_array($article_id) ? $article_id : explode(',', str_replace(' ', '', $article_id));
        $count = count($article_id);
        $query_part = ($count == 1 && !is_numeric($article_id[0]) ? 'slug' : 'id') . ' IN ("' . implode('","', $article_id) . '")';
        for ($i = 0; $i < $count; $i++) {
            mc_reports_update('articles-views', false, false, $article_id[$i]);
        }
        if ($count == 1) {
            $language = 'all';
        }
    }
    if (is_string($categories)) {
        $categories = [$categories];
    }
    if ($categories) {
        $query_part .= ($query_part ? ' AND ' : '') . '(category IN ("' . implode('","', $categories) . '") OR parent_category IN ("' . implode('","', $categories) . '"))';
    }
    $is_multilingual_by_translation = defined('MC_DIALOGFLOW') && $language && $language != 'all' && (mc_get_multi_setting('google', 'google-multilingual-translation'));
    $query_part .= ($language == 'all' || $is_multilingual_by_translation ? '' : ($query_part ? ' AND ' : '') . 'language = "' . ($language ? mc_db_escape($language) : '') . '"');
    $articles = mc_db_get('SELECT * FROM mc_articles' . ($query_part ? ' WHERE ' . $query_part : '') . '  ORDER BY id' . ($count ? ' LIMIT ' . mc_db_escape($count, true) : ''), false);
    if ($is_multilingual_by_translation) {
        $articles_2 = [];
        for ($i = 0; $i < count($articles); $i++) {
            if (empty($articles[$i]['parent_id'])) {
                $article_id_2 = $articles[$i]['id'];
                $is_translated = false;
                for ($j = 0; $j < count($articles); $j++) {
                    if ($article_id_2 == $articles[$j]['parent_id'] && $articles[$j]['language'] == $language) {
                        array_push($articles_2, $articles[$j]);
                        $is_translated = true;
                        break;
                    }
                }
                if (!$is_translated) {
                    $article = mc_google_translate_article($article_id_2, $language);
                    $article['id'] = mc_save_article($article);
                    array_push($articles_2, $article);
                }
            }
        }
        $articles = $articles_2;
    }
    if (empty($articles) && $language && $language != 'all') {
        return mc_get_articles($article_id, $count, $full, $categories);
    }
    if (!$full) {
        $articles = mc_articles_excerpt($articles);
    }
    return $articles;
}

function mc_get_articles_categories($category_type = false) {
    $categories = mc_isset($GLOBALS, 'MC_ARTICLES_CATEGORIES');
    if (!$categories) {
        $categories = mc_get_external_setting('articles-categories', []);
    }
    if ($category_type) {
        $is_parent = $category_type == 'parent';
        $response = [];
        for ($i = 0; $i < count($categories); $i++) {
            $is_parent_item = mc_isset($categories[$i], 'parent');
            if (($is_parent_item && $is_parent) || (!$is_parent_item && !$is_parent)) {
                array_push($response, $categories[$i]);
            }
        }
        return $response;
    }
    return $categories;
}

function mc_get_article_category($category_id) {
    $categories = mc_get_articles_categories();
    for ($i = 0; $i < count($categories); $i++) {
        if ($categories[$i]['id'] == $category_id) {
            return $categories[$i];
        }
    }
    return false;
}

function mc_search_articles($search, $language = false) {
    $search = mc_db_escape($search);
    if (empty($search)) {
        return [];
    }
    if (is_array($language)) {
        $language = $language[0];
    }
    if ($language == 'en') {
        $language = false;
    }
    $articles = mc_db_get('SELECT * FROM mc_articles WHERE (title LIKE "%' . $search . '%" OR content LIKE "%' . $search . '%" OR link LIKE "%' . $search . '%") ' . ($language == 'all' ? '' : 'AND language = "' . ($language ? mc_db_escape($language) : '') . '"') . ' ORDER BY id', false);
    if (empty($articles) && $language && $language != 'en') {
        return mc_search_articles($search);
    }
    $articles = mc_articles_excerpt($articles);
    mc_reports_update('articles-searches', $search);
    return $articles;
}

function mc_article_ratings($article_id, $rating = false) {
    $article_id = mc_db_escape($article_id);
    $rating = $rating ? mc_db_escape($rating) : false;
    $now = gmdate('Y-m-d');
    $ratings = mc_isset(mc_db_get('SELECT value FROM mc_reports WHERE name = "article-ratings" AND extra = "' . mc_db_escape($article_id) . '" AND creation_time = "' . $now . '"'), 'value', []);
    if ($rating) {
        if (empty($ratings)) {
            return mc_db_query('INSERT INTO mc_reports (name, value, creation_time, external_id, extra) VALUES ("article-ratings", "[' . $rating . ']", "' . $now . '", NULL, "' . $article_id . '")');
        } else {
            $ratings = json_decode($ratings);
            array_push($ratings, intval($rating));
            return mc_db_query('UPDATE mc_reports SET value = "' . json_encode($ratings) . '" WHERE name = "article-ratings" AND extra = "' . $article_id . '"');
        }
    }
    return $ratings;
}

function mc_init_articles_admin() {
    $articles = mc_get_external_setting('articles'); // Deprecated
    if ($articles) { // Deprecated
        mc_temp_deprecated_articles_migration(); // Deprecated
    } // Deprecated
    $articles_all = mc_db_get('SELECT id, title, language, parent_id FROM mc_articles ORDER BY id', false);
    $articles = [];
    $articles_translations = [];
    $cloud_chat_id = '';
    for ($i = 0; $i < count($articles_all); $i++) {
        if ($articles_all[$i]['language']) {
            $id = $articles_all[$i]['parent_id'];
            $languages = mc_isset($articles_translations, $id, []);
            array_push($languages, [$articles_all[$i]['language'], $articles_all[$i]['id']]);
            $articles_translations[$id] = $languages;
        } else {
            array_push($articles, $articles_all[$i]);
        }
    }
    if (defined('ARTICLES_URL')) {
        require_once(MC_CLOUD_PATH . '/account/functions.php');
        $cloud_chat_id = account_chat_id(get_active_account_id());
    }
    return [$articles, mc_get_articles_categories(), $articles_translations, mc_get_articles_page_url(), mc_is_articles_url_rewrite(false), $cloud_chat_id];
}

function mc_articles_excerpt($articles) {
    for ($i = 0; $i < count($articles); $i++) {
        $content = strip_tags(mc_isset($articles[$i], 'content', ''));
        $articles[$i]['editor_js'] = '';
        $articles[$i]['content'] = strlen($content) > 100 ? mb_substr($content, 0, 100) . '...' : $content;
    }
    return $articles;
}

function mc_get_articles_page() {
    require_once(MC_PATH . '/include/articles.php');
}

function mc_get_article_url($article) {
    if (is_numeric($article)) {
        $article = mc_db_get('SELECT slug, id FROM mc_articles WHERE id = ' . mc_db_escape($article, true));
    }
    if (empty($article)) {
        return '';
    }
    $articles_page_url = mc_get_articles_page_url();
    $articles_page_url_slash = $articles_page_url . (substr($articles_page_url, -1) == '/' ? '' : '/');
    $url_rewrite = $articles_page_url && mc_is_articles_url_rewrite();
    return $url_rewrite ? $articles_page_url_slash . mc_isset($article, 'slug', $article['id']) : $articles_page_url . '?article_id=' . $article['id'];
}

function mc_get_articles_page_url() {
    return trim(mc_get_setting('articles-page-url', mc_defined('ARTICLES_URL')));
}

function mc_is_articles_url_rewrite($check_referrer = true) {
    return mc_get_setting('articles-url-rewrite') || (defined('ARTICLES_URL') && (!$check_referrer || empty($_SERVER['HTTP_REFERER']) || strpos(ARTICLES_URL, $_SERVER['HTTP_REFERER'])) && (!mc_get_setting('articles-page-url') || strpos(ARTICLES_URL, parse_url(mc_get_setting('articles-page-url'), PHP_URL_HOST))));
}

?>