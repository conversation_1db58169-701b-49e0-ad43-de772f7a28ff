<?php

/*
 * ==========================================================
 * WHATSAPP APP POST FILE
 * ==========================================================
 *
 * WhatsApp app post file to receive messages sent by Twilio. © 2017-2025 masichat.com. All rights reserved.
 *
 */

if (isset($_GET['hub_mode']) && $_GET['hub_mode'] == 'subscribe') {
    require('../../include/functions.php');
    mc_cloud_load_by_url();
    if ($_GET['hub_verify_token'] == mc_get_multi_setting('whatsapp-cloud', 'whatsapp-cloud-key')) {
        echo $_GET['hub_challenge'];
    }
    die();
}
$raw = file_get_contents('php://input');
if ($raw) {
    require('../../include/functions.php');
    mc_cloud_load_by_url();
    $provider = mc_whatsapp_provider();
    $twilio = $provider == 'twilio';
    $response = [];
    if ($twilio) {
        $items = explode('&', urldecode($raw));
        for ($i = 0; $i < count($items); $i++) {
            $value = explode('=', $items[$i]);
            $response[$value[0]] = str_replace('\/', '/', $value[1]);
        }
    } else {
        $response = json_decode($raw, true);
        if (isset($response['entry']) && isset($response['entry'][0]['changes'])) {
            $response = $response['entry'][0]['changes'][0]['value'];
        }
    }
    $error = $twilio ? mc_isset($response, 'ErrorCode') : (isset($response['statuses']) && is_array($response['statuses']) && count($response['statuses']) && isset($response['statuses'][0]['errors']) ? $response['statuses'][0]['errors'][0]['code'] : false);
    if (($twilio && isset($response['From']) && !$error) || (!$twilio && isset($response['messages']))) {
        if ($twilio) {
            if (!isset($response['Body']) && !isset($response['MediaContentType0'])) {
                die();
            }
            if (strpos($response['From'], 'whatsapp:') === false) {
                require('../../include/api.php');
                return;
            }
        }
        $GLOBALS['MC_FORCE_ADMIN'] = true;
        $user_id = false;
        $conversation_id = false;
        $phone = $twilio ? str_replace('whatsapp:', '', $response['From']) : '+' . $response['contacts'][0]['wa_id'];
        $user = mc_get_user_by('phone', $phone);
        $phone_number_id = isset($response['metadata']) ? mc_isset($response['metadata'], 'phone_number_id') : false;
        $department = false;
        $tags = false;
        $delayed_message = false;
        $payload = '';
        $message = '';
        $new_conversation = false;
        $flow = false;
        $is_new_user = false;
        if ($provider == 'official' && $phone_number_id) {
            $phone_number_settings = mc_whatsapp_cloud_get_phone_numbers($phone_number_id);
            if (!$phone_number_settings) {
                mc_error('phone-number-not-found', 'post.php', 'Phone number not found. Phone number ID: ' . $phone_number_id, true);
                die();
            }
            $department = mc_isset($phone_number_settings, 'whatsapp-cloud-numbers-department');
            $tags = mc_isset($phone_number_settings, 'whatsapp-cloud-numbers-tags');
        }
        if ($twilio) {
            $message = $response['Body'];
        } else {
            $message_2 = $response['messages'][0];
            $message_type = $message_2['type'];
            if ($message_type == 'button') {
                $message = $message_2['button']['text'];
                $message_type = 'text';
            } else if ($message_type == 'text') {
                $message = $message_2['text']['body'];
                $referral = mc_isset($message_2, 'referral');
                if ($referral && mc_isset($referral, 'source_type') == 'post') {
                    $message .= PHP_EOL . PHP_EOL . '__*' . $referral['headline'] . '*' . PHP_EOL . $referral['body'] . PHP_EOL . $referral['source_url'] . '__';
                }
            } else if ($message_type == 'interactive') {
                $flow = json_decode(mc_isset(mc_isset(mc_isset($message_2, 'interactive'), 'nfm_reply'), 'response_json'), true);
            }
            $payload = json_encode(['waid' => $message_2['id']]);
        }

        // User and conversation
        if (!$user) {
            $name = mc_split_name($twilio ? $response['ProfileName'] : $response['contacts'][0]['profile']['name']);
            $extra = ['phone' => [$phone, 'Phone']];
            if (defined('MC_DIALOGFLOW')) {
                $extra['language'] = mc_google_language_detection_get_user_extra($message);
            }
            $user_id = mc_add_user([], $extra);
            $is_new_user = true;
            mc_update_user($user_id, ['first_name' => $name[0], 'last_name' => $name[1], 'user_type' => 'user']);
            $user = mc_get_user($user_id);
        } else {
            $user_id = $user['id'];
            $conversation_id = mc_whatsapp_get_conversation_id($user_id, $phone_number_id);
        }
        $GLOBALS['MC_LOGIN'] = $user;
        $is_routing = mc_routing_is_active();
        if (!$conversation_id) {
            $conversation_id = mc_isset(mc_new_conversation($user_id, 2, '', $department, $is_routing ? mc_routing_find_best_agent($department) : -1, 'wa', $phone_number_id, false, false, $tags), 'details', [])['id'];
            $new_conversation = true;
            if (empty($user['email']) && in_array(mc_get_setting('registration-required'), ['registration', 'registration-login'])) {
                $response_wa = mc_whatsapp_cloud_flow_send_builtin($phone, $phone_number_id, 'registration');
                if (empty($response_wa['error'])) {
                    $delayed_message = '[registration]';
                }
            }
        } else {
            if ($payload && mc_isset(mc_db_get('SELECT COUNT(*) AS `count` FROM mc_messages WHERE conversation_id =  ' . $conversation_id . ' AND payload LIKE "%' . mc_db_escape($payload) . '%"'), 'count') != 0) {
                die();
            }
            if ($is_routing && mc_isset(mc_db_get('SELECT status_code FROM mc_conversations WHERE id = ' . $conversation_id), 'status_code') == 3) {
                mc_update_conversation_agent($conversation_id, mc_routing_find_best_agent($department));
            }
        }
        if ($is_new_user) {
            mc_update_user($user_id, ['profile_image' => mc_get_avatar($name[0], $name[1])]);
        }

        // Attachments
        $attachments = [];
        if ($twilio) {
            $extension = mc_isset($response, 'MediaContentType0');
            if ($extension) {
                $extension = mc_whatsapp_get_extension($extension);
                if ($extension) {
                    $file_name = strtolower(basename($response['MediaUrl0'])) . ($extension == '.ogg' ? '_voice_message' : '') . $extension;
                    $settings = mc_get_setting('whatsapp-twilio');
                    array_push($attachments, [$file_name, mc_download_file($response['MediaUrl0'], $file_name, false, ['Authorization: Basic ' . base64_encode($settings['whatsapp-twilio-user'] . ':' . $settings['whatsapp-twilio-token'])])]);
                }
            }
        } else if ($message_type != 'text' && $message_type != 'unsupported') {
            $file_data = $message_2[$message_type];
            switch ($message_type) {
                case 'location':
                    $message = 'https://www.google.com/maps/place/' . $message_2[$message_type]['latitude'] . ',' . $message_2[$message_type]['longitude'];
                    break;
                case 'reaction':
                    $message = $file_data['emoji'];
                    break;
                case 'contacts':
                    for ($i = 0; $i < count($file_data); $i++) {
                        $message .= $file_data[$i]['phones'][0]['phone'] . PHP_EOL;
                    }
                    break;
                case 'interactive':
                    $message = $file_data[$file_data['type']]['title'];
                    break;
                case 'order':
                    $total = 0;
                    $products = $file_data['product_items'];
                    for ($i = 0; $i < count($products); $i++) {
                        $price = intval($products[$i]['item_price']);
                        $quantity = intval($products[$i]['quantity']);
                        $message .= '*' . $price . $products[$i]['currency'] . '* ' . $products[$i]['product_retailer_id'] . ($quantity > 1 ? ' __x' . $quantity . '__' : '') . PHP_EOL;
                        $total += $price;
                    }
                    $message = '`' . mc_('New order') . '` ' . $products[0]['currency'] . ' ' . $total . PHP_EOL . $message;
                    $url = mc_get_setting('whatsapp-order-webhook');
                    if ($url) {
                        mc_curl($url, $raw, ['Content-Type: application/json', 'Content-Length: ' . strlen($raw)]);
                    }
                    if (defined('MC_WOOCOMMERCE')) {
                        $woocommerce_wa_carts = mc_get_external_setting('wc-whatsapp-carts', []);
                        $products['expiration'] = time() + 2600000;
                        $woocommerce_wa_carts[trim(str_replace('+', '', $phone))] = $products;
                        mc_save_external_setting('wc-whatsapp-carts', $woocommerce_wa_carts);
                    }
                    break;
                default:
                    $sticker = $message_type == 'sticker';
                    $mime = mc_isset($file_data, 'mime_type');
                    $is_audio = $mime == 'audio/ogg; codecs=opus';
                    $file_name = mc_isset($file_data, 'filename', $file_data['id']) . ($is_audio ? '_voice_message.ogg' : ($sticker ? '.webp' : ''));
                    $url = false;
                    if ($provider == '360') {
                        $url = mc_download_file('https://waba-v2.360dialog.io/media/' . $file_data['id'], rand(9999999, 99999999999) . '_' . $file_name, $mime, ['D360-API-KEY: ' . mc_get_multi_setting('whatsapp-360', 'whatsapp-360-key')]);
                    } else {
                        $media_url = mc_isset(mc_whatsapp_cloud_curl($file_data['id'], false, $phone_number_id, 'GET'), 'url');
                        if ($media_url) {
                            $url = mc_download_file($media_url, ($sticker ? 'sticker_' : '') . rand(9999999, 99999999999) . '_' . $file_name, $mime, ['Authorization: Bearer ' . mc_whatsapp_cloud_get_token($phone_number_id)]);
                        }
                    }
                    if ($url) {
                        array_push($attachments, [mc_string_slug(basename($url)), $url]);
                    }
                    if (isset($file_data['caption']) && $file_data['caption'] != $file_name) {
                        $message = $file_data['caption'];
                    }
            }
        }

        // Flows response
        if ($flow && in_array(mc_isset($flow, 'mc_type'), ['registration', 'email'])) {
            unset($flow['flow_token']);
            unset($flow['mc_type']);
            $flow = array_merge($user, $flow);
            mc_update_user($user_id, $flow, $flow, true, true);
        }

        // Send message
        $response = mc_send_message($user_id, $conversation_id, $message, $attachments, false, $payload);

        // Dialogflow, Notifications, Bot messages
        $response_extarnal = mc_messaging_platforms_functions($conversation_id, $message, $attachments, $user, ['source' => 'wa', 'platform_value' => $phone, 'new_conversation' => $new_conversation, 'extra' => $phone_number_id]);

        // Queue
        mc_queue_check_and_run($conversation_id, $department, 'wa');

        // Miscellaneous
        mc_update_users_last_activity($user_id);
        if ($delayed_message) {
            mc_send_message(mc_get_bot_id(), $conversation_id, $delayed_message);
        }

        $GLOBALS['MC_FORCE_ADMIN'] = false;
    } else if ($error) {
        if (!$twilio) {
            $response = $response['statuses'][0];
        }
        $phone = $twilio ? str_replace('whatsapp:', '', $response['To']) : $response['recipient_id'];
        $user = mc_get_user_by('phone', $phone);
        if (!isset($response['ErrorMessage']) && isset($response['MessageStatus'])) {
            $response['ErrorMessage'] = $response['MessageStatus'];
        }
        if ($user) {
            $agents_ids = mc_get_agents_ids();
            $message = mc_db_get('SELECT id, message, attachments, conversation_id FROM mc_messages WHERE user_id IN (' . implode(',', $agents_ids) . ') AND conversation_id IN (SELECT id FROM mc_conversations WHERE source = "wa" AND user_id = ' . $user['id'] . ') ORDER BY id DESC LIMIT 1');
            if ($message) {
                $GLOBALS['MC_FORCE_ADMIN'] = true;
                $conversation_id = $message['conversation_id'];
                $user_language = mc_get_user_language($user['id']);
                $user_name = mc_get_user_name($user);
                $user_email = mc_isset($user, 'email', '');
                $conversation_url_parameter = $conversation_id && $user ? ('?conversation=' . $conversation_id . '&token=' . $user['token']) : '';

                // SMS
                if (mc_get_multi_setting('whatsapp-sms', 'whatsapp-sms-active')) {
                    $template = mc_get_multi_setting('whatsapp-sms', 'whatsapp-sms-template');
                    $message_sms = $template ? str_replace('{message}', $message['message'], mc_t($template, $user_language)) : $message['message'];
                    $message_sms = str_replace(['{conversation_url_parameter}', '{recipient_name}', '{recipient_email}'], [$conversation_url_parameter, $user_name, $user_email], $message_sms);
                    $response_sms = mc_send_sms($message_sms, $phone, false, $conversation_id, empty($message['attachments']) ? [] : json_decode($message['attachments']));
                    if ($response_sms['status'] == 'sent' || $response_sms['status'] == 'queued') {
                        $response = ['whatsapp-templates' => true];
                    }
                }

                // WhatsApp Template
                $phone_number_id = $conversation_id && !$twilio && is_array(mc_get_setting('whatsapp-cloud-numbers')) ? mc_isset(mc_db_get('SELECT extra FROM mc_conversations WHERE id = ' . mc_db_escape($conversation_id, true)), 'extra') : false;
                $response_template = mc_whatsapp_send_template($phone, $user_language, $conversation_url_parameter, $user_name, $user_email, false, $phone_number_id);
                if (($twilio && ($response_template['status'] == 'sent' || $response_template['status'] == 'queued')) || (!$twilio && $response_template && isset($response_template['messages']))) {
                    if (isset($response['whatsapp-templates'])) {
                        $response['whatsapp-template-fallback'] = true;
                    } else {
                        $response = ['whatsapp-template-fallback' => true];
                    }
                } else if (!$twilio && isset($response_template['errors'])) {
                    $response = ['ErrorCode' => true, 'ErrorMessage' => $response_template['errors'][0]['details']];
                }
                $response['delivery_failed'] = 'wa';
                mc_update_message($message['id'], false, false, $response);
                $GLOBALS['MC_FORCE_ADMIN'] = false;
            }
        }
    }
}

function mc_whatsapp_get_extension($mime_type) {
    switch ($mime_type) {
        case 'video/mp4':
            return '.mp4';
        case 'image/gif':
            return '.gif';
        case 'image/png':
            return '.png';
        case 'image/jpg':
        case 'image/jpeg':
            return '.jpg';
        case 'image/webp':
            return '.webp';
        case 'audio/ogg':
            return '.ogg';
        case 'audio/mpeg':
            return '.mp3';
        case 'audio/amr':
            return '.amr';
        case 'application/pdf':
            return '.pdf';
    }
    return false;
}

die();

?>