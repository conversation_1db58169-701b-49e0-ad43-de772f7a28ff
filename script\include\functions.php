<?php
use Swoole\Http\Response;

/*
 * ==========================================================
 * FUNCTIONS.PHP
 * ==========================================================
 *
 * Main PHP functions file. © 2017-2025 masichat.com. All rights reserved.
 *
 */

define('MC_VERSION', '3.8.2');

if (!defined('MC_PATH')) {
    $path = dirname(__DIR__, 1);
    define('MC_PATH', $path ? $path : dirname(__DIR__));
}
if (!defined('JSON_INVALID_UTF8_IGNORE')) {
    define('JSON_INVALID_UTF8_IGNORE', 0);
}
if (isset($_COOKIE['mc-cloud'])) {
    $_POST['cloud'] = $_COOKIE['mc-cloud'];
}

require_once(MC_PATH . '/config.php');
require_once(MC_PATH . '/include/functions_users.php');
require_once(MC_PATH . '/include/functions_messages.php');
require_once(MC_PATH . '/include/functions_settings.php');
require_once(MC_PATH . '/include/functions_email.php');

global $MC_CONNECTION;
global $MC_SETTINGS;
global $MC_LOGIN;
global $MC_LANGUAGE;
global $MC_TRANSLATIONS;
const SELECT_FROM_USERS = 'SELECT id, first_name, last_name, email, profile_image, user_type, creation_time, last_activity, department, token';

class MCError {
    public $error;

    function __construct($error_code, $function = '', $message = '', $response = '') {
        $this->error = ['message' => $message, 'function' => $function, 'code' => $error_code, 'response' => $response];
    }

    public function __toString() {
        return $this->code() . ' ' . $this->message();
    }

    function message() {
        return $this->error['message'];
    }

    function code() {
        return $this->error['code'];
    }

    function response() {
        return $this->error['response'];
    }

    function function_name() {
        return $this->error['function'];
    }
}

class MCValidationError {
    public $error;

    function __construct($error_code) {
        $this->error = $error_code;
    }

    public function __toString() {
        return $this->error;
    }

    function code() {
        return $this->error;
    }
}

$mc_apps = ['dialogflow', 'slack', 'wordpress', 'tickets', 'woocommerce', 'ump', 'perfex', 'whmcs', 'aecommerce', 'messenger', 'whatsapp', 'armember', 'viber', 'telegram', 'line', 'wechat', 'zalo', 'twitter', 'zendesk', 'martfury', 'opencart'];
for ($i = 0; $i < count($mc_apps); $i++) {
    $file = MC_PATH . '/apps/' . $mc_apps[$i] . '/functions.php';
    if (file_exists($file)) {
        require_once($file);
    }
}

/*
 * -----------------------------------------------------------
 * DATABASE
 * -----------------------------------------------------------
 *
 * 1. Connection to the database
 * 2. Get database values
 * 3. Insert or update database values
 * 4. Escape and sanatize values prior to databse insertion
 * 5. Escape a JSON string prior to databse insertion
 * 6. Set default database environment settings
 * 7. Database error function
 *
 */

function mc_db_connect() {
    global $MC_CONNECTION;
    if (!defined('MC_DB_NAME') || !MC_DB_NAME) {
        return false;
    }
    if ($MC_CONNECTION) {
        mc_db_init_settings();
        return true;
    }
    $MC_CONNECTION = new mysqli();
    $certificate_path = mc_defined('MC_DB_CERTIFICATE_PATH');
    $port = defined('MC_DB_PORT') && MC_DB_PORT ? intval(MC_DB_PORT) : ini_get('mysqli.default_port');
    $socket = mc_defined('MC_DB_SOCKET', null);
    if ($certificate_path) {
        $MC_CONNECTION->ssl_set(MC_PATH . $certificate_path . MC_DB_CERTIFICATE_CLIENT_KEY, MC_PATH . $certificate_path . MC_DB_CERTIFICATE_CLIENT, MC_PATH . $certificate_path . MC_DB_CERTIFICATE_CA, null, null);
    }
    try {
        if (!$MC_CONNECTION->real_connect(MC_DB_HOST, MC_DB_USER, MC_DB_PASSWORD, MC_DB_NAME, $socket ? null : $port, $socket, $certificate_path ? MYSQLI_CLIENT_SSL : 0)) {
            echo 'Connection error. Visit the admin area for more details or open the config.php file and check the database information. Message: ' . $MC_CONNECTION->connect_error . '.';
            return false;
        }
    } catch (Exception $exception) {
        if (isset($_COOKIE['mc-cloud'])) {
            setcookie('mc-cloud', '', 0, '/');
            setcookie('mc-login', '', 0, '/');
            die('<script>localStorage.removeItem("masi-chat"); location.reload();</script>');
        }
    }
    mc_db_init_settings();
    return true;
}

function mc_db_get($query, $single = true) {
    global $MC_CONNECTION;
    $status = mc_db_connect();
    $value = ($single ? '' : []);
    if ($status) {
        $result = $MC_CONNECTION->query($query);
        if ($result) {
            if ($result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    if ($single) {
                        $value = $row;
                    } else {
                        array_push($value, $row);
                    }
                }
            }
        } else {
            return mc_db_error('mc_db_get');
        }
    } else {
        return $status;
    }
    return $value;
}

function mc_db_query($query, $return = false) {
    global $MC_CONNECTION;
    $status = mc_db_connect();
    if ($status) {
        $result = $MC_CONNECTION->query($query);
        if ($result) {
            if ($return) {
                if (isset($MC_CONNECTION->insert_id) && $MC_CONNECTION->insert_id > 0) {
                    return $MC_CONNECTION->insert_id;
                } else {
                    return mc_db_error('mc_db_query');
                }
            } else {
                return true;
            }
        } else {
            return mc_db_error('mc_db_query');
        }
    } else {
        return $status;
    }
}

function mc_db_escape($value, $numeric = -1) {
    if (is_numeric($value)) {
        return $value;
    } else if ($numeric === true) {
        if (is_bool($numeric)) {
            return false;
        }
        die(mc_error('value-not-numeric', 'mc_db_escape', 'Value not numeric', true));
    }
    global $MC_CONNECTION;
    mc_db_connect();
    if ($MC_CONNECTION && $value) {
        $value = $MC_CONNECTION->real_escape_string($value);
    }
    $value = str_replace(['\"', '"'], ['"', '\"'], $value);
    $value = mc_sanatize_string($value);
    $value = htmlspecialchars($value, ENT_NOQUOTES | ENT_SUBSTITUTE, 'utf-8');
    $value = str_replace('&amp;lt;', '&lt;', $value);
    return $value;
}

function mc_db_json_escape($array) {
    if (empty($array)) {
        return '';
    }
    global $MC_CONNECTION;
    mc_db_connect();
    $value = str_replace(['"false"', '"true"'], ['false', 'true'], json_encode($array, JSON_INVALID_UTF8_IGNORE | JSON_UNESCAPED_UNICODE));
    $value = mc_sanatize_string($value);
    return $MC_CONNECTION ? $MC_CONNECTION->real_escape_string($value) : $value;
}

function mc_json_escape($value) {
    return str_replace(['"', "\'"], ['\"', "'"], $value);
}

function mc_db_error($function) {
    global $MC_CONNECTION;
    return mc_error('db-error', $function, $MC_CONNECTION->error);
}

function mc_db_check_connection($name = false, $user = false, $password = false, $host = false, $port = false) {
    global $MC_CONNECTION;
    $response = true;
    if ($name === false && defined('MC_DB_NAME')) {
        $name = MC_DB_NAME;
        $user = MC_DB_USER;
        $password = MC_DB_PASSWORD;
        $host = MC_DB_HOST;
        $port = defined('MC_DB_PORT') && MC_DB_PORT ? MC_DB_PORT : ini_get('mysqli.default_port');
    }
    if ($name === false || !$name) {
        return 'installation';
    }
    try {
        set_error_handler(function () {}, E_ALL);
        $MC_CONNECTION = new mysqli();
        $certificate_path = mc_defined('MC_DB_CERTIFICATE_PATH');
        $socket = mc_defined('MC_DB_SOCKET', null);
        if ($certificate_path) {
            $MC_CONNECTION->ssl_set(MC_PATH . $certificate_path . MC_DB_CERTIFICATE_CLIENT_KEY, MC_PATH . $certificate_path . MC_DB_CERTIFICATE_CLIENT, MC_PATH . $certificate_path . MC_DB_CERTIFICATE_CA, null, null);
        }
        $MC_CONNECTION->real_connect($host, $user, $password, $name, $socket ? null : intval($port), $socket, $certificate_path ? MYSQLI_CLIENT_SSL : 0);
        mc_db_init_settings();
    } catch (Exception $e) {
        $response = $e->getMessage();
    }
    if ($MC_CONNECTION->connect_error) {
        $response = $MC_CONNECTION->connect_error;
    }
    restore_error_handler();
    return $response;
}

function mc_db_init_settings() {
    if (mc_is_cloud()) {
        return;
    }
    global $MC_CONNECTION;
    $MC_CONNECTION->set_charset('utf8mb4');
    $MC_CONNECTION->query("SET SESSION sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''))");
}

function mc_external_db($action, $name, $query = '', $extra = false) {
    $NAME = strtoupper($name);
    $name = strtolower($name);
    switch ($action) {
        case 'connect':
            $connection = mc_isset($GLOBALS, 'MC_' . $NAME . '_CONNECTION');
            $defined = defined('MC_' . $NAME . '_DB_NAME');
            if (!empty($connection) && $connection->ping()) {
                return true;
            }
            if (!$defined) {
                $prefix = '';
                $database = mc_get_setting($name . '-db');
                if (empty($database[$name . '-db-name'])) {
                    return mc_error('db-error', 'mc_external_db', 'Missing database details in ' . $name . ' settings area.');
                }
                define('MC_' . $NAME . '_DB_HOST', $database[$name . '-db-host']);
                define('MC_' . $NAME . '_DB_USER', $database[$name . '-db-user']);
                define('MC_' . $NAME . '_DB_PASSWORD', $database[$name . '-db-password']);
                define('MC_' . $NAME . '_DB_NAME', $database[$name . '-db-name']);
                if ($name == 'perfex' || $name == 'whmcs') {
                    define('MC_' . $NAME . '_DB_PREFIX', empty($database[$name . '-db-prefix']) ? 'tbl' : $database[$name . '-db-prefix']);
                    $prefix = PHP_EOL . 'define(\'MC_' . $NAME . '_DB_PREFIX\', \'' . mc_isset($database, $name . '-db-prefix', 'tbl') . '\');';
                }
                mc_write_config_extra('/* ' . $NAME . ' CRM  */' . PHP_EOL . 'define(\'MC_' . $NAME . '_DB_HOST\', \'' . $database[$name . '-db-host'] . '\');' . PHP_EOL . 'define(\'MC_' . $NAME . '_DB_USER\', \'' . $database[$name . '-db-user'] . '\');' . PHP_EOL . 'define(\'MC_' . $NAME . '_DB_PASSWORD\', \'' . $database[$name . '-db-password'] . '\');' . PHP_EOL . 'define(\'MC_' . $NAME . '_DB_NAME\', \'' . $database[$name . '-db-name'] . '\');' . $prefix);
            }
            $connection = new mysqli(constant('MC_' . $NAME . '_DB_HOST'), constant('MC_' . $NAME . '_DB_USER'), constant('MC_' . $NAME . '_DB_PASSWORD'), constant('MC_' . $NAME . '_DB_NAME'));
            if ($connection->connect_error) {
                if ($defined) {
                    $database = mc_get_setting($name . '-db');
                    if (constant('MC_' . $NAME . '_DB_HOST') != $database[$name . '-db-host'] || constant('MC_' . $NAME . '_DB_USER') != $database[$name . '-db-user'] || constant('MC_' . $NAME . '_DB_PASSWORD') != $database[$name . '-db-password'] || constant('MC_' . $NAME . '_DB_NAME') != $database[$name . '-db-name'] || (defined('MC_' . $NAME . '_DB_PREFIX') && constant('MC_' . $NAME . '_DB_PREFIX') != $database[$name . '-db-prefix'])) {
                        $raw = file_get_contents(MC_PATH . '/config.php');
                        mc_file(MC_PATH . '/config.php', str_replace(['/* Perfex CRM  */', 'define(\'MC_' . $NAME . '_DB_HOST\', \'' . constant('MC_' . $NAME . '_DB_HOST') . '\');', 'define(\'MC_' . $NAME . '_DB_USER\', \'' . constant('MC_' . $NAME . '_DB_USER') . '\');', 'define(\'MC_' . $NAME . '_DB_PASSWORD\', \'' . constant('MC_' . $NAME . '_DB_PASSWORD') . '\');', 'define(\'MC_' . $NAME . '_DB_NAME\', \'' . constant('MC_' . $NAME . '_DB_NAME') . '\');', defined('MC_' . $NAME . '_DB_PREFIX') ? 'define(\'MC_' . $NAME . '_DB_PREFIX\', \'' . constant('MC_' . $NAME . '_DB_PREFIX') . '\');' : ''], '', $raw));
                    }
                }
                die($connection->connect_error);
            }
            $connection->set_charset('utf8mb4');
            $connection->query("SET SESSION sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''))");
            $GLOBALS['MC_' . $NAME . '_CONNECTION'] = $connection;
            return true;
        case 'read':
            $status = mc_external_db('connect', $name);
            $value = $extra ? '' : [];
            if ($status === true) {
                $result = $GLOBALS['MC_' . strtoupper($name) . '_CONNECTION']->query($query);
                if ($result) {
                    if ($result->num_rows > 0) {
                        while ($row = $result->fetch_assoc()) {
                            if ($extra) {
                                $value = $row;
                            } else {
                                array_push($value, $row);
                            }
                        }
                    }
                } else {
                    return mc_error('db-error', 'mc_external_db', $GLOBALS['MC_' . strtoupper($name) . '_CONNECTION']->error);
                }
            } else {
                return $status;
            }
            return $value;
        case 'write':
            $status = mc_external_db('connect', $name);
            if ($status === true) {
                $connection = $GLOBALS['MC_' . $NAME . '_CONNECTION'];
                $result = $connection->query($query);
                if ($result) {
                    if ($extra) {
                        if (isset($connection->insert_id) && $connection->insert_id > 0) {
                            return $connection->insert_id;
                        } else {
                            return mc_db_error('mc_db_query');
                        }
                    } else {
                        return true;
                    }
                } else {
                    return mc_error('db-error', 'mc_external_db', $connection->error);
                }
            }
            return $status;
    }
    return false;
}

function mc_is_error($object) {
    return is_a($object, 'MCError');
}

function mc_is_validation_error($object) {
    return is_a($object, 'MCValidationError');
}

/*
 * -----------------------------------------------------------
 * TRANSLATIONS
 * -----------------------------------------------------------
 *
 * 1. Return the translation of a string
 * 2. Echo the translation of a string
 * 3. Returns the translation of a setting
 * 4. Echos the translations of as setting
 * 5. Translate using AI multilingual translation if active
 * 6. Initialize the translations
 * 7. Return the current translations array
 * 8. Return all the translations of both admin and front areas of all languages
 * 9. Return the translations of a language
 * 10. Save a translation langauge file and a copy of it as backup
 * 11. Restore a translation language file from a backup
 * 12. Return the user langauge code
 * 13. Return the langauge code of the admin area relative to the active agent
 * 14. Translate a string in the given language
 * 15. Get language code
 * 16. Return the language code of the given language name
 * 17. Check if the language is RTL
 *
 */

function mc_($string) {
    if ($string) {
        global $MC_TRANSLATIONS;
        if (!isset($MC_TRANSLATIONS)) {
            mc_init_translations();
        }
        return empty($MC_TRANSLATIONS[$string]) ? $string : $MC_TRANSLATIONS[$string];
    }
    return $string;
}

function mc_e($string) {
    echo mc_($string);
}

function mc_s($string, $disabled = false) {
    if ($disabled) {
        return $string;
    }
    global $MC_TRANSLATIONS_SETTINGS;
    if (!isset($MC_TRANSLATIONS_SETTINGS)) {
        $language = mc_get_admin_language();
        if ($language && $language != 'en') {
            $file_path = MC_PATH . '/resources/languages/admin/settings/' . $language . '.json';
            if (file_exists($file_path)) {
                $MC_TRANSLATIONS_SETTINGS = json_decode(file_get_contents($file_path), true);
            }
        }
    }
    return empty($MC_TRANSLATIONS_SETTINGS[$string]) ? $string : $MC_TRANSLATIONS_SETTINGS[$string];
}

function mc_se($string) {
    echo mc_s($string);
}

function mc_t($string, $language_code = false) {
    global $MC_LANGUAGE;
    global $MC_TRANSLATIONS;
    if (!$language_code || $language_code == -1) {
        $language_code = mc_get_user_language(mc_get_active_user_ID());
    }
    if (!$language_code || $language_code == -1 || $language_code == 'en') {
        return $string;
    }
    if (empty($MC_LANGUAGE) || empty($MC_TRANSLATIONS) || $MC_LANGUAGE[0] != $language_code) {
        if (!empty($_POST['init.php']) && !mc_get_setting('front-auto-translations')) {
            $MC_LANGUAGE = [mc_defined('MC_CLOUD_DEFAULT_LANGUAGE_CODE', 'en'), 'front'];
        } else {
            $path = MC_PATH . '/resources/languages/front/' . $language_code . '.json';
            if (mc_is_cloud()) {
                $cloud_path = MC_PATH . '/uploads/cloud/languages/' . mc_isset(mc_cloud_account(), 'user_id') . '/front/' . $language_code . '.json';
                if (file_exists($cloud_path)) {
                    $path = $cloud_path;
                }
            }
            if (file_exists($path)) {
                $MC_TRANSLATIONS = json_decode(file_get_contents($path), true);
                $MC_LANGUAGE = [$language_code, 'front'];
            }
        }
    }
    if (empty($MC_TRANSLATIONS[$string])) {
        if (defined('MC_DIALOGFLOW') && (mc_get_setting('dialogflow-multilingual-translation') || mc_get_multi_setting('google', 'google-multilingual-translation'))) { // Deprecated: mc_get_setting('dialogflow-multilingual-translation')
            $response = mc_google_translate([$string], $language_code);
            if (!empty($response[0])) {
                $translations_to_save = $MC_TRANSLATIONS;
                if (empty($translations_to_save)) {
                    $path = MC_PATH . '/resources/languages/front/' . $language_code . '.json';
                    if (file_exists($path)) {
                        $translations_to_save = json_decode(file_get_contents($path), true);
                    }
                }
                $translation = $response[0][0];
                if ($translations_to_save) {
                    $translations_to_save[$string] = $translation;
                    $translations_file = [];
                    $translations_file[$language_code] = ['front' => $translations_to_save];
                    mc_save_translations($translations_file);
                }
                return $translation;
            }
        }
        return $string;
    }
    return $MC_TRANSLATIONS[$string];
}

function mc_init_translations() {
    global $MC_TRANSLATIONS;
    global $MC_LANGUAGE;
    $MC_CLOUD_DEFAULT_LANGUAGE_CODE = mc_defined('MC_CLOUD_DEFAULT_LANGUAGE_CODE');
    if (!empty($MC_LANGUAGE) && ($MC_LANGUAGE[0] != 'en' || $MC_CLOUD_DEFAULT_LANGUAGE_CODE)) {
        $path = MC_PATH . '/resources/languages/' . $MC_LANGUAGE[1] . '/' . $MC_LANGUAGE[0] . '.json';
        if (mc_is_cloud()) {
            $cloud_path = MC_PATH . '/uploads/cloud/languages/' . mc_isset(mc_cloud_account(), 'user_id') . '/' . $MC_LANGUAGE[1] . '/' . $MC_LANGUAGE[0] . '.json';
            if (file_exists($cloud_path)) {
                $path = $cloud_path;
            }
        }
        if (file_exists($path)) {
            $MC_TRANSLATIONS = json_decode(file_get_contents($path), true);
        } else {
            $MC_TRANSLATIONS = false;
        }
    } else if (!isset($MC_LANGUAGE)) {
        $MC_TRANSLATIONS = false;
        $MC_LANGUAGE = false;
        $is_init = !empty($_POST['init.php']);
        if ($is_init && !mc_get_setting('front-auto-translations')) {
            $MC_LANGUAGE = [!empty($_POST['language']) && $_POST['language'] != 'false' ? strtolower($_POST['language'][0]) : ($MC_CLOUD_DEFAULT_LANGUAGE_CODE ? $MC_CLOUD_DEFAULT_LANGUAGE_CODE : 'en'), 'front'];
            if ($MC_LANGUAGE[0] != 'en') {
                mc_init_translations();
            } else {
                return;
            }
        }
        $is_admin = mc_is_agent();
        $language = $is_admin ? mc_get_admin_language() : mc_get_user_language(mc_get_active_user_ID());
        if (($language && $language != 'en') && ($is_admin || isset($_GET['lang']) || $is_init || (mc_get_setting('dialogflow-multilingual-translation') || mc_get_multi_setting('google', 'google-multilingual-translation')))) { // Deprecated: mc_get_setting('dialogflow-multilingual-translation')
            switch ($language) {
                case 'nn':
                case 'nb':
                    $language = 'no';
                    break;
            }
            $area = $is_admin ? 'admin' : 'front';
            $path = MC_PATH . '/resources/languages/' . $area . '/' . $language . '.json';
            if (mc_is_cloud()) {
                $cloud_path = MC_PATH . '/uploads/cloud/languages/' . mc_isset(mc_cloud_account(), 'user_id') . '/' . $area . '/' . $language . '.json';
                if (file_exists($cloud_path)) {
                    $path = $cloud_path;
                }
            }
            if (file_exists($path)) {
                $MC_TRANSLATIONS = json_decode(file_get_contents($path), true);
                $MC_LANGUAGE = [$language, $area];
            }
        }
    }
}

function mc_get_current_translations() {
    global $MC_TRANSLATIONS;
    if (!isset($MC_TRANSLATIONS)) {
        mc_init_translations();
    }
    return $MC_TRANSLATIONS;
}

function mc_get_translations($is_user = false, $language_code = false) {
    $translations = [];
    $cloud_path = false;
    if ($is_user && !file_exists(MC_PATH . '/uploads/languages')) {
        return [];
    }
    $path = $is_user ? '/uploads' : '/resources';
    $language_codes = mc_get_json_resource('languages/language-codes.json');
    $paths = ['front', 'admin', 'admin/js', 'admin/settings'];
    if (mc_is_cloud()) {
        $cloud = mc_cloud_account();
        $cloud_path = MC_PATH . '/uploads/cloud/languages/' . $cloud['user_id'];
    }
    for ($i = 0; $i < count($paths); $i++) {
        $files = scandir(MC_PATH . $path . '/languages/' . $paths[$i]);
        for ($j = 0; $j < count($files); $j++) {
            $file = $files[$j];
            if (strpos($file, '.json')) {
                $code = substr($file, 0, -5);
                if (!isset($language_codes[$code]) || ($language_code && $language_code != $code)) {
                    continue;
                }
                if (!isset($translations[$code])) {
                    $translations[$code] = ['name' => $language_codes[$code]];
                }
                $translation_strings = json_decode(file_get_contents($cloud_path && file_exists($cloud_path . '/' . $paths[$i] . '/' . $file) ? ($cloud_path . '/' . $paths[$i] . '/' . $file) : (MC_PATH . $path . '/languages/' . $paths[$i] . '/' . $file)), true);
                $translations[$code][$paths[$i]] = $translation_strings;
            }
        }
    }
    return $translations;
}

function mc_get_translation($language_code) {
    return mc_isset(mc_get_translations(false, $language_code), $language_code);
}

function mc_save_translations($translations) {
    $is_cloud = mc_is_cloud();
    $cloud_path = false;
    if (!$is_cloud && !file_exists(MC_PATH . '/uploads/languages')) {
        mkdir(MC_PATH . '/uploads/languages', 0755, true);
        mkdir(MC_PATH . '/uploads/languages/front', 0755, true);
        mkdir(MC_PATH . '/uploads/languages/admin', 0755, true);
        mkdir(MC_PATH . '/uploads/languages/admin/js', 0755, true);
        mkdir(MC_PATH . '/uploads/languages/admin/settings', 0755, true);
    }
    if ($is_cloud) {
        $cloud = mc_cloud_account();
        $cloud_path = MC_PATH . '/uploads/cloud/languages/' . $cloud['user_id'];
        if (!file_exists(MC_PATH . '/uploads/cloud')) {
            mkdir(MC_PATH . '/uploads/cloud', 0755, true);
            mkdir(MC_PATH . '/uploads/cloud/languages', 0755, true);
        }
        if (!file_exists($cloud_path)) {
            mkdir($cloud_path, 0755, true);
            mkdir($cloud_path . '/front', 0755, true);
            mkdir($cloud_path . '/admin', 0755, true);
            mkdir($cloud_path . '/admin/js', 0755, true);
            mkdir($cloud_path . '/admin/settings', 0755, true);
        }
    }
    if (is_string($translations)) {
        $translations = json_decode($translations, true);
    }
    foreach ($translations as $key => $translation) {
        foreach ($translation as $key_area => $translations_list) {
            $json = str_replace('\\\n', '\n', html_entity_decode(json_encode($translations_list, JSON_INVALID_UTF8_IGNORE, JSON_UNESCAPED_UNICODE)));
            if ($json) {
                if ($is_cloud) {
                    mc_file($cloud_path . '/' . $key_area . '/' . $key . '.json', $json);
                } else {
                    $paths = ['resources', 'uploads'];
                    for ($i = 0; $i < 2; $i++) {
                        mc_file(MC_PATH . '/' . $paths[$i] . '/languages/' . $key_area . '/' . $key . '.json', $json);
                    }
                }
            }
        }
    }
    return true;
}

function mc_restore_user_translations() {
    $translations_all = mc_get_translations();
    $translations_user = mc_get_translations(true);
    $paths = ['front', 'admin', 'admin/js', 'admin/settings'];
    foreach ($translations_user as $key => $translations) {
        for ($i = 0; $i < count($paths); $i++) {
            $path = $paths[$i];
            if (isset($translations_all[$key]) && isset($translations_all[$key][$path])) {
                foreach ($translations_all[$key][$path] as $key_two => $translation) {
                    if (!isset($translations[$path][$key_two])) {
                        $translations[$path][$key_two] = $translations_all[$key][$path][$key_two];
                    }
                }
            }
            mc_file(MC_PATH . '/resources/languages/' . $path . '/' . $key . '.json', json_encode($translations[$path], JSON_INVALID_UTF8_IGNORE));
        }
    }
}

function mc_get_user_language($user_id = false, $allow_browser_language = false) {
    $setting_language = mc_get_setting('front-auto-translations');
    if ($setting_language && $setting_language != 'auto') {
        return $setting_language;
    }
    global $MC_LANGUAGE;
    $language = false;
    $post_language = mc_isset($_POST, 'language');
    if ($user_id && is_numeric($user_id)) {
        $language = mc_get_user_extra($user_id, 'language');
        if (!$language && (!$post_language || mc_isset($post_language, 0) == 'en') && ($allow_browser_language || $setting_language == 'auto')) {
            $language = mc_get_user_extra($user_id, 'browser_language');
        }
    }
    if (!$language && $post_language && $post_language != 'false') {
        $language = strtolower(mc_isset($post_language, 0));
    }
    if ($language) {
        return mc_language_code(strtolower($language));
    }
    if (empty($MC_LANGUAGE)) {
        $language_code = strtolower(mc_isset($_SERVER, 'HTTP_ACCEPT_LANGUAGE'));
        return $language_code ? mc_language_code($language_code) : '';
    }
    return $MC_LANGUAGE[0];
}

function mc_get_admin_language($user_id = false) {
    $language = defined('MC_ADMIN_LANG') ? trim(strtolower(MC_ADMIN_LANG)) : (mc_get_setting('admin-auto-translations') ? trim(strtolower(mc_get_user_language($user_id ? $user_id : mc_get_active_user_ID()))) : false);
    return $language && ($language != 'en' || defined('MC_CLOUD_DEFAULT_LANGUAGE_CODE')) ? $language : mc_defined('MC_CLOUD_DEFAULT_LANGUAGE_CODE', $language);
}

function mc_language_code($language_code_full) {
    switch (strtolower($language_code_full)) {
        case 'pt_br';
            return 'br';
        case 'zh_cn';
            return 'zh';
        case 'zh_tw';
            return 'tw';
    }
    return substr($language_code_full, 0, 2);
}

function mc_get_language_code_by_name($language_name, &$language_codes = false) {
    $language_codes = $language_codes ? $language_codes : mc_get_json_resource('languages/language-codes.json');
    if (strlen($language_name) > 2) {
        $language_code = ucfirst($language_name);
        foreach ($language_codes as $key => $value) {
            if ($language_code == $value) {
                return $key;
            }
        }
    }
    return $language_name;
}

function mc_is_rtl($language_code = false) {
    return in_array($language_code ? $language_code : mc_get_user_language(mc_get_active_user_ID()), ['ar', 'he', 'ku', 'fa', 'ur']);
}

/*
 * ----------------------------------------------------------
 * APPS, UPDATES, INSTALLATION
 * ----------------------------------------------------------
 *
 * 1. Get the plugin and apps versions and install, activate and update apps
 * 2. Check if the app license is valid and install or update it
 * 3. Install or update an app
 * 4. Update Masi Chat and all apps
 * 5. Compatibility function for new versions
 * 6. Check if there are updates available
 * 7. Get installed app versions array
 * 8. Plugin installation function
 * 9. Update the config.php file
 * 10. Return the upload path or url
 * 11. Return the installation directory name
 *
 */

function mc_get_versions() {
    return json_decode(mc_download('https://masichat.com/synch/versions.json'), true);
}

function mc_app_get_key($app_name) {
    $keys = mc_get_external_setting('app-keys');
    return isset($keys[$app_name]) ? $keys[$app_name] : '';
}

function mc_app_activation($app_name, $key) {
    if (mc_is_cloud()) {
        $active_apps = mc_get_external_setting('active_apps', []);
        array_push($active_apps, $app_name);
        return mc_save_external_setting('active_apps', $active_apps);
    }
    // Envato purchase code check removed - simulate successful activation
    $key = trim($key);
    return mc_app_update($app_name, 'success', $key);
}

function mc_app_disable($app_name) {
    $active_apps = mc_get_external_setting('active_apps', []);
    $index = array_search($app_name, $active_apps);
    if ($index !== false) {
        array_splice($active_apps, $index, 1);
        return mc_save_external_setting('active_apps', $active_apps);
    }
    return false;
}

function mc_app_update($app_name, $file_name, $key = false) {
    if (!$file_name) {
        return new MCValidationError('temporary-file-name-not-found');
    }
    $key = trim($key);
    $error = '';
    $zip = mc_download('https://masichat.com/synch/temp/' . $file_name);
    if ($zip) {
        $file_path = MC_PATH . '/uploads/' . $app_name . '.zip';
        if (!file_exists(dirname($file_path))) {
            mkdir(dirname($file_path), 0755, true);
        }
        file_put_contents($file_path, $zip);
        if (file_exists($file_path)) {
            $zip = new ZipArchive;
            if ($zip->open($file_path) === true) {
                $zip->extractTo($app_name == 'mc' ? (defined('MC_WP') ? substr(MC_PATH, 0, -13) : MC_PATH) : MC_PATH . '/apps');
                $zip->close();
                unlink($file_path);
                if ($app_name == 'mc') {
                    mc_restore_user_translations();
                    mc_file(MC_PATH . '/sw.js', str_replace('mc-' . str_replace('.', '-', MC_VERSION), 'mc-' . str_replace('.', '-', mc_get_versions()['mc']), file_get_contents(MC_PATH . '/sw.js')));
                    return 'success';
                }
                if (file_exists(MC_PATH . '/apps/' . $app_name)) {
                    if (!empty($key)) {
                        $keys = mc_get_external_setting('app-keys');
                        $keys[$app_name] = $key;
                        mc_save_external_setting('app-keys', $keys);
                    }
                    return 'success';
                } else {
                    $error = 'zip-extraction-error';
                }
            } else {
                $error = 'zip-error';
            }
        } else {
            $error = 'file-not-found';
        }
    } else {
        $error = 'download-error';
    }
    return $error ? new MCValidationError($error) : false;
}

function mc_update() {
    // Envato purchase code check removed - simulate successful update
    $latest_versions = mc_get_versions();
    $installed_apps_versions = mc_get_installed_apps_version();
    $result = [];

    // Simulate successful updates for all apps
    foreach ($installed_apps_versions as $key => $value) {
        if ($value && $value != $latest_versions[$key]) {
            $result[$key] = 'success';
        }
    }

    return $result;
}

function mc_updates_validation() {

    // Temp. Deprecated
    try {
        if (!mc_is_debug()) {
            mc_db_query('ALTER TABLE mc_conversations ADD COLUMN extra_3 varchar(191) AFTER extra_2');
            mc_db_query('CREATE TABLE IF NOT EXISTS mc_articles (id INT NOT NULL AUTO_INCREMENT, title VARCHAR(191) NOT NULL, content TEXT NOT NULL, editor_js TEXT NOT NULL, nav TEXT, link VARCHAR(191), category VARCHAR(191), parent_category VARCHAR(191), language VARCHAR(2), parent_id INT, slug VARCHAR(191), update_time DATE NOT NULL, PRIMARY KEY (id), FOREIGN KEY (parent_id) REFERENCES mc_articles(id) ON DELETE CASCADE) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
        }
    } catch (Exception $exception) {
    }
    // Temp. Deprecated

    if (mc_isset($_COOKIE, 'mc-updates') != MC_VERSION && !mc_is_debug()) {
        mc_cloud_load();
        $save = false;
        $settings = false;
        try {

            // 3.6.1
            mc_db_query('ALTER TABLE mc_conversations ADD COLUMN tags varchar(191) AFTER extra');

            // 3.6.4
            mc_db_query('ALTER TABLE `mc_reports` CHANGE COLUMN `value` `value` TEXT NOT NULL COLLATE \'utf8_unicode_ci\' AFTER `name`');

            // 3.6.8
            mc_db_query('ALTER TABLE mc_conversations ADD COLUMN extra_2 varchar(191) AFTER extra');

            // 3.7.1
            mc_db_query('ALTER TABLE mc_conversations ADD COLUMN extra_3 varchar(191) AFTER extra_2');

            //3.7.2
            mc_db_query('CREATE TABLE IF NOT EXISTS mc_articles (id INT NOT NULL AUTO_INCREMENT, title VARCHAR(191) NOT NULL, content TEXT NOT NULL, editor_js TEXT NOT NULL, nav TEXT, link VARCHAR(191), category VARCHAR(191), parent_category VARCHAR(191), language VARCHAR(2), parent_id INT, slug VARCHAR(191), update_time DATE NOT NULL, PRIMARY KEY (id), FOREIGN KEY (parent_id) REFERENCES mc_articles(id) ON DELETE CASCADE) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');

            //3.7.7
            $settings = mc_get_settings();
            if (mc_isset(mc_isset($settings, 'routing'), 0) === true) {
                $settings['routing'] = [['routing-active' => [true, 'checkbox'], 'routing-disable-status-check' => [false, 'checkbox']], 'multi-input'];
                $save = true;
            }

            //3.7.8
            if (mc_isset(mc_isset($settings, 'email-piping'), 1) === 'multi-input') {
                $settings['email-piping'] = [[array_map(fn($item) => $item[0], mc_isset($settings, 'email-piping')[0])], 'repeater'];
                $save = true;
            }

            if (!headers_sent()) {
                setcookie('mc-updates', MC_VERSION, time() + ********, '/');
            }
            if ($save && $settings) {
                mc_save_settings($settings);
            }
        } catch (Exception $e) {
            if ($save && $settings) {
                mc_save_settings($settings);
            }
        }
    }
}

function mc_updates_available() {
    $latest_versions = mc_get_versions();
    if (MC_VERSION != $latest_versions['mc']) {
        return true;
    }
    $installed_apps_versions = mc_get_installed_apps_version();
    foreach ($installed_apps_versions as $key => $value) {
        if ($value && $value != $latest_versions[$key]) {
            return true;
        }
    }
    return false;
}

function mc_get_installed_apps_version() {
    $is_not_cloud = !mc_is_cloud();
    return ['dialogflow' => mc_defined('MC_DIALOGFLOW'), 'slack' => mc_defined('MC_SLACK'), 'tickets' => mc_defined('MC_TICKETS'), 'woocommerce' => $is_not_cloud ? mc_defined('MC_WOOCOMMERCE') : false, 'ump' => $is_not_cloud ? mc_defined('MC_UMP') : false, 'perfex' => $is_not_cloud ? mc_defined('MC_PERFEX') : false, 'whmcs' => $is_not_cloud ? mc_defined('MC_WHMCS') : false, 'aecommerce' => $is_not_cloud ? mc_defined('MC_AECOMMERCE') : false, 'messenger' => mc_defined('MC_MESSENGER'), 'whatsapp' => mc_defined('MC_WHATSAPP'), 'armember' => $is_not_cloud ? mc_defined('MC_ARMEMBER') : false, 'telegram' => mc_defined('MC_TELEGRAM'), 'viber' => mc_defined('MC_VIBER'), 'line' => mc_defined('MC_LINE'), 'wechat' => mc_defined('MC_WECHAT'), 'zalo' => mc_defined('MC_ZALO'), 'twitter' => mc_defined('MC_TWITTER'), 'zendesk' => mc_defined('MC_ZENDESK'), 'martfury' => $is_not_cloud ? mc_defined('MC_MARTFURY') : false, 'opencart' => $is_not_cloud ? mc_defined('MC_OPENCART') : false];
}


function mc_installation($details, $force = false) {
    $database = [];
    $not_cloud = !mc_is_cloud();
    if (mc_db_check_connection() === true && !$force) {
        return true;
    }
    if (!isset($details['db-name']) || !isset($details['db-user']) || !isset($details['db-password']) || !isset($details['db-host'])) {
        return new MCValidationError('missing-details');
    } else {
        $database = ['name' => $details['db-name'][0], 'user' => $details['db-user'][0], 'password' => $details['db-password'][0], 'host' => $details['db-host'][0], 'port' => (isset($details['db-port']) && $details['db-port'][0] ? intval($details['db-port'][0]) : ini_get('mysqli.default_port'))];
    }
    if ($not_cloud) {
        if (!isset($details['url'])) {
            return new MCValidationError('missing-url');
        } else if (substr($details['url'], -1) == '/') {
            $details['url'] = substr($details['url'], 0, -1);
        }
    }
    $connection_check = mc_db_check_connection($database['name'], $database['user'], $database['password'], $database['host'], $database['port']);
    $db_respones = [];
    $success = '';
    if ($connection_check === true) {

        // Create the database
        $connection = new mysqli($database['host'], $database['user'], $database['password'], $database['name'], $database['port']);
        if ($not_cloud) {
            $connection->set_charset('utf8mb4');
        }
        $db_respones['users'] = $connection->query('CREATE TABLE IF NOT EXISTS mc_users (id INT NOT NULL AUTO_INCREMENT, first_name VARCHAR(100) NOT NULL, last_name VARCHAR(100) NOT NULL, password VARCHAR(100), email VARCHAR(191) UNIQUE, profile_image VARCHAR(191), user_type VARCHAR(10) NOT NULL, creation_time DATETIME NOT NULL, token VARCHAR(50) NOT NULL UNIQUE, last_activity DATETIME, typing INT DEFAULT -1, department TINYINT, PRIMARY KEY (id)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
        $db_respones['users_data'] = $connection->query('CREATE TABLE IF NOT EXISTS mc_users_data (id INT NOT NULL AUTO_INCREMENT, user_id INT NOT NULL, slug VARCHAR(191) NOT NULL, name VARCHAR(191) NOT NULL, value TEXT NOT NULL, PRIMARY KEY (id), FOREIGN KEY (user_id) REFERENCES mc_users(id) ON DELETE CASCADE, UNIQUE INDEX mc_users_data_index (user_id, slug)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
        $db_respones['conversations'] = $connection->query('CREATE TABLE IF NOT EXISTS mc_conversations (id int NOT NULL AUTO_INCREMENT, user_id INT NOT NULL, title VARCHAR(191), creation_time DATETIME NOT NULL, status_code TINYINT DEFAULT 0, department SMALLINT, agent_id INT, source VARCHAR(2), extra VARCHAR(191), extra_2 VARCHAR(191), extra_3 VARCHAR(191), tags VARCHAR(191), PRIMARY KEY (id), FOREIGN KEY (agent_id) REFERENCES mc_users(id) ON DELETE CASCADE, FOREIGN KEY (user_id) REFERENCES mc_users(id) ON DELETE CASCADE) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
        $db_respones['messages'] = $connection->query('CREATE TABLE IF NOT EXISTS mc_messages (id int NOT NULL AUTO_INCREMENT, user_id INT NOT NULL, message TEXT NOT NULL, creation_time DATETIME NOT NULL, status_code TINYINT DEFAULT 0, attachments TEXT, payload TEXT, conversation_id INT NOT NULL, PRIMARY KEY (id), FOREIGN KEY (user_id) REFERENCES mc_users(id) ON DELETE CASCADE, FOREIGN KEY (conversation_id) REFERENCES mc_conversations(id) ON DELETE CASCADE) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin');
        $db_respones['settings'] = $connection->query('CREATE TABLE IF NOT EXISTS mc_settings (name VARCHAR(191) NOT NULL, value LONGTEXT, PRIMARY KEY (name)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
        $db_respones['reports'] = $connection->query('CREATE TABLE IF NOT EXISTS mc_reports (id INT NOT NULL AUTO_INCREMENT, name VARCHAR(191) NOT NULL, value TEXT NOT NULL, creation_time DATE NOT NULL, external_id INT, extra VARCHAR(191), PRIMARY KEY (id)) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
        $db_respones['articles'] = $connection->query('CREATE TABLE IF NOT EXISTS mc_articles (id INT NOT NULL AUTO_INCREMENT, title VARCHAR(191) NOT NULL, content TEXT NOT NULL, editor_js TEXT NOT NULL, nav TEXT, link VARCHAR(191), category VARCHAR(191), parent_category VARCHAR(191), language VARCHAR(2), parent_id INT, slug VARCHAR(191), update_time DATE NOT NULL, PRIMARY KEY (id), FOREIGN KEY (parent_id) REFERENCES mc_articles(id) ON DELETE CASCADE) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');

        // Create the admin user
        if (isset($details['first-name']) && isset($details['last-name']) && isset($details['email']) && isset($details['password'])) {
            $now = mc_gmt_now();
            $token = bin2hex(openssl_random_pseudo_bytes(20));
            $db_respones['admin'] = $connection->query('INSERT IGNORE INTO mc_users(first_name, last_name, password, email, profile_image, user_type, creation_time, token, last_activity) VALUES ("' . mc_db_escape($details['first-name'][0]) . '", "' . mc_db_escape($details['last-name'][0]) . '", "' . (defined('MC_WP') ? $details['password'][0] : password_hash($details['password'][0], PASSWORD_DEFAULT)) . '", "' . mc_db_escape($details['email'][0]) . '", "' . mc_db_escape($details['url']) . '/media/user.svg' . '", "admin", "' . $now . '", "' . $token . '", "' . $now . '")');
        }

        // Create the config.php file and other files
        if ($not_cloud) {
            $raw = file_get_contents(MC_PATH . '/resources/config-source.php');
            $raw = str_replace(['[url]', '[name]', '[user]', '[password]', '[host]', '[port]'], [$details['url'], $database['name'], $database['user'], $database['password'], $database['host'], (isset($details['db-port']) && $details['db-port'][0] ? $database['port'] : '')], $raw);
            $path = MC_PATH . '/sw.js';
            if (defined('MC_WP')) {
                $raw = str_replace('/* [extra] */', mc_wp_config(), $raw);
            }
            mc_file(MC_PATH . '/config.php', $raw);
            if (!file_exists($path)) {
                copy(MC_PATH . '/resources/sw.js', $path);
            }
        }

        // Return
        mc_get('https://masichat.com/synch/index.php?site=' . $details['url']);
        foreach ($db_respones as $key => $value) {
            if ($value !== true) {
                $success .= $key . ': ' . ($value === false ? 'false' : $value) . ',';
            }
        }
        if (!$success) {
            return true;
        } else {
            return substr($success, 0, -1);
        }
    } else {
        return $connection_check;
    }
}

function mc_write_config_extra($content) {
    $raw = file_get_contents(MC_PATH . '/config.php');
    mc_file(MC_PATH . '/config.php', str_replace('?>', $content . PHP_EOL . PHP_EOL . '?>', $raw));
}

function mc_upload_path($url = false, $date = false) {
    return (defined('MC_UPLOAD_PATH') && MC_UPLOAD_PATH && defined('MC_UPLOAD_URL') && MC_UPLOAD_URL ? ($url ? MC_UPLOAD_URL : MC_UPLOAD_PATH) : ($url ? (MC_URL . '/') : (MC_PATH . '/')) . 'uploads') . ($date ? ('/' . date('d-m-y')) : '');
}

function mc_dir_name() {
    return substr(MC_URL, strrpos(MC_URL, '/') + 1);
}

/*
 * ----------------------------------------------------------
 * PUSHER AND PUSH NOTIFICATIONS
 * ----------------------------------------------------------
 *
 * 1. Send a Push notification
 * 2. Trigger a event on a channel
 * 3. Get all online users including admins and agents
 * 4. Check if there is at least one agent online
 * 5. Check if pusher is active
 * 6. Initialize the Pusher PHP SDK
 * 7. Pusher curl
 * 8. OneSignal curl
 *
 */

function mc_push_notification($title = '', $message = '', $icon = '', $interest = false, $conversation_id = false, $user_id = false, $attachments = false) {
    $recipient_agent = false;
    if (!$user_id) {
        $user_id = mc_get_active_user_ID();
    }
    if ($interest == 'agents' || (is_string($interest) && strpos($interest, 'department-') !== false)) {
        $agents = mc_db_get('SELECT id FROM mc_users WHERE (user_type = "admin" OR user_type = "agent") AND ' . ($interest == 'agents' ? 'department IS NULL OR department = ""' : ' department = ' . substr($interest, 11)), false);
        $interest = [];
        for ($i = 0; $i < count($agents); $i++) {
            array_push($interest, $agents[$i]['id']);
        }
        $recipient_agent = true;
    } else if (is_numeric($interest) || is_array($interest)) {
        $agents_ids = mc_get_agents_ids();
        $is_user = !mc_is_agent();
        if (is_numeric($interest)) {
            if (!in_array(intval($interest), $agents_ids)) {
                if ($is_user && empty($GLOBALS['MC_FORCE_ADMIN'])) {
                    return mc_error('security-error', 'mc_push_notification');
                }
            } else {
                $recipient_agent = true;
            }
        } else {
            for ($i = 0; $i < count($interest); $i++) {
                if (!in_array(intval($interest[$i]), $agents_ids)) {
                    if ($is_user && empty($GLOBALS['MC_FORCE_ADMIN'])) {
                        return mc_error('security-error', 'mc_push_notification');
                    }
                } else {
                    $recipient_agent = true;
                }
            }
        }
    } else if ($interest == 'all-agents') {
        $interest == 'agents';
    }
    if (empty($interest)) {
        return false;
    }
    if (empty($icon) || strpos($icon, 'user.svg')) {
        $icon = mc_is_cloud() ? MC_CLOUD_BRAND_ICON_PNG : mc_get_setting('notifications-icon', MC_URL . '/media/icon.png');
    }
    if (mc_is_agent() && !$recipient_agent) {
        $link = $conversation_id ? mc_isset(mc_db_get('SELECT B.value FROM mc_conversations A, mc_users_data B WHERE A.id = ' . mc_db_escape($conversation_id, true) . ' AND A.user_id = B.user_id AND B.slug = "current_url" LIMIT 1'), 'value', '') : false;
    } else {
        $link = (mc_is_cloud() ? CLOUD_URL : MC_URL . '/admin.php') . ($conversation_id ? '?conversation=' . $conversation_id : '');
    }
    if (defined('MC_DIALOGFLOW') && (is_numeric($interest) || (is_array($interest) && is_numeric($interest[0])))) {
        $message_translated = mc_google_translate_auto($message, is_numeric($interest) ? $interest : $interest[0]);
        $message = empty($message_translated) ? $message : $message_translated;
    }
    $is_pusher = !mc_is_cloud() && mc_get_multi_setting('push-notifications', 'push-notifications-provider') == 'pusher';
    $image = $attachments && count($attachments) && in_array(pathinfo($attachments[0][1], PATHINFO_EXTENSION), ['jpeg', 'jpg', 'png', 'gif']) ? $attachments[0][1] : false;
    $title = str_replace('"', '', $title);
    $message = str_replace('"', '', mc_clear_text_formatting(trim(preg_replace('/\s+/', ' ', $message))));
    $response = false;
    $query_data = ['conversation_id' => $conversation_id, 'user_id' => $user_id, 'image' => $image ? $image : '', 'badge' => MC_URL . '/media/badge.png'];
    if ($is_pusher) {
        $query = ['web' => ['notification' => ['title' => $title, 'body' => $message, 'icon' => $icon, 'hide_notification_if_site_has_focus' => true,], 'data' => $query_data]];
        if ($link) {
            $query['web']['notification']['deep_link'] = $link;
        }
        if (is_array($interest) && count($interest) > 100) {
            $interests = [];
            $index = 0;
            $count = count($interest);
            for ($i = 0; $i < $count; $i++) {
                array_push($interests, $interest[$i]);
                $index++;
                if ($index == 100 || $i == $count - 1) {
                    $query['interests'] = $interests;
                    $response = mc_pusher_curl('publishes', json_encode($query, JSON_INVALID_UTF8_IGNORE | JSON_UNESCAPED_UNICODE));
                    $interests = [];
                    $index = 0;
                }
            }
        } else {
            $query['interests'] = is_array($interest) ? $interest : [str_replace(' ', '', $interest)];
            $response = mc_pusher_curl('publishes', json_encode($query, JSON_INVALID_UTF8_IGNORE | JSON_UNESCAPED_UNICODE));
        }
    } else {
        $query = ['headings' => ['en' => $title], 'contents' => ['en' => $message], 'chrome_web_badge' => MC_URL . '/media/badge.png', 'firefox_icon' => $icon, 'chrome_web_icon' => $icon, 'priority' => 10, 'data' => $query_data];
        if ($link) {
            $query['url'] = $link;
        }
        if ($conversation_id) {
            $query['collapse_id'] = $conversation_id;
        }
        if ($image) {
            $query['chrome_web_image'] = $image;
            $query['chrome_big_picture'] = $image;
            if (!$message) {
                $query['contents']['en'] = $image;
            }
        }
        if (is_numeric($interest) || is_array($interest)) {
            $cloud_id = mc_is_cloud() ? mc_cloud_account()['user_id'] . '-' : '';
            if (is_numeric($interest)) {
                $interest = [$interest];
            }
            for ($i = 0; $i < count($interest); $i++) {
                $interest[$i] = $cloud_id . strval($interest[$i]);
                if ($interest[$i] == 1) {
                    $interest[$i] = 'MC-1';
                }
            }
            $query['include_aliases'] = ['external_id' => $interest];
            $query['target_channel'] = ['push'];
        }
        $response = mc_onesignal_curl('notifications', $query);
    }
    return isset($response['error']) ? trigger_error($response['description']) : $response;
}

function mc_pusher_trigger($channel, $event, $data = []) {
    $pusher = mc_pusher_init();
    $user_id = mc_get_active_user_ID();
    $data['user_id'] = $user_id;
    $security = mc_isset($GLOBALS, 'MC_FORCE_ADMIN');
    $count = is_array($channel) ? count($channel) : false;
    if (!$security) {
        switch ($event) {
            case 'message-status-update':
            case 'set-agent-status':
            case 'agent-active-conversation-changed':
            case 'add-user-presence':
            case 'init':
            case 'new-message':
            case 'new-conversation':
            case 'client-typing':
            case 'close-notifications':
            case 'close-notifications-received':
            case 'typing':
                $security = mc_is_agent() || $channel == ('private-user-' . $user_id);
                break;
            case 'update-conversations':
                if ($user_id) {
                    $security = true;
                }
                break;
        }
    }
    if (mc_is_cloud()) {
        $account_id = mc_isset(mc_cloud_account(), 'user_id');
        if ($account_id) {
            if ($count) {
                for ($i = 0; $i < $count; $i++) {
                    $channel[$i] .= '-' . $account_id;
                }
            } else {
                $channel .= '-' . $account_id;
            }
        }
    }
    if ($security) {
        if ($count > 100) {
            $channels = [];
            $index = 0;
            for ($i = 0; $i < $count; $i++) {
                array_push($channels, $channel[$i]);
                $index++;
                if ($index == 100 || $i == $count - 1) {
                    $response = $pusher->trigger($channels, $event, $data);
                    $channels = [];
                    $index = 0;
                }
            }
            return $response;
        } else {
            $response = $pusher->trigger($channel, $event, $data);
            if ($response && $response !== true && !($response instanceof stdClass && empty((array) $response))) {
                mc_debug('Pusher error [channel: ' . $channel . ']:');
                mc_debug($response);
            }
            return $response;
        }
    }
    return mc_error('pusher-security-error', 'mc_pusher_trigger');
}

function mc_pusher_get_online_users() {
    global $MC_PUSHER_ONLINE_USERS;
    if ($MC_PUSHER_ONLINE_USERS) {
        return $MC_PUSHER_ONLINE_USERS;
    }
    $online_users_db = mc_isset(mc_db_get('SELECT value FROM mc_settings WHERE name = "pusher-online-users"'), 'value');
    if ($online_users_db) {
        $online_users_db = json_decode($online_users_db);
        if ($online_users_db && $online_users_db[1] > (time() - 10)) {
            $MC_PUSHER_ONLINE_USERS = $online_users_db[0];
            return $online_users_db[0];
        }
    }
    $index = 1;
    $pusher = mc_pusher_init();
    $continue = true;
    $users = [];
    $account_id = mc_is_cloud() ? '-' . mc_cloud_account()['user_id'] : '';
    while ($continue) {
        $channel = $pusher->get_users_info('presence-' . $index . $account_id);
        if (!empty($channel)) {
            $channel = $channel->users;
            $users = array_merge($users, $channel);
            if (count($channel) > 98) {
                $continue = true;
                $index++;
            } else
                $continue = false;
        } else
            $continue = false;
    }
    mc_save_external_setting('pusher-online-users', [$users, time()]);
    $MC_PUSHER_ONLINE_USERS = $users;
    return $users;
}

function mc_pusher_agents_online() {
    $agents_id = mc_get_agents_ids();
    $users = mc_pusher_get_online_users();
    for ($i = 0; $i < count($users); $i++) {
        if (in_array($users[$i]->id, $agents_id)) {
            return true;
        }
    }
    return false;
}

function mc_pusher_active() {
    return mc_is_cloud() || mc_get_multi_setting('pusher', 'pusher-active');
}

function mc_pusher_init() {
    require_once MC_PATH . '/vendor/pusher/autoload.php';
    $pusher_details = mc_pusher_get_details();
    return new Pusher\Pusher($pusher_details[0], $pusher_details[1], $pusher_details[2], ['cluster' => $pusher_details[3]]);
}

function mc_pusher_get_details() {
    if (mc_is_cloud()) {
        $account_id = mc_cloud_account_id();
        if (!$account_id) {
            $account_id = mc_isset($_POST, 'cloud_user_id');
        }
        return $account_id && defined('CLOUD_PUSHER_' . $account_id) ? constant('CLOUD_PUSHER_' . $account_id) : [CLOUD_PUSHER_KEY, CLOUD_PUSHER_SECRET, CLOUD_PUSHER_ID, CLOUD_PUSHER_CLUSTER];
    }
    $settings = mc_get_setting('pusher');
    return [$settings['pusher-key'], $settings['pusher-secret'], $settings['pusher-id'], $settings['pusher-cluster']];
}

function mc_pusher_curl($url_part, $post_fields = '') {
    $instance_ID = mc_get_multi_setting('push-notifications', 'push-notifications-id');
    return mc_curl('https://' . $instance_ID . '.pushnotifications.pusher.com/publish_api/v1/instances/' . $instance_ID . '/' . $url_part, is_string($post_fields) ? $post_fields : json_encode($post_fields, JSON_INVALID_UTF8_IGNORE), ['Content-Type: application/json', 'Authorization: Bearer ' . mc_get_multi_setting('push-notifications', 'push-notifications-key')]);
}

function mc_onesignal_curl($url_part, $post_fields = []) {
    $post_fields['app_id'] = mc_is_cloud() ? ONESIGNAL_APP_ID : mc_get_multi_setting('push-notifications', 'push-notifications-onesignal-app-id');
    return mc_curl('https://onesignal.com/api/v1/' . $url_part, json_encode($post_fields, JSON_INVALID_UTF8_IGNORE), ['Authorization: basic ' . (mc_is_cloud() ? ONESIGNAL_API_KEY : trim(mc_get_multi_setting('push-notifications', 'push-notifications-onesignal-api-key'))), 'Content-Type: application/json']);
}

/*
 * -----------------------------------------------------------
 * MISCELLANEOUS
 * -----------------------------------------------------------
 *
 * 1. Check if a value and key of an array exists and is not empty and return it
 * 2. Check if a number and key of an array exists and is not empty and return it
 * 3. Check if a constant exists
 * 4. Encrypt a string or decrypt an encrypted string
 * 5. Convert a string to a slug or a slug to a string
 * 6. Send a curl request
 * 7. Return the content of a URL as a string
 * 8. Return the content of a URL as a string via GET
 * 9. Create a CSV file from an array
 * 10. Read a CSV and return the array
 * 11. Create a new file containing the given content and save it in the destination path.
 * 12. Delete a file
 * 13. Debug function
 * 14. Convert a JSON string to an array
 * 15. Get max server file size
 * 16. Delete visitors older than 24h, messages in trash older than 30 days. Archive conversation older than 24h with status code equal to 4 (pending user reply).
 * 17. Chat editor
 * 18. Return the position of the least occurence on left searching from right to left
 * 19. Verification cookie
 * 20. On Masi Chat close
 * 21. Check if dialogflow active
 * 22. Logs
 * 23. Webhook
 * 24. Add a cron job
 * 25. Run cron jobs
 * 26. Sanatize string
 * 27. Amazon S3
 * 28. Return the current unix UTC time
 * 29. Convert a GMT date to local time and date
 * 30. Masi Chat error reporting
 * 31. Return an array from a JSON string of the resources folder
 * 32. Return the file name without the initial random number
 *
 */

function mc_isset($array, $key, $default = false) {
    if (mc_is_error($array) || mc_is_validation_error($array)) {
        return $array;
    }
    return !empty($array) && isset($array[$key]) && $array[$key] !== '' ? $array[$key] : $default;
}

function mc_isset_num($value) {
    return $value != -1 && $value && !is_null($value) && !is_bool($value) && is_numeric($value);
}

function mc_defined($name, $default = false) {
    return defined($name) ? constant($name) : $default;
}

function mc_encryption($string, $encrypt = true) {
    $output = false;
    $encrypt_method = 'AES-256-CBC';
    $key = hash('sha256', defined('MC_CLOUD_KEY') && !empty(MC_CLOUD_KEY) ? MC_CLOUD_KEY : (defined('MC_DB_PASSWORD') && !empty(MC_DB_PASSWORD) ? MC_DB_PASSWORD : mc_defined('AUTH_KEY', 'masichat')));
    $iv = substr(hash('sha256', 'masichat_iv'), 0, 16);
    if ($encrypt) {
        $output = openssl_encrypt($string, $encrypt_method, $key, 0, $iv);
        $output = base64_encode($output);
        if (substr($output, -1) == '=')
            $output = substr($output, 0, -1);
    } else {
        $output = openssl_decrypt(base64_decode($string), $encrypt_method, $key, 0, $iv);
        if ($output === false) {
            $output = openssl_decrypt(base64_decode($string), $encrypt_method, hash('sha256', 'masichat'), 0, $iv);
        }
    }
    return $output;
}

function mc_string_slug($string, $action = 'slug', $is_alphanumeric = false) {
    $string = trim($string);
    if ($action == 'slug') {
        $string = mb_strtolower(str_replace([' ', ' '], '-', $string), 'UTF-8');
        $string = preg_replace('/[^\p{L}\p{N}._\-]/u', '', mc_sanatize_string($string, true));
        if ($is_alphanumeric) {
            $string_ = preg_replace('/[^a-z0-9\-]/', '', $string);
            $string = empty($string_) ? 'slug-' . strlen($string) : $string_;
        }
    } else if ($action == 'string') {
        return ucfirst(strtolower(str_replace(['-', '_'], ' ', $string)));
    }
    return $string;
}

function mc_curl($url, $post_fields = '', $header = [], $method = 'POST', $timeout = false, $include_headers = false) {
    $ch = curl_init($url);
    $headers = [];
    $post_value = $post_fields ? (is_string($post_fields) ? $post_fields : (in_array('Content-Type: multipart/form-data', $header) ? $post_fields : http_build_query($post_fields))) : false;
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 Chrome/********* Safari/537.36');
    switch ($method) {
        case 'DELETE':
        case 'PUT':
        case 'PATCH':
        case 'POST':
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
            curl_setopt($ch, CURLOPT_TIMEOUT, $timeout ? $timeout : 7);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $post_value);
            if ($method != 'POST') {
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
            }
            break;
        case 'GET-SC':
        case 'GET':
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
            curl_setopt($ch, CURLOPT_TIMEOUT, $timeout ? $timeout : 70);
            curl_setopt($ch, CURLOPT_HEADER, false);
            if ($post_value) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, $post_value);
            }
            if ($method == 'GET-SC') {
                curl_setopt($ch, CURLOPT_ENCODING, 'gzip,deflate');
            }
            break;
        case 'DOWNLOAD':
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
            curl_setopt($ch, CURLOPT_TIMEOUT, $timeout ? $timeout : 70);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            break;
        case 'FILE':
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
            curl_setopt($ch, CURLOPT_TIMEOUT, $timeout ? $timeout : 400);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            $path = mc_upload_path(false, true);
            if (!file_exists($path)) {
                mkdir($path, 0755, true);
            }
            if (strpos($url, '?')) {
                $url = substr($url, 0, strpos($url, '?'));
            }
            $basename = mc_sanatize_file_name(basename($url));
            $extension = pathinfo($basename, PATHINFO_EXTENSION);
            if ($extension && !mc_is_allowed_extension($extension)) {
                return 'extension-not-allowed';
            }
            while (file_exists($path . '/' . $basename)) {
                $basename = rand(100, 1000000) . $basename;
            }
            $file = fopen($path . '/' . $basename, 'wb');
            curl_setopt($ch, CURLOPT_FILE, $file);
            break;
        case 'UPLOAD':
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 300);
            curl_setopt($ch, CURLOPT_TIMEOUT, 400);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);
            $header = array_merge($header, ['Content-Type: multipart/form-data']);
            break;
    }
    if (!empty($header)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    }
    if ($include_headers) {
        curl_setopt($ch, CURLOPT_HEADER, true);
    }
    $response = curl_exec($ch);
    $status_code = $method == 'GET-SC' ? curl_getinfo($ch, CURLINFO_HTTP_CODE) : false;
    if (curl_errno($ch) > 0) {
        $error = curl_error($ch);
        curl_close($ch);
        return $error;
    }
    if ($include_headers) {
        $size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $headers_ = substr($response, 0, $size);
        $response = substr($response, $size);
        foreach (explode("\r\n", $headers_) as $header) {
            if (strpos($header, ':')) {
                list($key, $value) = explode(':', $header, 2);
                $headers[trim($key)] = trim($value);
            }
        }
    }
    curl_close($ch);
    if ($include_headers) {
        return [json_decode($response, true), $headers];
    }
    switch ($method) {
        case 'UPLOAD':
        case 'PATCH':
        case 'POST':
            $response_ = json_decode($response, true);
            return JSON_ERROR_NONE !== json_last_error() ? $response : $response_;
        case 'FILE':
            return mc_upload_path(true, true) . '/' . $basename;
        case 'GET-SC':
            return [$response, $status_code];
    }
    return $response;
}

function mc_download($url) {
    return mc_curl($url, '', '', 'DOWNLOAD');
}

function mc_download_file($url, $file_name = false, $mime = false, $header = [], $recursion = 0, $return_path = false) {
    $url = mc_curl($url, '', $header, 'FILE');
    $path_2 = false;
    $extension = pathinfo(basename($file_name ? $file_name : $url), PATHINFO_EXTENSION);
    if ($extension && !mc_is_allowed_extension($extension)) {
        return 'extension-not-allowed';
    }
    if ($file_name && !mc_is_error($url) && !empty($url)) {
        $date = date('d-m-y');
        $path = mc_upload_path() . '/' . $date;
        if ($mime) {
            $mime_types = [['image/gif', 'gif'], ['image/jpeg', 'jpg'], ['video/quicktime', 'mov'], ['video/mpeg', 'mp3'], ['application/pdf', 'pdf'], ['image/png', 'png'], ['image/x-png', 'png'], ['application/rtf', 'rtf'], ['text/plain', 'txt'], ['x-zip-compressed', 'zip'], ['video/mp4', 'mp4'], ['audio/mp4', 'mp4']];
            $mime = $mime === true ? mime_content_type($path . '/' . basename($url)) : $mime;
            for ($i = 0; $i < count($mime_types); $i++) {
                if ($mime == $mime_types[$i][0] && substr_compare($file_name, '.' . $mime_types[$i][1], -strlen('.' . $mime_types[$i][1])) !== 0) {
                    $file_name .= '.' . $mime_types[$i][1];
                    break;
                }
            }
        }
        $file_name = mc_string_slug($file_name);
        $path_2 = $path . '/' . $file_name;
        rename($path . '/' . basename($url), $path_2);
        if (!file_exists($path_2) && $recursion < 3) {
            return mc_download_file($url, $file_name, $mime, $header, $recursion + 1);
        }
        $url = mc_upload_path(true) . '/' . $date . '/' . $file_name;
        if (!$return_path && (mc_get_multi_setting('amazon-s3', 'amazon-s3-active') || defined('MC_CLOUD_AWS_S3'))) {
            $url_aws = mc_aws_s3($path_2);
            if (strpos($url_aws, 'http') === 0) {
                $url = $url_aws;
                unlink($path_2);
            }
        }
    }
    return $return_path ? $path_2 : $url;
}

function mc_is_allowed_extension($extension) {
    $extension = strtolower($extension);
    $allowed_extensions = ['step', 'stl', 'obj', '3mf', 'bmp', 'aac', 'webm', 'oga', 'json', 'psd', 'ai', 'jpg', 'jpeg', 'png', 'gif', 'webp', 'pdf', 'doc', 'docx', 'key', 'ppt', 'odt', 'xls', 'xlsx', 'zip', 'rar', 'mp3', 'm4a', 'ogg', 'wav', 'mp4', 'mov', 'wmv', 'avi', 'mpg', 'ogv', '3gp', '3g2', 'mkv', 'txt', 'ico', 'csv', 'ttf', 'font', 'css', 'scss'];
    return in_array($extension, $allowed_extensions) || (defined('MC_FILE_EXTENSIONS') && in_array($extension, MC_FILE_EXTENSIONS));
}

function mc_get($url, $json = false) {
    $response = mc_curl($url, '', '', 'GET');
    return $json ? json_decode($response, true) : $response;
}

function mc_csv($items, $header, $filename, $return_url = true) {
    $filename = rand(100000000, 99999999999) . '-' . $filename . '.csv';
    $file = fopen(mc_upload_path() . '/' . $filename, 'w');
    fprintf($file, chr(0xEF) . chr(0xBB) . chr(0xBF));
    if ($header) {
        fputcsv($file, $header);
    }
    for ($i = 0; $i < count($items); $i++) {
        fputcsv($file, $items[$i]);
    }
    fclose($file);
    return mc_upload_path($return_url) . '/' . $filename;
}

function mc_csv_read($path) {
    $rows = [];
    if (($handle = fopen($path, 'r')) !== false) {
        $headers = false;
        while (($data = fgetcsv($handle, 0, ',')) !== false) {
            if (!$headers) {
                $headers = $data;
                continue;
            }
            $row = [];
            for ($i = 0; $i < count($headers); $i++) {
                $row[$headers[$i]] = isset($data[$i]) ? $data[$i] : null;
            }
            $rows[] = $row;
        }
        fclose($handle);
    }
    return $rows;
}

function mc_file($path, $content) {
    try {
        $file = fopen($path, 'w');
        fwrite($file, (substr($path, -4) == '.txt' ? "\xEF\xBB\xBF" : '') . $content);
        fclose($file);
        return true;
    } catch (Exception $e) {
        return $e->getMessage();
    }
}

function mc_file_delete($url_or_path) {
    $aws = (mc_get_multi_setting('amazon-s3', 'amazon-s3-active') || defined('MC_CLOUD_AWS_S3')) && (strpos($url_or_path, '.s3.') || strpos($url_or_path, 'amazonaws.com'));
    if ($aws) {
        return mc_aws_s3($url_or_path, 'DELETE');
    } else {
        $path = strpos($url_or_path, 'http') === 0 ? mc_upload_path() . str_replace(mc_upload_path(true), '', $url_or_path) : $url_or_path;
        if (mc_is_valid_path($path)) {
            return unlink($path);
        }
    }
    return false;
}

function mc_debug($value) {
    $value = is_string($value) ? $value : json_encode($value, JSON_INVALID_UTF8_IGNORE | JSON_UNESCAPED_UNICODE);
    $path = __DIR__ . '/debug.txt';
    if (file_exists($path)) {
        $value = file_get_contents($path) . PHP_EOL . PHP_EOL . $value;
    }
    mc_file($path, $value);
}

function mc_json_array($json, $default = []) {
    if (is_string($json)) {
        $json = json_decode($json, true);
        return $json === false || $json === null ? $default : $json;
    } else if (is_bool($json)) {
        return [];
    } else {
        return $json;
    }
}

function mc_get_server_max_file_size() {
    $size = ini_get('post_max_size');
    if (empty($size)) {
        return 9999;
    }
    $suffix = strtoupper(substr($size, -1));
    $size = substr($size, 0, -1);
    if ($size === 0) {
        return 9999;
    }
    switch ($suffix) {
        case 'G':
            $size *= 1024;
            break;
        case 'K':
            $size /= 1024;
            break;
    }
    return $size;
}

function mc_clean_data() {
    $time_24h = mc_gmt_now(86400);
    $time_30d = mc_gmt_now(2592000);
    $ids = mc_db_get('SELECT id FROM mc_conversations WHERE status_code = 4 AND creation_time < "' . $time_30d . '"', false);
    mc_db_query('DELETE FROM mc_users WHERE user_type = "visitor" AND creation_time < "' . $time_24h . '"');
    for ($i = 0; $i < count($ids); $i++) {
        mc_delete_attachments($ids[$i]['id']);
    }
    mc_db_query('DELETE FROM mc_conversations WHERE status_code = 4 AND creation_time < "' . $time_30d . '"');
    if (mc_get_setting('admin-auto-archive')) {
        mc_db_query('UPDATE mc_conversations SET status_code = 3 WHERE (status_code = 1 OR status_code = 0) AND id IN (SELECT conversation_id FROM mc_messages WHERE id IN (SELECT max(id) FROM mc_messages GROUP BY conversation_id) AND creation_time < "' . $time_24h . '")');
    }
    return true;
}

function mc_component_editor($admin = false) {
    $enabled = [$admin || !mc_get_setting('disable-uploads'), !mc_get_setting('disable-voice-messages')];
    ?>
    <div class="mc-editor<?php echo !$enabled[0] || !$enabled[1] ? ' mc-disabled-' . (!$enabled[0] && !$enabled[1] ? '2' : '1') : '' ?>">
        <?php
        if ($admin) {
            echo '<div class="mc-labels"></div>';
        }
        ?>
        <div class="mc-textarea">
            <textarea placeholder="<?php mc_e('Write a message...') ?>"></textarea>
        </div>
        <div class="mc-attachments"></div>
        <?php
        $code = ($admin ? '<div class="mc-suggestions"></div>' : '') . '<div class="mc-bar"><div class="mc-bar-icons">';
        if ($enabled[0]) {
            $code .= '<div class="mc-btn-attachment" data-mc-tooltip="' . mc_('Attach a file') . '"></div>';
        }
        $code .= '<div class="mc-btn-saved-replies" data-mc-tooltip="' . mc_('Add a saved reply') . '"></div>';
        if ($enabled[1]) {
            $code .= '<div class="mc-btn-audio-clip" data-mc-tooltip="' . mc_('Voice message') . '"></div>';
        }
        $code .= '<div class="mc-btn-emoji" data-mc-tooltip="' . mc_('Add an emoji') . '"></div>';
        if ($admin && defined('MC_DIALOGFLOW') && mc_get_multi_setting('open-ai', 'open-ai-rewrite')) {
            $code .= '<div class="mc-btn-open-ai mc-icon-openai mc-btn-open-ai-editor" data-mc-tooltip="' . mc_('Rewrite') . '"></div>';
        }
        if ($admin && defined('MC_WOOCOMMERCE')) {
            $code .= '<div class="mc-btn-woocommerce" data-mc-tooltip="' . mc_('Add a product') . '"></div>';
        }
        echo $code . '</div><div class="mc-icon-send mc-submit" data-mc-tooltip="' . mc_('Send message') . '"></div><i class="mc-loader"></i></div>';
        ?>
        <div class="mc-popup mc-emoji">
            <div class="mc-header">
                <div class="mc-select">
                    <p>
                        <?php mc_e('All') ?>
                    </p>
                    <ul>
                        <li data-value="all" class="mc-active">
                            <?php mc_e('All') ?>
                        </li>
                        <li data-value="Smileys">
                            <?php mc_e('Smileys & Emotions') ?>
                        </li>
                        <li data-value="People">
                            <?php mc_e('People & Body') ?>
                        </li>
                        <li data-value="Animals">
                            <?php mc_e('Animals & Nature') ?>
                        </li>
                        <li data-value="Food">
                            <?php mc_e('Food & Drink') ?>
                        </li>
                        <li data-value="Travel">
                            <?php mc_e('Travel & Places') ?>
                        </li>
                        <li data-value="Activities">
                            <?php mc_e('Activities') ?>
                        </li>
                        <li data-value="Objects">
                            <?php mc_e('Objects') ?>
                        </li>
                        <li data-value="Symbols">
                            <?php mc_e('Symbols') ?>
                        </li>
                    </ul>
                </div>
                <div class="mc-search-btn">
                    <i class="mc-icon mc-icon-search"></i>
                    <input type="text" placeholder="<?php mc_e('Search emoji...') ?>" />
                </div>
            </div>
            <div class="mc-emoji-list">
                <ul></ul>
            </div>
            <div class="mc-emoji-bar"></div>
        </div>
        <?php if ($admin) { ?>
            <div class="mc-popup mc-replies">
                <div class="mc-header">
                    <div class="mc-title">
                        <?php mc_e('Saved replies') ?>
                    </div>
                    <div class="mc-search-btn">
                        <i class="mc-icon mc-icon-search"></i>
                        <input type="text" autocomplete="false" placeholder="<?php mc_e(mc_get_multi_setting('google', 'google-project-id') ? 'Search replies and Intents...' : 'Search replies...') ?>" />
                    </div>
                </div>
                <div class="mc-replies-list mc-scroll-area">
                    <ul class="mc-loading"></ul>
                </div>
            </div>
            <?php
            if (defined('MC_WOOCOMMERCE')) {
                mc_woocommerce_products_popup();
            }
        } ?>
        <form class="mc-upload-form-editor" action="#" method="post" enctype="multipart/form-data">
            <input type="file" name="files[]" class="mc-upload-files" multiple />
        </form>
        <div id="mc-audio-clip">
            <div class="mc-icon mc-icon-close"></div>
            <div class="mc-audio-clip-cnt">
                <div class="mc-audio-clip-time"></div>
                <div class="mc-icon mc-icon-play mc-btn-clip-player"></div>
            </div>
            <div class="mc-icon mc-icon-pause mc-btn-mic"></div>
        </div>
    </div>
    <?php
    if (!$admin) {
        echo '<div class="mc-overlay-panel"><div><div></div><i class="mc-btn-icon mc-icon-close"></i></div><div></div></div>';
    }
}

function mc_strpos_reverse($string, $search, $offset) {
    return strrpos(substr($string, 0, $offset), $search);
}

function mc_mb_strpos_reverse($string, $search, $offset) {
    $index = mb_strrpos(mb_substr($string, 0, $offset), $search);
    return $index ? $index : $offset;
}

function mc_verification_cookie($code, $domain) {
    // Envato verification removed - always return success
    return [true, password_hash('VGCKME' . 'NS', PASSWORD_DEFAULT)];
}

function mc_on_close() {
    mc_set_agent_active_conversation(0);
}

function mc_chatbot_active($check_dialogflow = true, $check_open_ai = true, $source = false) {
    if (defined('MC_DIALOGFLOW')) {
        if ($source && mc_get_setting(($source == 'ig' ? 'fb' : $source) . '-disable-chatbot')) {
            return false;
        }
        if ($check_dialogflow && $check_open_ai) {
            return mc_get_setting('dialogflow-active') || mc_get_multi_setting('google', 'dialogflow-active') || mc_get_multi_setting('open-ai', 'open-ai-active'); // Deprecated: mc_get_setting('dialogflow-active')
        }
        return (!$check_dialogflow || mc_get_setting('dialogflow-active') || mc_get_multi_setting('google', 'dialogflow-active')) && (!$check_open_ai || mc_get_multi_setting('open-ai', 'open-ai-active')); // Deprecated: mc_get_setting('dialogflow-active')
    }
    return false;
}

function mc_logs($string, $user = false) {
    if (mc_is_cloud()) {
        return false;
    }
    $string = date('d-m-Y H:i:s') . ' Agent ' . mc_get_user_name($user) . ' #' . ($user ? $user['id'] : mc_get_active_user_ID()) . ' ' . $string;
    $path = MC_PATH . '/log.txt';
    if (file_exists($path)) {
        $string = file_get_contents($path) . PHP_EOL . $string;
    }
    return mc_file($path, $string);
}

function mc_webhooks($function_name, $parameters) {
    $names = ['MCSMSSent' => 'sms-sent', 'MCLoginForm' => 'login', 'MCRegistrationForm' => 'registration', 'MCUserDeleted' => 'user-deleted', 'MCMessageSent' => 'message-sent', 'MCDialogflowMessage' => 'dialogflow-message', 'MCBotMessage' => 'bot-message', 'MCEmailSent' => 'email-sent', 'MCNewMessagesReceived' => 'new-messages', 'MCNewConversationReceived' => 'new-conversation', 'MCNewConversationCreated' => 'new-conversation-created', 'MCActiveConversationStatusUpdated' => 'conversation-status-updated', 'MCSlackMessageSent' => 'slack-message-sent', 'MCMessageDeleted' => 'message-deleted', 'MCRichMessageSubmit' => 'rich-message', 'MCNewEmailAddress' => 'new-email-address'];
    $webhook_name = mc_isset($names, $function_name);
    if ($webhook_name) {
        $webhooks = mc_get_setting('webhooks');
        $webhook_url = mc_isset($webhooks, 'webhooks-url');
        if ($webhooks && $webhook_url && $webhooks['webhooks-active']) {
            $allowed_webhooks = $webhooks['webhooks-allowed'];
            if ($allowed_webhooks && $allowed_webhooks !== true) {
                $allowed_webhooks = explode(',', str_replace(' ', '', $allowed_webhooks));
                if (!in_array($webhook_name, $allowed_webhooks)) {
                    return false;
                }
            }
            $query = json_encode(['function' => $webhook_name, 'key' => $webhooks['webhooks-key'], 'data' => $parameters, 'sender-url' => (isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '')], JSON_INVALID_UTF8_IGNORE | JSON_UNESCAPED_UNICODE);
            if ($query) {
                if (mc_is_cloud() && $webhook_url == 'zapier') {
                    $webhook_url = mc_isset(mc_get_external_setting('zapier', []), $function_name);
                    if (!$webhook_url) {
                        return false;
                    }
                }
                return mc_curl($webhook_url, $query, ['Content-Type: application/json', 'Content-Length: ' . strlen($query)]);
            } else {
                return mc_error('webhook-json-error', 'mc_webhooks', $function_name);
            }
        } else {
            return new MCValidationError('webhook-not-active-or-empty-url');
        }
    } else {
        return new MCValidationError('webhook-not-found');
    }
}

function mc_cron_jobs_add($key, $content = false, $job_time = false) {

    // Add the job to the cron jobs
    $cron_functions = mc_get_external_setting('cron-functions');
    if (empty($cron_functions) || empty($cron_functions['value'])) {
        mc_save_external_setting('cron-functions', [$key]);
    } else {
        $cron_functions = json_decode($cron_functions['value'], true);
        if (!in_array($key, $cron_functions)) {
            array_push($cron_functions, $key);
            mc_db_query('UPDATE mc_settings SET value = \'' . mc_db_json_escape($cron_functions) . '\' WHERE name = "cron-functions"');
        }
    }

    // Set the cron job data
    if (!empty($content) && !empty($job_time)) {
        $user = mc_get_active_user();
        if ($user) {
            $key = 'cron-' . $key;
            $scheduled = mc_get_external_setting($key);
            if (empty($scheduled)) {
                $scheduled = [];
            }
            $scheduled[$user['id']] = [$content, strtotime('+' . $job_time)];
            mc_save_external_setting($key, $scheduled);
        }
    }
}

function mc_cron_jobs() {
    ignore_user_abort(true);
    set_time_limit(180);
    $now = date('H');
    $cron_functions = mc_get_external_setting('cron-functions');
    if (defined('MC_WOOCOMMERCE')) {
        mc_woocommerce_cron_jobs($cron_functions);
    }
    if (defined('MC_AECOMMERCE')) {
        mc_aecommerce_clean_carts();
    }
    if (mc_chatbot_active(false, true) && mc_get_multi_setting('open-ai', 'open-ai-user-train-conversations')) {
        mc_open_ai_conversations_training();
    }
    mc_clean_data();
    mc_db_query('DELETE FROM mc_settings WHERE name="cron-functions"');
    mc_save_external_setting('cron', $now);
}

function mc_sanatize_string($string, $is_secure = false) {
    do {
        $previous = $string;
        $string = str_ireplace(['onload', 'javascript:', 'onclick', 'onerror', 'onmouseover', 'oncontextmenu', 'ondblclick', 'onmousedown', 'onmouseenter', 'onmouseleave', 'onmousemove', 'onmouseout', 'onmouseup', 'ontoggle'], '', $string);
    } while ($string !== $previous);
    while (strpos($string, '<script') !== false) {
        $string = str_ireplace('<script', '&lt;script', $string);
    }
    while (strpos($string, '</script') !== false) {
        $string = str_ireplace('</script', '&lt;/script', $string);
    }
    return $is_secure ? htmlspecialchars($string, ENT_NOQUOTES | ENT_SUBSTITUTE | ENT_HTML401, 'UTF-8') : $string;
}

function mc_sanatize_file_name($file_name) {
    do {
        $previous = $file_name;
        $file_name = str_ireplace(['../', '\\', '/', ':', '?', '"', '*', '<', '>', '|', '..\/'], '', $file_name);
    } while ($file_name !== $previous);
    return mc_sanatize_string($file_name, true);
}

function mc_aws_s3($file_path, $action = 'PUT', $bucket_name = false) {
    $settings = mc_get_setting('amazon-s3');
    if ((!$settings || empty($settings['amazon-s3-bucket-name'])) && defined('MC_CLOUD_AWS_S3')) {
        $settings = MC_CLOUD_AWS_S3;
    }
    if ($settings) {
        if (!$bucket_name) {
            $bucket_name = $settings['amazon-s3-bucket-name'];
        }
        $recursion = 0;
        $put = $action == 'PUT';
        $host_name = $bucket_name . '.s3.amazonaws.com';
        $file = '';
        $timeout = false;
        if ($put) {
            $file_size = strlen($file);
            while ((!$file_size || $file_size < filesize($file_path)) && $recursion < 10) {
                $file = file_get_contents($file_path);
                $file_size = strlen($file);
                if ($recursion) {
                    sleep(1);
                }
                $recursion++;
            }
            $timeout = intval(filesize($file_path) / 1000000);
            $timeout = $timeout < 7 ? 7 : ($timeout > 600 ? 600 : $timeout);
        }
        $file_name = basename($file_path);
        $timestamp = gmdate('Ymd\THis\Z');
        $date = gmdate('Ymd');
        $request_headers = ['Content-Type' => $put ? mime_content_type($file_path) : '', 'Date' => $timestamp, 'Host' => $bucket_name . '.s3.amazonaws.com', 'x-amz-acl' => 'public-read', 'x-amz-content-sha256' => hash('sha256', $file)];
        ksort($request_headers);
        $canonical_headers = [];
        $signed_headers = [];
        foreach ($request_headers as $key => $value) {
            $canonical_headers[] = strtolower($key) . ':' . $value;
        }
        foreach ($request_headers as $key => $value) {
            $signed_headers[] = strtolower($key);
        }
        $canonical_headers = implode("\n", $canonical_headers);
        $signed_headers = implode(';', $signed_headers);
        $hashed_canonical_request = hash('sha256', implode("\n", [$action, '/' . $file_name, '', $canonical_headers, '', $signed_headers, hash('sha256', $file)]));
        $scope = [$date, $settings['amazon-s3-region'], 's3', 'aws4_request'];
        $string_to_sign = implode("\n", ['AWS4-HMAC-SHA256', $timestamp, implode('/', $scope), $hashed_canonical_request]);
        $kSecret = 'AWS4' . $settings['amazon-s3-secret-access-key'];
        $kDate = hash_hmac('sha256', $date, $kSecret, true);
        $kRegion = hash_hmac('sha256', $settings['amazon-s3-region'], $kDate, true);
        $kService = hash_hmac('sha256', 's3', $kRegion, true);
        $kSigning = hash_hmac('sha256', 'aws4_request', $kService, true);
        $authorization = 'AWS4-HMAC-SHA256' . ' ' . implode(',', ['Credential=' . $settings['amazon-s3-access-key'] . '/' . implode('/', $scope), 'SignedHeaders=' . $signed_headers, 'Signature=' . hash_hmac('sha256', $string_to_sign, $kSigning)]);
        $curl_headers = ['Authorization: ' . $authorization];
        foreach ($request_headers as $key => $value) {
            $curl_headers[] = $key . ": " . $value;
        }
        $url = 'https://' . $host_name . '/' . $file_name;
        $response = mc_curl($url, $file, $curl_headers, $action, $timeout);
        return $response ? $response : $url;
    }
    return false;
}

function mc_gmt_now($less_seconds = 0, $is_unix = false) {
    $now = gmdate('Y-m-d H:i:s', time() - $less_seconds);
    return $is_unix ? strtotime($now) : $now;
}

function mc_gmt_date_to_local($date_string, $utc_offset) {
    $date = DateTime::createFromFormat('Y-m-d H:i:s', $date_string, new DateTimeZone('UTC'));
    $date = $date->getTimestamp();
    return date('d/m/Y H:i:s', strtotime($date_string) + ($utc_offset * -1 * 3600));
}

function mc_error($error_code, $function_name, $message = '', $force = false) {
    $message_2 = (mc_is_cloud() ? MC_CLOUD_BRAND_NAME : 'Masi Chat ') . '[' . $function_name . '][' . $error_code . ']' . ($message ? ': ' . (is_string($message) ? $message : json_encode($message)) : '');
    if (($force && !mc_is_cloud()) || mc_is_debug()) {
        mc_debug($message_2);
        trigger_error($message_2);
        die($message_2);
    }
    return new MCError($error_code, $function_name, $message_2, $message);
}

function mc_is_debug() {
    return isset($_GET['debug']) || mc_isset($_POST, 'debug') || strpos(mc_isset($_SERVER, 'HTTP_REFERER'), 'debug');
}

function mc_get_json_resource($path_part) {
    return json_decode(file_get_contents(MC_PATH . '/resources/' . $path_part), true);
}

function mc_beautify_file_name($file_name) {
    $parts = explode('_', $file_name, 2);
    return isset($parts[1]) ? $parts[1] : $file_name;
}

function mc_is_valid_path($path) {
    $real_path = realpath($path);
    return ((defined('MC_URL') && strpos($real_path, MC_URL) === 0) || (defined('MC_PATH') && strpos($real_path, MC_PATH) === 0) || (defined('CLOUD_URL') && strpos($real_path, CLOUD_URL) === 0) || (defined('MC_CLOUD_PATH') && strpos($real_path, MC_CLOUD_PATH) === 0)) && file_exists($path);
}

/*
 * -----------------------------------------------------------
 * REPORTS
 * -----------------------------------------------------------
 *
 * 1. Return the data of a report
 * 2. Update the values of a report
 * 3. Export the report in a CSV file
 *
 */

function mc_reports($report_name, $date_start = false, $date_end = false, $timezone = false) {
    $date = '';
    $data = [];
    $data_final = [];
    $title = '';
    $table = [mc_('Date'), mc_('Count')];
    $description = '';
    $period = [];
    $query = '';
    $time_range = true;
    $label_type = 1;
    $chart_type = 'line';

    // Set up date range
    if ($date_start) {
        $date_start = date('Y-m-d', strtotime(str_replace('/', '-', $date_start)));
        $date = 'A.creation_time >= "' . mc_db_escape($date_start) . ' 00:00"';
    }
    if ($date_end) {
        $date_end = date('Y-m-d', strtotime(str_replace('/', '-', $date_end)));
        $date .= ($date ? ' AND ' : '') . 'A.creation_time <= "' . mc_db_escape($date_end) . ' 23:59"';
    }

    // Get the mc_pa
    switch ($report_name) {
        case 'conversations':
            $query = 'SELECT A.creation_time FROM mc_conversations A, mc_users B WHERE B.id = A.user_id AND B.user_type <> "visitor"';
            $title = 'Conversations count';
            $description = 'Count of new conversations started by users.';
            break;
        case 'missed-conversations':
            $query = 'SELECT creation_time FROM mc_conversations A WHERE id NOT IN (SELECT conversation_id FROM mc_messages A, mc_users B WHERE A.user_id = B.id AND (B.user_type = "agent" OR B.user_type = "admin"))';
            $title = 'Missed conversations count';
            $description = 'Count of conversations without a reply from an human agent. Conversations with a reply from the bot are counted.';
            break;
        case 'conversations-time':
            $query = 'SELECT creation_time, conversation_id FROM mc_messages A';
            $title = 'Average conversations duration';
            $description = 'Average conversations duration. Messages sent more than 7 days after the previous message are counted as part of a new conversation.';
            $table = [mc_('Date'), mc_('Average time')];
            $label_type = 2;
            break;
        case 'visitors':
            $query = 'SELECT creation_time, value FROM mc_reports A WHERE name = "visitors"';
            $title = 'Visitor registrations count';
            $description = 'Visitors count. Visitors are users who have not started any conversations and who are not registered.';
            break;
        case 'leads':
            $query = 'SELECT creation_time FROM mc_users A WHERE user_type = "lead"';
            $title = 'Lead registrations count';
            $description = 'Leads count. Leads are users who have started at least one conversation but who are not registered.';
            break;
        case 'users':
            $query = 'SELECT creation_time FROM mc_users A WHERE user_type = "user"';
            $title = 'User registrations count';
            $description = 'Users count. Users are registered with an email address.';
            break;
        case 'agents-response-time':
            $title = 'Average agent response time';
            $description = 'Average time for agents to send the first reply after the user sends the first message.';
            $table = [mc_('Agent name'), mc_('Average time')];
            $time_range = false;
            $chart_type = 'bar';
            $label_type = 2;
            break;
        case 'agents-conversations':
            $title = 'Agent conversations count';
            $description = 'Number of conversations which at least one reply from the agent.';
            $table = [mc_('Agent name'), mc_('Count')];
            $chart_type = 'bar';
            $time_range = false;
            break;
        case 'agents-conversations-time':
            $query = 'SELECT creation_time, conversation_id FROM mc_messages A';
            $title = 'Average agent conversations duration';
            $description = 'Average conversations duration of each agent. Messages sent more than 7 days after the previous message are counted as part of a new conversation.';
            $table = [mc_('Agent name'), mc_('Average time')];
            $chart_type = 'bar';
            $label_type = 2;
            $time_range = false;
            break;
        case 'agents-ratings':
            $title = 'Agent ratings';
            $description = 'Ratings assigned to agents.';
            $table = [mc_('Agent name'), mc_('Ratings')];
            $chart_type = 'horizontalBar';
            $time_range = false;
            $label_type = 3;
            break;
        case 'countries':
            $title = 'User countries';
            $description = 'Countries of users who started at least one chat.';
            $table = [mc_('Country'), mc_('Count')];
            $time_range = false;
            $chart_type = 'pie';
            $label_type = 4;
            break;
        case 'languages':
            $title = 'User languages';
            $description = 'Languages of users who started at least one chat.';
            $table = [mc_('Language'), mc_('Count')];
            $time_range = false;
            $chart_type = 'pie';
            $label_type = 4;
            break;
        case 'browsers':
            $title = 'User browsers';
            $description = 'Browsers used by users who started at least one chat.';
            $table = [mc_('Browser'), mc_('Count')];
            $time_range = false;
            $chart_type = 'pie';
            $label_type = 4;
            break;
        case 'os':
            $title = 'User operating systems';
            $description = 'Operating systems used by users who started at least one chat.';
            $table = [mc_('Operating system'), mc_('Count')];
            $time_range = false;
            $chart_type = 'pie';
            $label_type = 4;
            break;
        case 'follow-up':
            $query = 'SELECT creation_time, value FROM mc_reports A WHERE name = "follow-up"';
            $title = 'Follow-up emails count';
            $description = 'Number of users who registered their email via follow-up message.';
            break;
        case 'registrations':
            $query = 'SELECT creation_time, value FROM mc_reports A WHERE name = "registrations"';
            $title = 'Registrations count';
            $description = 'Number of users who created an account via the registration form of the chat.';
            break;
        case 'articles-searches':
            $query = 'SELECT creation_time, value FROM mc_reports A WHERE name = "articles-searches"';
            $title = 'Article searches';
            $description = 'Article searches made by users.';
            $table = [mc_('Date'), mc_('Search terms')];
            break;
        case 'articles-ratings':
            $query = 'SELECT value, extra FROM mc_reports A WHERE name = "article-ratings"';
            $title = 'Article ratings';
            $description = 'Ratings assigned to articles by users.';
            $table = [mc_('Article name'), mc_('Ratings')];
            $chart_type = 'horizontalBar';
            $time_range = false;
            $label_type = 3;
            break;
        case 'articles-views-single':
        case 'articles-views':
            $query = 'SELECT creation_time, value, extra FROM mc_reports A WHERE name = "articles-views"';
            $title = 'Article views';
            $description = 'Number of times articles have been viewed by users.';
            if ($report_name == 'articles-views-single') {
                $chart_type = 'horizontalBar';
                $time_range = false;
                $table = [mc_('Article'), mc_('Count')];
            }
            break;
        case 'sms-automations':
        case 'email-automations':
        case 'message-automations':
            $query = 'SELECT creation_time, value FROM mc_reports A WHERE name = "' . $report_name . '"';
            $title = $description = mc_string_slug($report_name, 'string') . ' count';
            break;
        case 'direct-sms':
        case 'direct-emails':
        case 'direct-messages':
            $query = 'SELECT creation_time, value FROM mc_reports A WHERE name = "' . $report_name . '"';
            $name = $report_name == 'direct-emails' ? 'emails' : ($report_name == 'direct-messages' ? 'chat messages' : 'text messages');
            $title = 'Direct ' . $name;
            $description = 'Direct messages sent to users. The details column shows the first part of the message and the number of users to which it has been sent to.';
            $table = [mc_('Date'), mc_('Details')];
            break;
    }
    switch ($report_name) {
        case 'sms-automations':
        case 'email-automations':
        case 'message-automations':
        case 'registrations':
        case 'follow-up':
        case 'users':
        case 'leads':
        case 'visitors':
        case 'conversations':
        case 'missed-conversations':
            $rows = mc_db_get($query . ($date ? ' AND ' . $date : '') . ' ORDER BY STR_TO_DATE(A.creation_time, "%Y-%m-%d %T")', false);
            $sum = !in_array($report_name, ['visitors', 'follow-up', 'registrations', 'message-automations', 'email-automations', 'sms-automations']);
            for ($i = 0; $i < count($rows); $i++) {
                $date_row = date('d/m/Y', strtotime($rows[$i]['creation_time']));
                $data[$date_row] = $sum ? [empty($data[$date_row]) ? 1 : $data[$date_row][0] + 1] : [$rows[$i]['value']];
            }
            break;
        case 'agents-conversations-time':
        case 'conversations-time':
            $rows = mc_db_get($query . ($date ? ' WHERE ' . $date : '') . ' ORDER BY STR_TO_DATE(creation_time, "%Y-%m-%d %T")', false);
            $count = count($rows);
            if ($count == 0) {
                return false;
            }
            $last_id = $rows[0]['conversation_id'];
            $first_time = $rows[0]['creation_time'];
            $times = [];
            $agents_times = $report_name == 'agents-conversations-time';
            for ($i = 1; $i < $count; $i++) {
                $time = $rows[$i]['creation_time'];
                if (($rows[$i]['conversation_id'] != $last_id) || (strtotime('+7 day', strtotime($first_time)) < strtotime($time)) || ($i == $count - 1)) {
                    $last_time = strtotime($rows[$i - 1]['creation_time']);
                    array_push($times, [$agents_times ? $last_id : date('d/m/Y', $last_time), $last_time - strtotime($first_time)]);
                    $first_time = $time;
                    $last_id = $rows[$i]['conversation_id'];
                }
            }
            if ($agents_times) {
                $agents_counts = [];
                $agents_conversations = [];
                $rows = mc_db_get('SELECT conversation_id, first_name, last_name FROM mc_messages A, mc_users B WHERE A.user_id = B.id AND (B.user_type = "agent" OR  B.user_type = "admin") GROUP BY conversation_id', false);
                for ($i = 0; $i < count($rows); $i++) {
                    $agents_conversations[$rows[$i]['conversation_id']] = mc_get_user_name($rows[$i]);
                }
                for ($i = 0; $i < count($times); $i++) {
                    if (isset($agents_conversations[$times[$i][0]])) {
                        $name = $agents_conversations[$times[$i][0]];
                        $data[$name] = empty($data[$name]) ? $times[$i][1] : $data[$name] + $times[$i][1];
                        $agents_counts[$name] = empty($agents_counts[$name]) ? 1 : $agents_counts[$name] + 1;
                    }
                }
                foreach ($data as $key => $value) {
                    $data[$key] = [intval($value / $agents_counts[$key]), gmdate('H:i:s', intval($value / $agents_counts[$key]))];
                }
            } else {
                for ($i = 0; $i < count($times); $i++) {
                    $time = $times[$i][0];
                    $count = 0;
                    $sum = 0;
                    if (!isset($data[$time])) {
                        for ($y = 0; $y < count($times); $y++) {
                            if ($times[$y][0] == $time) {
                                $sum += $times[$y][1];
                                $count++;
                            }
                        }
                        $data[$time] = [intval($sum / $count), gmdate('H:i:s', intval($sum / $count))];
                    }
                }
            }
            break;
        case 'agents-conversations':
            $rows = mc_db_get('SELECT first_name, last_name FROM mc_messages A, mc_users B WHERE A.user_id = B.id AND (B.user_type = "agent" OR  B.user_type = "admin") ' . ($date ? ' AND ' . $date : '') . ' GROUP BY conversation_id, B.id', false);
            for ($i = 0; $i < count($rows); $i++) {
                $name = mc_get_user_name($rows[$i]);
                $data[$name] = [empty($data[$name]) ? 1 : $data[$name][0] + 1];
            }
            break;
        case 'agents-response-time':
            $conversations = mc_db_get('SELECT A.user_id, B.user_type, A.conversation_id, A.creation_time FROM mc_messages A, mc_users B WHERE B.id = A.user_id AND A.conversation_id IN (SELECT conversation_id FROM mc_messages A WHERE user_id IN (SELECT id FROM mc_users WHERE user_type = "agent" OR user_type = "admin") ' . ($date ? ' AND ' . $date : '') . ') ORDER BY A.conversation_id, STR_TO_DATE(A.creation_time, "%Y-%m-%d %T")', false);
            $count = count($conversations);
            if ($count == 0) {
                return false;
            }
            $agents = [];
            $active_conversation = $conversations[0];
            $skip = false;
            $agents_ids = '';
            for ($i = 1; $i < $count; $i++) {
                if ($skip) {
                    if ($active_conversation['conversation_id'] != $conversations[$i]['conversation_id']) {
                        $active_conversation = $conversations[$i];
                        $skip = false;
                    }
                    continue;
                }
                if (mc_is_agent($conversations[$i], true)) {
                    $conversation_time = strtotime($conversations[$i]['creation_time']) - strtotime($active_conversation['creation_time']);
                    $agent_id = $conversations[$i]['user_id'];
                    if (!isset($agents[$agent_id])) {
                        $agents[$agent_id] = [];
                        $agents_ids .= $agent_id . ',';
                    }
                    array_push($agents[$agent_id], $conversation_time);
                    $skip = true;
                }
            }
            $rows = mc_db_get('SELECT id, first_name, last_name FROM mc_users WHERE id IN (' . substr($agents_ids, 0, -1) . ')', false);
            $agent_names = [];
            for ($i = 0; $i < count($rows); $i++) {
                $agent_names[$rows[$i]['id']] = mc_get_user_name($rows[$i]);
            }
            foreach ($agents as $key => $times) {
                $sum = 0;
                $count = count($times);
                for ($i = 0; $i < $count; $i++) {
                    $sum += $times[$i];
                }
                $data[$agent_names[$key]] = [intval($sum / $count), gmdate('H:i:s', intval($sum / $count))];
            }
            break;
        case 'articles-ratings':
        case 'agents-ratings':
            $article = $report_name == 'articles-ratings';
            $ratings = $article ? mc_db_get($query, false) : mc_get_external_setting('ratings');
            if ($ratings) {
                $rows = $article ? mc_get_articles() : mc_db_get('SELECT id, first_name, last_name FROM mc_users WHERE user_type = "agent" OR user_type = "admin"', false);
                $items = [];
                for ($i = 0; $i < count($rows); $i++) {
                    $items[$rows[$i]['id']] = $article ? $rows[$i]['title'] : mc_get_user_name($rows[$i]);
                }
                if ($article) {
                    for ($i = 0; $i < count($ratings); $i++) {
                        $rating = $ratings[$i];
                        if (isset($rating['extra'])) {
                            $id = $rating['extra'];
                            if (isset($items[$id]) && !empty($rating['value'])) {
                                $article_ratings = json_decode($rating['value']);
                                $positives = 0;
                                $negatives = 0;
                                $name = strlen($items[$id]) > 40 ? substr($items[$id], 0, 40) . '...' : $items[$id];
                                for ($y = 0; $y < count($article_ratings); $y++) {
                                    $positives += $article_ratings[$y] == 1 ? 1 : 0;
                                    $negatives += $article_ratings[$y] == 1 ? 0 : 1;
                                }
                                $data[$name] = [$positives, $negatives];
                            }
                        }
                    }
                } else {
                    foreach ($ratings as $rating) {
                        if (isset($rating['agent_id'])) {
                            $id = $rating['agent_id'];
                            if (isset($items[$id])) {
                                $positive = $rating['rating'] == 1 ? 1 : 0;
                                $negative = $rating['rating'] == 1 ? 0 : 1;
                                $name = $items[$id];
                                $data[$name] = isset($data[$name]) ? [$data[$name][0] + $positive, $data[$name][1] + $negative] : [$positive, $negative];
                            }
                        }
                    }
                }
                foreach ($data as $key => $value) {
                    $positive = $value[0];
                    $negative = $value[1];
                    $average = round($positive / ($negative + $positive) * 100, 2);
                    $data[$key] = [[$average, 100 - $average], '<i class="mc-icon-like"></i>' . $positive . ' (' . $average . '%) <i class="mc-icon-dislike"></i>' . $negative];
                }
            }
            break;
        case 'articles-views':
        case 'articles-views-single':
            $rows = mc_db_get($query . ($date ? ' AND ' . $date : '') . ' ORDER BY STR_TO_DATE(A.creation_time, "%Y-%m-%d %T")', false);
            $single = $report_name == 'articles-views-single';
            for ($i = 0; $i < count($rows); $i++) {
                $date_row = $single ? $rows[$i]['extra'] : date('d/m/Y', strtotime($rows[$i]['creation_time']));
                $data[$date_row] = [intval($rows[$i]['value']) + (empty($data[$date_row]) ? 0 : $data[$date_row][0])];
            }
            if ($single) {
                $articles = mc_get_articles();
                $data_names = [];
                for ($i = 0; $i < count($articles); $i++) {
                    $id = mc_isset($articles[$i], 'id');
                    if ($id && isset($data[$id])) {
                        $article_title = $articles[$i]['title'];
                        $data_names[strlen($article_title) > 40 ? substr($article_title, 0, 40) . '...' : $article_title] = $data[$id];
                    }
                }
                $data = $data_names;
            }
            break;
        case 'os':
        case 'browsers':
        case 'languages':
        case 'countries':
            $field = 'location';
            $is_languages = $report_name == 'languages';
            $is_browser = $report_name == 'browsers';
            $is_os = $report_name == 'os';
            $is_country = $report_name == 'countries';
            if ($is_languages) {
                $field = 'browser_language';
            } else if ($is_browser) {
                $field = 'browser';
            } else if ($is_os) {
                $field = 'os';
            }
            $language_codes = mc_get_json_resource('languages/language-codes.json');
            $country_codes = $is_country ? mc_get_json_resource('json/countries.json') : false;
            $rows = mc_db_get('SELECT value FROM mc_users_data WHERE slug = "' . $field . '" AND user_id IN (SELECT id FROM mc_users A WHERE (user_type = "lead" OR user_type = "user")' . ($date ? ' AND ' . $date : '') . ')', false);
            $total = 0;
            $flags = [];
            for ($i = 0; $i < count($rows); $i++) {
                $value = $rows[$i]['value'];
                $valid = false;
                if ($is_country && strpos($value, ',')) {
                    $value = trim(substr($value, strpos($value, ',') + 1));
                    $valid = true;
                }
                if (($is_languages && isset($language_codes[strtolower($value)])) || ($is_country && isset($country_codes[strtoupper($value)]))) {
                    $code = strtolower($is_languages ? $value : $country_codes[strtoupper($value)]);
                    $value = $language_codes[$code];
                    if (!isset($flags[$value]) && file_exists(MC_PATH . '/media/flags/' . $code . '.png')) {
                        $flags[$value] = $code;
                    }
                    $valid = true;
                }
                if ($valid || $is_browser || $is_os) {
                    $data[$value] = empty($data[$value]) ? 1 : $data[$value] + 1;
                    $total++;
                }
            }
            arsort($data);
            foreach ($data as $key => $value) {
                $image = '';
                if (isset($flags[$key]))
                    $image = '<img class="mc-flag" src="' . MC_URL . '/media/flags/' . $flags[$key] . '.png" />';
                if ($is_browser) {
                    $lowercase = strtolower($key);
                    if (strpos($lowercase, 'chrome') !== false) {
                        $image = 'chrome';
                    } else if (strpos($lowercase, 'edge') !== false) {
                        $image = 'edge';
                    } else if (strpos($lowercase, 'firefox') !== false) {
                        $image = 'firefox';
                    } else if (strpos($lowercase, 'opera') !== false) {
                        $image = 'opera';
                    } else if (strpos($lowercase, 'safari') !== false) {
                        $image = 'safari';
                    }
                    if ($image)
                        $image = '<img src="' . MC_URL . '/media/devices/' . $image . '.svg" />';
                }
                if ($is_os) {
                    $lowercase = strtolower($key);
                    if (strpos($lowercase, 'windows') !== false) {
                        $image = 'windows';
                    } else if (strpos($lowercase, 'mac') !== false || strpos($lowercase, 'apple') !== false || strpos($lowercase, 'ipad') !== false || strpos($lowercase, 'iphone') !== false) {
                        $image = 'apple';
                    } else if (strpos($lowercase, 'android') !== false) {
                        $image = 'android';
                    } else if (strpos($lowercase, 'linux') !== false) {
                        $image = 'linux';
                    } else if (strpos($lowercase, 'ubuntu') !== false) {
                        $image = 'ubuntu';
                    }
                    if ($image)
                        $image = '<img src="' . MC_URL . '/media/devices/' . $image . '.svg" />';
                }
                $data[$key] = [$value, $image . $value . ' (' . round($value / $total * 100, 2) . '%)'];
            }
            break;
        case 'direct-sms':
        case 'direct-emails':
        case 'direct-messages':
        case 'articles-searches':
            $rows = mc_db_get($query . ($date ? ' AND ' . $date : '') . ' ORDER BY STR_TO_DATE(A.creation_time, "%Y-%m-%d %T")', false);
            for ($i = 0; $i < count($rows); $i++) {
                $date_row = date('d/m/Y', strtotime($rows[$i]['creation_time']));
                $search = '<div>' . $rows[$i]['value'] . '</div>';
                $data[$date_row] = empty($data[$date_row]) ? [1, $search] : [$data[$date_row][0] + 1, $data[$date_row][1] . $search];
            }
            break;
    }

    // Generate all days, months, years within the date range
    if (!count($data)) {
        return false;
    }
    if ($time_range) {
        if (!$date_start) {
            $date_start = date('Y-m-d', strtotime(str_replace('/', '-', array_keys($data)[0])));
        }
        if (!$date_end) {
            $date_end = date('Y-m-d', strtotime(str_replace('/', '-', array_keys($data)[count($data) - 1])));
        }
        if ($timezone) {
            date_default_timezone_set($timezone);
        }
        $period = new DatePeriod(new DateTime($date_start), new DateInterval('P1D'), new DateTime(date('Y-m-d', strtotime($date_end . '+1 days'))));
        $period = iterator_to_array($period);
        $period_count = count($period);
        $date_format = $period_count > 730 ? 'Y' : ($period_count > 62 ? 'm/Y' : 'd/m/Y');
        $is_array = count(reset($data)) > 1;
        $counts = [];
        $average = $label_type == 2;
        for ($i = 0; $i < $period_count; $i++) {
            $key = $period[$i]->format($date_format);
            $key_original = $period[$i]->format('d/m/Y');
            $value = empty($data[$key_original]) ? 0 : $data[$key_original][0];
            $data_final[$key] = [empty($data_final[$key]) ? $value : $data_final[$key][0] + $value];
            if ($average) {
                $counts[$key] = empty($counts[$key]) ? 1 : $counts[$key] + 1;
            }
            if ($is_array) {
                array_push($data_final[$key], empty($data[$key_original][1]) ? '' : $data[$key_original][1]);
            }
        }
        if ($average && $period_count > 62) {
            foreach ($data_final as $key => $value) {
                $data_final[$key] = [intval($value[0] / $counts[$key]), gmdate('H:i:s', intval($value[0] / $counts[$key]))];
            }
        }
    } else {
        $data_final = $data;
    }

    // Return the data
    return ['title' => mc_($title), 'description' => mc_($description), 'data' => $data_final, 'table' => $table, 'table_inverse' => $time_range, 'label_type' => $label_type, 'chart_type' => $chart_type];
}

function mc_reports_update($name, $value = false, $external_id = false, $extra = false) {
    if (mc_get_multi_setting('performance', 'performance-reports')) {
        return false;
    }
    $now = gmdate('Y-m-d');
    $name = mc_db_escape($name);
    $extra = mc_db_escape($extra);
    switch ($name) {
        case 'direct-sms':
        case 'direct-emails':
        case 'direct-messages':
        case 'articles-searches':
            return mc_db_query('INSERT INTO mc_reports (name, value, creation_time, external_id, extra) VALUES ("' . $name . '", "' . mc_db_escape($value) . '", "' . $now . '", NULL, NULL)');
        case 'articles-views':
            $where = ' WHERE name = "articles-views" AND extra = "' . $extra . '" AND creation_time = "' . $now . '"';
            $count = mc_db_get('SELECT value FROM mc_reports' . $where . ' LIMIT 1');
            return mc_db_query(empty($count) ? 'INSERT INTO mc_reports (name, value, creation_time, external_id, extra) VALUES ("' . $name . '", 1, "' . $now . '", NULL, "' . $extra . '")' : 'UPDATE mc_reports SET value = ' . (intval($count['value']) + 1) . $where);
        default:
            $where = ' WHERE name = "' . $name . '" AND creation_time = "' . $now . '"';
            $count = mc_db_get('SELECT value FROM mc_reports' . $where . ' LIMIT 1');
            return mc_db_query(empty($count) ? 'INSERT INTO mc_reports (name, value, creation_time, external_id, extra) VALUES ("' . $name . '", 1, "' . $now . '", ' . ($external_id === false ? 'NULL' : '"' . $external_id . '"') . ', ' . ($extra === false ? 'NULL' : '"' . $extra . '"') . ')' : 'UPDATE mc_reports SET value = ' . (intval($count['value']) + 1) . $where);
    }
}

function mc_reports_export($report_name, $date_start = false, $date_end = false, $timezone = false) {
    if ($timezone) {
        date_default_timezone_set($timezone);
    }
    $response = mc_reports($report_name, $date_start, $date_end, $timezone);
    if ($response) {
        $data = mc_isset($response, 'data', []);
        $rows = [];
        if ($report_name == 'agents-ratings') {
            $response['table'] = [$response['table'][0], mc_('Positive'), mc_('Positive percentage'), mc_('Negative')];
            foreach ($data as $key => $value) {
                $ratings = explode('<i class="mc-icon-dislike"></i>', $value[1]);
                $ratings[0] = str_replace('<i class="mc-icon-like"></i>', '', $ratings[0]);
                $ratings[0] = substr($ratings[0], 0, strpos($ratings[0], '('));
                array_push($rows, [$key, $ratings[0], $value[0], $ratings[1]]);
            }
        } else if ($report_name == 'agents-availability') {
            $response['table'] = [$response['table'][0], mc_('Date'), mc_('Intervals')];
            foreach ($data as $key => $value) {
                foreach ($value[1] as $date => $intervals) {
                    array_push($rows, [$key, $date, $intervals]);
                }
            }
        } else {
            foreach ($data as $key => $value) {
                $value = $value[count($value) - 1];
                if (strpos($value, ' />')) {
                    $value = substr($value, strpos($value, '/>') + 2);
                }
                array_push($rows, [$key, $value]);
            }
        }
        return mc_csv($rows, $response['table'], 'report-' . rand(100000, 999999999));
    }
    return false;
}

/*
 * -----------------------------------------------------------
 * AUTOMATIONS
 * -----------------------------------------------------------
 *
 * 1. Get all automations
 * 2. Save all automations
 * 3. Run all valid automations and return the ones which need client-side validations
 * 4. Check if an automation is valid and can be executed
 * 5. Execute an automation
 *
 */

function mc_automations_get() {
    $types = ['messages', 'emails', 'sms', 'popups', 'design', 'more'];
    $automations = mc_get_external_setting('automations', []);
    $translations = [];
    $rows = mc_db_get('SELECT name, value FROM mc_settings WHERE name LIKE "automations-translations-%"', false);
    for ($i = 0; $i < count($rows); $i++) {
        $translations[substr($rows[$i]['name'], -2)] = json_decode($rows[$i]['value'], true);
    }
    for ($i = 0; $i < count($types); $i++) {
        if (!$automations || !isset($automations[$types[$i]]))
            $automations[$types[$i]] = [];
    }
    return [$automations, $translations];
}

function mc_automations_save($automations, $translations = false) {
    if ($translations) {
        $db = '';
        foreach ($translations as $key => $value) {
            $name = 'automations-translations-' . $key;
            mc_save_external_setting($name, $value);
            $db .= '"' . $name . '",';
        }
        mc_db_query('DELETE FROM mc_settings WHERE name LIKE "automations-translations-%" AND name NOT IN (' . substr($db, 0, -1) . ')');
    }
    return mc_save_external_setting('automations', empty($automations) ? [] : $automations);
}

function mc_automations_run_all() {
    if (mc_is_agent()) {
        return false;
    }
    $response = [];
    $automations_all = mc_automations_get();
    $user_language = mc_get_user_language();
    foreach ($automations_all[0] as $type => $automations) {
        for ($i = 0; $i < count($automations); $i++) {
            $automations[$i]['type'] = $type;
            $validation = mc_automations_validate($automations[$i]);
            if ($validation) {
                $automation_id = $automations[$i]['id'];
                $conditions = mc_isset($validation, 'conditions', []);

                // Translations
                if ($user_language && isset($automations_all[1][$user_language])) {
                    $translations = mc_isset($automations_all[1][$user_language], $type, []);
                    for ($x = 0; $x < count($translations); $x++) {
                        if ($translations[$x]['id'] == $automation_id) {
                            $automations[$i] = $translations[$x];
                            $automations[$i]['type'] = $type;
                            break;
                        }
                    }
                }
                if (!empty($validation['repeat_id'])) {
                    $automations[$i]['repeat_id'] = $validation['repeat_id'];
                }
                if (count($conditions) || $type == 'popups' || $type == 'design' || $type == 'more' || !mc_get_active_user()) {

                    // Automation with client-side conditions, server-side invalid conditions, or popup, design
                    $automations[$i]['conditions'] = $conditions;
                    array_push($response, $automations[$i]);
                } else {

                    // Run automation
                    mc_automations_run($automations[$i]);
                }
            }
        }
    }
    return $response;
}

function mc_automations_validate($automation, $is_flow = false) {
    $conditions = $is_flow ? $automation : mc_isset($automation, 'conditions', []);
    if (empty($conditions)) {
        return true;
    }
    $invalid_conditions = [];
    $repeat_id = false;
    $valid = false;
    $active_user = mc_get_active_user();
    $active_user_id = mc_isset($active_user, 'id');
    $custom_fields = array_column(mc_get_setting('user-additional-fields', []), 'extra-field-slug');
    for ($i = 0; $i < count($conditions); $i++) {
        $valid = false;
        $criteria = $conditions[$i][1];
        $type = $conditions[$i][0];
        switch ($type) {
            case 'birthdate':
                if ($active_user) {
                    $user_value = date('d/m', strtotime(mc_get_user_extra($active_user_id, 'birthdate')));
                    if (($criteria == 'is-set' && !empty($user_value)) || ($criteria == 'is-not-set' && empty($user_value))) {
                        $valid = true;
                    } else {
                        $dates = str_replace(' ', '', $conditions[$i][2]);
                        if ($criteria == 'is-between') {
                            $dates = explode('-', $dates);
                            if (count($dates) == 2) {
                                $dates = [strtotime(str_replace('/', '-', $dates[0]) . '-1900'), strtotime(str_replace('/', '-', $dates[1]) . '-1900')];
                                $user_value = strtotime(str_replace('/', '-', $user_value) . '-1900');
                                $valid = ($user_value >= $dates[0] && $user_value <= $dates[1]);
                            }
                        } else if ($criteria == 'is-exactly') {
                            $valid = $user_value == $dates;
                        }
                    }
                } else {
                    $valid = true;
                    array_push($invalid_conditions, $conditions[$i]);
                }
                break;
            case 'creation_time':
            case 'datetime':
                $user_value = $type == 'datetime' ? time() : strtotime(mc_get_user(mc_get_active_user_ID())[$type]);
                $offset = intval(mc_get_setting('timetable-utc', 0)) * 3600;
                if ($criteria == 'is-between') {
                    $dates = explode('-', str_replace(' ', '', $conditions[$i][2]));
                    if (count($dates) == 2) {
                        $unix = date_timestamp_get(DateTime::createFromFormat('d/m/Y H:i', $dates[0] . (strpos($dates[0], ':') ? '' : ' 00:00'))) + (strpos($dates[0], ':') ? $offset : 0);
                        $unix_end = date_timestamp_get(DateTime::createFromFormat('d/m/Y H:i', $dates[1] . (strpos($dates[1], ':') ? '' : ' 23:59'))) + (strpos($dates[1], ':') ? $offset : 0);
                        $valid = ($user_value >= $unix) && ($user_value <= $unix_end);
                        $continue = true;
                    }
                } else {
                    $is_time = strpos($conditions[$i][2], ':');
                    $unix = date_timestamp_get(DateTime::createFromFormat('d/m/Y H:i', $conditions[$i][2] . ($is_time ? '' : ' 00:00'))) + $offset;
                    $valid = $user_value == $unix || (!$is_time && $user_value > $unix && $user_value < $unix + 86400);
                }
                if (!$valid) {
                    for ($j = 0; $j < count($conditions); $j++) {
                        if ($conditions[$j][0] == 'repeat') {
                            $condition = $conditions[$j][1];
                            if ($criteria == 'is-between' && $continue) {
                                $hhmm = false;
                                $hhmm_end = false;
                                if (strpos($dates[0], ':') && strpos($dates[1], ':')) {
                                    $hhmm = strtotime(date('Y-m-d ' . explode(' ', $dates[0])[1])) + $offset;
                                    $hhmm_end = strtotime(date('Y-m-d ' . explode(' ', $dates[1])[1])) + $offset;
                                }
                                if ($condition == 'every-day') {
                                    $valid = $hhmm ? ($user_value >= $hhmm) && ($user_value <= $hhmm_end) : true;
                                    $repeat_id = $valid ? date('z') : false;
                                } else {
                                    $letter = $condition == 'every-week' ? 'w' : ($condition == 'every-month' ? 'd' : 'z');
                                    $letter_value_now = date($letter);
                                    $letter_value_unix = date($letter, $unix);
                                    $letter_value_unix_end = date($letter, $unix_end);
                                    if ($letter == 'z') {
                                        $letter_value_now -= date('L');
                                        $letter_value_unix -= date('L', $unix);
                                        $letter_value_unix_end -= date('L', $unix_end);
                                    }
                                    $valid = ($letter_value_now >= $letter_value_unix) && (date($letter, strtotime('+' . ($letter_value_unix_end - $letter_value_unix - (($letter_value_now >= $letter_value_unix) && ($letter_value_now <= $letter_value_unix_end) ? $letter_value_now - $letter_value_unix : 0)) . ' days')) <= $letter_value_unix_end);
                                    if ($valid && $hhmm) {
                                        $valid = ($user_value >= $hhmm) && ($user_value <= $hhmm_end);
                                    }
                                    $repeat_id = $valid ? $letter_value_now : false;
                                }
                            } else if ($criteria == 'is-exactly') {
                                if ($condition == 'every-day') {
                                    $valid = true;
                                    $repeat_id = date('z');
                                } else {
                                    $letter = $condition == 'every-week' ? 'w' : ($condition == 'every-month' ? 'd' : 'z');
                                    $valid = $letter == 'z' ? ((date($letter, $unix) - date('L', $unix)) == (date($letter) - date('L'))) : (date($letter, $unix) == date($letter));
                                    $repeat_id = $valid ? date($letter) : false;
                                }
                            }
                            break;
                        }
                    }
                }
                break;
            case 'include_urls': // Deprecated: all this block
            case 'exclude_urls': // Deprecated: all this block
                $url = strtolower(str_replace(['https://', 'http://', 'www.'], '', mc_isset($_POST, 'current_url', mc_isset($_SERVER, 'HTTP_REFERER'))));
                $checks = explode(',', strtolower(str_replace(['https://', 'http://', 'www.', ' '], '', $conditions[$i][2])));
                if (($criteria == 'is-set' && !empty($checks)) || ($criteria == 'is-not-set' && empty($checks))) {
                    $valid = true;
                } else {
                    $include = $criteria != 'exclude_urls';
                    if (!$include) {
                        $valid = true;
                    }
                    for ($j = 0; $j < count($checks); $j++) {
                        if (($criteria == 'contains' && strpos($url . '/', $checks[$j]) !== false) || ($criteria == 'does-not-contain' && strpos($url, $checks[$j]) === false) || ($criteria == 'is-exactly' && $checks[$j] == $url) || ($criteria == 'is-not' && $checks[$j] != $url)) {
                            $valid = $include;
                            break;
                        }
                    }
                }
                break;
            case 'user_type':
                if ($active_user) {
                    $user_type = mc_isset($active_user, 'user_type');
                    $valid = ($criteria == 'is-visitor' && $user_type == 'visitor') || ($criteria == 'is-lead' && $user_type == 'is-lead') || ($criteria == 'is-user' && $user_type == 'user') || ($criteria == 'is-not-visitor' && $user_type != 'visitor') || ($criteria == 'is-not-lead' && $user_type != 'lead') || ($criteria == 'is-not-user' && $user_type != 'user');
                } else {
                    $valid = true;
                    array_push($invalid_conditions, $conditions[$i]);
                }
                break;
            case 'postal_code':
            case 'company':
            case 'cities':
            case 'languages':
            case 'countries':
                if ($active_user || $type == 'languages') {
                    if ($type == 'languages') {
                        $user_value = mc_get_user_language($active_user_id, true);
                    } else if ($type == 'cities') {
                        $user_value = mc_get_user_extra($active_user_id, 'location');
                        if ($user_value) {
                            $user_value = substr($user_value, 0, strpos($user_value, ','));
                        } else {
                            $user_value = mc_get_user_extra($active_user_id, 'city');
                        }
                    } else if ($type == 'countries') {
                        $user_value = mc_get_user_extra($active_user_id, 'country_code');
                        if (!$user_value) {
                            $user_value = mc_get_user_extra($active_user_id, 'country');
                            if (!$user_value) {
                                $user_value = mc_get_user_extra($active_user_id, 'location');
                                if ($user_value) {
                                    $user_value = trim(substr($user_value, strpos($user_value, ',')));
                                }
                            }
                            if ($user_value) {
                                $countries = mc_get_json_resource('json/countries.json');
                                if (isset($countries[$user_value])) {
                                    $user_value = $countries[$user_value];
                                } else if (strlen($user_value) > 2) {
                                    $user_value = substr($user_value, 0, 2);
                                }
                            }
                        }
                    } else {
                        $user_value = mc_get_user_extra($active_user_id, $type);
                    }
                    if ($user_value) {
                        $user_value = strtolower(trim($user_value));
                        $values = explode(',', strtolower(str_replace(' ', '', $conditions[$i][2])));
                        if ($criteria == 'is-included' && in_array($user_value, $values)) {
                            $valid = true;
                        } else if ($criteria == 'is-not-included' && !in_array($user_value, $values)) {
                            $valid = true;
                        } else {
                            $valid = ($criteria == 'is-set' && !empty($user_value)) || ($criteria == 'is-not-set' && empty($user_value));
                        }
                    }
                } else {
                    $valid = true;
                    array_push($invalid_conditions, $conditions[$i]);
                }
                break;
            case 'returning_visitor':
                $is_first_visitor = $criteria == 'first-time-visitor';
                if ($active_user) {
                    $times = mc_db_get('SELECT creation_time, last_activity FROM mc_users WHERE id = ' . $active_user_id);
                    if ($times) {
                        $difference = strtotime($times['last_activity']) - strtotime($times['creation_time']);
                        $valid = $is_first_visitor ? $difference < 86400 : $difference > 86400;
                    }
                } else if ($is_first_visitor) {
                    $valid = true;
                }
                break;
            case 'repeat':
                $valid = true;
                break;
        }
        if (in_array($type, $custom_fields) || in_array($type, ['url', 'website', 'email', 'phone'])) {
            if ($active_user) {
                $user_value = strtolower($type == 'email' ? $active_user['email'] : ($type == 'url' ? mc_isset($_POST, 'current_url', $_SERVER['HTTP_REFERER']) : mc_get_user_extra($active_user_id, $type)));
                if ($type == 'url' || $type == 'website') {
                    $conditions[$i][2] = str_replace(['https://', 'http://', 'www.'], '', $conditions[$i][2]);
                    $user_value = str_replace(['https://', 'http://', 'www.'], '', $user_value);
                }
                $checks = explode(',', strtolower(str_replace(' ', '', $conditions[$i][2])));
                if ($criteria == 'is-set' || $criteria == 'is-not-set') {
                    $valid = ($criteria == 'is-set' && !empty($user_value)) || ($criteria == 'is-not-set' && empty($user_value));
                } else {
                    $valid = $criteria != 'contains';
                    for ($j = 0; $j < count($checks); $j++) {
                        if (strpos($user_value, $checks[$j]) !== false) {
                            $valid = $criteria == 'contains';
                            break;
                        }
                    }
                }
            } else {
                $valid = true;
                array_push($invalid_conditions, $conditions[$i]);
            }
        }
        if (!$valid) {
            break;
        }
    }
    if ($is_flow) {
        return $valid && empty($invalid_conditions);
    }
    if ($valid && !mc_automations_is_sent($active_user_id, $automation, $repeat_id)) {

        // Check user details conditions
        if ($automation['type'] == 'emails' && (!$active_user || empty($active_user['email']))) {
            array_push($invalid_conditions, ['email']);
        } else if ($automation['type'] == 'sms' && !mc_get_user_extra($active_user_id, 'phone')) {
            array_push($invalid_conditions, ['phone']);
        }

        // Return the result
        return ['conditions' => $invalid_conditions, 'repeat_id' => $repeat_id];
    }
    return false;
}

function mc_automations_run($automation, $validate = false) {
    $active_user = mc_get_active_user();
    $response = false;
    if ($validate) {
        $validation = mc_automations_validate($automation);
        if (!$validation || count($validation['conditions']) > 0) {
            return false;
        }
    }
    if ($active_user) {
        $active_user_id = $active_user['id'];
        if (mc_automations_is_sent($active_user_id, $automation)) {
            return false;
        }
        switch ($automation['type']) {
            case 'messages':
                $response = mc_send_message(mc_get_bot_id(), mc_get_last_conversation_id_or_create($active_user_id, 3), mc_t($automation['message']), [], 3, '{ "event": "open-chat" }');
                mc_reports_update('message-automations');
                break;
            case 'emails':
                $response = empty($active_user['email']) ? false : mc_email_send($active_user['email'], mc_merge_fields($automation['subject']), mc_merge_fields(mc_email_default_parts($automation['message'], $active_user_id)));
                mc_reports_update('email-automations');
                break;
            case 'sms':
                $phone = mc_get_user_extra($active_user_id, 'phone');
                $response = $phone ? mc_send_sms(mc_merge_fields($automation['message']), $phone, false) : false;
                mc_reports_update('sms-automations');
                break;
            default:
                trigger_error('Invalid automation type in mc_automations_run()');
                return false;
        }
        $history = mc_get_external_setting('automations-history', []);
        $history_value = [$active_user['id'], $automation['id']];
        if (count($history) > 10000) {
            $history = array_slice($history, 1000);
        }
        if (isset($automation['repeat_id'])) {
            array_push($history_value, $automation['repeat_id']);
        }
        if ($response) {
            array_push($history, $history_value);
        }
        mc_save_external_setting('automations-history', $history);
    }
    return $response;
}

function mc_automations_is_sent($user_id, $automation, $repeat_id = false) {
    $history = mc_get_external_setting('automations-history', []);
    if ($user_id) {
        for ($x = 0, $length = count($history); $x < $length; $x++) {
            if ($history[$x][0] == $user_id && $history[$x][1] == $automation['id'] && (!$repeat_id || (count($history[$x]) > 2 && $history[$x][2] == $repeat_id))) {
                return true;
            }
        }
    }
    return false;
}

/*
 * -----------------------------------------------------------
 * CLOUD
 * -----------------------------------------------------------
 *
 * 1. Increase the membership messages count for the current month
 * 2. Check if the membership is valid
 * 3. Cloud account
 * 4. Load the config.php file
 * 5. Load cloud environment from token URL
 * 6. Load reseller js and css codes
 * 7. Add or delete agent
 * 8. Set and return cloud login data
 * 9. Check if cloud version
 * 10. Check the the user has credits
 * 11. Reset the login data and reload
 *
 */

function mc_cloud_increase_count() {
    require_once(MC_CLOUD_PATH . '/account/functions.php');
    cloud_increase_count();
}

function mc_cloud_membership_validation($die = false) {
    require_once(MC_CLOUD_PATH . '/account/functions.php');
    $membership = membership_get_active();
    $expiration = DateTime::createFromFormat('d-m-y', $membership['expiration']);
    return !$membership || !isset($membership['count']) || intval($membership['count']) > intval($membership['quota']) || (isset($membership['count_agents']) && isset($membership['quota_agents']) && intval($membership['count_agents']) > intval($membership['quota_agents'])) || ($membership['price'] != 0 && (!$expiration || time() > $expiration->getTimestamp())) ? ($die || !mc_isset(account(), 'owner') ? die('account-suspended') : '<script>document.location = "' . CLOUD_URL . '/account"</script>') : '<script>var MC_CLOUD_FREE = ' . (empty($membership['id']) || $membership['id'] == 'free' ? 'true' : 'false') . '</script>';
}

function mc_cloud_account() {
    return json_decode(mc_encryption(isset($_POST['cloud']) ? $_POST['cloud'] : mc_isset($_GET, 'cloud'), false), true);
}

function mc_cloud_account_id() {
    global $ACTIVE_ACCOUNT;
    global $ACTIVE_ACCOUNT_ID;
    if ($ACTIVE_ACCOUNT_ID) {
        return $ACTIVE_ACCOUNT_ID;
    }
    if ($ACTIVE_ACCOUNT) {
        $ACTIVE_ACCOUNT_ID = $ACTIVE_ACCOUNT['user_id'];
        return $ACTIVE_ACCOUNT_ID;
    }
    require_once(MC_CLOUD_PATH . '/account/functions.php');
    $ACTIVE_ACCOUNT_ID = get_active_account_id();
    return $ACTIVE_ACCOUNT_ID;
}

function mc_cloud_ajax_function_forbidden($function_name) {
    return in_array($function_name, ['installation', 'upload-path', 'get-versions', 'update', 'app-activation', 'app-get-key', 'system-requirements', 'path']);
}

function mc_cloud_load() {
    if (!defined('MC_DB_NAME')) {
        $data = !empty($_POST['cloud']) ? $_POST['cloud'] : (!empty($_GET['cloud']) ? $_GET['cloud'] : (empty($_COOKIE['mc-cloud']) ? false : $_COOKIE['mc-cloud']));
        if ($data) {
            $cookie = json_decode(mc_encryption($data, false), true);
            $path = MC_CLOUD_PATH . '/script/config/config_' . $cookie['token'] . '.php';
            if (file_exists($path)) {
                require_once($path);
                return true;
            }
            return 'config-file-not-found';
        } else {
            return 'cloud-data-not-found';
        }
    }
    return true;
}

function mc_cloud_load_by_url() {
    if (mc_is_cloud()) {
        $token = isset($_GET['cloud']) ? $_GET['cloud'] : false;
        if ($token) {
            $token = mc_sanatize_file_name($token);
            $path = MC_CLOUD_PATH . '/script/config/config_' . $token . '.php';
            if (mc_is_valid_path($path)) {
                require_once($path);
                mc_cloud_set_login($token);
            } else {
                mc_error('config-file-not-found', 'mc_cloud_load_by_url', 'Config file not found: ' . $path);
            }
            return $token;
        }
    }
    return false;
}

function mc_cloud_css_js() {
    require_once(MC_CLOUD_PATH . '/account/functions.php');
    cloud_css_js();
}

function mc_cloud_set_agent($email, $action = 'add', $extra = false) {
    if ($email) {
        require_once(MC_CLOUD_PATH . '/account/functions.php');
        $cloud = mc_cloud_account();
        membership_delete_cache($cloud['user_id']);
        if ($action == 'add') {
            return db_query('INSERT INTO agents(admin_id, email) VALUE ("' . $cloud['user_id'] . '", "' . $email . '")');
        }
        if ($action == 'update') {
            return db_query('UPDATE agents SET email = "' . $extra . '" WHERE email = "' . $email . '"');
        }
        if ($action == 'delete') {
            return db_query('DELETE FROM agents WHERE email = "' . $email . '"');
        }
    }
    return false;
}

function mc_cloud_set_login($token) {
    require_once(MC_CLOUD_PATH . '/account/functions.php');
    $cloud_user = db_get('SELECT id AS `user_id`, first_name, last_name, email, password, token, customer_id FROM users WHERE token = "' . $token . '" LIMIT 1');
    if ($cloud_user) {
        $cloud_user = mc_encryption(json_encode($cloud_user));
        $_POST['cloud'] = $cloud_user;
        return $cloud_user;
    }
    return false;
}

function mc_is_cloud() {
    return defined('MC_CLOUD');
}

function mc_cloud_membership_has_credits($source = false, $notification = true) {
    if (mc_is_cloud() && (!$source || !mc_ai_is_manual_sync($source))) {
        require_once(MC_CLOUD_PATH . '/account/functions.php');
        $user_id = db_escape(account()['user_id'], true);
        if ($user_id) {
            $user_info = db_get('SELECT credits, email FROM users WHERE id = ' . $user_id);
            $credits = mc_isset($user_info, 'credits', 0);
            $continue = $credits < 1 && super_get_user_data('auto_recharge', $user_id) ? membership_auto_recharge() : true;
            if ($continue === true && $credits <= 0 && $notification && cloud_suspended_notifications_counter($user_id, false, true) < 2) {
                $email = [super_get_setting('email_subject_no_credits'), super_get_setting('email_template_no_credits')];
                send_email(mc_isset($user_info, 'email'), $email[0], $email[1]);
                cloud_suspended_notifications_counter($user_id, true, true);
            }
            return $credits > 0;
        }
        return false;
    }
    return true;
}

function mc_cloud_membership_use_credits($spending_source, $source, $extra = false) {
    if (mc_is_cloud() && !mc_ai_is_manual_sync($source)) {
        require_once(MC_CLOUD_PATH . '/account/functions.php');
        membership_use_credits($spending_source, $extra);
    }
}

function mc_cloud_reset_login() {
    die('<script>document.cookie="mc-login=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/";document.cookie="mc-cloud=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/";document.location="' . CLOUD_URL . '/account?login";</script>');
}

?>