<?php

/*
 * ==========================================================
 * ADMINISTRATION PAGE
 * ==========================================================
 *
 * Administration page to manage the settings and reply to the users. © 2017-2025 masichat.com. All rights reserved.
 *
 */

header('X-Frame-Options: DENY');

global $MC_CONNECTION;
$connection_success = false;
$connection_check = false;
$minify = false;
$is_cloud = false;
$cloud_code = '';
$mc_url = '';
define('MC_PATH', getcwd());
if (file_exists('config.php')) {
    require('include/functions.php');
    $is_cloud = mc_is_cloud();
    if ($is_cloud) {
        if (isset($_GET['reset-login']) || isset($_GET['login_email'])) {
            mc_cloud_reset_login();
        }
        if (isset($_GET['magic'])) {
            require_once(MC_CLOUD_PATH . '/account/functions.php');
            account_magic_link_login($_GET['magic']);
        }
        mc_cloud_load();
        if (!defined('MC_DB_NAME') || !mc_is_agent()) {
            die('<script>document.location = "' . CLOUD_URL . '/account?login"</script>');
        }
        $cloud_code = mc_cloud_membership_validation();
    } else if (!defined('MC_URL')) {
        define('MC_URL', '');
    }
    $connection_check = mc_db_check_connection();
    $connection_success = $connection_check === true;
    $minify = false;
    $mc_url = '';
    if ($connection_success) {
        $mc_url = MC_URL . '/';
        $minify = mc_get_multi_setting('performance', 'performance-minify');
        mc_updates_validation();
    }
} else {
    define('MC_URL', '');
    $file = fopen('config.php', 'w');
    fwrite($file, '');
    fclose($file);
    require('include/functions.php');
}
require('include/components.php');

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no" />
    <meta name="theme-color" content="#566069" />
    <title>
        <?php echo !$is_cloud && $connection_success && mc_get_setting('admin-title') ? mc_get_setting('admin-title') : ($is_cloud ? MC_CLOUD_BRAND_NAME : 'Masi Chat') ?>
    </title>
    <script src="<?php echo $mc_url . 'js/min/jquery.min.js?v=' . MC_VERSION ?>"></script>
    <script src="<?php echo $mc_url . ((($is_cloud || $minify) && !mc_is_debug()) ? 'js/min/main.min.js?v=' : 'js/main.js?v=') . MC_VERSION ?>"></script>
    <script src="<?php echo $mc_url . ((($is_cloud || $minify) && !mc_is_debug()) ? 'js/min/admin.min.js?v=' : 'js/admin.js?v=') . MC_VERSION ?>"></script>
    <link rel="stylesheet" href="<?php echo $mc_url . 'css/admin.css?v=' . MC_VERSION ?>" media="all" />
    <link rel="stylesheet" href="<?php echo $mc_url . 'css/responsive-admin.css?v=' . MC_VERSION ?>" media="(max-width: 464px)" />
    <?php
    if ($connection_success && (($is_cloud && defined('MC_CLOUD_DEFAULT_RTL')) || mc_is_rtl())) {
        echo '<link rel="stylesheet" href="' . $mc_url . 'css/rtl-admin.css?v=' . MC_VERSION . '" />';
    }
    ?>
    <link rel="shortcut icon" type="image/png" href="<?php echo $is_cloud ? MC_CLOUD_BRAND_ICON_PNG : mc_get_setting('admin-icon', $mc_url . 'media/icon.png') ?>" />
    <link rel="apple-touch-icon" href="<?php echo $is_cloud ? MC_CLOUD_BRAND_ICON_PNG : mc_get_setting('admin-icon', $mc_url . 'resources/pwa/icons/icon-192x192.png') ?>" />
    <link rel="manifest" href="<?php echo $is_cloud ? MC_CLOUD_MANIFEST_URL : mc_get_setting('manifest-url', $mc_url . '/manifest.json') ?>" />
    <?php
    if ($is_cloud) {
        cloud_js_admin();
        echo $cloud_code;
    }
    if ($connection_success) {
        $GLOBALS['MC_FORCE_ADMIN'] = true;
        mc_js_global();
        $GLOBALS['MC_FORCE_ADMIN'] = false;
        mc_js_admin();
    }
    ?>
</head>
<body>
    <?php
    if (!$connection_success) {
        mc_installation_box($connection_check);
        die();
    }
    mc_component_admin();
    ?>
</body>
</html>
