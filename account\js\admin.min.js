!function(e){function t(t,i=!0){let n=a.find({google:"#google-client-id, #google-client-secret, #google-refresh-token","open-ai":"#open-ai-key","whatsapp-cloud":"#whatsapp-twilio-btn, #whatsapp-cloud-key",messenger:"#messenger-key, #messenger-path-btn"}[t]),s=a.find({"whatsapp-cloud":"#whatsapp-cloud-sync-btn, #whatsapp-cloud-reconnect-btn"}[t]);n.mcActive(i),s.mcActive(!i),i||n.each(function(){e(this).find("input").val("")})}function i(e){return MC_TRANSLATIONS&&e in MC_TRANSLATIONS?MC_TRANSLATIONS[e]:e}function n(t=!0){let i=t?{config_id:MC_CLOUD_WHATSAPP.configuration_id,response_type:"code",override_default_response_type:!0}:{config_id:MC_CLOUD_MESSENGER.configuration_id,response_type:"token"};FB.logout(),FB.login(function(i){if((i=!!i.authResponse&&i.authResponse)&&(t&&i.code||!t&&i.accessToken)){let n=a.find(t?"#whatsapp-cloud-sync-btn a":"#messenger-sync-btn a");if(MCAdmin.loading(n))return;!function(t,i={},n=!1){e.extend(i,{function:t}),e.ajax({method:"POST",url:"account/ajax.php",data:i}).done(e=>{n&&n(!1!==e&&JSON.parse(e))})}(t?"whatsapp-sync":"messenger-sync",{access_token:i.accessToken,code:i.code},i=>{if(n.mcLoading(!1),i&&(t&&i.access_token||!t&&Array.isArray(i)&&i.length)){let n=a.find(t?"#whatsapp-cloud-numbers":"#messenger-pages"),o=n.find(".repeater-item"),d=o.length,c=0;1!=d||o.eq(0).find("input").eq(0).val()||(d=0);let l=(t?i.phone_numbers.length:i.length)+d,p=n.find(t?"[data-id=whatsapp-cloud-numbers-phone-id]":"[data-id=messenger-page-id]").map(function(){return e(this).val()}).get();for(var s=d;s<l;s++){if(!p.includes(t?i.phone_numbers[c]:i[c].page_id)){s>=o.length&&(n.find(".mc-repeater-add").click(),o=n.find(".repeater-item"));let e=o.last();t?(e.find("[data-id=whatsapp-cloud-numbers-phone-id]").val(i.phone_numbers[c]),e.find("[data-id=whatsapp-cloud-numbers-token]").val(i.access_token),e.find("[data-id=whatsapp-cloud-numbers-account-id]").val(i.waba_id)):(e.find("[data-id=messenger-page-name]").val(i[c].name),e.find("[data-id=messenger-page-id]").val(i[c].page_id),e.find("[data-id=messenger-page-token]").val(i[c].access_token),e.find("[data-id=messenger-instagram-id]").val(i[c].instagram))}c++}MCAdmin.settings.save(),MCAdmin.infoBottom("Synchronization completed.")}else console.error(i)})}},i)}var a,s,o=!1,d={shopify_products_box:!1,shopify_products_box_ul:!1,removeAdminID:function(e){let t=e.indexOf(MC_ADMIN_SETTINGS.cloud.id);return-1!=t&&e.splice(t,1),e},creditsAlert:function(t,n){let s=e(t).closest("[id]").attr("id");return!!(MC_ADMIN_SETTINGS.credits<=0&&((s.includes("google")||s.includes("dialogflow"))&&"open-ai-spelling-correction-dialogflow"!=s&&"auto"==a.find("#google-sync-mode select").val()||s.includes("open-ai")&&"auto"==a.find("#open-ai-sync-mode select").val()))&&(MCAdmin.genericPanel("credits-panel","Credits required","<p>"+i("To use the {R} feature in automatic sync mode, credits are required. If you don't want to buy credits, switch to manual sync mode and use your own API key.").replace("{R}","<b>"+e(t).prev().html()+"</b>")+"</p>",[["Buy credits","plus"]]),MCAdmin.settings.input.reset(t),n.preventDefault(),!0)},creditsAlertQuota:function(){let e=a.find(".mc-docs").attr("href");MCAdmin.infoBottom(i("You have used all of your credits. Add more credits {R}.").replace("{R}",'<a href="account?tab=membership#credits">'+i("here")+"</a>")+(e?'<a href="'+e+'#cloud-credits" target="_blank" class="mc-icon-link"><i class="mc-icon-help"></i></a>':""),"error")},shopify:{conversationPanel:function(){let t="",n=MCF.activeUser().getExtra("shopify_id");this.panel||(this.panel=a.find(".mc-panel-shopify")),(n||MC_ADMIN_SETTINGS.shopify_shop&&MCF.activeUser().getExtra("current_url")&&MCF.activeUser().getExtra("current_url").value.includes(MC_ADMIN_SETTINGS.shopify_shop))&&!MCAdmin.loading(this.panel)?MCF.ajax({function:"shopify-get-conversation-details",shopify_id:!!n&&n.value},n=>{if(t=`<i class="mc-icon-refresh"></i><h3>Shopify</h3><div><div class="mc-split"><div><div class="mc-title">${i("Number of orders")}</div><span>${n.orders_count} ${i("orders")}</span></div><div><div class="mc-title">${i("Total spend")}</div><span>${n.total}</span></div></div><div class="mc-title">${i("Cart")}</div><div class="mc-list-items mc-list-links mc-shopify-cart">`,n.cart.items)for(a=0;a<n.cart.items.length;a++){let e=n.cart.items[a];t+=`<a href="${e.url}" target="_blank" data-id="${e.id}"><span>${e.quantity} x</span><span>${e.title}</span><span>${e.price}</span></a>`}if(t+=(n.cart.items&&n.cart.items.length?"":"<p>"+i("The cart is currently empty.")+"</p>")+"</div>",n.orders.length){t+=`<div class="mc-title">${i("Orders")}</div><div class="mc-list-items mc-shopify-orders mc-accordion">`;for(var a=0;a<n.orders.length;a++){let e=n.orders[a],o=e.id,d=e.items;t+=`<div data-id="${o}"><span><span>#${o}</span><span>${e.price}</span><span>${MCF.beautifyTime(e.date,!0)}</span><a href="${e.url}" target="_blank" class="mc-icon-next"></a></span><div>`;for(s=0;s<d.length;s++)t+=`<a data-product-id="${d[s].id}"><span>${d[s].quantity} x</span> <span>${d[s].name}</span></a>`;for(var s=0;s<2;s++){let n=0==s?"shipping":"billing";e[n+"_address"]&&(t+=`<div class="mc-title">${i((0==s?"Shipping":"Billing")+" address")}</div><div class="mc-multiline">${e[n+"_address"].replace(/\n/g,"<br>")}</div>`)}t+=`<span data-status="${e.status}">${e.status}</span></div></div>`}t+="</div>"}e(this.panel).html(t).mcLoading(!1),MCAdmin.collapse(this.panel,160)}):e(this.panel).html(t)}}};window.MCCloud=d,e(document).ready(function(){a=e(".mc-admin"),s=MC_URL.substring(0,MC_URL.substring(0,MC_URL.length-2).lastIndexOf("/")),"true"!=DISABLE_APPS||MC_CLOUD_MEMBERSHIP&&"0"!=MC_CLOUD_MEMBERSHIP&&"free"!=MC_CLOUD_MEMBERSHIP||(a.find("#tab-messenger,#tab-whatsapp,#tab-twitter,#tab-telegram,#tab-wechat,#tab-viber,#tab-line").hide(),a.find('[data-app="messenger"],[data-app="whatsapp"],[data-app="twitter"],[data-app="telegram"],[data-app="wechat"],[data-app="viber"],[data-app="line"],[data-app="zalo"]').addClass("mc-disabled")),MC_ADMIN_SETTINGS.shopify_shop&&(a.find(".mc-btn-saved-replies").after(`<div class="mc-btn-shopify" data-mc-tooltip="${i("Add Shopify product")}"></div>`),a.find(".mc-editor").append(`<div class="mc-popup mc-shopify-products"><div class="mc-header"><div class="mc-select"><p data-value="">${i("All")}</p><ul class="mc-scroll-area"></ul></div><div class="mc-search-btn"><i class="mc-icon mc-icon-search"></i><input type="text" placeholder="${i("Search ...")}" /></div></div><div class="mc-shopify-products-list mc-list-thumbs mc-scroll-area"><ul class="mc-loading"></ul></div><i class="mc-icon-close mc-popup-close"></i></div>`),d.shopify_products_box=a.find(".mc-shopify-products"),d.shopify_products_box_ul=d.shopify_products_box.find(" > div > ul")),e(document).on("MCSettingsLoaded",function(e,i){o=!0;let n=[["google","client-id"],["open-ai","key"],["whatsapp-cloud","key"]];for(var s=0;s<n.length;s++){let e=n[s][0];i[e]&&(i[e][0][e+"-sync-mode"]&&"manual"==i[e][0][e+"-sync-mode"][0]||i[e][0][e+"-"+n[s][1]][0])&&(t(e),a.find("#"+e+"-sync-mode select").val("manual"))}for(var d in MC_AUTO_SYNC)if(!MC_AUTO_SYNC[d]){let e=a.find("#"+d+"-sync-mode");e.find("select").val("manual"),e.addClass("mc-hide"),t(d,!0)}}),e(a).on("change","#google-sync-mode select, #open-ai-sync-mode select, #whatsapp-cloud-sync-mode select",function(){t(e(this).parent().attr("id").replace("-sync-mode",""),"manual"==e(this).val()),MCAdmin.infoBottom("Save changes to apply new sync mode.","info")}),e(a).on("click","#open-ai-active input, #open-ai-spelling-correction input, #open-ai-spelling-correction-dialogflow input, #open-ai-rewrite input, #open-ai-speech-recognition input, #mc-train-chatbot, #dialogflow-sync-btn .mc-btn, #dialogflow-active input, #google-multilingual input, #google-multilingual-translation input, #google-translation input, #google-language-detection input",function(e){if(d.creditsAlert(this,e))return!1}),e(a).on("change","#open-ai-mode select",function(){"assistant"==e(this).val()&&(a.find("#open-ai-sync-mode select").val("manual"),a.find("#open-ai-key").mcActive(!0))}),e(a).on("change","#open-ai-sync-mode select",function(){if("auto"==e(this).val()){let e=a.find("#open-ai-mode select");"assistant"==e.val()&&e.val(""),a.find("#open-ai-assistant-id").mcActive(!1)}}),MC_ADMIN_SETTINGS.credits_required&&d.creditsAlertQuota();let c=!1;e(a).on("click","#whatsapp-cloud-sync-btn .mc-btn, #whatsapp-cloud-reconnect-btn .mc-btn, #messenger-sync-btn a",function(t){let i=e(this).parent().attr("id"),a="messenger-sync-btn"!=i,s="whatsapp-cloud-reconnect-btn"==i&&{scope:"whatsapp_business_messaging, whatsapp_business_management, business_management"};return c?s?FB.login(()=>{},s):n(a):(window.fbAsyncInit=function(){FB.init(a?{appId:MC_CLOUD_WHATSAPP.app_id,autoLogAppEvents:!0,xfbml:!0,version:"v18.0"}:{appId:MC_CLOUD_MESSENGER.app_id,cookie:!0,xfbml:!0,version:"v18.0"})},e.getScript("https://connect.facebook.net/en_US/sdk.js",()=>{c=!0,s?FB.login(()=>{},s):n(a)})),t.preventDefault(),!1});let l=document.location.href;if((l.includes("masichat.com")||l.includes("masi-chat"))&&l.includes("welcome")){a.find("#mc-settings").click();let t=[["Need help?","Not sure what to do? Contact us for help. Log in with your existing email and password. We reply in a few hours.",[],"","Contact us","https://masichat.com/contact"],["Notifications","Activate Push and Email notifications to receive alerts for incoming messages. On iPhone, the mobile app is required.",["t-qzDPG88Xg","enb291Aai5Q"],"#notifications","Activate"],["Chatbot","Activate the OpenAI chatbot and Human Takeover to transfer the chat to an agent when needed.",["0p2YWQtsglg"],"#optimal-configuration-ai","Activate"],["Try out the chat","Try out the chat and send a test message to test notifications and the chatbot functionalities.",["mxjRevd_8bw"],"#widget-hidden","Try now","https://app.masichat.com/"+MC_ADMIN_SETTINGS.cloud.chat_id],["Mobile app","The admin area is a PWA that can be installed on iPhones, Android, and mobile devices.",["IhoAlXFywFY"],"#pwa","Read more","https://masichat.com/knowledge-base/#pwa"]],i="<div>",n=["push-notifications-active","open-ai-active"];for(var p=0;p<t.length;p++){let e=`id="onboarding-${t[p][3].replace("#","")}"`,n="";for(var r=0;r<t[p][2].length;r++)n+=`<a href="https://www.youtube.com/watch?v=${t[p][2][r]}" target="_blank"><img src="account/media/play-video.svg"></a>`;i+=`<div class="mc-setting"><div><h2>${t[p][0]} ${n}<a href="https://masichat.com/docs/${t[p][3]}" target="_blank"><i class="mc-icon-help"></i></a></h2><p>${t[p][1]}</p></div><div>${t[p][5]?`<a ${e} href="${t[p][5]}" target="_blank" class="mc-btn">${t[p][4]}</a>`:`<div ${e} class="mc-btn">${t[p][4]}</div>`}</div></div>`}setTimeout(()=>{MCAdmin.genericPanel("onboarding",`Welcome ${a.find("> .mc-header > .mc-admin-nav-right .mc-account .mc-name").html()} 👋`,"<p>Masi Chat is a powerful tool with many options. Let's start by activating the basic functionalities below. Don't hesitate to reach out to us if you have any questions.</p>"+i+"</div>",[],"",!0);for(var t=0;t<n.length;t++)a.find("#"+n[t]+" input").prop("checked")&&e(".mc-onboarding-box [id].mc-btn").eq(t).mcActive(!0);a.find(".mc-admin-list .mc-scroll-area > ul li").length&&e("#onboarding-widget-hidden").mcActive(!0)},1e3)}e(a).on("click","#onboarding-notifications",function(){if(e(this).mcActive()||MCAdmin.loading(this))return;let t=["notify-agent-email","notify-user-email","push-notifications-active"];for(var i=0;i<t.length;i++)a.find("#"+t[i]+" input").prop("checked",!0);"undefined"!=typeof OneSignal?OneSignal.Slidedown.promptPush({force:!0}):MCF.serviceWorker.initPushNotifications(),e(document).on("MCPushNotificationSubscription",(t,i)=>{e(this).mcLoading(!1),i.optedIn&&e(this).mcActive(!0)}),MCAdmin.settings.save()}),e(a).on("click","#onboarding-optimal-configuration-ai",function(){a.find("#open-ai-active input").prop("checked",!0),a.find("#dialogflow-human-takeover-active input").prop("checked",!0),a.find("#dialogflow-human-takeover-message textarea").val("I'm a chatbot. Do you want to get in touch with one of our agents?"),a.find("#dialogflow-human-takeover-message-confirmation textarea").val("Alright! We will get in touch soon!"),a.find("#dialogflow-human-takeover-confirm input").val("Yes"),a.find("#dialogflow-human-takeover-cancel input").val("Cancel"),MCAdmin.settings.save(),e(this).mcActive(!0)}),e(a).on("click","#onboarding-widget-hidden",function(){e(this).mcActive()||MCAdmin.loading(this)||e(document).on("MCAdminNewConversation",()=>{e(this).mcLoading(!1),e(this).mcActive(!0)})}),e(a).on("click","#onboarding-pwa",function(){e(this).mcActive(!0)}),e(a).on("click","[data-product-id]:not([href])",function(){MCF.ajax({function:"shopify-get-product-link",product_id:e(this).attr("data-product-id")},t=>{e(this).attr("href",t).attr("target","_blank"),window.open(t,"_blank")})}),e(a).on("click",".mc-panel-shopify > i",function(){d.shopify.conversationPanel()}),e(a).on("click",".mc-btn-shopify",function(){(d.shopify_products_box_ul.mcLoading()||MC_ADMIN_SETTINGS.languages&&MCF.activeUser()&&MC_ADMIN_SETTINGS.languages.includes(activeUser().language)&&MCF.activeUser().language!=MCAdmin.apps.itemsPanel.panel_language)&&MCAdmin.apps.itemsPanel.populate("shopify"),d.shopify_products_box.find(".mc-search-btn").mcActive(!0).find("input").get(0).focus(),d.shopify_products_box.mcTogglePopup(this)}),e(d.shopify_products_box).find(".mc-shopify-products-list").on("scroll",function(){(function(t,i=!1,n=0){if(i)return e(t).scrollTop()+e(t).innerHeight()>=e(t)[0].scrollHeight-1;e(t).scrollTop(e(t)[0].scrollHeight-n)})(this,!0)&&MCAdmin.apps.itemsPanel.pagination(this,"shopify")}),e(d.shopify_products_box).on("click",".mc-select li",function(){MCAdmin.apps.itemsPanel.filter(this,"shopify")}),e(d.shopify_products_box).on("input",".mc-search-btn input",function(){MCAdmin.apps.itemsPanel.search(this,"shopify")}),e(d.shopify_products_box).on("click",".mc-search-btn i",function(){MCF.searchClear(this,()=>{MCAdmin.apps.itemsPanel.search(e(this).next(),"shopify")})}),e(d.shopify_products_box).on("click",".mc-shopify-products-list li",function(){MCChat.insertText(`{shopify product_id="${e(this).data("id")}"}`),MCF.deactivateAll(),a.removeClass("mc-popup-active")}),e(a).on("click",'.mc-admin-nav-right [data-value="account"], #mc-buy-credits',function(){document.location=s+"/account?tab=membership"+("mc-buy-credits"==e(this).attr("id")?"#credits":"")}),e(a).on("click",".mc-btn-app-disable",function(){MCAdmin.loading(this)||MCF.ajax({function:"app-disable",app_name:e(this).closest("[data-app]").attr("data-app")},e=>{location.reload()})})})}(jQuery);