{"{product_name} has no {product_attribute_name} variants.": "{product_name}–ს არ აქვს {product_attribute_name} ვარიანტები.", "360dialog settings": "360 დიალოგის პარამეტრები", "360dialog template": "360 დიალოგის შაბლონი", "Abandoned cart notification": "მიტოვებული კალათის შეტყობინება", "Abandoned cart notification - Admin email": "მიტოვებული კალათის შეტყობინება - ადმინისტრატორის ელფოსტა", "Abandoned cart notification - First email": "მიტოვებული კალათის შეტყობინება - პირველი ელფოსტა", "Abandoned cart notification - Second email": "მიტოვებული კალათის შეტყობინება - მეორე ელფოსტა", "Accept button text": "ღილაკის ტექსტის მიღება", "Account SID": "ანგარიშის SID", "Activate the Slack integration.": "გაააქტიურეთ Slack ინტეგრაცია.", "Activate the Zendesk integration": "გააქტიურეთ Zendesk ინტეგრაცია", "Activate this option if you don't want to translate the settings area.": "Გააქტიურეთ ეს პარამეტრი, თუ არ გსურთ პარამეტრების ზონის თარგმნა.", "Active": "აქტიური", "Active - admin": "Აქტიური - ადმინი", "Active eCommerce CMS URL. Ex. https://shop.com/": "Active eCommerce CMS URL. მაგ. https://shop.com/", "Active eCommerce URL": "Active eCommerce URL", "Active for agents": "აქტიურია აგენტებისთვის", "Active for users": "აქტიურია მომხმარებლებისთვის", "Active webhooks": "აქტიური ვებჰუკები", "Add a delay (ms) to the bot's responses. Default is 2000.": "Დაამატეთ დაყოვნება (ms) ბოტის პასუხებს. ნაგულისხმევი არის 2000.", "Add and manage additional support departments.": "დაამატეთ და მართეთ დამატებითი მხარდაჭერის განყოფილებები.", "Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \\n to do a line break.": "Დაამატეთ და მართეთ შენახული პასუხები, რომლებიც შეიძლება გამოიყენონ აგენტებმა ჩატის რედაქტორში. შენახული პასუხების დაბეჭდვა შესაძლებელია # აკრეფით, რასაც მოჰყვება პასუხის სახელი პლუს სივრცე. გამოიყენეთ \\n ხაზის გაწყვეტის გასაკეთებლად.", "Add and manage tags.": "Ტეგების დამატება და მართვა.", "Add comma separated WordPress user roles. The Masi Chat administration area will be available for new roles, in addition to the default one: editor, administrator, author.": "დაამატეთ მძიმით გამოყოფილი WordPress მომხმარებლის როლები. დამხმარე საბჭოს ადმინისტრაციის ზონა ხელმისაწვდომი იქნება ახალი როლებისთვის, ნაგულისხმევის გარდა: რედაქტორი, ადმინისტრატორი, ავტორი.", "Add custom fields to the new ticket form.": "დაამატეთ მორგებული ველები ბილეთის ახალ ფორმას.", "Add custom fields to the user profile details.": "დაამატეთ მორგებული ველები მომხმარებლის პროფილის დეტალებს.", "Add Intents": "დაამატეთ Intents", "Add Intents to saved replies": "დაამატეთ Intents შენახულ პასუხებს", "Add WhatsApp phone number details here.": "Დაამატეთ WhatsApp ტელეფონის ნომრის დეტალები აქ.", "Adjust the chat button position. Values are in px.": "დაარეგულირეთ ჩატის ღილაკის პოზიცია. მნიშვნელობები არის px-ში.", "Admin icon": "ადმინისტრატორის ხატულა", "Admin IDs": "Ადმინისტრატორის ID", "Admin login logo": "ადმინისტრატორის შესვლის ლოგო", "Admin login message": "ადმინისტრატორის შესვლის შეტყობინება", "Admin notifications": "ადმინისტრატორის შეტყობინებები", "Admin title": "ადმინისტრატორის სათაური", "Agent area": "აგენტის ტერიტორია", "Agent details": "აგენტის დეტალები", "Agent email notifications": "აგენტის ელფოსტის შეტყობინებები", "Agent ID": "Აგენტის ID", "Agent linking": "აგენტის დაკავშირება", "Agent message template": "აგენტის შეტყობინების შაბლონი", "Agent notification email": "აგენტის შეტყობინების ელფოსტა", "Agent privileges": "Აგენტის პრივილეგიები", "Agents": "აგენტები", "Agents and admins tab": "Აგენტების და ადმინისტრატორების ჩანართი", "Agents menu": "აგენტების მენიუ", "Agents only": "მხოლოდ აგენტები", "All": "ყველა", "All channels": "ყველა არხი", "All messages": "ყველა შეტყობინება", "All questions": "Ყველა კითხვა", "Allow only extended licenses": "Მხოლოდ გაფართოებული ლიცენზიების დაშვება", "Allow only one conversation": "დაუშვით მხოლოდ ერთი საუბარი", "Allow only one conversation per user.": "დაუშვით მხოლოდ ერთი საუბარი თითო მომხმარებლისთვის.", "Allow the chatbot to reply to the user's emails if the answer is known and email piping is active.": "ნება მიეცით ჩატბოტს უპასუხოს მომხმარებლის ელფოსტას, თუ პასუხი ცნობილია და ელექტრონული ფოსტის მიწოდება აქტიურია.", "Allow the chatbot to reply to the user's text messages if the answer is known.": "ნება მიეცით ჩატბოტს უპასუხოს მომხმარებლის ტექსტურ შეტყობინებებს, თუ პასუხი ცნობილია.", "Allow the user to archive a conversation and hide archived conversations.": "მიეცით საშუალება მომხმარებელს დაარქივოს საუბარი და დამალოს დაარქივებული საუბრები.", "Allow users to contact you via their favorite messaging apps.": "Მიეცით საშუალება მომხმარებლებს დაგიკავშირდეთ მათი საყვარელი შეტყობინებების აპების საშუალებით.", "Allow users to select a product on ticket creation.": "მიეცით საშუალება მომხმარებლებს აირჩიონ პროდუქტი ბილეთის შექმნისას.", "Always all messages": "ყოველთვის ყველა შეტყობინება", "Always incoming messages only": "ყოველთვის მხოლოდ შემომავალი შეტყობინებები", "Always sort conversations by date in the admin area.": "Ყოველთვის დაალაგეთ საუბრები თარიღის მიხედვით ადმინისტრატორის ზონაში.", "API key": "API გასაღები", "Append the registration user details to the success message.": "დაურთეთ რეგისტრაციის მომხმარებლის დეტალები წარმატების შეტყობინებას.", "Apply a custom background image for the header area.": "გამოიყენეთ მორგებული ფონის სურათი სათაურის ზონისთვის.", "Apply changes": "Ცვლილებების გამოყენება", "Apply to": "მიმართეთ", "Archive all user channels in the Slack app. This operation may take a long time to complete. Important: All of your slack channels will be archived.": "დაარქივეთ ყველა მომხმარებლის არხი Slack აპში. ამ ოპერაციის დასრულებას შეიძლება დიდი დრო დასჭირდეს. მნიშვნელოვანია: ყველა თქვენი დაუღალავი არხი დაარქივდება.", "Archive automatically the conversations marked as read every 24h.": "წაკითხულად მონიშნული საუბრების დაარქივება ავტომატურად ყოველ 24 საათში.", "Archive channels": "არხების დაარქივება", "Archive channels now": "დაარქივეთ არხები ახლავე", "Articles": "სტატიები", "Articles area": "Სტატიების არეალი", "Articles button link": "Სტატიების ღილაკის ბმული", "Articles page URL": "Სტატიების გვერდის URL", "Artificial Intelligence": "Ხელოვნური ინტელექტი", "Assign a department to all conversations started from Google Business Messages. Enter the department ID.": "Მიანიჭეთ განყოფილება Google Business Messages-დან დაწყებულ ყველა საუბარს. შეიყვანეთ განყოფილების ID.", "Assign a department to all conversations started from Twitter. Enter the department ID.": "Დანიშნეთ განყოფილება Twitter-დან დაწყებულ ყველა საუბარში. შეიყვანეთ განყოფილების ID.", "Assign a department to all conversations started from Viber. Enter the department ID.": "Დანიშნეთ განყოფილება Viber-დან დაწყებულ ყველა საუბარზე. შეიყვანეთ განყოფილების ID.", "Assign a department to all conversations started from WeChat. Enter the department ID.": "Მიანიჭეთ განყოფილება WeChat-დან დაწყებულ ყველა საუბარს. შეიყვანეთ განყოფილების ID.", "Assign different departments to conversations started from different Google Business Messages locations. This setting overrides the default department.": "Მიამაგრეთ სხვადასხვა განყოფილებები Google Business Messages-ის სხვადასხვა მდებარეობიდან დაწყებულ საუბრებში. ეს პარამეტრი არღვევს ნაგულისხმევ განყოფილებას.", "Assistant": "Ასისტენტი", "Assistant ID": "Ასისტენტის ID", "Attachments list": "Დანართების სია", "Audio file URL - admin": "Აუდიო ფაილის URL - admin", "Automatic": "Ავტომატური", "Automatic human takeover": "ადამიანის ავტომატური აღება", "Automatic translation": "ავტომატური თარგმანი", "Automatic updates": "Ავტომატური განახლებები", "Automatically archive conversations": "საუბრების ავტომატურად დაარქივება", "Automatically assigns a department based on the user's active plans. Insert -1 as plan ID for users without any plan.": "ავტომატურად ანიჭებს განყოფილებას მომხმარებლის აქტიური გეგმების საფუძველზე. ჩადეთ -1 როგორც გეგმის ID მომხმარებლებისთვის ყოველგვარი გეგმის გარეშე.", "Automatically check and install new updates. A valid Envato Purchase Code and valid apps's license keys are required.": "ავტომატურად შეამოწმეთ და დააინსტალირეთ ახალი განახლებები. საჭიროა მოქმედი Envato შესყიდვის კოდი და მოქმედი აპების ლიცენზიის გასაღებები.", "Automatically collapse the conversation details panel, and other panels, of the admin area.": "Საუბრის დეტალების პანელის და ადმინისტრატორის ზონის სხვა პანელების ავტომატურად ჩაკეცვა.", "Automatically create a department for each website and route the conversations of each website to the right department. This setting requires a WordPress Multisite installation.": "ავტომატურად შექმენით განყოფილება თითოეული ვებსაიტისთვის და გადაიტანეთ თითოეული ვებსაიტის საუბრები სწორ განყოფილებაში. ეს პარამეტრი მოითხოვს WordPress Multisite ინსტალაციას.", "Automatically hide the conversation details panel.": "Საუბრის დეტალების პანელის ავტომატურად დამალვა.", "Automatically send cart reminders to customers with products in their carts. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "ავტომატურად გაუგზავნეთ კალათის შეხსენებები კლიენტებს პროდუქტებით მათ კალათებში. შეგიძლიათ გამოიყენოთ შემდეგი შერწყმის ველები და სხვა: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.", "Automatically sync Zendesk customers with {R}, view Zendesk tickets, or create new ones without leaving {R}.": "Ავტომატური სინქრონიზაცია Zendesk-ის კლიენტებთან {R}-თან, იხილეთ Zendesk-ის ბილეთები ან შექმენით ახლები {R} გაუსვლელად.", "Automatically synchronize products, categories, tags, and more with Dialogflow, and enable the bot to answer autonomously to questions related to your shop.": "ავტომატურად მოახდინე პროდუქტების, კატეგორიების, ტეგების და სხვათა სინქრონიზაცია Dialogflow-თან და მიეცით საშუალება ბოტს, ავტონომიურად უპასუხოს თქვენს მაღაზიასთან დაკავშირებულ კითხვებს.", "Automatically translate admin area": "ადმინისტრატორის არეალის ავტომატური თარგმნა", "Automatically translate the admin area to match the agent profile language or browser language.": "ავტომატურად თარგმნეთ ადმინისტრატორის ზონა აგენტის პროფილის ენის ან ბრაუზერის ენის შესატყვისად.", "Avatar image": "Ავატარის სურათი", "Away mode": "Მოშორების რეჟიმი", "Before initiating the chat, the user must accept a privacy message in order to gain access.": "Ჩეთის დაწყებამდე მომხმარებელმა უნდა მიიღოს კონფიდენციალურობის შეტყობინება, რათა მიიღოს წვდომა.", "Birthday": "დაბადების დღე", "Body variables": "Სხეულის ცვლადები", "Bot name": "ბოტის სახელი", "Bot profile image": "ბოტის პროფილის სურათი", "Bot response delay": "ბოტის პასუხის დაგვიანება", "Bottom": "ქვედა", "Brand": "ბრენდი", "Built-in chat button icons": "ჩატის ღილაკების ჩაშენებული ხატები", "Business Account ID": "Ბიზნეს ანგარიშის ID", "Button action": "ღილაკის მოქმედება", "Button name": "ღილაკის სახელი", "Button text": "ღილაკის ტექსტი", "Button variables": "Ღილაკის ცვლადები", "Cancel button text": "გაუქმების ღილაკის ტექსტი", "Cart": "კალათა", "Cart follow up message": "კალათის შემდგომი შეტყობინება", "Catalogue details": "Კატალოგის დეტალები", "Catalogue ID": "Კატალოგის ID", "Change the chat button image with a custom one.": "შეცვალეთ ჩატის ღილაკის სურათი მორგებულით.", "Change the default field names.": "შეცვალეთ ნაგულისხმევი ველების სახელები.", "Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent.": "შეცვალეთ შეტყობინების ტექსტი ჩეთის ვიჯეტის სათაურის არეში. ეს ტექსტი შეიცვლება აგენტის სათაურით, როგორც კი პირველი პასუხი გაიგზავნება.", "Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent.": "შეცვალეთ სათაურის ტექსტი ჩატის ვიჯეტის სათაურის არეში. ეს ტექსტი შეიცვლება აგენტის სახელით პირველი პასუხის გაგზავნის შემდეგ.", "Channel ID": "არხის ID", "Channels": "არხები", "Channels filter": "Არხების ფილტრი", "Chat": "Სასაუბრო", "Chat and admin": "Ჩატი და ადმინი", "Chat background": "ჩეთის ფონი", "Chat button icon": "ჩატის ღილაკის ხატულა", "Chat button offset": "ჩატის ღილაკის ოფსეტური", "Chat message": "ჩატის შეტყობინება", "Chat only": "მხოლოდ ჩატი", "Chat position": "ჩატის პოზიცია", "Chatbot": "Ჩატბოტი", "Chatbot mode": "Ჩატბოტის რეჟიმი", "Check Requirements": "შეამოწმეთ მოთხოვნები", "Check the server configurations and make sure it has all the requirements.": "შეამოწმეთ სერვერის კონფიგურაცია და დარწმუნდით, რომ მას აქვს ყველა მოთხოვნა.", "Checkout": "გადახდა", "Choose a background texture for the chat header and conversation area.": "აირჩიეთ ფონის ტექსტურა ჩეთის სათაურისა და საუბრის ზონისთვის.", "Choose where to display the chat. Enter the values separated by commas.": "Აირჩიეთ ჩატის ჩვენების ადგილი. შეიყვანეთ მძიმეებით გამოყოფილი მნიშვნელობები.", "Choose which fields to disable from the tickets area.": "აირჩიეთ რომელი ველები უნდა გამორთოთ ბილეთების ზონიდან.", "Choose which fields to include in the new ticket form.": "აირჩიეთ რომელი ველები შეიტანოთ ბილეთის ახალ ფორმაში.", "Choose which fields to include in the registration form. The name field is included by default.": "Აირჩიეთ, რომელი ველები შეიტანოთ სარეგისტრაციო ფორმაში. სახელის ველი ჩართულია ნაგულისხმევად.", "Choose which user system the front-end chat will use to register and log in users.": "აირჩიეთ მომხმარებლის რომელ სისტემას გამოიყენებს წინა ჩატი მომხმარებლების რეგისტრაციისა და შესვლისთვის.", "City": "ქალაქი", "Clear flows": "Წმინდა ნაკადები", "Click the button to start the Dialogflow synchronization.": "დააწკაპუნეთ ღილაკზე Dialogflow სინქრონიზაციის დასაწყებად.", "Click the button to start the Slack synchronization. Localhost cannot and does not receive messages. Log in with another account or as a visitor to perform your tests.": "დააჭირეთ ღილაკს Slack სინქრონიზაციის დასაწყებად. ლოკალჰოსტი არ შეუძლია და არ იღებს შეტყობინებებს. შედით სხვა ანგარიშით ან როგორც სტუმარი თქვენი ტესტების შესასრულებლად.", "Client email": "Კლიენტის ელ.წერილი", "Client ID": "კლიენტის ID", "Client token": "Კლიენტის ჟეტონი", "Close chat": "ჩატის დახურვა", "Close message": "შეტყობინების დახურვა", "Cloud API numbers": "Cloud API ნომრები", "Cloud API settings": "Cloud API პარამეტრები", "Cloud API template fallback": "Cloud API შაბლონის სარეზერვო", "Code": "Კოდი", "Collapse panels": "პანელების ჩაკეცვა", "Color": "ფერი", "Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.": "დაუკავშირდით თქვენს მომხმარებლებს პირდაპირ Slack-დან. გაგზავნეთ და მიიღეთ შეტყობინებები და დანართები, გამოიყენეთ emojis და მრავალი სხვა.", "Company": "კომპანია", "Concurrent chats": "კონკურენტი კატები", "Configuration URL": "კონფიგურაციის URL", "Confirm button text": "დაადასტურეთ ღილაკის ტექსტი", "Confirmation message": "დამადასტურებელი შეტყობინება", "Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.": "დააკავშირეთ ჭკვიანი ჩეთბოტები და ავტომატიზირებული საუბრები მსოფლიოში ხელოვნური ინტელექტის ერთ-ერთი ყველაზე მოწინავე ფორმის გამოყენებით.", "Connect stores to agents.": "Დააკავშირეთ მაღაზიები აგენტებთან.", "Connect your Telegram bot to {R} to read and reply to all messages sent to your Telegram bot directly in {R}.": "Დაუკავშირეთ თქვენი Telegram ბოტი {R}-ს, რათა წაიკითხოთ და უპასუხოთ თქვენს Telegram-ის ბოტზე გაგზავნილ ყველა შეტყობინებას პირდაპირ {R}-ში.", "Connect your Viber bot to {R} to read and reply to all messages sent to your Viber bot directly in {R}.": "Შეაერთეთ თქვენი Viber ბოტი {R}-ს, რათა წაიკითხოთ და უპასუხოთ თქვენს Viber bot-ზე გაგზავნილ ყველა შეტყობინებას პირდაპირ {R}-ში.", "Connect your Zalo Official Account to {R} to read and reply to all messages sent to your Zalo Official Account directly in {R}.": "Დაუკავშირეთ თქვენი Zalo ოფიციალური ანგარიში {R}-ს, რათა წაიკითხოთ და უპასუხოთ ყველა შეტყობინებას, რომელიც გაგზავნილია თქვენს Zalo-ს ოფიციალურ ანგარიშზე პირდაპირ {R}-ში.", "Content": "შინაარსი", "Content template SID": "Შინაარსის შაბლონი SID", "Conversation profile": "საუბრის პროფილი", "Conversations data": "საუბრების მონაცემები", "Convert all emails": "ყველა ელფოსტის კონვერტაცია", "Cookie domain": "ქუქი დომენი", "Country": "ქვეყანა", "Coupon discount (%)": "კუპონის ფასდაკლება (%)", "Coupon expiration (days)": "კუპონის ვადის გასვლა (დღეებში)", "Coupon expiration (seconds)": "კუპონის ვადის გასვლა (წამში)", "Create a WordPress user upon registration.": "შექმენით WordPress მომხმარებელი რეგისტრაციისას.", "Create Intents now": "შექმენით Intents ახლავე", "Currency symbol": "ვალუტის სიმბოლო", "Custom CSS": "მორგებული CSS", "Custom fields": "მორგებული ველები", "Custom JS": "მორგებული JS", "Custom model ID": "Მორგებული მოდელის ID", "Custom parameters": "Მორგებული პარამეტრები", "Customize the link for the 'All articles' button.": "„ყველა სტატიის“ ღილაკის ბმულის მორგება.", "Dashboard display": "დაფის ჩვენება", "Dashboard title": "დაფის სათაური", "Database details": "მონაცემთა ბაზის დეტალები", "Database host": "მონაცემთა ბაზის მასპინძელი", "Database name": "მონაცემთა ბაზის სახელი", "Database password": "მონაცემთა ბაზის პაროლი", "Database prefix": "მონაცემთა ბაზის პრეფიქსი", "Database user": "მონაცემთა ბაზის მომხმარებელი", "Decline button text": "ღილაკის უარყოფის ტექსტი", "Declined message": "უარყო შეტყობინება", "Default": "Ნაგულისხმევი", "Default body text": "Ძირითადი ტექსტის ნაგულისხმევი ტექსტი", "Default conversation name": "საუბრის ნაგულისხმევი სახელი", "Default department": "ნაგულისხმევი განყოფილება", "Default department ID": "Ნაგულისხმევი დეპარტამენტის ID", "Default form": "ნაგულისხმევი ფორმა", "Default header text": "Სათაურის ნაგულისხმევი ტექსტი", "Delay (ms)": "დაყოვნება (ms)", "Delete all leads and all messages and conversations linked to them.": "წაშალეთ ყველა წამყვანი და მათთან დაკავშირებული ყველა შეტყობინება და საუბარი.", "Delete conversation": "Წაშალეთ საუბარი", "Delete leads": "წაშალეთ ლიდერები", "Delete message": "Შეტყობინების წაშლა", "Delete the built-in flows.": "Წაშალეთ ჩაშენებული ნაკადები.", "Delimiter": "გამსაზღვრელი", "Department": "დეპარტამენტი", "Department ID": "დეპარტამენტის ID", "Departments": "დეპარტამენტები", "Departments settings": "დეპარტამენტების პარამეტრები", "Desktop notifications": "დესკტოპის შეტყობინებები", "Dialogflow - Department linking": "Dialogflow - დეპარტამენტის დაკავშირება", "Dialogflow chatbot": "Dialogflow ჩატბოტი", "Dialogflow edition": "Dialogflow გამოცემა", "Dialogflow Intent detection confidence": "Dialogflow განზრახვის ამოცნობის ნდობა", "Dialogflow location": "Dialogflow მდებარეობა", "Dialogflow spelling correction": "Dialogflow მართლწერის კორექტირება", "Dialogflow welcome Intent": "Dialogflow მისასალმებელი განზრახვა", "Disable agents check": "გამორთეთ აგენტების შემოწმება", "Disable and hide the chat widget if all agents are offline.": "გამორთეთ და დამალეთ ჩეთის ვიჯეტი, თუ ყველა აგენტი ხაზგარეშეა.", "Disable and hide the chat widget outside of scheduled office hours.": "გამორთეთ და დამალეთ ჩეთის ვიჯეტი დაგეგმილი სამუშაო საათების მიღმა.", "Disable any features that you don't need.": "Გამორთეთ ნებისმიერი ფუნქცია, რომელიც არ გჭირდებათ.", "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it.": "გამორთეთ ჩეთის ვიჯეტის ავტომატური ინიციალიზაცია. როდესაც ეს პარამეტრი აქტიურია, თქვენ უნდა მოაწყოთ ჩატის ვიჯეტი თქვენს მიერ დაწერილი JavaScript API კოდით. თუ ჩატი არ გამოჩნდება და ეს პარამეტრი ჩართულია, გამორთეთ იგი.", "Disable auto-initialization of the tickets area. When this setting is active you must initialize the tickets area with a custom JavaScript API code written by you. If the tickets area doesn't appear and this setting is enabled, disable it.": "გამორთეთ ბილეთების არეალის ავტომატური ინიციალიზაცია. როდესაც ეს პარამეტრი აქტიურია, თქვენ უნდა მოაწყოთ ბილეთების არეალი თქვენს მიერ დაწერილი JavaScript API კოდით. თუ ბილეთების ზონა არ ჩანს და ეს პარამეტრი ჩართულია, გამორთეთ იგი.", "Disable chatbot": "Გამორთეთ ჩატბოტი", "Disable cron job": "გამორთეთ cron job", "Disable dashboard": "დაფის გათიშვა", "Disable during office hours": "გამორთეთ სამუშაო საათებში", "Disable features": "ფუნქციების გამორთვა", "Disable features you don't use and improve the chat performance.": "გამორთეთ ფუნქციები, რომლებსაც არ იყენებთ და გააუმჯობესეთ ჩეთის შესრულება.", "Disable file uploading capabilities within the chat.": "გამორთეთ ფაილების ატვირთვის შესაძლებლობები ჩატში.", "Disable for messaging channels": "Გამორთეთ შეტყობინებების არხებისთვის", "Disable for the tickets area": "გამორთეთ ბილეთების ზონისთვის", "Disable invitation": "გამორთეთ მოწვევა", "Disable online status check": "Გამორთეთ ონლაინ სტატუსის შემოწმება", "Disable outside of office hours": "გამორთეთ სამუშაო საათების მიღმა", "Disable password": "პაროლის გამორთვა", "Disable registration during office hours": "გამორთეთ რეგისტრაცია სამუშაო საათებში", "Disable registration if agents online": "გამორთეთ რეგისტრაცია, თუ აგენტები ონლაინ არიან", "Disable the automatic invitation of agents to the channels.": "გამორთეთ არხებზე აგენტების ავტომატური მოწვევა.", "Disable the channels filter.": "Გამორთეთ არხების ფილტრი.", "Disable the chatbot for the tickets area.": "გამორთეთ ჩატბოტი ბილეთების ზონისთვის.", "Disable the chatbot for this channel only.": "Გამორთეთ ჩატბოტი მხოლოდ ამ არხისთვის.", "Disable the dashboard, and allow only one conversation per user.": "გამორთეთ საინფორმაციო დაფა და დაუშვით მხოლოდ ერთი საუბარი თითო მომხმარებლისთვის.", "Disable the login and remove the password field from the registration form.": "გამორთეთ შესვლა და ამოიღეთ პაროლის ველი რეგისტრაციის ფორმიდან.", "Disable uploads": "გამორთეთ ატვირთვები", "Disable voice message capabilities within the chat.": "Გამორთეთ ხმოვანი შეტყობინების შესაძლებლობები ჩატის ფარგლებში.", "Disable voice messages": "Გამორთეთ ხმოვანი შეტყობინებები", "Disabled": "გამორთულია", "Display a brand image in the header area. This only applies for the 'brand' header type.": "ბრენდის სურათის ჩვენება სათაურის ზონაში. ეს ეხება მხოლოდ „ბრენდის“ სათაურის ტიპს.", "Display categories": "Კატეგორიების ჩვენება", "Display images": "სურათების ჩვენება", "Display in conversation list": "Საუბრის სიაში ჩვენება", "Display in dashboard": "ჩვენება დაფაზე", "Display online agents only": "Აჩვენე მხოლოდ ონლაინ აგენტები", "Display the articles section in the right area.": "აჩვენეთ სტატიების განყოფილება სწორ ზონაში.", "Display the dashboard instead of the chat area on initialization.": "ინიციალიზაციისას ჩეთის არეალის ნაცვლად აჩვენეთ დაფა.", "Display the feedback form to rate the conversation when it is archived.": "Აჩვენეთ გამოხმაურების ფორმა, რათა შეაფასოთ საუბარი, როდესაც ის დაარქივებულია.", "Display the user full name in the left panel instead of the conversation title.": "აჩვენეთ მომხმარებლის სრული სახელი მარცხენა პანელში საუბრის სათაურის ნაცვლად.", "Display the user's profile image within the chat.": "აჩვენეთ მომხმარებლის პროფილის სურათი ჩატში.", "Display user name in header": "მომხმარებლის სახელის ჩვენება სათაურში", "Display user's profile image": "მომხმარებლის პროფილის სურათის ჩვენება", "Displays additional columns in the user table. Enter the name of the fields to add.": "Აჩვენებს დამატებით სვეტებს მომხმარებლის ცხრილში. შეიყვანეთ დასამატებელი ველების სახელი.", "Distribute conversations proportionately between agents and notify visitors of their position within the queue. Response time is in minutes. You can use the following merge fields in the message: {position}, {minutes}. They will be replaced by the real values in real-time.": "პროპორციულად გაანაწილეთ საუბრები აგენტებს შორის და აცნობეთ სტუმრებს მათი პოზიციის შესახებ რიგში. პასუხის დრო არის წუთებში. შეტყობინებაში შეგიძლიათ გამოიყენოთ შემდეგი შერწყმის ველები: {პოზიცია}, {წუთები}. ისინი რეალურ დროში შეიცვლება რეალური მნიშვნელობებით.", "Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.": "პროპორციულად გაანაწილეთ საუბრები აგენტებს შორის და დაბლოკეთ აგენტი სხვა აგენტების საუბრების ნახვის საშუალებას.", "Do not send email notifications to admins": "Არ გაუგზავნოთ ელფოსტის შეტყობინებები ადმინისტრატორებს", "Do not show tickets in chat": "Არ აჩვენო ბილეთები ჩატში", "Do not translate settings area": "Არ თარგმნოთ პარამეტრების არე", "Download": "ჩამოტვირთვა", "Edit profile": "Პროფილის რედაქტირება", "Edit user": "Მომხმარებლის რედაქტირება", "Email address": "Ელექტრონული მისამართი", "Email and ticket": "ფოსტა და ბილეთი", "Email header": "ელფოსტის სათაური", "Email notification delay (hours)": "ელფოსტის შეტყობინების დაგვიანება (საათები)", "Email notifications via cron job": "Ელ.ფოსტის შეტყობინებები cron job მეშვეობით", "Email only": "მხოლოდ ელ.წერილი", "Email piping": "ელ.ფოსტის მილსადენი", "Email piping server information and more settings.": "ელ.ფოსტის მილების სერვერის ინფორმაცია და სხვა პარამეტრები.", "Email request message": "ელფოსტის მოთხოვნის შეტყობინება", "Email signature": "ელ.ფოსტის ხელმოწერა", "Email template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "Ელფოსტის შაბლონი მომხმარებლისთვის გაგზავნილი ელფოსტისთვის, როდესაც აგენტი პასუხობს. შეგიძლიათ გამოიყენოთ ტექსტი, HTML და შემდეგი გაერთიანების ველები: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "Ელ. ფოსტის შაბლონი ელფოსტისთვის, რომელიც გაგზავნილია აგენტისთვის, როდესაც მომხმარებელი აგზავნის ახალ შეტყობინებას. შეგიძლიათ გამოიყენოთ ტექსტი, HTML და შემდეგი გაერთიანების ველები: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to the user after submitting their email through the follow-up message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.": "Ელ.ფოსტის შაბლონი მომხმარებლისთვის გაგზავნილი ელ. ფოსტის შემდგომი შეტყობინების ფორმის საშუალებით. შეგიძლიათ გამოიყენოთ ტექსტი, HTML და შემდეგი გაერთიანების ველები: {user_name}, {user_email}.", "Email template for the email sent to the user to verify their email address. Include the {code} merge field within your content, it will be replaced with the one-time code.": "Ელ.ფოსტის შაბლონი მომხმარებლისთვის გაგზავნილი ელ. ფოსტის მისამართის დასადასტურებლად. ჩართეთ {code} შერწყმის ველი თქვენს კონტენტში, ის შეიცვლება ერთჯერადი კოდით.", "Email verification": "Ელ.ფოსტის დადასტურება", "Email verification content": "Ელ.ფოსტის გადამოწმების შინაარსი", "Enable email verification with OTP.": "Ჩართეთ ელფოსტის დადასტურება OTP-ით.", "Enable logging of agent activity": "Აგენტის აქტივობის აღრიცხვის ჩართვა", "Enable the chatbot outside of scheduled office hours only.": "ჩართეთ ჩატბოტი მხოლოდ დაგეგმილი სამუშაო საათების გარეთ.", "Enable the registration only if all agents are offline.": "ჩართეთ რეგისტრაცია მხოლოდ იმ შემთხვევაში, თუ ყველა აგენტი ხაზგარეშეა.", "Enable the registration outside of scheduled office hours only.": "ჩართეთ რეგისტრაცია მხოლოდ დაგეგმილი სამუშაო საათების გარეთ.", "Enable this option if email notifications are sent via cron job.": "Ჩართეთ ეს პარამეტრი, თუ ელ.ფოსტის შეტყობინებები იგზავნება cron job მეშვეობით.", "Enable ticket and chat support for subscribers only, view member profile details and subscription details in the admin area.": "ჩართეთ ბილეთების და ჩეთის მხარდაჭერა მხოლოდ აბონენტებისთვის, იხილეთ წევრის პროფილის დეტალები და გამოწერის დეტალები ადმინისტრატორის ზონაში.", "Enter the bot token and click the button to synchronize the Telegram bot. Localhost cannot receive messages.": "Შეიყვანეთ ბოტის ჟეტონი და დააჭირეთ ღილაკს Telegram ბოტის სინქრონიზაციისთვის. ლოკალჰოსტი ვერ მიიღებს შეტყობინებებს.", "Enter the bot token and click the button to synchronize the Viber bot. Localhost cannot receive messages.": "Შეიყვანეთ ბოტის ჟეტონი და დააჭირეთ ღილაკს Viber bot-ის სინქრონიზაციისთვის. ლოკალჰოსტი ვერ მიიღებს შეტყობინებებს.", "Enter the database details of the Active eCommerce CMS database.": "Შეიყვანეთ Active eCommerce CMS მონაცემთა ბაზის დეტალები.", "Enter the database details of the Martfury database.": "Შეიყვანეთ Martfury მონაცემთა ბაზის დეტალები.", "Enter the database details of the Perfex database.": "Შეიყვანეთ პერფექსის მონაცემთა ბაზის დეტალები.", "Enter the database details of the WHMCS database.": "Შეიყვანეთ WHMCS მონაცემთა ბაზის დეტალები.", "Enter the default messages used by the chatbot when user question requires a dynamic answer.": "Შეიყვანეთ ჩატბოტის მიერ გამოყენებული ნაგულისხმევი შეტყობინებები, როდესაც მომხმარებლის შეკითხვა დინამიურ პასუხს მოითხოვს.", "Enter the details of your Google Business Messages.": "Შეიყვანეთ თქვენი Google Business Messages-ის დეტალები.", "Enter the details of your Twitter app.": "Შეიყვანეთ თქვენი Twitter აპის დეტალები.", "Enter the LINE details to start using it. Localhost cannot receive messages.": "Შეიყვანეთ LINE დეტალები მისი გამოყენების დასაწყებად. ლოკალჰოსტი ვერ მიიღებს შეტყობინებებს.", "Enter the URL of a .css file, to load it automatically in the admin area.": "შეიყვანეთ .css ფაილის URL, რომ ავტომატურად ჩატვირთოს იგი ადმინისტრაციულ ზონაში.", "Enter the URL of a .js file, to load it automatically in the admin area.": "შეიყვანეთ .js ფაილის URL, რომ ავტომატურად ჩატვირთოს იგი ადმინისტრაციულ ზონაში.", "Enter the URL of the articles page.": "Შეიყვანეთ სტატიების გვერდის URL.", "Enter the URLs of your shop": "შეიყვანეთ თქვენი მაღაზიის URL-ები", "Enter the WeChat official account token. See the docs for more details.": "Შეიყვანეთ WeChat ოფიციალური ანგარიშის ნიშანი. დამატებითი ინფორმაციისთვის იხილეთ დოკუმენტები.", "Enter the Zalo details to start using it. Localhost cannot receive messages.": "Შეიყვანეთ Zalo-ს დეტალები მისი გამოყენების დასაწყებად. ლოკალჰოსტი ვერ მიიღებს შეტყობინებებს.", "Enter your 360dialog account settings information.": "შეიყვანეთ თქვენი 360dialog ანგარიშის პარამეტრების ინფორმაცია.", "Enter your Envato Purchase Code to activate automatic updates and unlock all the features.": "შეიყვანეთ თქვენი Envato შესყიდვის კოდი, რომ გაააქტიუროთ ავტომატური განახლებები და განბლოკოთ ყველა ფუნქცია.", "Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.": "შეიყვანეთ თქვენი Twilio ანგარიშის დეტალები. შეგიძლიათ გამოიყენოთ ტექსტი და შემდეგი გაერთიანების ველები: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.", "Enter your Twilio account settings information.": "შეიყვანეთ თქვენი Twilio ანგარიშის პარამეტრების ინფორმაცია.", "Enter your WeChat Official Account information.": "შეიყვანეთ თქვენი WeChat ოფიციალური ანგარიშის ინფორმაცია.", "Enter your Zendesk information.": "შეიყვანეთ თქვენი Zendesk ინფორმაცია.", "Entities": "Entities", "Envato Purchase Code": "Envato შესყიდვის კოდი", "Envato purchase code validation": "Envato შესყიდვის კოდის ვალიდაცია", "Exclude products": "გამორიცხეთ პროდუქტები", "Export all settings.": "ყველა პარამეტრის ექსპორტი.", "Export settings": "ექსპორტის პარამეტრები", "Facebook pages": "ფეისბუქის გვერდები", "Fallback message": "Სარეზერვო შეტყობინება", "Filters": "Ფილტრები", "First chat message": "პირველი ჩატის შეტყობინება", "First reminder delay (hours)": "პირველი შეხსენების დაგვიანება (საათები)", "First ticket form": "პირველი ბილეთის ფორმა", "Flash notifications": "ფლეშ შეტყობინებები", "Follow up - Email": "დაკვირვება - ელ", "Follow up email": "Მიჰყევით ელ.წერილს", "Follow up message": "შემდგომი შეტყობინება", "Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.": "მიჰყვება საუბარს ადამიანურ აგენტსა და საბოლოო მომხმარებელს შორის და რეალურ დროში აწვდის პასუხების შეთავაზებებს ადამიანის აგენტს.", "Follow-up email template. You can use text, HTML, and the following merge fields and more: {coupon}, {product_names}, {user_name}.": "შემდგომი ელ.ფოსტის შაბლონი. შეგიძლიათ გამოიყენოთ ტექსტი, HTML და შემდეგი შერწყმის ველები და სხვა: {coupon}, {product_names}, {user_name}.", "Force language": "ძალის ენა", "Force log out": "იძულებით გასვლა", "Force the chat to ignore the language preferences, and to use always the same language.": "აიძულეთ ჩატი უგულებელყოს ენის პრეფერენციები და გამოიყენოს ყოველთვის ერთი და იგივე ენა.", "Force the loggout of Masi Chat agents if they are not logged in WordPress.": "აიძულეთ <PERSON><PERSON> აგენტების გასვლა, თუ ისინი არ არიან შესული WordPress-ში.", "Force users to use a different conversation for each store and hide conversations from other stores from store administrators.": "Აიძულეთ მომხმარებლები გამოიყენონ განსხვავებული მიმოწერა თითოეული მაღაზიისთვის და დამალონ საუბრები სხვა მაღაზიებიდან მაღაზიის ადმინისტრატორებისგან.", "Force users to use only one phone country code.": "აიძულეთ მომხმარებლები გამოიყენონ მხოლოდ ერთი ტელეფონის ქვეყნის კოდი.", "Form message": "შეტყობინების ფორმა", "Form title": "ფორმის სათაური", "Frequency penalty": "Სიხშირის ჯარიმა", "Full visitor details": "სრული ვიზიტორის დეტალები", "Function name": "Ფუნქციის სახელი", "Generate conversations data": "საუბრების მონაცემების გენერირება", "Generate user questions": "Შექმენით მომხმარებლის კითხვები", "Get configuration URL": "მიიღეთ კონფიგურაციის URL", "Get it from the APP_KEY value of the file .env located in the root directory of Active eCommerce.": "მიიღეთ ის ფაილის APP_KEY მნიშვნელობიდან .env, რომელიც მდებარეობს Active eCommerce root დირექტორიაში.", "Get it from the APP_KEY value of the file .env located in the root directory of Martfury.": "Მიიღეთ ის ფაილის APP_KEY მნიშვნელობიდან .env, რომელიც მდებარეობს Martfury-ის ძირეულ დირექტორიაში.", "Get Path": "მიიღეთ გზა", "Get Service Worker path": "Მიიღეთ Service Worker გზა", "Get URL": "Მიიღეთ URL", "Google and Dialogflow settings.": "Google და Dialogflow პარამეტრები.", "Google search": "Google ძებნა", "Header": "Სათაური", "Header background image": "სათაურის ფონის სურათი", "Header brand image": "ჰედერის ბრენდის სურათი", "Header message": "სათაურის შეტყობინება", "Header title": "სათაურის სათაური", "Header type": "სათაურის ტიპი", "Header variables": "Სათაურის ცვლადები", "Hide": "დამალვა", "Hide agent's profile image": "აგენტის პროფილის სურათის დამალვა", "Hide archived tickets": "Დაარქივებული ბილეთების დამალვა", "Hide archived tickets from users.": "Დამალეთ დაარქივებული ბილეთები მომხმარებლებისგან.", "Hide chat if no agents online": "დამალე ჩატი, თუ არ არის აგენტები ონლაინ", "Hide chat outside of office hours": "ჩეთის დამალვა სამუშაო საათების მიღმა", "Hide conversation details panel": "Საუბრის დეტალების პანელის დამალვა", "Hide conversations of other agents": "სხვა აგენტების საუბრების დამალვა", "Hide on mobile": "მობილურზე დამალვა", "Hide the agent's profile image within the chat.": "დამალეთ აგენტის პროფილის სურათი ჩატში.", "Hide tickets from the chat widget and chats from the ticket area.": "Დამალეთ ბილეთები ჩატის ვიჯეტიდან და ჩეთები ბილეთების ზონიდან.", "Hide timetable": "განრიგის დამალვა", "Host": "მასპინძელი", "Human takeover": "ადამიანის ხელში ჩაგდება", "If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.": "Თუ არცერთი აგენტი არ პასუხობს მითითებულ დროის ინტერვალში, გაიგზავნება შეტყობინება მომხმარებლის დეტალების მოთხოვნით, როგორიცაა მათი ელფოსტა.", "If the chatbot doesn't understand a user's question, forwards the conversation to an agent.": "Თუ ჩეთბოტს არ ესმის მომხმარებლის შეკითხვა, გადაუგზავნის საუბარს აგენტს.", "Image": "გამოსახულება", "Import admins": "ადმინისტრატორების იმპორტი", "Import all settings.": "ყველა პარამეტრის იმპორტი.", "Import articles": "სტატიების იმპორტი", "Import contacts": "კონტაქტების იმპორტი", "Import customers": "მომხმარებლების იმპორტი", "Import customers into Masi Chat. Only new customers will be imported.": "Კლიენტების იმპორტი <PERSON><PERSON>-ში. მოხდება მხოლოდ ახალი მომხმარებლების იმპორტი.", "Import settings": "პარამეტრების იმპორტი", "Import users": "მომხმარებლების იმპორტი", "Import users from a CSV file.": "Მომხმარებლების იმპორტი CSV ფაილიდან.", "Import vendors": "Მოვაჭრეების იმპორტი", "Import vendors into Masi Chat as agents. Only new vendors will be imported.": "Მოვაჭრეების იმპორტი <PERSON><PERSON>-ში აგენტებად. შემოვა მხოლოდ ახალი გამყიდველები.", "Improve chat performance with Pusher and WebSockets. This setting stops all AJAX/HTTP real-time requests that slow down your server and use instead the WebSockets.": "გააუმჯობესეთ ჩატის შესრულება Pusher-ით და WebSockets-ით. ეს პარამეტრი აჩერებს ყველა AJAX/HTTP მოთხოვნას რეალურ დროში, რომელიც ანელებს თქვენს სერვერს და იყენებს WebSockets-ს.", "Include custom fields": "ჩართეთ მორგებული ველები", "Include custom fields in the registration form.": "სარეგისტრაციო ფორმაში ჩართეთ მორგებული ველები.", "Include the password field in the registration form.": "სარეგისტრაციო ფორმაში შეიყვანეთ პაროლის ველი.", "Incoming conversations and messages": "შემომავალი საუბრები და შეტყობინებები", "Incoming conversations only": "მხოლოდ შემომავალი საუბრები", "Incoming messages only": "მხოლოდ შემომავალი შეტყობინებები", "Increase sales and connect you and sellers with customers in real-time by integrating Active eCommerce with Masi Chat.": "გაზარდეთ გაყიდვები და დაუკავშირდით თქვენ და გამყიდველებს კლიენტებთან რეალურ დროში Active eCommerce მხარდაჭერის საბჭოსთან ინტეგრირებით.", "Increase sales, provide better support, and faster solutions, by integrating WooCommerce with Masi Chat.": "გაზარდეთ გაყიდვები, უზრუნველყოთ უკეთესი მხარდაჭერა და უფრო სწრაფი გადაწყვეტილებები WooCommerce-ის მხარდაჭერის საბჭოსთან ინტეგრირებით.", "Info message": "საინფორმაციო შეტყობინება", "Initialize and display the chat widget and tickets only for members.": "ჩატის ვიჯეტისა და ბილეთების ინიცირება და ჩვენება მხოლოდ წევრებისთვის.", "Initialize and display the chat widget only when the user is logged in.": "Ჩატის ვიჯეტის ინიცირება და ჩვენება მხოლოდ მაშინ, როდესაც მომხმარებელი შესულია.", "Instance ID": "ინსტანციის ID", "Integrate OpenCart with {R} for real-time syncing of customers, order history access, and customer cart visibility.": "Გააერთიანეთ OpenCart-თან {R} კლიენტების რეალურ დროში სინქრონიზაციისთვის, შეკვეთების ისტორიაზე წვდომისთვის და კლიენტების კალათის ხილვადობისთვის.", "Interval (sec)": "Ინტერვალი (წმ)", "IP banning": "IP აკრძალვა", "Label": "ლეიბლი", "Language": "Ენა", "Language detection": "ენის ამოცნობა", "Language detection message": "Ენის ამოცნობის შეტყობინება", "Last name": "Გვარი", "Leave it blank if you don't know what this setting is! Entering an incorrect value will break the chat. Sets the main domain where chat is used to enable login and conversations sharing between the main domain and sub domains.": "დატოვეთ ცარიელი, თუ არ იცით რა არის ეს პარამეტრი! არასწორი მნიშვნელობის შეყვანისას ჩატი გაფუჭდება. აყენებს მთავარ დომენს, სადაც ჩატი გამოიყენება შესვლისა და საუბრების გაზიარების გასააქტიურებლად მთავარ დომენსა და ქვედომენებს შორის.", "Left": "მარცხენა", "Left panel": "მარცხენა პანელი", "Left profile image": "პროფილის მარცხენა სურათი", "Let the bot to search on Google to find answers to user questions.": "ნება მიეცით ბოტს მოძებნოს Google-ში მომხმარებლის კითხვებზე პასუხების მოსაძებნად.", "Let the chatbot search on Google to find answers to user questions.": "Მიეცით საშუალება ჩეთბოტს მოძებნოს Google-ში მომხმარებლის კითხვებზე პასუხების მოსაძებნად.", "Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from {R}.": "Საშუალებას აძლევს თქვენს მომხმარებლებს დაგიკავშირდეთ Twitter-ის საშუალებით. წაიკითხეთ და უპასუხეთ თქვენს Twitter ანგარიშზე გაგზავნილ შეტყობინებებს პირდაპირ {R}-დან.", "Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from {R}.": "Საშუალებას აძლევს თქვენს მომხმარებლებს დაგიკავშირდეთ WeChat-ის საშუალებით. წაიკითხეთ და უპასუხეთ თქვენს WeChat ოფიციალურ ანგარიშზე გაგზავნილ ყველა შეტყობინებას პირდაპირ {R}-დან.", "Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from {R}.": "Საშუალებას აძლევს თქვენს მომხმარებლებს დაგიკავშირდეთ WhatsApp-ის საშუალებით. წაიკითხეთ და უპასუხეთ თქვენს WhatsApp Business ანგარიშზე გაგზავნილ ყველა შეტყობინებას პირდაპირ {R}-დან.", "Link each agent with the corresponding Slack user, so when an agent replies via Slack it will be displayed as the assigned agent.": "დააკავშირეთ თითოეული აგენტი შესაბამის Slack მომხმარებელთან, ასე რომ, როდესაც აგენტი პასუხობს Slack-ის მეშვეობით, ის გამოჩნდება, როგორც მინიჭებული აგენტი.", "Link name": "ლინკის სახელი", "Login form": "შესვლის ფორმა", "Login initialization": "შესვლის ინიციალიზაცია", "Login verification URL": "Შესვლის დამადასტურებელი URL", "Logit bias": "Ლოჯიტის მიკერძოება", "Make a backup of your Dialogflow agent first. This operation can take several minutes.": "Ჯერ გააკეთეთ თქვენი Dialogflow აგენტის სარეზერვო ასლი. ამ ოპერაციას შეიძლება რამდენიმე წუთი დასჭირდეს.", "Make the registration phone field mandatory.": "გახადეთ რეგისტრაციის ტელეფონის ველი სავალდებულო.", "Manage": "Მართვა", "Manage here the departments settings.": "მართეთ აქ დეპარტამენტების პარამეტრები.", "Manage the tags settings.": "Ტეგების პარამეტრების მართვა.", "Manifest file URL": "Manifest ფაილის URL", "Manual": "Სახელმძღვანელო", "Manual initialization": "ხელით ინიციალიზაცია", "Martfury root directory path, e.g. /var/www/": "Martfury root დირექტორიას გზა, მაგ. /var/www/", "Martfury shop URL, e.g. https://shop.com": "Martfury მაღაზიის URL, მაგ: https://shop.com", "Max message limit": "Შეტყობინებების მაქსიმალური ლიმიტი", "Max tokens": "Მაქს ჟეტონები", "Members only": "Მხოლოდ წევრები", "Members with an active paid plan only": "მხოლოდ აქტიური ფასიანი გეგმის მქონე წევრები", "Message": "შეტყობინება", "Message area": "Შეტყობინებების არეალი", "Message rewrite button": "Შეტყობინების გადაწერის ღილაკი", "Message template": "შეტყობინების შაბლონი", "Message type": "შეტყობინების ტიპი", "Messaging channels": "Შეტყობინებების არხები", "Messenger and Instagram settings": "Messenger და Instagram-ის პარამეტრები", "Minify JS": "Minify JS", "Minimal": "მინიმალური", "Model": "Მოდელი", "Multilingual": "მრავალენოვანი", "Multilingual plugin": "მრავალენოვანი მოდული", "Multilingual via translation": "Მრავალენოვანი თარგმანის საშუალებით", "Multlilingual training sources": "Მრავალენოვანი ტრენინგის წყაროები", "Name": "სახელი", "Namespace": "Სახელთა სივრცე", "New conversation email": "Ახალი საუბრის ელფოსტა", "New conversation notification": "ახალი საუბრის შეტყობინება", "New ticket button": "ახალი ბილეთის ღილაკი", "Newsletter": "ბიულეტენი", "No delay": "არანაირი დაგვიანება", "No results found.": "Შედეგები არ მოიძებნა.", "No, we don't ship in": "არა, ჩვენ არ ვაგზავნით", "None": "არცერთი", "Note data scraping": "Შენიშვნა მონაცემების გახეხვა", "Notes": "Შენიშვნები", "Notifications icon": "შეტყობინებების ხატულა", "Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.": "Შეატყობინეთ მომხმარებელს, როდესაც მისი შეტყობინება იგზავნება დაგეგმილი სამუშაო საათების მიღმა ან ყველა აგენტი ხაზგარეშეა.", "OA secret key": "OA საიდუმლო გასაღები", "Offline message": "ოფლაინ შეტყობინება", "Offset": "ოფსეტური", "On chat open": "ჩატში გახსნილია", "On page load": "გვერდის ჩატვირთვაზე", "One conversation per agent": "Ერთი საუბარი თითო აგენტზე", "One conversation per department": "Თითო განყოფილებაში ერთი საუბარი", "Online users notification": "ონლაინ მომხმარებლების შეტყობინება", "Only desktop": "მხოლოდ დესკტოპზე", "Only general questions": "Მხოლოდ ზოგადი კითხვები", "Only mobile devices": "მხოლოდ მობილური მოწყობილობები", "Only questions related to your sources": "Მხოლოდ თქვენს წყაროებთან დაკავშირებული კითხვები", "Open automatically": "ავტომატურად გახსნა", "Open chat": "გახსენით ჩატი", "Open the chat window automatically when a new message is received.": "ახალი შეტყობინების მიღებისას ავტომატურად გახსენით ჩატის ფანჯარა.", "OpenAI Assistants - Department linking": "OpenAI Assistants - დეპარტამენტის დაკავშირება", "OpenAI settings.": "OpenAI პარამეტრები.", "Optional link": "სურვილისამებრ ბმული", "Order webhook": "შეუკვეთეთ ვებჰუკი", "Other": "სხვა", "Outgoing SMTP server information.": "გამავალი SMTP სერვერის ინფორმაცია.", "Page ID": "გვერდის ID", "Page IDs": "გვერდის ID", "Page name": "გვერდის სახელი", "Page token": "გვერდის ნიშანი", "Panel height": "პანელის სიმაღლე", "Panel name": "პანელის სახელი", "Panel title": "პანელის სათაური", "Panels arrows": "პანელების ისრები", "Password": "პაროლი", "Perfex URL": "Perfex URL", "Performance optimization": "შესრულების ოპტიმიზაცია", "Phone": "ტელეფონი", "Phone number ID": "Ტელეფონის ნომერი ID", "Phone required": "საჭიროა ტელეფონი", "Place ID": "Ადგილის ID", "Placeholder text": "ჩანაცვლების ტექსტი", "Play a sound for new messages and conversations.": "Დაუკარით ხმა ახალი შეტყობინებებისა და საუბრებისთვის.", "Popup message": "ამომხტარი შეტყობინება", "Port": "პორტი", "Post Type slugs": "Post Type შლაკები", "Presence penalty": "Დასწრების ჯარიმა", "Prevent admins from receiving email notifications.": "Აღკვეთეთ ადმინისტრატორები ელ.ფოსტის შეტყობინებების მიღებისგან.", "Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.": "აარიდეთ აგენტებს სხვა აგენტებისთვის მინიჭებული საუბრების ნახვა. ეს პარამეტრი ავტომატურად ჩართულია, თუ მარშრუტიზაცია ან რიგი აქტიურია.", "Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.": "Თავიდან აიცილეთ მომხმარებლების მხრიდან რაიმე სახის შეურაცხყოფა ერთი მოწყობილობიდან ჩატბოტზე გაგზავნილი შეტყობინებების რაოდენობის შეზღუდვით.", "Primary color": "პირველადი ფერი", "Priority": "პრიორიტეტი", "Privacy link": "კონფიდენციალურობის ბმული", "Privacy message": "კონფიდენციალურობის შეტყობინება", "Private chat": "Პირადი ჩატი", "Private chat linking": "Პირადი ჩატის დაკავშირება", "Private key": "Პირადი გასაღები", "Product IDs": "Პროდუქტის ID", "Product removed notification": "პროდუქტის წაშლის შეტყობინება", "Product removed notification - Email": "პროდუქტის წაშლის შეტყობინება - ელ", "Profile image": "პროფილის სურათი", "Project ID": "პროექტის ID", "Project ID or Agent Name": "პროექტის ID ან აგენტის სახელი", "Prompt": "Სწრაფი", "Prompt - Message rewriting": "Მოთხოვნა - შეტყობინების გადაწერა", "Protect the tickets area from spam and abuse with Google reCAPTCHA.": "Დაიცავით ბილეთების არეალი სპამისგან და ბოროტად გამოყენებისგან Google-ით reCAPTCHA.", "Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.": "მიაწოდეთ დამხმარე მაგიდას მხარდაჭერა თქვენს კლიენტებს ბილეთების არეალის ჩათვლით, ჩატის ყველა ფუნქციით, ნებისმიერ ვებ გვერდზე წამებში.", "Provider": "Პროვაიდერი", "Purchase button text": "შესყიდვის ღილაკის ტექსტი", "Push notifications": "Push შეტყობინებები", "Push notifications settings.": "Push შეტყობინებების პარამეტრები.", "Queue": "რიგი", "Rating": "Რეიტინგი", "Read and reply to messages sent from Google Search, Maps and brand-owned channels directly in {R}.": "Წაიკითხეთ და უპასუხეთ შეტყობინებებს, რომლებიც გაგზავნილია Google Search-იდან, Maps-დან და ბრენდის საკუთრებაში არსებული არხებიდან პირდაპირ {R}-ში.", "Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from {R}.": "Წაიკითხეთ, მართეთ და უპასუხეთ ყველა შეტყობინებას, რომელიც გაგზავნილია თქვენს Facebook გვერდებსა და Instagram ანგარიშებზე პირდაპირ {R}-დან.", "Reconnect": "Ხელახლა დაკავშირება", "Redirect the user to the registration link instead of showing the registration form.": "სარეგისტრაციო ფორმის ჩვენების ნაცვლად მომხმარებლის გადამისამართება რეგისტრაციის ბმულზე.", "Redirect the user to the specified URL if the registration is required and the user is not logged in. Leave blank to use the default registration form.": "მომხმარებლის გადამისამართება მითითებულ URL-ზე, თუ რეგისტრაციაა საჭირო და მომხმარებელი არ არის შესული. დატოვეთ ცარიელი ნაგულისხმევი სარეგისტრაციო ფორმის გამოსაყენებლად.", "Refresh token": "Განახლების ჟეტონი", "Register all visitors": "დაარეგისტრირეთ ყველა ვიზიტორი", "Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered.": "დაარეგისტრირეთ ყველა ვიზიტორი ავტომატურად. როდესაც ეს პარამეტრი არ არის აქტიური, დარეგისტრირდებიან მხოლოდ ის ვიზიტორები, რომლებიც დაიწყებენ ჩატს.", "Registration / Login": "რეგისტრაცია / შესვლა", "Registration and login form": "რეგისტრაციისა და შესვლის ფორმა", "Registration fields": "Რეგისტრაციის ველები", "Registration form": "Სარეგისტრაციო ფორმა", "Registration link": "რეგისტრაციის ლინკი", "Registration redirect": "რეგისტრაციის გადამისამართება", "Rename the chat bot. Default is 'Bot'.": "გადარქმევა ჩატის ბოტი. ნაგულისხმევი არის &quot;ბოტი&quot;.", "Rename the visitor name prefix. Default is 'User'.": "გადაარქვით ვიზიტორის სახელის პრეფიქსი. ნაგულისხმევი არის &quot;მომხმარებელი&quot;.", "Repeat": "Გაიმეორეთ", "Repeat - admin": "Გაიმეორეთ - ადმინ", "Replace the admin login page message.": "შეცვალეთ ადმინისტრატორის შესვლის გვერდის შეტყობინება.", "Replace the brand logo on the admin login page.": "შეცვალეთ ბრენდის ლოგო ადმინისტრატორის შესვლის გვერდზე.", "Replace the header title with the user's first name and last name when available.": "შეცვალეთ სათაურის სათაური მომხმარებლის სახელით და გვარით, როცა ხელმისაწვდომია.", "Replace the top-left brand icon on the admin area and the browser favicon.": "შეცვალეთ ზედა მარცხენა ბრენდის ხატულა ადმინისტრატორის ზონაში და ბრაუზერის ფავიკონი.", "Reply to user emails": "უპასუხეთ მომხმარებლის წერილებს", "Reply to user text messages": "უპასუხეთ მომხმარებლის ტექსტურ შეტყობინებებს", "Reports": "ანგარიშები", "Reports area": "Ანგარიშების არეალი", "Request a valid Envato purchase code for registration.": "Მოითხოვეთ მოქმედი Envato შესყიდვის კოდი რეგისტრაციისთვის.", "Request the user to provide their email address and then send a confirmation email to the user.": "Სთხოვეთ მომხმარებელს მიაწოდოს თავისი ელ.ფოსტის მისამართი და შემდეგ გაუგზავნოს მომხმარებელს დამადასტურებელი ელ.წერილი.", "Require phone": "საჭიროა ტელეფონი", "Require registration": "მოითხოვს რეგისტრაციას", "Require the user registration or login before start a chat. To enable the login area the password field must be included.": "მოითხოვეთ მომხმარებლის რეგისტრაცია ან შესვლა ჩატის დაწყებამდე. შესვლის არეალის გასააქტიურებლად პაროლის ველი უნდა იყოს ჩართული.", "Require the user registration or login in order to use the tickets area.": "მოითხოვეთ მომხმარებლის რეგისტრაცია ან შესვლა ბილეთების ზონით სარგებლობისთვის.", "Required": "საჭირო", "Response time": "Რეაგირების დრო", "Restrict chat access by blocking IPs. List IPs with commas.": "Შეზღუდეთ ჩატში წვდომა IP-ების დაბლოკვით. ჩამოთვალეთ IP-ები მძიმეებით.", "Returning visitor message": "დაბრუნებული ვიზიტორის შეტყობინება", "Rich messages": "მდიდარი შეტყობინებები", "Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.": "მდიდარი შეტყობინებები არის კოდის ფრაგმენტები, რომლებიც შეიძლება გამოყენებულ იქნას ჩატის შეტყობინებაში. ისინი შეიძლება შეიცავდეს HTML კოდს და ავტომატურად გადაიცემა ჩატში. მდიდარი შეტყობინებების გამოყენება შესაძლებელია შემდეგი სინტაქსით: [rich-message-name]. არსებობს უამრავი ჩაშენებული შეტყობინებების არჩევანი.", "Right": "უფლება", "Right panel": "მარჯვენა პანელი", "Routing": "მარშრუტიზაცია", "Routing if offline": "მარშრუტიზაცია თუ ხაზგარეშე", "Save useful information like user country and language also for visitors.": "შეინახეთ სასარგებლო ინფორმაცია, როგორიცაა მომხმარებლის ქვეყანა და ენა, ასევე ვიზიტორებისთვის.", "Saved replies": "შენახული პასუხები", "Scheduled office hours": "დაგეგმილი სამუშაო საათები", "Search engine ID": "საძიებო სისტემის ID", "Second chat message": "მეორე ჩატის შეტყობინება", "Second reminder delay (hours)": "მეორე შეხსენების დაგვიანება (საათები)", "Secondary color": "მეორადი ფერი", "Secret key": "Საიდუმლო გასაღები", "Send a message to allow customers to be notified when they can purchase a product they are interested in, but that is currently out of stock. You can use the following merge fields: {user_name}, {product_name}.": "გაგზავნეთ შეტყობინება, რათა მომხმარებელს მიეცეთ შეტყობინება, როდესაც შეძლებენ შეიძინონ პროდუქტი, რომელიც მათ აინტერესებთ, მაგრამ ის ამჟამად არ არის მარაგში. შეგიძლიათ გამოიყენოთ შემდეგი შერწყმის ველები: {user_name}, {product_name}.", "Send a message to new users when they create the first ticket. Text formatting and merge fields are supported.": "გაუგზავნეთ შეტყობინება ახალ მომხმარებლებს, როდესაც ისინი შექმნიან პირველ ბილეთს. ტექსტის ფორმატირება და გაერთიანების ველები მხარდაჭერილია.", "Send a message to new users when they visit the website for the first time.": "Გაუგზავნეთ შეტყობინება ახალ მომხმარებლებს, როდესაც ისინი პირველად ეწვევიან ვებსაიტს.", "Send a message to the customer after a product has been removed from the cart. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.": "გაუგზავნეთ შეტყობინება მომხმარებელს პროდუქტის კალათიდან ამოღების შემდეგ. შეგიძლიათ გამოიყენოთ შემდეგი შერწყმის ველები და სხვა: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.", "Send a message to the customers who complete a purchase asking to share the product they just bought. You can use the following merge fields and more: {product_name}, {user_name}.": "გაუგზავნეთ შეტყობინება იმ კლიენტებს, რომლებიც ასრულებენ შენაძენს, თხოვნით, გააზიარონ მათ მიერ ახლახან შეძენილი პროდუქტი. შეგიძლიათ გამოიყენოთ შემდეგი შერწყმის ველები და სხვა: {product_name}, {user_name}.", "Send a message to the customers who complete a purchase. You can use the following merge fields and more: {coupon}, {product_names}, {user_name}.": "გაუგზავნეთ შეტყობინება კლიენტებს, რომლებიც ასრულებენ შეძენას. შეგიძლიათ გამოიყენოთ შემდეგი შერწყმის ველები და სხვა: {coupon}, {product_names}, {user_name}.", "Send a message to the user when the agent archive the conversation.": "გაუგზავნეთ შეტყობინება მომხმარებელს, როდესაც აგენტი დაარქივებს საუბარს.", "Send a message to users who visit the website again after at least 24 hours. You can use the following merge fields and more: {coupon}, {user_name}. See the docs for more details.": "გაუგზავნეთ შეტყობინება მომხმარებლებს, რომლებიც კვლავ ეწვევიან ვებსაიტს მინიმუმ 24 საათის შემდეგ. შეგიძლიათ გამოიყენოთ შემდეგი შერწყმის ველები და სხვა: {coupon}, {user_name}. დამატებითი ინფორმაციისთვის იხილეთ დოკუმენტები.", "Send a test agent notification email to verify email settings.": "გაგზავნეთ სატესტო აგენტის შეტყობინების ელფოსტა ელფოსტის პარამეტრების დასადასტურებლად.", "Send a test message to your Slack channel. This only tests the sending functionality of outgoing messages.": "გაგზავნეთ სატესტო შეტყობინება თქვენს Slack არხზე. ეს მხოლოდ ამოწმებს გამავალი შეტყობინებების გაგზავნის ფუნქციას.", "Send a test user notification email to verify email settings.": "გაგზავნეთ სატესტო მომხმარებლის შეტყობინების ელფოსტა ელფოსტის პარამეტრების დასადასტურებლად.", "Send a text message to the provided phone number.": "Გაგზავნეთ ტექსტური შეტყობინება მითითებულ ტელეფონის ნომერზე.", "Send a user email notification": "Გაგზავნეთ მომხმარებლის ელ.ფოსტის შეტყობინება", "Send a user text message notifcation": "Გაუგზავნეთ მომხმარებლის ტექსტური შეტყობინების შეტყობინება", "Send a user text message notification": "Გაგზავნეთ მომხმარებლის ტექსტური შეტყობინების შეტყობინება", "Send an agent email notification": "Გაგზავნეთ აგენტის ელფოსტის შეტყობინება", "Send an agent text message notification": "Გაგზავნეთ აგენტის ტექსტური შეტყობინების შეტყობინება", "Send an agent user text notification": "Გაგზავნეთ აგენტის მომხმარებლის ტექსტური შეტყობინება", "Send an email notification to the provided email address.": "Გაგზავნეთ შეტყობინება ელექტრონული ფოსტის მითითებულ მისამართზე.", "Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.": "გაუგზავნეთ ელფოსტა აგენტს, როდესაც მომხმარებელი პასუხობს და აგენტი ხაზგარეშეა. ელფოსტა ავტომატურად ეგზავნება ყველა აგენტს ახალი საუბრებისთვის.", "Send an email to the user when a new conversation is created.": "Გაუგზავნეთ ელ.წერილი მომხმარებელს ახალი მიმოწერის შექმნისას.", "Send an email to the user when a new conversation or ticket is created": "გაუგზავნეთ ელ.წერილი მომხმარებელს, როდესაც შეიქმნება ახალი საუბარი ან ბილეთი", "Send an email to the user when an agent replies and the user is offline.": "გაუგზავნეთ ელ.წერილი მომხმარებელს, როდესაც აგენტი პასუხობს და მომხმარებელი ხაზგარეშეა.", "Send email": "ელ.ფოსტის გაგზავნა", "Send login details to the specified URL and allow access only if the response is positive.": "Გაგზავნეთ შესვლის დეტალები მითითებულ URL-ზე და დაუშვით წვდომა მხოლოდ იმ შემთხვევაში, თუ პასუხი დადებითია.", "Send message": "Შეტყობინების გაგზავნა", "Send message to Slack": "გაგზავნეთ შეტყობინება Slack-ზე", "Send message via enter button": "შეტყობინების გაგზავნა enter ღილაკით", "Send text message": "Ტექსტური შეტყობინების გაგზავნა", "Send the message template to a WhatsApp number.": "Გაგზავნეთ შეტყობინების შაბლონი WhatsApp ნომერზე.", "Send the message via the ENTER keyboard button.": "გაგზავნეთ შეტყობინება კლავიატურის ღილაკით ENTER.", "Send the user details of the registration form and email rich messages to Dialogflow.": "გაუგზავნეთ სარეგისტრაციო ფორმის მომხმარებლის დეტალები და ელფოსტით მდიდარი შეტყობინებები Dialogflow-ს.", "Send the WhatsApp order details to the URL provided.": "Გაგზავნეთ WhatsApp შეკვეთის დეტალები მითითებულ URL-ზე.", "Send to user's email": "გაგზავნეთ მომხმარებლის მეილზე", "Send transcript to user's email": "გაგზავნეთ ტრანსკრიპტი მომხმარებლის ელფოსტაზე", "Send user details": "მომხმარებლის დეტალების გაგზავნა", "Sender": "Გამგზავნი", "Sender email": "ელფოსტის გაგზავნა", "Sender name": "Გამგზავნის სახელი", "Sender number": "გამგზავნის ნომერი", "Sends a text message if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.": "აგზავნის ტექსტურ შეტყობინებას, თუ WhatsApp შეტყობინების გაგზავნა ვერ მოხერხდა. შეგიძლიათ გამოიყენოთ ტექსტი და შემდეგი გაერთიანების ველები: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.", "Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.": "აგზავნის WhatsApp შაბლონის შეტყობინებას, თუ WhatsApp შეტყობინების გაგზავნა ვერ მოხერხდა. შეგიძლიათ გამოიყენოთ ტექსტი და შემდეგი შერწყმის ველები: {conversation_url_parameter}, {recipient_name}, {recipient_email}.", "Service": "სერვისი", "Service Worker path": "Service Worker გზა", "Service Worker URL": "Service Worker URL", "Set a dedicated Dialogflow agent for each department.": "დააყენეთ გამოყოფილი Dialogflow აგენტი თითოეული დეპარტამენტისთვის.", "Set a dedicated OpenAI Assistants for each department.": "Დააყენეთ გამოყოფილი OpenAI ასისტენტები თითოეული დეპარტამენტისთვის.", "Set a dedicated Slack channel for each department.": "დააყენეთ გამოყოფილი Slack არხი თითოეული დეპარტამენტისთვის.", "Set a profile image for the chat bot.": "დააყენეთ პროფილის სურათი ჩატის ბოტისთვის.", "Set the articles panel title. Default is 'Help Center'.": "Დააყენეთ სტატიების პანელის სათაური. ნაგულისხმევი არის „დახმარების ცენტრი“.", "Set the avatar image shown next to the message. It must be a JPG image of 1024x1024px with a maximum size of 50KB.": "Დააყენეთ ავატარის სურათი, რომელიც ნაჩვენებია შეტყობინების გვერდით. ეს უნდა იყოს JPG გამოსახულება 1024x1024 პიქსელი, მაქსიმალური ზომით 50 კბ.", "Set the chat language or translate it automatically to match the user language. Default is English.": "Დააყენეთ ჩატის ენა ან ავტომატურად თარგმნეთ იგი მომხმარებლის ენასთან შესატყვისად. ნაგულისხმევი არის ინგლისური.", "Set the currency symbol of the membership prices.": "Დააყენეთ წევრობის ფასების ვალუტის სიმბოლო.", "Set the currency symbol used by your system.": "დააყენეთ თქვენი სისტემის მიერ გამოყენებული ვალუტის სიმბოლო.", "Set the default departments for all tickets. Enter the department ID.": "დააყენეთ ნაგულისხმევი განყოფილებები ყველა ბილეთისთვის. შეიყვანეთ განყოფილების ID.", "Set the default email header that will be prepended to automated emails and direct emails.": "დააყენეთ ნაგულისხმევი ელფოსტის სათაური, რომელიც დაემატება ავტომატურ ელფოსტას და პირდაპირ წერილებს.", "Set the default email signature that will be appended to automated emails and direct emails.": "დააყენეთ ელ.ფოსტის ნაგულისხმევი ხელმოწერა, რომელიც დაემატება ავტომატურ ელ.წერილს და პირდაპირ წერილებს.", "Set the default form to display if the registraion is required.": "დააყენეთ ნაგულისხმევი ფორმა, რათა გამოჩნდეს რეგისტრაცია საჭიროების შემთხვევაში.", "Set the default name to use for conversations without a name.": "დააყენეთ ნაგულისხმევი სახელი სახელის გარეშე საუბრებისთვის გამოსაყენებლად.", "Set the default notifications icon. The icon will be used as a profile image if the user doesn't have one.": "დააყენეთ ნაგულისხმევი შეტყობინებების ხატულა. ხატულა გამოყენებული იქნება პროფილის სურათად, თუ მომხმარებელს არ აქვს.", "Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.": "დააყენეთ ნაგულისხმევი სამუშაო საათები, როდესაც აგენტები ხელმისაწვდომი იქნება. ეს პარამეტრები ასევე გამოიყენება ყველა სხვა პარამეტრისთვის, რომელიც ეყრდნობა სამუშაო საათებს.", "Set the default username to use in bot messages and emails when the user doesn't have a name.": "დააყენეთ მომხმარებლის ნაგულისხმევი სახელი, რომ გამოიყენოთ ბოტის შეტყობინებებში და ელფოსტაში, როდესაც მომხმარებელს სახელი არ აქვს.", "Set the header appearance.": "Დააყენეთ სათაურის გარეგნობა.", "Set the maximum height of the tickets panel.": "დააყენეთ ბილეთების პანელის მაქსიმალური სიმაღლე.", "Set the multilingual plugin you're using, or leave it disabled if your site uses only one language.": "დააყენეთ მრავალენოვანი მოდული, რომელსაც იყენებთ, ან დატოვეთ ის გამორთული, თუ თქვენი საიტი იყენებს მხოლოდ ერთ ენას.", "Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes.": "Ავტომატურად დააყენეთ ხაზგარეშე სტატუსი, როდესაც აგენტი ან ადმინი უმოქმედო დარჩება ადმინისტრატორის ზონაში მინიმუმ 10 წუთის განმავლობაში.", "Set the position of the chat widget.": "დააყენეთ ჩატის ვიჯეტის პოზიცია.", "Set the primary color of the admin area.": "Დააყენეთ ადმინისტრატორის ზონის ძირითადი ფერი.", "Set the primary color of the chat widget.": "დააყენეთ ჩეთის ვიჯეტის ძირითადი ფერი.", "Set the secondary color of the admin area.": "Დააყენეთ ადმინისტრატორის არეალის მეორადი ფერი.", "Set the secondary color of the chat widget.": "დააყენეთ ჩეთის ვიჯეტის მეორადი ფერი.", "Set the tertiary color of the chat widget.": "დააყენეთ ჩეთის ვიჯეტის მესამეული ფერი.", "Set the title of the administration area.": "დააყენეთ ადმინისტრაციის არეალის სათაური.", "Set the title of the conversations panel.": "დააყენეთ საუბრების პანელის სათაური.", "Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty.": "დააყენეთ ოფისის საათების გრაფიკის UTC ოფსეტური. სწორი მნიშვნელობა შეიძლება იყოს უარყოფითი და ის ავტომატურად გენერირებულია, როგორც კი დააწკაპუნებთ ამ შეყვანის ველზე, თუ ის ცარიელია.", "Set which actions to allow agents.": "Დააყენეთ რომელი მოქმედებები დაუშვას აგენტებს.", "Set which actions to allow supervisors.": "Დააყენეთ რომელი ქმედებები დაუშვას ზედამხედველებს.", "Set which user details to send to the main channel. Add comma separated values.": "დააყენეთ მომხმარებლის რომელი დეტალები გაიგზავნოს მთავარ არხზე. დაამატეთ მძიმით გამოყოფილი მნიშვნელობები.", "Settings area": "Პარამეტრების არე", "settings information": "პარამეტრების ინფორმაცია", "Shop": "Მაღაზია", "Show": "ჩვენება", "Show a browser tab notification when a new message is received.": "ახალი შეტყობინების მიღებისას ბრაუზერის ჩანართის შეტყობინების ჩვენება.", "Show a desktop notification when a new message is received.": "ახალი შეტყობინების მიღებისას დესკტოპის შეტყობინების ჩვენება.", "Show a notification and play a sound when a new user is online.": "აჩვენეთ შეტყობინება და დაუკარით ხმა, როდესაც ახალი მომხმარებელი ონლაინ რეჟიმშია.", "Show a pop-up notification to all users.": "Ყველა მომხმარებლისთვის pop-up შეტყობინების ჩვენება.", "Show profile images": "პროფილის სურათების ჩვენება", "Show sender's name": "Აჩვენე გამგზავნის სახელი", "Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.": "აჩვენეთ აგენტების მენიუ დაფაზე და აიძულეთ მომხმარებელი აირჩიოს აგენტი საუბრის დასაწყებად.", "Show the articles panel on the chat dashboard.": "Აჩვენეთ სტატიების პანელი ჩეთის საინფორმაციო დაფაზე.", "Show the categories instead of the articles list.": "Სტატიების სიის ნაცვლად კატეგორიების ჩვენება.", "Show the follow up message when a visitor add an item to the cart. The message is sent only if the user has not provided an email yet.": "აჩვენეთ შემდგომი შეტყობინება, როდესაც ვიზიტორი დაამატებს ნივთს კალათაში. შეტყობინება იგზავნება მხოლოდ იმ შემთხვევაში, თუ მომხმარებელს ჯერ არ მიუწოდებია ელ.წერილი.", "Show the list of all Slack channels.": "აჩვენეთ ყველა Slack არხის სია.", "Show the profile image of agents and users within the conversation.": "აჩვენეთ აგენტებისა და მომხმარებლების პროფილის სურათი საუბრის ფარგლებში.", "Show the sender's name in every message.": "Აჩვენეთ გამგზავნის სახელი ყველა შეტყობინებაში.", "Single label": "ერთი ლეიბლი", "Single phone country code": "ერთი ტელეფონის ქვეყნის კოდი", "Site key": "Საიტის გასაღები", "Slug": "შლაგ", "Smart Reply": "Ჭკვიანი პასუხი", "Social share message": "სოციალური გაზიარების შეტყობინება", "Sort conversations by date": "Დაალაგეთ საუბრები თარიღის მიხედვით", "Sound": "ხმა", "Sound settings": "Ხმის პარამეტრები", "Sounds": "ხმები", "Sounds - admin": "Ხმები - ადმინ", "Source links": "Წყაროს ბმულები", "Speech recognition": "Სიტყვის აღიარება", "Spelling correction": "მართლწერის შესწორება", "Starred tag": "Ვარსკვლავით მონიშნული თეგი", "Start importing": "დაიწყე იმპორტი", "Store name": "Მაღაზიის სახელი", "Subject": "საგანი", "Subscribe": "Გამოწერა", "Subscribe users to your preferred newsletter service when they provide an email.": "გამოიწერეთ მომხმარებლები თქვენთვის სასურველი საინფორმაციო ბიულეტენის სერვისში, როდესაც ისინი მიაწვდიან ელ.წერილს.", "Subtract the offset value from the height value.": "გამოვაკლოთ ოფსეტური მნიშვნელობა სიმაღლის მნიშვნელობას.", "Success message": "წარმატების შეტყობინება", "Supervisors": "Ზედამხედველები", "Masi Chat path": "დამხმარე საბჭოს გზა", "Sync admin and staff accounts with Masi Chat. Staff users will be registered as agents, while admins as admins. Only new users will be imported.": "ადმინისტრატორის და პერსონალის ანგარიშების სინქრონიზაცია დამხმარე საბჭოსთან. პერსონალის მომხმარებლები დარეგისტრირდებიან როგორც აგენტები, ხოლო ადმინები როგორც ადმინები. მხოლოდ ახალი მომხმარებლების იმპორტი მოხდება.", "Sync all contacts of all clients with Masi Chat. Only new contacts will be imported.": "ყველა კლიენტის ყველა კონტაქტის სინქრონიზაცია დამხმარე საბჭოსთან. მხოლოდ ახალი კონტაქტების იმპორტი მოხდება.", "Sync all users with Masi Chat. Only new users will be imported.": "ყველა მომხმარებლის სინქრონიზაცია დამხმარე საბჭოსთან. მხოლოდ ახალი მომხმარებლების იმპორტი მოხდება.", "Sync all WordPress users with Masi Chat. Only new users will be imported.": "WordPress-ის ყველა მომხმარებლის სინქრონიზაცია დამხმარე საბჭოსთან. მხოლოდ ახალი მომხმარებლების იმპორტი მოხდება.", "Sync knowledge base articles with Masi Chat. Only new articles will be imported.": "ცოდნის ბაზის სტატიების სინქრონიზაცია დამხმარე საბჭოსთან. მოხდება მხოლოდ ახალი სტატიების იმპორტი.", "Sync mode": "Სინქრონიზაციის რეჟიმი", "Synchronization": "სინქრონიზაცია", "Synchronize": "Სინქრონიზაცია", "Synchronize customers, enable ticket and chat support for subscribers only, view subscription plans in the admin area.": "კლიენტების სინქრონიზაცია, ჩართეთ ბილეთების და ჩეთის მხარდაჭერა მხოლოდ აბონენტებისთვის, იხილეთ გამოწერის გეგმები ადმინისტრატორის ზონაში.", "Synchronize emails": "ელ.ფოსტის სინქრონიზაცია", "Synchronize Entities": "Entities სინქრონიზაცია", "Synchronize Entities now": "სინქრონიზაცია Entities ახლავე", "Synchronize now": "Სინქრონიზაცია ახლავე", "Synchronize users": "მომხმარებლების სინქრონიზაცია", "Synchronize your customers in real-time, chat with them and boost their engagement, or provide a better and faster support.": "მოახდინეთ თქვენი მომხმარებლების სინქრონიზაცია რეალურ დროში, ესაუბრეთ მათ და გაზარდეთ მათი ჩართულობა, ან უზრუნველყოთ უკეთესი და სწრაფი მხარდაჭერა.", "Synchronize your Messenger and Instagram accounts.": "Თქვენი Messenger და Instagram ანგარიშების სინქრონიზაცია.", "Synchronize your Perfex customers in real-time and let them contact you via chat! View profile details, proactively engage them, and more.": "მოახდინეთ თქვენი Perfex-ის მომხმარებლების სინქრონიზაცია რეალურ დროში და მიეცით მათ დაკავშირება ჩეთის საშუალებით! პროფილის დეტალების ნახვა, პროაქტიულად ჩართვა და სხვა.", "Synchronize your WhatsApp Cloud API account.": "Თქვენი WhatsApp Cloud API ანგარიშის სინქრონიზაცია.", "System requirements": "Სისტემის მოთხოვნები", "Tags": "Ტეგები", "Tags settings": "Ტეგების პარამეტრები", "Template default language": "შაბლონის ნაგულისხმევი ენა", "Template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "მომხმარებლისთვის გაგზავნილი ელფოსტის შაბლონი, როდესაც აგენტი პასუხობს. შეგიძლიათ გამოიყენოთ ტექსტი, HTML და შემდეგი გაერთიანების ველები: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Template for the email sent to the user when a new conversation is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.": "Მომხმარებლისთვის გაგზავნილი ელფოსტის შაბლონი ახალი საუბრის შექმნისას. შეგიძლიათ გამოიყენოთ ტექსტი, HTML და შემდეგი გაერთიანების ველები: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.", "Template for the email sent to the user when a new conversation or ticket is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}.": "მომხმარებლისთვის გაგზავნილი ელფოსტის შაბლონი ახალი საუბრის ან ბილეთის შექმნისას. შეგიძლიათ გამოიყენოთ ტექსტი, HTML და შემდეგი გაერთიანების ველები: {conversation_url_parameter}, {user_name}, {message}, {attachments}.", "Template languages": "Შაბლონის ენები", "Template name": "შაბლონის სახელი", "Template of the admin notification email. You can use text, HTML, and the following merge field and more: {carts}. Enter the email you want to send notifications to in the email address field.": "ადმინისტრატორის შეტყობინების ელფოსტის შაბლონი. შეგიძლიათ გამოიყენოთ ტექსტი, HTML და შემდეგი შერწყმის ველი და სხვა: {carts}. ელ.ფოსტის მისამართის ველში შეიყვანეთ ელფოსტა, რომელზეც გსურთ შეტყობინებების გაგზავნა.", "Template of the email sent to the customer after a product has been removed from the cart. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "მომხმარებლისთვის გაგზავნილი ელ.ფოსტის შაბლონი პროდუქტის კალათიდან ამოღების შემდეგ. შეგიძლიათ გამოიყენოთ ტექსტი, HTML და შემდეგი გაერთიანების ველები და სხვა: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the first notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "პირველი შეტყობინების ელფოსტის შაბლონი. შეგიძლიათ გამოიყენოთ ტექსტი, HTML და შემდეგი გაერთიანების ველები და სხვა: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the second notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "მეორე შეტყობინების ელფოსტის შაბლონი. შეგიძლიათ გამოიყენოთ ტექსტი, HTML და შემდეგი გაერთიანების ველები და სხვა: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the waiting list notification email. You can use text, HTML, and the following merge field and more: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.": "ლოდინის სიის შეტყობინების ელფოსტის შაბლონი. შეგიძლიათ გამოიყენოთ ტექსტი, HTML და გაერთიანების შემდეგი ველი და სხვა: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.", "Terms link": "პირობების ბმული", "Tertiary color": "მესამეული ფერი", "Test Slack": "ტესტი Slack", "Test template": "Ტესტის შაბლონი", "Text": "ტექსტი", "Text message fallback": "ტექსტური შეტყობინების სარეზერვო", "Text message notifications": "ტექსტური შეტყობინების შეტყობინებები", "Text messages": "Ტექსტური შეტყობინება", "The product is not in the cart.": "პროდუქტი არ არის კალათაში.", "The workspace name you are using to synchronize Slack.": "Workspace სახელი, რომელსაც იყენებთ Slack-ის სინქრონიზაციისთვის.", "This is your main Slack channel ID, which is usually the #general channel. You will get this code by completing the Slack synchronization.": "ეს არის თქვენი მთავარი Slack არხის ID, რომელიც ჩვეულებრივ არის #ზოგადი არხი. თქვენ მიიღებთ ამ კოდს Slack სინქრონიზაციის დასრულებით.", "This returns the Masi Chat path of your server.": "Ეს აბრუნებს თქვენი სერვერის <PERSON><PERSON> გზას.", "This returns your Masi Chat URL.": "Ეს აბრუნებს თქვენს <PERSON><PERSON> URL-ს.", "Ticket custom fields": "ბილეთის მორგებული ველები", "Ticket email": "ბილეთის ელ.წერილი", "Ticket field names": "ბილეთების ველების სახელები", "Ticket fields": "ბილეთების ველები", "Ticket only": "მხოლოდ ბილეთი", "Ticket products selector": "ბილეთების პროდუქტების ამომრჩევი", "Title": "სათაური", "Top": "ზედა", "Top bar": "Ზედა ბარი", "Training via cron job": "Ტრენინგი cron job-ით", "Transcript": "Ტრანსკრიფცია", "Transcript settings.": "ტრანსკრიპტის პარამეტრები.", "Trigger": "გამომწვევი", "Trigger the Dialogflow Welcome Intent for new visitors when the welcome message is active.": "გააქტიურეთ Dialogflow Welcome Intent ახალი ვიზიტორებისთვის, როცა მისასალმებელი შეტყობინება აქტიურია.", "Troubleshoot": "Პრობლემების მოგვარება", "Troubleshoot problems": "Პრობლემების მოგვარება", "Twilio settings": "<PERSON><PERSON><PERSON> პარამეტრები", "Twilio template": "<PERSON><PERSON><PERSON> შაბლონი", "Unsubscribe": "Გამოწერის გაუქმება", "Upload attachments to Amazon S3.": "Ატვირთეთ დანართები Amazon S3-ზე.", "Usage Limit": "Გამოყენების ლიმიტი", "Use this option to change the PWA icon. See the docs for more details.": "Გამოიყენეთ ეს პარამეტრი PWA ხატის შესაცვლელად. დამატებითი ინფორმაციისთვის იხილეთ დოკუმენტები.", "User details": "მომხმარებლის დეტალები", "User details in success message": "მომხმარებლის დეტალები წარმატების შეტყობინებაში", "User email notifications": "მომხმარებლის ელ.ფოსტის შეტყობინებები", "User login form information.": "Მომხმარებლის შესვლის ფორმის ინფორმაცია.", "User message template": "მომხმარებლის შეტყობინების შაბლონი", "User name as title": "მომხმარებლის სახელი, როგორც სათაური", "User notification email": "მომხმარებლის შეტყობინების ელფოსტა", "User registration form information.": "მომხმარებლის რეგისტრაციის ფორმის ინფორმაცია.", "User roles": "მომხმარებლის როლები", "User system": "მომხმარებლის სისტემა", "Username": "მომხმარებლის სახელი", "Users and agents": "მომხმარებლები და აგენტები", "Users area": "Მომხმარებელთა არეალი", "Users only": "მხოლოდ მომხმარებლები", "Users table additional columns": "მომხმარებლები ცხრილის დამატებით სვეტებს", "UTC offset": "UTC ოფსეტი", "Variables": "Ცვლადები", "View channels": "არხების ნახვა", "View unassigned conversations": "მინიჭებული მიმოწერების ნახვა", "Visibility": "ხილვადობა", "Visitor default name": "ვიზიტორის ნაგულისხმევი სახელი", "Visitor name prefix": "ვიზიტორის სახელის პრეფიქსი", "Volume": "Მოცულობა", "Volume - admin": "Მოცულობა - ადმინ", "Waiting list": "ლოდინის სია", "Waiting list - Email": "ლოდინის სია - ელ", "Webhook URL": "Webhook URL", "Webhooks": "ვებჰუკები", "Webhooks are information sent in background to a unique URL defined by you when something happens.": "Webhooks არის ინფორმაცია, რომელიც იგზავნება თქვენს მიერ განსაზღვრულ უნიკალურ URL-ზე, როდესაც რაღაც ხდება.", "Website": "საიტი", "WeChat settings": "WeChat პარამეტრები", "Welcome message": "მისასალმებელი შეტყობინება", "Whmcs admin URL": "Whmcs ადმინისტრატორის URL", "Whmcs admin URL. Ex. https://example.com/whmcs/admin/": "Whmcs ადმინისტრატორის URL. მაგ. https://example.com/whmcs/admin/", "WordPress registration": "WordPress რეგისტრაცია", "Yes, we ship in": "დიახ, ჩვენ ვაგზავნით", "You haven't placed an order yet.": "შეკვეთა ჯერ არ გაგიკეთებიათ.", "You will get this code by completing the Dialogflow synchronization.": "თქვენ მიიღებთ ამ კოდს Dialogflow სინქრონიზაციის დასრულებით.", "You will get this code by completing the Slack synchronization.": "თქვენ მიიღებთ ამ კოდს Slack სინქრონიზაციის დასრულებით.", "You will get this information by completing the synchronization.": "ამ ინფორმაციას მიიღებთ სინქრონიზაციის დასრულებით.", "Your cart is empty.": "Თქვენი კალათა ცარიელია.", "Your turn message": "შენი რიგის შეტყობინება", "Your username": "Შენი მომხმარებლის სახელი", "Your WhatsApp catalogue details.": "Თქვენი WhatsApp კატალოგის დეტალები.", "Zendesk settings": "Zendesk პარამეტრები", "Activate the Right-To-Left (RTL) reading layout for the admin area.": "Ადმინისტრაციული ზონისთვის გაააქტიურეთ მარჯვნიდან მარცხნივ (RTL) წაკითხვის განლაგება.", "Activate the Right-To-Left (RTL) reading layout.": "Გაააქტიურეთ მარჯვნიდან მარცხნივ (RTL) წაკითხვის განლაგება.", "Disable chatbot store integration": "Ჩატბოტების მაღაზიის ინტეგრაციის გამორთვა", "Enable logs": "Ჟურნალების ჩართვა", "Hide note information": "Შენიშვნის ინფორმაციის დამალვა", "Manage the notes settings.": "Შენიშვნების პარამეტრების მართვა.", "Notes settings": "Შენიშვნების პარამეტრები", "One conversation per user": "Ერთი საუბარი თითო მომხმარებელზე", "RTL": "Მარჯვნივ", "Stop the chatbot from directly accessing your store to provide answers.": "Შეაჩერეთ ჩატბოტი თქვენს მაღაზიაში პირდაპირ წვდომას პასუხების გასაცემად."}