<?php

/*
 * ==========================================================
 * ZENDESK APP
 * ==========================================================
 *
 * Zendesk app. © 2017-2025 masichat.com. All rights reserved.
 *
 * 1.
 * 2.
 * 3.
 *
 */

define('MC_ZENDESK', '1.0.0');

function mc_zendesk_get_conversation_details($user_id, $conversation_id = false, $zendesk_id = false, $email = false, $phone = false) {
    $code = '<h3><img src="' . MC_URL . '/media/apps/zendesk.svg"/> Zendesk</h3>';
    $sync = false;
    if (!$zendesk_id) {
        $zendesk_id = mc_get_user_extra($user_id, 'zendesk-id');
        if (!$zendesk_id) {
            $zendesk_id = mc_zendesk_curl('users/search.json?query=' . ($email ? 'email:' . $email : 'phone:' . str_replace('+', '', $phone)));
            if (!isset($zendesk_id['users'])) return $zendesk_id;
            if ($zendesk_id && count($zendesk_id)) {
                $zendesk_id = $zendesk_id[0]['id'];
                mc_add_new_user_extra($user_id, ['zendesk-id' => [$zendesk_id, 'Zendesk ID']]);
            }
        }
    }
    if ($zendesk_id) {
        $tickets = [];
        $code .= '<div class="mc-list-items mc-list-links">';
        $tickets = mc_zendesk_curl('users/' . $zendesk_id . '/tickets/requested');
        if (isset($tickets['tickets'])) {
            $tickets = $tickets['tickets'];
        } else {
            mc_update_user_value($user_id, 'zendesk-id', false);
            $tickets = [];
        }
        $count = count($tickets);
        if ($count) {
            $url = 'https://' . mc_get_multi_setting('zendesk', 'zendesk-domain') . '.zendesk.com/agent/tickets/';
            for ($i = 0; $i < count($tickets); $i++) {
                $ticket = $tickets[$i];
                $status = substr($ticket['status'], 0, 1);
                $sync_2 = mc_isset($ticket, 'external_id') == $conversation_id;
                if ($sync_2) $sync = true;
                $code .= '<a href="' . $url . $ticket['id'] . '" target="_blank" data-id="' . $ticket['id'] . '"' . ($sync_2 ? ' class="mc-zendesk-sync"' : '') . '><span><i class="mc-zendesk-status-' . $status. '">' . strtoupper($status) . '</i>' . $ticket['description'] . '</span><span>#' . $ticket['id'] . ' <span class="mc-zendesk-date">' . str_replace(['T', 'Z'], [' ', ''], $ticket['updated_at']) . '</span></span>' . ($sync_2 ? '<i id="mc-zendesk-update-ticket" class="mc-icon-refresh"></i>' : '') . '</a>';
            }
        }
        $code .= '</div>';
    }
    return $code . ($sync ? '' : '<a id="mc-zendesk-btn" class="mc-btn">' . mc_('Send to Zendesk') . '</a>');
}

function mc_zendesk_create_ticket($conversation_id) {
    $zendesk_ticket_id = false;
    if (is_numeric($conversation_id)) {
        $messages = mc_get_conversation(false, $conversation_id)['messages'];
    } else if (is_array($conversation_id)) {
        $messages = $conversation_id[2];
        $zendesk_ticket_id = $conversation_id[0];
        $conversation_id = $conversation_id[1];
    }
    $count = count($messages);
    if ($count) {
        $errors = [];
        $zendesk_ids = [];
        for ($i = 0; $i < $count; $i++) {
            $message = $messages[$i];
            $user_id = $message['user_id'];

            // User
            $user = mc_get_user($message['user_id']);
            $zendesk_id = mc_isset($zendesk_ids, $user_id);
            if (!$zendesk_id) {
                $zendesk_id = mc_get_user_extra($user['id'], 'zendesk-id');
                if ($zendesk_id) $zendesk_ids[$user['id']] = $zendesk_id;
            }
            if (!$zendesk_id) {
                $user_details = mc_zendesk_get_user_array($user['id']);
                $response = mc_zendesk_curl('users/create_or_update', ['user' => $user_details], 'POST');
                if ($response && isset($response['user'])) {
                    $zendesk_id = $response['user']['id'];
                    mc_add_new_user_extra($user['id'], ['zendesk-id' => [$zendesk_id, 'Zendesk ID']]);
                    $zendesk_ids[$user['id']] = $zendesk_id;
                }
            }

            // Message
            $attachments = $message['attachments'];
            $query = mc_zendesk_get_ticket_array($conversation_id, $message['message'], $attachments, $zendesk_ids[$user_id]);
            if ($zendesk_ticket_id) {
                $query['comment']['author_id'] = $zendesk_ids[$user_id];
                unset($query['requester_id']);
                unset($query['external_id']);
                $response = mc_zendesk_curl('tickets/' . $zendesk_ticket_id, ['ticket' => $query], 'PUT');
            } else {
                $response = mc_zendesk_curl('tickets', ['ticket' => $query], 'POST');
                $zendesk_ticket_id = $response['ticket']['id'];
            }
            if (!$response || !isset($response['ticket'])) {
                array_push($errors, $response);
            }
        }
        return count($errors) ? $errors : true;
    }
    return false;
}

function mc_zendesk_update_ticket($conversation_id, $zendesk_ticket_id) {
    $comments_count = mc_isset(mc_zendesk_curl('tickets/' . $zendesk_ticket_id . '/comments/count'), 'count');
    if ($comments_count) {
        $comments_count = $comments_count['value'];
        $messages = mc_get_conversation(false, $conversation_id)['messages'];
        if (count($messages) > $comments_count) {
            return mc_zendesk_create_ticket([$zendesk_ticket_id, $conversation_id, array_slice($messages, $comments_count)]);
        }
    }
    return false;
}

function mc_zendesk_upload($url) {
    $path = substr($url, strrpos(substr($url, 0, strrpos($url, '/')), '/'));
    return mc_zendesk_curl('uploads?filename=' . basename($path), file_get_contents(mc_upload_path() . $path), 'UPLOAD');
}

function mc_zendesk_curl($url_part, $post_fields = '', $type = 'GET') {
    $settings = mc_get_setting('zendesk');
    $is_upload = $type == 'UPLOAD';
    $header = ['Authorization: Basic ' . base64_encode($settings['zendesk-email'] . '/token:' . $settings['zendesk-key']), $is_upload ? 'Content-Type: application/binary' : 'Content-Type: application/json'];
    $response = mc_curl('https://' . $settings['zendesk-domain'] . '.zendesk.com/api/v2/' . $url_part, $post_fields ? ($is_upload ? $post_fields : json_encode($post_fields, JSON_INVALID_UTF8_IGNORE | JSON_UNESCAPED_UNICODE)) : '', $header, $type);
    return $type == 'GET' || $type == 'PUT' ? json_decode($response, true) : $response;
}

function mc_zendesk_get_user_array($user_id) {
    $user = mc_get_user($user_id);
    $array = ['name' => $user['first_name'] . ' ' . $user['last_name'], 'role' => mc_is_agent($user) ? 'agent' : 'end-user', 'verified' => true];
    $phone = mc_get_user_extra($user['id'], 'phone');
    if ($user['email']) $array['email'] = $user['email'];
    if ($phone) $array['phone'] = $phone;
    return $array;
}

function mc_zendesk_get_ticket_array($conversation_id, $message, $attachments, $zendesk_id) {
    $query = ['external_id' => $conversation_id, 'comment' => ['body' => $message], 'requester_id' => $zendesk_id];
    if ($attachments) {
        $ids = [];
        $attachments = json_decode($attachments, true);
        for ($i = 0; $i < count($attachments); $i++) {
            $response_attachment = mc_isset(mc_zendesk_upload($attachments[$i][1]), 'upload');
            if ($response_attachment) {
                array_push($ids, $response_attachment['token']);
            }
        }
        if (count($ids)) {
            $query['comment']['uploads'] = $ids;
            if (empty($query['comment']['body'])) {
                $query['comment']['body'] = basename($attachments[0][1]);
            }
        }
    }
    return $query;
}

?>