﻿
/*
 * ==========================================================
 * TICKETS SCRIPT
 * ==========================================================
 *
 * Tickets App main Javascript file. © 2017-2025 masichat.com. All rights reserved.
 * 
 */

'use strict';

(function ($) {

    var main;
    var main_panel;
    var conversation_area;
    var panel;
    var editor;
    var active_panel;
    var left_conversations;
    var cache_agents = {};
    var cache_account = {};
    var main_title;
    var agent_profile;
    var user_profile;
    var width;
    var mobile = $(window).width() < 426;
    var recaptcha = false;

    /*
    * ----------------------------------------------------------
    * # FUNCTIONS
    * ----------------------------------------------------------
    */

    var MCTickets = {

        // Display the conversation area or a panel
        showPanel: function (name = '', title = false) {
            let previous = active_panel;
            active_panel = name;
            main.addClass('mc-panel-active mc-load').removeClass('mc-panel-form').attr('data-panel', name);
            if (recaptcha) {
                recaptcha.hide();
                }
            switch (name) {
                case 'privacy':
                    MCF.ajax({
                        function: 'get-block-setting',
                        value: 'privacy'
                    }, (response) => {
                        setTitle(mc_(response['title']));
                        panel.append(`<div class="mc-privacy mc-init-form" data-decline="${mc_(response['decline'].replace(/"/g, ''))}"><div class="mc-text">${mc_(response['message'])}</div>` + (response['link'] != '' ? `<a target="_blank" href="${response['link']}">${mc_(response['link-name'])}</a>` : '') + `<div class="mc-buttons"><a class="mc-btn mc-approve">${mc_(response['btn-approve'])}</a><a class="mc-btn mc-decline">${mc_(response['btn-decline'])}</a></div></div>`);
                    });
                    this.showSidePanels(false);
                    break;
                case 'articles':
                    setTitle(mc_(title == false ? 'Articles' : title));
                    this.showSidePanels(false);
                    break;
                case 'edit-profile':
                case 'login':
                case 'registration':
                    let is_edit_profile = name == 'edit-profile';
                    this.showSidePanels(false);
                    main.addClass('mc-panel-form');
                    if (name in cache_account) {
                        panel.html(cache_account[name]);
                        setTitle(mc_(panel.find('.mc-top').html()));
                    } else {
                        MCF.ajax({
                            function: 'get-rich-message',
                            name: (is_edit_profile ? 'registration' : name) + '-tickets'
                        }, (response) => {
                            panel.html(response);
                            let title = panel.find('.mc-top').html();
                            if (is_edit_profile) {
                                panel.find('.mc-top').html(mc_('Edit profile'));
                            }
                            setTitle(mc_(title));
                            setTimeout(function () {
                                setTitle(mc_(title));
                            }, 300);
                            panel.find('.mc-link-area').insertAfter('.mc-buttons');
                            panel.find('.mc-info').insertBefore('.mc-buttons');
                            cache_account[name] = panel.html();
                        });
                        panel.html('<div class="mc-loading"></div>');
                    }
                    break;
                case 'new-ticket':
                    let names = { 'title': 'Title', 'message': 'Message', 'panel': 'Create a new ticket', 'button': 'Create a new ticket' };
                    this.showSidePanels(false);
                    if (MCF.setting('tickets_names')) {
                        let names_new = MCF.setting('tickets_names');
                        for (var key in names_new) {
                            if (names_new[key]) names[key.replace('tickets-names-', '')] = names_new[key];
                        }
                    }
                    setTitle(mc_(names.panel));
                    panel.html(`<div class="mc-info"></div><div class="mc-input mc-input-text mc-ticket-title"><span>${mc_(names.title)}</span><input type="text" required></div>${main.find('.mc-ticket-fields').html()}<div class="mc-input mc-editor-cnt"><span>${mc_(names.message)}</span></div><div class="mc-btn mc-icon mc-create-ticket"><i class="mc-icon-plus"></i>${mc_(names.button)}</div>`);
                    main_panel.find('.mc-editor-cnt').append(editor);
                    if (MCF.setting('tickets_recaptcha')) {
                        if (recaptcha) {
                            recaptcha.show();
                        } else {
                            $.getScript('https://www.google.com/recaptcha/api.js?render=' + MCF.setting('tickets_recaptcha'), () => {
                                setTimeout(() => { recaptcha = $('.grecaptcha-badge') }, 500);
                            });
                        }
                    }
                    break;
                default:
                    this.showSidePanels(true);
                    if (previous == 'new-ticket') {
                        editor.find('textarea').val('');
                        editor.mcActive(false).removeClass('mc-error');
                        conversation_area.after(editor);
                    }
                    panel.html('');
                    main.removeClass('mc-panel-active mc-load').removeAttr('data-panel');
                    setConversationName(MCChat.conversation);
                    break;
            }
            MCF.event('MCPanelActive', name);
            setTimeout(function () {
                main.removeClass('mc-load');
            }, 300);
        },

        // Display or hide the side panels
        showSidePanels: function (show = true) {
            let button = main.find('.mc-btn-collapse');
            setCollapsing();
            if (!show || width > 800) {
                let panels = main.find('.mc-panel-left,.mc-panel-right');
                if (show) {
                    panels.removeClass('mc-collapsed');
                } else {
                    panels.addClass('mc-collapsed');
                }
            } else if (width <= 800) {
                $(button).mcActive(true);
            }
            $(button).css('display', show ? '' : 'none');
        },

        // Get the agent details and display them
        setAgent: function (agent_id) {
            let label = main.find('.mc-agent-label').mcActive(false);
            MCChat.agent_id = agent_id;
            if (agent_id in cache_agents) {
                let agent = cache_agents[agent_id];
                $(agent_profile).setProfile(agent.name, agent.image).mcActive(true);
                MCChat.updateUsersActivity();
                if ('details' in agent) {
                    MCF.getLocationTimeString(agent.extra, (response) => {
                        $(label).html((agent.get('flag') ? `<img src="${MC_URL}/media/flags/${agent.get('flag')}">` : '<i class="mc-icon mc-icon-marker"></i>') + response).mcActive(true);
                    });
                }
            } else {
                MCF.ajax({
                    function: 'get-agent',
                    agent_id: agent_id
                }, (response) => {
                    if (response != false) {
                        cache_agents[agent_id] = new MCUser(response);
                        this.setAgent(agent_id);
                    }
                });
            }
        },

        // Activate a conversation
        activateConversation: function (conversation) {
            if (conversation instanceof MCConversation) {
                let last_agent = MCChat.lastAgent();
                let details = ['id', 'creation_time', 'last_update'];
                let code = '';

                // Activate the conversation
                this.selectConversation(conversation.id);
                setConversationName(conversation);
                main.find('.mc-panel-right .mc-scroll-area > div').mcActive(false);

                // Set the agent details
                if (last_agent) {
                    $(agent_profile).setProfile(last_agent['full_name'], last_agent['profile_image']);
                    this.setAgent(last_agent['user_id']);
                    setTimeout(() => { MCChat.updateUsersActivity() }, 300);
                } else {
                    $(agent_profile).mcActive(false);
                }

                // Set the ticket details
                if (conversation.get('department') != '') {
                    MCChat.getDepartmentCode(conversation.get('department'), (response) => {
                        let department = main.find('.mc-department');
                        $(department).html(`<span class="mc-title">${$(department).data('label')}</span>${response}`).mcActive(true);
                    });
                }
                for (var i = 0; i < details.length; i++) {
                    let values;
                    switch (details[i]) {
                        case 'id':
                            values = ['padlock', mc_('Ticket ID'), conversation.id];
                            break;
                        case 'creation_time':
                            values = ['calendar', mc_('Creation time'), MCF.beautifyTime(conversation.get('creation_time'), true)];
                            break;
                        case 'last_update':
                            values = ['reload', mc_('Last update'), MCF.beautifyTime(conversation.getLastMessage() ? conversation.getLastMessage().get('creation_time') : conversation.get('last_update_time'), true)];
                            break;
                    }
                    code += `<div data-id="${details[i]}"><i class="mc-icon mc-icon-${values[0]}"></i><span>${values[1]}</span><div>${values[2]}</div></div>`;
                }
                main.find('.mc-ticket-details').html(code);

                // Attachments
                let attachments = conversation.getAttachments();
                code = '';
                for (var i = 0; i < attachments.length; i++) {
                    code += `<a href="${attachments[i][1]}" target="_blank"><i class="mc-icon mc-icon-file"></i>${attachments[i][0]}</a>`;
                }
                main.find('.mc-conversation-attachments').html((code ? `<div class="mc-title">${mc_('Attachments')}</div>` : '') + code);

                // Miscellaneous
                conversation_area.mcLoading(false);
            } else {
                MCF.error('Value not of type MCConversation', 'activateConversation');
            }
        },

        // Apply the selected style to the active conversation
        selectConversation: function (conversation_id) {
            let conversation = left_conversations.find(`[data-conversation-id="${conversation_id}"]`);
            left_conversations.find('> li').mcActive(false);
            if (conversation.attr('data-conversation-status') == 1) {
                conversation.attr('data-conversation-status', 0);
            }
            conversation.find('[data-count]').remove();
            conversation.mcActive(true);
        },

        // Get the ID of the active conversation
        getActiveConversation: function (type = '') {
            let conversation = left_conversations.find(' > .mc-active');
            return conversation.length ? (type == 'ID' ? conversation.attr('data-conversation-id') : conversation) : false;
        },

        // Tickets welcome message
        welcome: function () {
            let message = MCF.setting('tickets_welcome_message');
            if (message && !MCF.storage('tickets-welcome')) {
                setTimeout(() => {
                    MCChat.sendMessage(MCF.setting('bot_id'), message);
                    MCF.storage('tickets-welcome', true);
                }, 1000);
            }
        },

        // Initialize the tickets area
        init: function () {

            main = $('body').find('.mc-tickets');
            main_panel = main.find(' > div > .mc-panel-main');
            panel = main_panel.find('.mc-panel');
            editor = main_panel.find('.mc-editor');
            main_title = main_panel.find(' > .mc-top .mc-title');
            left_conversations = main.find('.mc-user-conversations');
            conversation_area = main_panel.find('.mc-list');
            agent_profile = main.find('.mc-profile-agent');
            user_profile = main.find('.mc-panel-right > .mc-top .mc-profile');
            width = main.width();
            cache_agents[MCF.setting('bot_id')] = { name: MCF.setting('bot_name'), image: MCF.setting('bot_image') };
            ticketsInit();

            if (!main.length) {
                return;
            }
            if (MCF.setting('tickets_registration_required') && (!activeUser() || ['visitor', 'lead'].includes(activeUser().type))) {
                let redirect = MCF.setting('tickets_registration_redirect');
                if (redirect) {
                    document.location = redirect + (redirect.includes('?') ? '&' : '?') + 'mc=true';
                    return;
                } else {
                    main.addClass('mc-no-conversations');
                    MCTickets.showPanel(MCF.setting('tickets_default_form'));
                }
            } else {
                if (activeUser() && activeUser().conversations.length) {
                    if (!MCTickets.getActiveConversation()) {
                        MCChat.openConversation(MCF.getURL('conversation') ? MCF.getURL('conversation') : activeUser().conversations[0].id);
                    }
                } else {
                    main.addClass('mc-no-conversations');
                    if (MCF.setting('privacy') && !MCF.storage('privacy_approved')) {
                        MCTickets.showPanel('privacy');
                    } else if (!MCF.setting('tickets_disable_first')) {
                        MCTickets.showPanel('new-ticket');
                    } else {
                        setConversationName();
                    }
                }
            }
            let height = parseInt(MCF.null(main.data('height')) ? ($(window).height()) : main.data('height'));
            let height_offset = parseInt(MCF.null(main.data('offset')) ? 0 : main.data('offset'));
            if (width <= 800) {
                main.addClass('mc-800');
                main.find('.mc-panel-left,.mc-panel-right').addClass('mc-collapsed');
                main.find('.mc-btn-collapse').mcActive(true);
            } else if (width <= 1000) {
                main.addClass('mc-1000');
            } else if (width <= 1300) {
                main.addClass('mc-1300');
            }
            setUserProfile();
            main.removeClass('mc-loading').find('.mc-tickets-area').attr('style', `height: ${height - height_offset}px`);
            setTimeout(function () {
                main.removeClass('mc-load');
            }, 300);
            MCChat.startRealTime();
            MCF.event('MCTicketsInit');
        },

        // Triggered when a message is sent
        onMessageSent: function () {
            if (active_panel == 'new-ticket') {
                let title = main_panel.find('.mc-ticket-title input').val();
                MCChat.updateConversations();
                main.find('.mc-panel-right .mc-scroll-area > div').mcActive(false);
                main.find('.mc-conversation-attachments,.mc-ticket-details').html('');
                main.removeClass('mc-no-conversations');
                MCTickets.showPanel();
                setTitle(title);
            }
        },

        // Triggered when a new conversation is received
        onConversationReceived: function (conversation) {
            if (conversation.id == MCChat.conversation.id) {
                if (MCTickets.getActiveConversation('ID') != conversation.id) {
                    setTimeout(() => {
                        MCTickets.activateConversation(MCChat.conversation);
                        if (left_conversations.length) {
                            left_conversations.scrollTop(left_conversations[0].scrollHeight);
                        }
                    }, 300);
                }
            }
        },

        // Triggered when a new message is received
        onNewMessageReceived: function (message, conversation_id) {
            if (message instanceof MCMessage && conversation_id == MCChat.conversation.id) {
                let last_agent = MCChat.lastAgent();
                let code = MCChat.conversation.getCode();
                let active_conversation = MCTickets.getActiveConversation();
                if (active_conversation) {
                    $(active_conversation).html(code);
                } else {
                    left_conversations.append(`<li data-conversation-id="${MCChat.conversation.id}" class="mc-active">${code}</li>`).find(' > p').remove();
                }
                if (last_agent && MCF.isAgent(message.get('user_type')) && last_agent.id != message.get('user_id')) {
                    MCTickets.setAgent(message.get('user_id'));
                }
            }
        }
    }
    window.MCTickets = MCTickets;

    // Set overflow hidden for 1s
    function setCollapsing() {
        main.addClass('mc-collapsing');
        setTimeout(function () {
            main.removeClass('mc-collapsing');
        }, 1000);
    }

    // Access the global user variable
    function activeUser(value) {
        if (typeof value == 'undefined') {
            return window.mc_current_user;
        } else {
            window.mc_current_user = value;
        }
    }

    // Masi Chat js translations
    function mc_(string) {
        return MCF.translate(string);
    }

    // Set the profile box of the user
    function setUserProfile() {
        if (activeUser() != false) {
            $(user_profile).setProfile(activeUser().get('last_name').charAt(0) == '#' ? mc_('Account') : activeUser().name);
        }
    }

    // More
    function loading(element) {
        if ($(element).mcLoading()) return true;
        else $(element).mcLoading(true);
        return false;
    }

    function setConversationName(conversation = false) {
        let name = conversation && 'title' in conversation.details && !MCF.null(conversation.details['title']) ? conversation.get('title') : MCF.setting('tickets_conversation_name');
        setTitle(!name || name == -1 ? activeUser().name : name);
    }

    function submitTicketPartial() {
        let message = '';
        let settings = MCForm.getAll(panel);
        let department = 'department' in settings ? settings['department'][0] : null;
        let attachments = [];
        MCChat.clear();
        editor.mcActive(false);
        for (var key in settings) {
            if (settings[key][1] && settings[key][0]) {
                message += `*${mc_(settings[key][1])}*\n${key == 'department' ? panel.find('#department li.mc-active').html() : settings[key][0]}\n\n`;
            }
        }
        message += editor.find('textarea').val().trim();
        panel.find('.mc-attachments > div').each(function () {
            attachments.push([$(this).attr('data-name'), $(this).attr('data-value')]);
        });
        if (!activeUser()) {
            MCChat.addUserAndLogin(() => {
                MCChat.newConversation(2, -1, message, attachments, department, null, function () { MCTickets.welcome() });
            });
        } else {
            MCChat.newConversation(2, -1, message, attachments, department, null, function () { MCTickets.welcome() });
        }
    }

    function ticketsInit() {

        /*
        * ----------------------------------------------------------
        * # MISCELLANEOUS
        * ----------------------------------------------------------
        */

        main.on('click', '.mc-btn-collapse', function () {
            setCollapsing();
            main.find('.mc-panel-' + ($(this).hasClass('mc-left') ? 'left' : 'right')).toggleClass('mc-collapsed');
            $(this).toggleClass('mc-active');
        });

        editor.on('focus focusout', 'textarea', function () {
            $(this).parent().parent().toggleClass('mc-focus');
        });

        if (!mobile) {
            editor.on('click', '.mc-btn-emoji', function () {
                let settings = active_panel == 'new-ticket' ? [panel, 'padding-top', 415] : [main, 'margin-top', 335];
                if (editor.find('.mc-emoji').mcActive()) {
                    let offset_emoji = $(this).offset().top + $(settings[0])[0].scrollTop - window.scrollY;
                    let offset_tickets = main.offset().top - window.scrollY;
                    if (offset_emoji - offset_tickets < 380) {
                        $(settings[0]).css(settings[1], (settings[2] - (offset_emoji - offset_tickets)) + 'px');
                    }
                } else {
                    $(settings[0]).css(settings[1], '');
                }
            });
            editor.on('click', '.mc-emoji-list > ul > li', function () {
                let settings = active_panel == 'new-ticket' ? [panel, 'padding-top', 415] : [main, 'margin-top', 335];
                $(settings[0]).css(settings[1], '');
            });
        }

        /*
        * ----------------------------------------------------------
        * # MAIN PANEL
        * ----------------------------------------------------------
        */

        main_panel.on('click', '> .mc-top .mc-close', function () {
            MCTickets.showPanel();
        });

        /*
        * ----------------------------------------------------------
        * # CONVERSATION AREA
        * ----------------------------------------------------------
        */

        main_panel.on('click', '.mc-create-ticket', function () {
            let errors = false;
            editor.removeClass('mc-error');
            if (MCForm.errors(panel)) {
                MCForm.showErrorMessage(panel, 'Please fill in all the required fields.');
                errors = true;
            }
            if (!editor.find('textarea').val().trim() && !editor.hasClass('mc-audio-message-active')) {
                if (!errors) {
                    MCForm.showErrorMessage(panel, 'Please write a message.');
                }
                editor.addClass('mc-error');
                errors = true;
            }
            if (!errors && !MCChat.is_busy) {
                MCChat.busy(true);
                if (MCF.setting('tickets_recaptcha')) {
                    grecaptcha.ready(function () {
                        grecaptcha.execute(MCF.setting('tickets_recaptcha'), { action: 'submit' }).then(function (token) {
                            MCF.ajax({
                                function: 'recaptcha',
                                token: token
                            }, (response) => {
                                if (response === true) {
                                    submitTicketPartial();
                                    $('.grecaptcha-badge').hide();
                                } else MCChat.busy(false);
                            });
                        });
                    });
                    return;
                }
                submitTicketPartial();
            }
        });

        main.on('click', '.mc-new-ticket', function () {
            MCTickets.showPanel('new-ticket');
        });

        left_conversations.on('click', 'li', function () {
            MCChat.clear();
            MCTickets.selectConversation($(this).attr('data-conversation-id'));
            conversation_area.mcLoading(true);
            if (mobile) {
                main.find('.mc-panel-left').addClass('mc-collapsed');
            }
        });

        main.find('.mc-panel-left .mc-search-btn input').on('input', function () {
            let search = $(this).val();
            let button = $(this).prev();
            MCF.search(search, () => {
                if (search.length > 1) {
                    if (loading(button)) return;
                    MCF.ajax({
                        function: 'search-user-conversations',
                        search: search
                    }, (response) => {
                        button.mcLoading(false);
                        let conversations = [];
                        let count = response.length;
                        for (var i = 0; i < count; i++) {
                            if (!MCF.setting('tickets_close')) {
                                conversations.push(new MCConversation([new MCMessage(response[i])], response[i]));
                            }
                        }
                        left_conversations.html(count ? activeUser().getConversationsCode(conversations) : '<p>' + mc_('No results found.') + '</p>');
                    });
                } else {
                    MCChat.populateConversations();
                }
            });
        });

        main.on('click', '.mc-panel-left .mc-search-btn i', function () {
            MCF.searchClear(this, () => { MCChat.populateConversations() });
        });

        /*
        * ----------------------------------------------------------
        * # REGISTRATION AND LOGIN
        * ----------------------------------------------------------
        */

        panel.on('click', '.mc-login-area', function () {
            MCTickets.showPanel('login');
        });

        panel.on('click', '.mc-registration-area', function () {
            MCTickets.showPanel('registration');
        });

        main.on('click', '.mc-profile-menu [data-value="edit-profile"]', function () {
            MCTickets.showPanel('edit-profile');
        });

        main.on('click', '.mc-profile-menu [data-value="logout"]', function () {
            MCF.logout(false);
            MCTickets.showPanel('login');
        });

        panel.on('click', '> .mc-buttons .mc-submit', function () {
            if (!$(this).mcLoading()) {
                let settings = MCForm.getAll(panel);
                let settings_extra = MCForm.getAll(panel.find('.mc-form-extra'));
                let is_edit_profile = active_panel == 'edit-profile';
                for (var key in settings) {
                    settings[key] = settings[key][0];
                }
                if (MCForm.errors(panel)) {
                    MCForm.showErrorMessage(panel, MCForm.getRegistrationErrorMessage(panel));
                } else {
                    $(this).mcLoading(true);
                    settings.user_type = 'user';
                    MCF.ajax({
                        function: is_edit_profile || activeUser() ? 'update-user' : 'add-user-and-login',
                        settings: settings,
                        settings_extra: settings_extra
                    }, (response) => {
                        if (response && !MCF.errorValidation(response)) {
                            MCF.loginCookie(response[1]);
                            if (!activeUser()) {
                                activeUser(new MCUser(response[0]));
                                for (var key in settings_extra) {
                                    activeUser().setExtra(key, settings_extra[key][0]);
                                }
                                MCPusher.start();
                                MCChat.initChat();
                            } else {
                                for (var key in settings) {
                                    activeUser().set(key, settings[key][0]);
                                }
                                for (var key in settings_extra) {
                                    activeUser().setExtra(key, settings_extra[key][0]);
                                }
                            }
                            setUserProfile();
                            if (is_edit_profile) {
                                MCTickets.showPanel();
                            } else {
                                MCF.event('MCRegistrationForm', { user: settings });
                                MCF.event('MCNewEmailAddress', { name: activeUser().name, email: activeUser().get('email') });
                                MCTickets.showPanel('new-ticket');
                            }
                            if (MCF.setting('wp_registration') && 'email' in settings && 'password' in settings) {
                                console.log(settings);
                                MCApps.wordpress.ajax('wp_registration', { user_id: response[0].id, first_name: response[0].first_name, last_name: response[0].last_name, password: settings.password[0], email: settings.email[0] });
                            } else if (MCF.setting('wp_users_system') == 'wp') {
                                MCApps.wordpress.ajax('wp_login', { user: settings.email[0], password: settings.password[0] });
                            }
                        } else {
                            MCForm.showErrorMessage(panel, MCForm.getRegistrationErrorMessage(response, 'response'));
                        }
                        $(this).mcLoading(false);
                    });
                }
            }
        });

        panel.on('click', '.mc-submit-login', function () {
            MCF.loginForm(this, panel, (response) => {
                activeUser(new MCUser(response[0]));
                setUserProfile();
                MCChat.populateConversations((response) => {
                    if (response.length == 0) {
                        main.addClass('mc-no-conversations');
                        MCTickets.showPanel('new-ticket');
                    } else {
                        MCChat.openConversation(response[0].id);
                        MCTickets.showPanel();
                    }
                });
            });
        });
    }

    function setTitle(title) {
        $(main_title).html(title).mcActive(title);
    }
}(jQuery));