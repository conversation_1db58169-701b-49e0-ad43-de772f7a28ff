
/*
* 
* ==========================================================
* RESPONSIVE.SCSS
* ==========================================================
*
* Front-end responsive CSS. This file is imported only.
*
*/

@media (max-width: 464px) {
    body.mc-chat-open {
        overscroll-behavior-y: contain;
    }

    .mc-grid {
        display: block !important;

        > div + div, > a + a {
            margin-top: 15px;
        }
    }

    .mc-init-form-active .mc-header .mc-responsive-close-btn {
        display: none !important;
    }

    .mc-chat {

        .mc-label-date-top {
            top: 58px;
        }

        .mc-btn-emoji, &.mc-header-hidden .mc-header-main {
            display: none !important;
        }

        &.mc-header-hidden {

            .mc-header-main + .mc-list {
                padding-top: 0 !important;
                margin-top: 0 !important;
            }
        }

        &.mc-dashboard-active .mc-body .mc-scroll-area {
            height: 100%;
        }

        .mc-dashboard-end {
            display: block;
        }

        .mc-icon-close, .mc-bar-icons > div, .mc-search-btn > i, .mc-select, .mc-submit {
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
            user-select: none;
        }

        .mc-responsive-close-btn {
            display: none;
            position: fixed;
            width: 50px;
            height: 50px;
            right: 0;
            top: 0;
            text-align: right;
            z-index: 99995;

            &:before {
                font-size: 12px;
                line-height: 19px;
                z-index: 9;
                position: relative;
                top: 8px;
                right: 10px;
                color: #FFF;
            }
        }

        &.mc-active {
            .mc-chat-btn {
                display: none;
            }

            .mc-body {
                display: flex;
                max-height: 100% !important;
                right: 0;
                left: 0;
            }

            &.mc-dashboard-active .mc-responsive-close-btn, &.mc-queue-active .mc-responsive-close-btn, .mc-header-main > .mc-responsive-close-btn {
                display: block;
            }
        }

        &.mc-panel-active .mc-responsive-close-btn {
            display: none !important;
        }

        .mc-body {
            position: fixed;
            flex-direction: column;
            width: auto;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            border: none;
            border-radius: 0;
            box-shadow: none;
            animation: none !important;

            .mc-scroll-area {
                height: calc(100% - 55px);
                min-height: 0;

                .mc-header {
                    position: fixed;
                    left: 0;
                    right: 0;

                    .mc-dashboard-btn {
                        opacity: 1;
                        font-size: 12px;
                        right: 15px;
                    }

                    &.mc-header-main .mc-dashboard-btn {
                        width: 50px !important;
                        height: 50px !important;
                        top: 0;
                        right: 0;

                        &:before {
                            position: relative;
                            top: 6px;
                            right: 10px;
                        }
                    }

                    &.mc-header-panel {
                        padding: 5px 30px 5px 60px;

                        .mc-dashboard-btn {
                            left: 10px !important;
                            right: auto !important;

                            &:before {
                                content: "\74";
                            }
                        }
                    }

                    &:hover .mc-dashboard-btn {
                        left: 15px;
                    }
                }

                .mc-header-agent {
                    padding: 10px 15px;

                    + .mc-list {
                        min-height: calc(100% - 65px);
                        padding-top: 65px;

                        > .mc-notify-message {
                            top: 60px;
                        }
                    }

                    .mc-responsive-close-btn {
                        display: block;
                    }
                }
            }
        }

        .mc-popup-message {
            position: fixed;
            width: auto;
            left: 15px;
            right: 15px;
            bottom: 100px;

            > img {
                display: none;
            }

            .mc-icon-close {
                width: 50px;
                height: 50px;
                top: 0;
                right: 0;
                min-width: 0;
                border: none;

                &:before {
                    right: 15px;
                    top: 15px;
                    position: absolute;
                }
            }
        }

        .mc-popup .mc-header .mc-search-btn > input {
            width: 200px;
            box-sizing: border-box;
            min-width: 0;
        }

        .mc-editor {
            .mc-textarea {
                padding-right: 80px;
            }

            &.mc-disabled-1 .mc-textarea {
                padding-right: 55px;
            }

            &.mc-disabled-2 .mc-textarea {
                padding-right: 15px;
            }

            &.mc-active .mc-textarea {
                padding-right: 50px !important;
            }

            .mc-attachments > div > i {
                opacity: 1;
            }

            .mc-bar div.mc-btn-attachment {
                margin-right: 0;
            }
        }

        &.mc-notify-active .mc-body .mc-scroll-area .mc-header-agent + .mc-list {
            padding-top: 185px;
        }
    }

    .mc-list > div.mc-label-date span, .mc-label-date-top span {
        font-size: 14px;
        line-height: 28px;
    }

    .mc-list {
        .mc-time {
            font-size: 13px;
            line-height: 19px;
        }
    }

    .mc-editor textarea, .mc-list .mc-message, .mc-list .mc-message a, .mc-chat .mc-dashboard > div > .mc-title, .mc-chat .mc-dashboard > div > .mc-top,
    .mc-chat .mc-dashboard > div > div > .mc-title, .mc-chat .mc-dashboard > div > div > .mc-top, .mc-chat .mc-init-form > .mc-title, .mc-chat .mc-init-form > .mc-top,
    .mc-chat .mc-init-form > div > .mc-title, .mc-chat .mc-init-form > div > .mc-top, .mc-input > input, .mc-input > select, .mc-input > textarea, .mc-articles > div > div,
    .mc-articles > div > span, .mc-article .mc-content, .mc-chat .mc-departments-list > div span, .mc-chat .mc-agents-list > div span, .mc-chat .mc-channels-list > div span,
    .mc-rich-message .mc-top, .mc-rich-message .mc-title, .mc-rich-message .mc-text, .mc-user-conversations > li > div .mc-message, .mc-user-conversations > li > div {
        font-size: 16px;
        line-height: 25px;
    }

    .mc-chat .mc-header-agent .mc-profile .mc-name {
        font-size: 16px;
        line-height: 22px;
    }

    .mc-chat .mc-header-agent .mc-profile .mc-status, .mc-chat .mc-dashboard > div > .mc-btn, .mc-btn-text, .mc-rating > span, .mc-rating i span {
        font-size: 15px;
    }

    .mc-chat .mc-scroll-area .mc-header .mc-text {
        font-size: 15px;
        line-height: 24px;
    }

    .mc-rating i {
        font-size: 17px;
    }

    .mc-editor .mc-attachments > div {
        font-size: 12px;
    }

    .mc-articles > div > span {
        height: 22px;
    }

    .mc-lightbox {
        left: 0;
        right: 0;
        width: auto;
        transform: translateY(-50%);
        margin: 0 15px;
    }

    .mc-lightbox-overlay i {
        background: #000;
        border-radius: 4px;
    }

    .mc-select ul {
        z-index: 8;
    }

    .mc-articles-page {
        flex-direction: column-reverse;

        > div {
            min-width: 0 !important;
            width: auto !important;
        }

        .mc-panel-side {
            border-left: none;
            border-bottom: 1px solid #d4d4d4;
            border-radius: 0;
        }
    }

    .mc-rtl {

        .mc-editor .mc-textarea {
            padding-right: 15px;
            padding-left: 55px;
        }

        .mc-editor.mc-active .mc-textarea {
            padding-right: 15px !important;
            padding-left: 50px !important;
        }
    }

    .mc-overlay-panel {
        border-radius: 0;
    }

    .mc-input-select-input > div.mc-select-phone ul {
        height: 150px !important;
    }
}
