{"{product_name} has no {product_attribute_name} variants.": "{product_name}-nak nincs {product_attribute_name}-os v<PERSON><PERSON>ozata.", "360dialog settings": "360 párbesz<PERSON>d<PERSON><PERSON> be<PERSON>llí<PERSON>ai", "360dialog template": "360 párbeszédablak sablon", "Abandoned cart notification": "Elhagyott k<PERSON><PERSON>r <PERSON>", "Abandoned cart notification - Admin email": "Elhagyott k<PERSON><PERSON>r <PERSON> – Adminisztrátori e-mail", "Abandoned cart notification - First email": "Elhagyott kos<PERSON>r <PERSON> – Első e-mail", "Abandoned cart notification - Second email": "Elhagyott k<PERSON><PERSON>r <PERSON> – Második e-mail", "Accept button text": "Gomb szövegének elfogadása", "Account SID": "Fiók SID", "Activate the Slack integration.": "Aktiválja a Slack integrációt.", "Activate the Zendesk integration": "Aktiválja a Zendesk integrációt", "Activate this option if you don't want to translate the settings area.": "Aktiválja ezt az opciót, ha nem szeretné le<PERSON> a beállításokat.", "Active": "Aktív", "Active - admin": "Aktív - admin", "Active eCommerce CMS URL. Ex. https://shop.com/": "Active eCommerce CMS URL. Volt. https://shop.com/", "Active eCommerce URL": "Active eCommerce URL", "Active for agents": "Aktív ügynökök számára", "Active for users": "Aktív a felhasználók számára", "Active webhooks": "Aktív webhookok", "Add a delay (ms) to the bot's responses. Default is 2000.": "Adjon hozzá késleltetést (ms) a bot válaszaihoz. Az alapértelmezett 2000.", "Add and manage additional support departments.": "További támogatási osztályok hozzáadása és kezelése.", "Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \\n to do a line break.": "Mentett válaszok hozzáadása és kezelése, amelyeket az ügynökök használhatnak a csevegésszerkesztőben. A mentett válaszokat a # karakter beír<PERSON>, majd a válasz nevének és szóközzel írhatja ki. Használja \\n sortörést.", "Add and manage tags.": "Címkék hozzáadása és kezelése.", "Add comma separated WordPress user roles. The Masi Chat administration area will be available for new roles, in addition to the default one: editor, administrator, author.": "Vesszővel elválasztott WordPress felhasználói szerepkörök hozzáadása. A Támogatási Tanács adminisztrációs területe új szerepkörök számára lesz elérhető, az alapértelmezetten kívül: szerkesztő, rendszergazda, szerző.", "Add custom fields to the new ticket form.": "Adjon hozzá egyéni mezőket az új jegyűrlaphoz.", "Add custom fields to the user profile details.": "Adjon hozzá egyéni mezőket a felhasználói profil részleteihez.", "Add Intents": "Intents hozzáadása", "Add Intents to saved replies": "Intents hozzáadása a mentett válaszokhoz", "Add WhatsApp phone number details here.": "Adja meg a WhatsApp telefonszám részleteit itt.", "Adjust the chat button position. Values are in px.": "<PERSON><PERSON><PERSON><PERSON>a be a chat gomb pozícióját. Az értékek px-<PERSON>.", "Admin icon": "Adminisztr<PERSON><PERSON>", "Admin IDs": "Rendszergazdai azonosítók", "Admin login logo": "<PERSON><PERSON> be<PERSON><PERSON> logó", "Admin login message": "<PERSON><PERSON> be<PERSON>lentkezési üzenet", "Admin notifications": "Adminisztrátori értesítések", "Admin title": "<PERSON><PERSON> c<PERSON>", "Agent area": "Ügynöki terület", "Agent details": "Az ügynök adatai", "Agent email notifications": "Ügynöki e-mail értesítések", "Agent ID": "Ügynökazonosító", "Agent linking": "Ügynök összekapcsolása", "Agent message template": "Ügynök üzenet sablon", "Agent notification email": "Ügynök értesítési e-mail", "Agent privileges": "Ügynöki jogosultságok", "Agents": "Ügynökök", "Agents and admins tab": "Ügynökök és rendszergazdák lap", "Agents menu": "Ügynökök menü", "Agents only": "Csak ügynökök", "All": "Összes", "All channels": "<PERSON><PERSON> c<PERSON>a", "All messages": "Minden üzenet", "All questions": "<PERSON><PERSON>", "Allow only extended licenses": "Csak kiterjesztett licencek engedélyezése", "Allow only one conversation": "Csak egy beszélgeté<PERSON> engedélyez", "Allow only one conversation per user.": "Felhasználónként csak egy beszélgetést engedélyez.", "Allow the chatbot to reply to the user's emails if the answer is known and email piping is active.": "Engedélyezze a chatbot <PERSON>, hogy válaszoljon a felhasználó e-mailjeire, ha a v<PERSON><PERSON> is<PERSON>t, és az e-mailek továbbítása aktív.", "Allow the chatbot to reply to the user's text messages if the answer is known.": "Engedélyezze a chatbot számára, hogy válaszoljon a felhasználó szöveges üzeneteire, ha a válasz ismert.", "Allow the user to archive a conversation and hide archived conversations.": "Lehetővé teszi a felhasználó számára egy beszélgetés archiválását és az archivált beszélgetések elrejtését.", "Allow users to contact you via their favorite messaging apps.": "Lehetővé teszi a felhasználók számára, hogy felvehessék Önnel a kapcsolatot kedvenc üzenetküldő alkalmazásaikon keresztül.", "Allow users to select a product on ticket creation.": "Lehetővé teszi a felhasználóknak, hogy a jegykészítés során válasszanak ki egy terméket.", "Always all messages": "Mindig minden üzenet", "Always incoming messages only": "Csak mindig bejövő üzenetek", "Always sort conversations by date in the admin area.": "A beszélgetéseket mindig dátum szerint rendezze az adminisztrációs területen.", "API key": "API kulcs", "Append the registration user details to the success message.": "A sikeres üzenethez csatolja a regisztrációs felhasználói adatokat.", "Apply a custom background image for the header area.": "Alkalmazzon egyéni háttérképet a fejlécterülethez.", "Apply changes": "Módosítások elfogadása", "Apply to": "Vonatkoznak", "Archive all user channels in the Slack app. This operation may take a long time to complete. Important: All of your slack channels will be archived.": "Archiválja az összes felhasználói csatornát a Slack alkalmazásban. A művelet végrehajtása hosszú ideig tarthat. Fontos: Az összes laza csatornád archiválva lesz.", "Archive automatically the conversations marked as read every 24h.": "24 óránként automatikusan archiválja az olvasottként megjelölt beszélgetéseket.", "Archive channels": "<PERSON><PERSON><PERSON>", "Archive channels now": "A csatornák archiválása most", "Articles": "Cikkek", "Articles area": "Cikkek terület", "Articles button link": "Cikkek gomb linkje", "Articles page URL": "Cikkek oldal URL-je", "Artificial Intelligence": "Mesterséges intelligencia", "Assign a department to all conversations started from Google Business Messages. Enter the department ID.": "Rendeljen hozz<PERSON> egy osztályt a Google Business Messages szolgáltatásból indított összes beszélgetéshez. Írja be a részleg azonosítóját.", "Assign a department to all conversations started from Twitter. Enter the department ID.": "Rendeljen hozz<PERSON> egy osztályt a Twitterről indított összes beszélgetéshez. Írja be a részleg azonosítóját.", "Assign a department to all conversations started from Viber. Enter the department ID.": "Rendeljen hozz<PERSON> egy osztályt a Viberből indított összes beszélgetéshez. <PERSON><PERSON><PERSON> be a részleg azonosítóját.", "Assign a department to all conversations started from WeChat. Enter the department ID.": "Rendeljen ho<PERSON> egy osztályt a WeChat szolgáltatásból indított összes beszélgetéshez. Írja be a részleg azonosítóját.", "Assign different departments to conversations started from different Google Business Messages locations. This setting overrides the default department.": "Különböző részlegeket rendelhet a Google Business Messages különböző helyeiről indított beszélgetésekhez. Ez a beállítás felülírja az alapértelmezett osztályt.", "Assistant": "<PERSON><PERSON><PERSON>", "Assistant ID": "Segédazonosító", "Attachments list": "Mellékletek listája", "Audio file URL - admin": "Hangfájl URL - admin", "Automatic": "Automatikus", "Automatic human takeover": "Automatikus emberátvétel", "Automatic translation": "Automatikus fordí<PERSON>ás", "Automatic updates": "Automatikus <PERSON>", "Automatically archive conversations": "A beszélgetések automatikus archiválása", "Automatically assigns a department based on the user's active plans. Insert -1 as plan ID for users without any plan.": "Automatikusan hozzárendel egy osztályt a felhasználó aktív tervei alapján. Szúrjon be -1-et tervazonosítóként a tervvel nem rendelkező felhasználók számára.", "Automatically check and install new updates. A valid Envato Purchase Code and valid apps's license keys are required.": "Az új frissítések automatikus ellenőrzése és telepítése. Érvényes Envato vásárlási kódra és érvényes alkalmazások licenckulcsára van szükség.", "Automatically collapse the conversation details panel, and other panels, of the admin area.": "A beszélgetés részleteit tartalmazó panel és a rendszergazdai terület többi paneljének automatikus összecsukása.", "Automatically create a department for each website and route the conversations of each website to the right department. This setting requires a WordPress Multisite installation.": "Automatikusan hozzon létre egy osztályt minden webhelyhez, és irányítsa az egyes webhelyek beszélgetéseit a megfelelő részleghez. Ehhez a beállításhoz a WordPress többoldalas telepítése szükséges.", "Automatically hide the conversation details panel.": "A beszélgetés részletei panel automatikus elrejtése.", "Automatically send cart reminders to customers with products in their carts. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Automatikus kosár emlékeztetők küldése azoknak a vásárlóknak, akiknek a kosarában vannak termékek. Használhatja a következő egyesítési mezőket és még sok mást: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.", "Automatically sync Zendesk customers with {R}, view Zendesk tickets, or create new ones without leaving {R}.": "Automatikusan szinkronizálja a Zendesk ügyfeleit a(z) {R} szolgáltatással, tekintse meg a Zendesk jegyeket, vagy hozzon létre új jegyeket a(z) {R} elhagyása nélkül.", "Automatically synchronize products, categories, tags, and more with Dialogflow, and enable the bot to answer autonomously to questions related to your shop.": "Automatikusan szinkronizálja a termékeket, kategóriákat, címkéket és egyebeket a Dialogflow-val, és lehetővé teszi a bot számára, hogy önállóan válaszoljon az üzletével kapcsolatos kérdésekre.", "Automatically translate admin area": "Az adminisztrátori terület automatikus fordítása", "Automatically translate the admin area to match the agent profile language or browser language.": "Az adminisztrációs terület automatikus fordítása az ügynökprofil nyelvének vagy a böngésző nyelvének megfelelően.", "Avatar image": "Avatar kép", "Away mode": "Távoll<PERSON><PERSON> mód", "Before initiating the chat, the user must accept a privacy message in order to gain access.": "A csevegés indítása előtt a felhasználónak el kell fogadnia egy adatvédelmi üzenetet a hozzáféréshez.", "Birthday": "Születésnap", "Body variables": "Testváltozók", "Bot name": "Bot neve", "Bot profile image": "<PERSON><PERSON> pro<PERSON>", "Bot response delay": "Bot válasz késése", "Bottom": "Alsó", "Brand": "<PERSON><PERSON><PERSON>", "Built-in chat button icons": "Beépített chat gomb ikonok", "Business Account ID": "Üzleti számlaazonosító", "Button action": "Gombművelet", "Button name": "<PERSON><PERSON> neve", "Button text": "Gomb szövege", "Button variables": "Gombváltozók", "Cancel button text": "Mégse gomb szövege", "Cart": "<PERSON><PERSON><PERSON><PERSON>", "Cart follow up message": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>mon követési üzenet", "Catalogue details": "Katalógus <PERSON>", "Catalogue ID": "Katalógusazonosító", "Change the chat button image with a custom one.": "Módosítsa a csevegés gomb képét egyénire.", "Change the default field names.": "Módosítsa az alapértelmezett mezőneveket.", "Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent.": "Módosítsa az üzenet szövegét a csevegőmodul fejlécében. Ezt a szöveget az ügynök címsora váltja fel az első válasz elküldése után.", "Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent.": "Módosítsa a cím szövegét a csevegési modul fejlécében. Az első válasz elküldése után ezt a szöveget az ügynök neve váltja fel.", "Channel ID": "Csatornaazonosító", "Channels": "Csatornák", "Channels filter": "Csatornaszűrő", "Chat": "Csevegés", "Chat and admin": "Chat és adminisztrátor", "Chat background": "<PERSON><PERSON>", "Chat button icon": "Cseve<PERSON><PERSON> gomb ikon", "Chat button offset": "A csevegés gomb eltolása", "Chat message": "Chat üzenet", "Chat only": "Csak chat", "Chat position": "<PERSON><PERSON>", "Chatbot": "<PERSON><PERSON><PERSON>", "Chatbot mode": "<PERSON><PERSON><PERSON> mód", "Check Requirements": "Ellenőrizze a Követelményeket", "Check the server configurations and make sure it has all the requirements.": "Ellenőrizze a kiszolgáló konfigurációit, és győződjön meg arról, hogy minden követelménynek megfelel.", "Checkout": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Choose a background texture for the chat header and conversation area.": "Válasszon háttértextúrát a csevegési fejléchez és a beszélgetési területhez.", "Choose where to display the chat. Enter the values separated by commas.": "Válassza ki a csevegés megjelenítési helyét. Adja meg az értékeket vesszővel elválasztva.", "Choose which fields to disable from the tickets area.": "V<PERSON><PERSON><PERSON> ki, mely mez<PERSON>ket szeretné letiltani a jegyek területen.", "Choose which fields to include in the new ticket form.": "V<PERSON><PERSON><PERSON> ki, mely mez<PERSON>ket szeretné felvenni az új j<PERSON>yűrlapra.", "Choose which fields to include in the registration form. The name field is included by default.": "Válassza ki, mely mezőket kívánja szerepeltetni a regisztrációs űrlapon. A név mező alapértelmezés szerint szerepel.", "Choose which user system the front-end chat will use to register and log in users.": "Válassza ki, hogy a front-end chat melyik felhasználói rendszert használja a felhasználók regisztrálásához és bejelentkezéséhez.", "City": "<PERSON><PERSON><PERSON>", "Clear flows": "<PERSON><PERSON>z<PERSON>", "Click the button to start the Dialogflow synchronization.": "Kattintson a gombra a Dialogflow szinkronizálás elindításához.", "Click the button to start the Slack synchronization. Localhost cannot and does not receive messages. Log in with another account or as a visitor to perform your tests.": "Kattintson a gombra a Slack szinkronizálás elindításához. A Localhost nem tud és nem is fogad üzeneteket. A tesztek elvégzéséhez jelentkezzen be másik fiókkal vagy látogatóként.", "Client email": "Ügyfél e-mail", "Client ID": "Ügyfélazonosító", "Client token": "Ügyfél token", "Close chat": "Csevegés <PERSON>", "Close message": "Üzenet bezárása", "Cloud API numbers": "Cloud API-számok", "Cloud API settings": "Cloud API beállítások", "Cloud API template fallback": "Cloud API sablon tartalék", "Code": "<PERSON><PERSON><PERSON>", "Collapse panels": "Panelek összecsukása", "Color": "Szín", "Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.": "Közvetlenül a Slackből kommunikáljon felhasználóival. Üzenetek és mellékletek küldése és fogadása, hangulatjelek használata és még sok más.", "Company": "<PERSON><PERSON><PERSON><PERSON>", "Concurrent chats": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Configuration URL": "Konfigurációs URL", "Confirm button text": "Erősítse meg a gomb szövegét", "Confirmation message": "Megerősítő üzenet", "Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.": "Csatlakoztasson intelligens chatbotokat, és automatizálja a beszélgetéseket a mesterséges intelligencia egyik legfejlettebb formájával a világon.", "Connect stores to agents.": "Kapcsolja össze az üzleteket az ügynökökkel.", "Connect your Telegram bot to {R} to read and reply to all messages sent to your Telegram bot directly in {R}.": "Csatlakoztassa Telegram robotját a(z) {R} hálózathoz, hogy elolvashassa a Telegram robotnak küldött összes üzenetet és válaszoljon rájuk közvetlenül a(z) {R} alkalmazásban.", "Connect your Viber bot to {R} to read and reply to all messages sent to your Viber bot directly in {R}.": "Csatlakoztassa Viber botját a(z) {R} hálózathoz, hogy elolvashassa a Viber robotjára küldött összes üzenetet és válaszoljon rájuk közvetlenül a(z) {R} alkalmazásban.", "Connect your Zalo Official Account to {R} to read and reply to all messages sent to your Zalo Official Account directly in {R}.": "Csatlakoztassa hivatalos Zalo-fiókját a(z) {R} fiókhoz, hogy közvetlenül a(z) {R} alkalmazásban elolvashassa a hivatalos Zalo-fiókjába küldött összes üzenetet és válaszoljon rájuk.", "Content": "Tartalom", "Content template SID": "Tartalomsablon SID", "Conversation profile": "Beszélgetési profil", "Conversations data": "Beszélgetések adatai", "Convert all emails": "Konvertálja az összes e-mailt", "Cookie domain": "Cookie domain", "Country": "<PERSON><PERSON><PERSON><PERSON>", "Coupon discount (%)": "<PERSON><PERSON><PERSON> (%)", "Coupon expiration (days)": "A kupon lejárata (nap)", "Coupon expiration (seconds)": "A kupon lejárata (másodpercben)", "Create a WordPress user upon registration.": "Hozzon létre egy WordPress felhasználót a regisztráció után.", "Create Intents now": "Intents létrehozása most", "Currency symbol": "<PERSON>uta szimbólum", "Custom CSS": "Egyedi CSS", "Custom fields": "Egyéni <PERSON>", "Custom JS": "Egyedi JS", "Custom model ID": "Egyedi <PERSON>la<PERSON>ító", "Custom parameters": "<PERSON><PERSON><PERSON><PERSON>", "Customize the link for the 'All articles' button.": "A &quot;<PERSON>en cikk&quot; gomb hi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> testreszabása.", "Dashboard display": "Műszerfal kijelző", "Dashboard title": "Irányítópult címe", "Database details": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Database host": "Adatbázis <PERSON>", "Database name": "Adatbázis név", "Database password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Database prefix": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Database user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Decline button text": "Elutasítás gomb szövege", "Declined message": "Elutasított üzenet", "Default": "Alap<PERSON><PERSON><PERSON><PERSON><PERSON>", "Default body text": "Alapértelmezett törzsszöveg", "Default conversation name": "Alapértelmezett beszélgetési név", "Default department": "Alapértelmezett r<PERSON>", "Default department ID": "Alapértelmezett részlegazonosító", "Default form": "Alapértelmezett űrlap", "Default header text": "Alapértelmezett fejléc szövege", "Delay (ms)": "<PERSON><PERSON><PERSON><PERSON>etés (ms)", "Delete all leads and all messages and conversations linked to them.": "Törölje az összes érdeklődőt, valamint a hozzájuk kapcsolódó összes üzenetet és beszélgetést.", "Delete conversation": "Beszélgetés törlése", "Delete leads": "Érdeklődések törlése", "Delete message": "Üzenet törlése", "Delete the built-in flows.": "Törölje a beépített folyamokat.", "Delimiter": "<PERSON><PERSON><PERSON><PERSON>", "Department": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Department ID": "Osztályazonosító", "Departments": "Osztályok", "Departments settings": "<PERSON>z osztályok beállításai", "Desktop notifications": "<PERSON><PERSON><PERSON><PERSON>", "Dialogflow - Department linking": "Dialogflow – <PERSON><PERSON><PERSON><PERSON><PERSON>", "Dialogflow chatbot": "Dialogflow chatbot", "Dialogflow edition": "Dialogflow kiadás", "Dialogflow Intent detection confidence": "Dialogflow Intent észlelés megbízhatósága", "Dialogflow location": "Dialog<PERSON> helye", "Dialogflow spelling correction": "Dialogflow helyesírási javítás", "Dialogflow welcome Intent": "Dialogflow üdvözlő szándék", "Disable agents check": "Ügynökellenőrzés letilt<PERSON>", "Disable and hide the chat widget if all agents are offline.": "Tiltsa le és rejtse el a csevegési modult, ha minden ügynök offline állapotban van.", "Disable and hide the chat widget outside of scheduled office hours.": "A csevegési widget letiltása és elrejtése az ütemezett munkaidőn kívül.", "Disable any features that you don't need.": "<PERSON>iltsa le azokat a funkciókat, am<PERSON><PERSON><PERSON> nincs szüksége.", "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it.": "A csevegési widget automatikus inicializálásának letiltása. Ha ez a beállítás aktív, inicializálnia kell a csevegési widgetet egy Ön által írt egyéni JavaScript API kóddal. Ha a csevegés nem jelenik meg, és ez a beállítás engedélyezve <PERSON>, tiltsa le.", "Disable auto-initialization of the tickets area. When this setting is active you must initialize the tickets area with a custom JavaScript API code written by you. If the tickets area doesn't appear and this setting is enabled, disable it.": "Tiltsa le a jegyek terület automatikus inicializálását. Ha ez a beállítás aktív, inicializálnia kell a jegyek területet az Ön által írt egyéni JavaScript API kóddal. Ha a jegyek terület nem jelenik meg, és ez a beállítás engedélyezve van, tiltsa le.", "Disable chatbot": "A chatbot letiltása", "Disable cron job": "A cron feladat letiltása", "Disable dashboard": "Az irányítópult letiltása", "Disable during office hours": "Letiltása munk<PERSON>", "Disable features": "Funkciók letiltása", "Disable features you don't use and improve the chat performance.": "Tiltsa le a nem használt funkciókat, és javítsa a csevegési teljesítményt.", "Disable file uploading capabilities within the chat.": "Fájlfeltöltési lehetőségek letiltása a csevegésen belül.", "Disable for messaging channels": "Letiltás üzenetküldő csatornáknál", "Disable for the tickets area": "Letiltás a jegyek területén", "Disable invitation": "Meg<PERSON>í<PERSON><PERSON>", "Disable online status check": "Az online állapotellenőrzés letiltása", "Disable outside of office hours": "Munkaidőn kívül letiltása", "Disable password": "<PERSON><PERSON><PERSON><PERSON>", "Disable registration during office hours": "A regisztráció letiltása munkaidőben", "Disable registration if agents online": "Tiltsa le a regisztrációt, ha az ügynökök online", "Disable the automatic invitation of agents to the channels.": "Tiltsa le az ügynökök automatikus meghívását a csatornákra.", "Disable the channels filter.": "Kapcsolja ki a csatornaszűrőt.", "Disable the chatbot for the tickets area.": "Tiltsa le a chatbotot a jegyek területén.", "Disable the chatbot for this channel only.": "Csak ezen a csatornán kapcsold ki a chatbotot.", "Disable the dashboard, and allow only one conversation per user.": "Tiltsa le az irányítópultot, és felhasználónként csak egy beszélgeté<PERSON> engedélyezzen.", "Disable the login and remove the password field from the registration form.": "Tiltsa le a bejelentkezést, és távolítsa el a j<PERSON>ó mezőt a regisztrációs űrlapról.", "Disable uploads": "Feltöltés letiltása", "Disable voice message capabilities within the chat.": "A hangüzenetek funkciójának letiltása a csevegésen belül.", "Disable voice messages": "Hangüzenetek letiltása", "Disabled": "Tilt<PERSON>", "Display a brand image in the header area. This only applies for the 'brand' header type.": "Márkakép megjelenítése a fejléc területén. Ez csak a „márka” fejléctípusra vonatkozik.", "Display categories": "Kategóriák megjeleníté<PERSON>", "Display images": "Képek megjelenítése", "Display in conversation list": "Megjelenítés a beszélgetéslistában", "Display in dashboard": "Megjelenítés a műszerfalon", "Display online agents only": "Csak online ügynökök megjelenítése", "Display the articles section in the right area.": "Jelenítse meg a cikkeket a megfelelő területen.", "Display the dashboard instead of the chat area on initialization.": "Inicializáláskor a csevegési terület helyett az irányítópultot jelenítse meg.", "Display the feedback form to rate the conversation when it is archived.": "Jelenítse meg a visszajelzési űrlapot a beszélgetés értékeléséhez, amikor az archiválásra került.", "Display the user full name in the left panel instead of the conversation title.": "Jelenítse meg a felhasználó teljes nevét a bal oldali panelen a beszélgetés címe he<PERSON>.", "Display the user's profile image within the chat.": "Jelenítse meg a felhasználó profilképét a csevegésben.", "Display user name in header": "Felhasználónév megjelenítése a fejlécben", "Display user's profile image": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> profilképének megjelení<PERSON>", "Displays additional columns in the user table. Enter the name of the fields to add.": "További oszlopokat jelenít meg a felhasználói táblázatban. Adja meg a hozzáadni kívánt mezők nevét.", "Distribute conversations proportionately between agents and notify visitors of their position within the queue. Response time is in minutes. You can use the following merge fields in the message: {position}, {minutes}. They will be replaced by the real values in real-time.": "Ossza el arányosan a beszélgetéseket az ügynökök között, és értesítse a látogatókat a sorban elfoglalt helyükről. A válaszidő percekben értendő. Az üzenetben a következő egyesítési mezőket használhatja: {pozíció}, {perc}. Ezeket valós időben váltják fel a valós értékek.", "Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.": "Ossza el arányosan a beszélgetéseket az ügynökök között, és tiltsa le, hogy egy ügynök megtekintse a többi ügynök beszélgetéseit.", "Do not send email notifications to admins": "Ne küldjön e-mail értesítést a rendszergazdáknak", "Do not show tickets in chat": "<PERSON>e mutasson jegyeket a chatben", "Do not translate settings area": "Ne fordítsa le a beállítások területét", "Download": "Letöltés", "Edit profile": "<PERSON><PERSON>", "Edit user": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Email address": "<PERSON><PERSON>", "Email and ticket": "E-mail és jegy", "Email header": "E-mail fejléc", "Email notification delay (hours)": "E-mailes értesítés késése (óra)", "Email notifications via cron job": "E-mail értesítések a cron jobon keresztül", "Email only": "Csak e-mailben", "Email piping": "E-mail csövek", "Email piping server information and more settings.": "E-mail csővezeték-szerver információk és további beállítások.", "Email request message": "E-mail kérés üzenet", "Email signature": "E-mail aláírás", "Email template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "E-mail sablon a felhasználónak küldött e-mailhez, amikor egy ügynök válaszol. Használhat szöveget, HTML-t és a következő egyesítési mezőket: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "E-mail sablon az ügynöknek küldött e-mailhez, amikor egy fel<PERSON>z<PERSON><PERSON>ó új üzenetet küld. Hasz<PERSON><PERSON><PERSON> szöveget, HTML-t és a következő egyesítési mezőket: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to the user after submitting their email through the follow-up message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.": "E-mail sablon a felhasználónak küldött e-mailhez, mi<PERSON><PERSON> az e-mailt a követési üzenet űrlapon keresztül. Használhat szöveget, HTML-t és a következő egyesítési mezőket: {user_name}, {user_email}.", "Email template for the email sent to the user to verify their email address. Include the {code} merge field within your content, it will be replaced with the one-time code.": "E-mail sablon a felhasználónak küldött e-mailhez az e-mail cím ellenőrzése céljából. Szerelje be a {code} egyesítési mezőt a tartalomba, ez lecserélődik az egyszeri kódra.", "Email verification": "E-mail ellenőrzés", "Email verification content": "E-mail ellen<PERSON><PERSON><PERSON> tartalom", "Enable email verification with OTP.": "Engedélyezze az e-mail-ellenőrzést az OTP-vel.", "Enable logging of agent activity": "Az ügynöktevékenység naplózásának engedélyezése", "Enable the chatbot outside of scheduled office hours only.": "Csak a tervezett munkaidőn kívül engedélyezze a chatbotot.", "Enable the registration only if all agents are offline.": "Csak akkor engedélyezze a regisztrációt, ha minden ügynök offline állapotban van.", "Enable the registration outside of scheduled office hours only.": "Csak a tervezett munkaidőn kívül engedélyezze a regisztrációt.", "Enable this option if email notifications are sent via cron job.": "Engedélyezze ezt a lehetőséget, ha az e-mail értesítéseket cron jobon keresztül küldi.", "Enable ticket and chat support for subscribers only, view member profile details and subscription details in the admin area.": "Csak előfizetők számára engedélyezze a jegy- és csevegési támogatást, és tekintse meg a tagprofil adatait és az előfizetés részleteit az adminisztrációs területen.", "Enter the bot token and click the button to synchronize the Telegram bot. Localhost cannot receive messages.": "<PERSON><PERSON><PERSON> be a bot tokent, és kattintson a gombra a Telegram bot szinkronizálásához. A Localhost nem tud üzeneteket fogadni.", "Enter the bot token and click the button to synchronize the Viber bot. Localhost cannot receive messages.": "<PERSON><PERSON><PERSON> be a bot tokent, és kattintson a gombra a Viber bot szinkronizálásához. A Localhost nem tud üzeneteket fogadni.", "Enter the database details of the Active eCommerce CMS database.": "Adja meg a Active eCommerce CMS-adatbázis adatbázisának adatait.", "Enter the database details of the Martfury database.": "Adja meg a <PERSON><PERSON><PERSON> adatb<PERSON>s adatb<PERSON>zis adatait.", "Enter the database details of the Perfex database.": "Adja meg a Perfex adatbázis adatbázis adatait.", "Enter the database details of the WHMCS database.": "Adja meg a WHMCS adatbázis adatbázis adatait.", "Enter the default messages used by the chatbot when user question requires a dynamic answer.": "Adja meg a chatbot által használt alapértelmezett üzeneteket, amikor a felhasználói kérdés dinamikus választ igényel.", "Enter the details of your Google Business Messages.": "Adja meg a Google Business Messages adatait.", "Enter the details of your Twitter app.": "Adja meg Twitter-alkalmazásának adatait.", "Enter the LINE details to start using it. Localhost cannot receive messages.": "Adja meg a(z) LINE adatait a használat megkezdéséhez. A Localhost nem tud üzeneteket fogadni.", "Enter the URL of a .css file, to load it automatically in the admin area.": "Adja meg a .css fájl URL-címét, hogy automatikusan betölthesse az adminisztrációs területen.", "Enter the URL of a .js file, to load it automatically in the admin area.": "Adja meg a .js fájl URL-cí<PERSON>t, hogy automatikusan betölthesse az adminisztrációs területen.", "Enter the URL of the articles page.": "Adja meg a cikkoldal URL-jét.", "Enter the URLs of your shop": "Adja meg üzlete URL-címét", "Enter the WeChat official account token. See the docs for more details.": "Adja meg a WeChat hivatalos fióktokenjét. További részletekért lásd a dokumentumokat.", "Enter the Zalo details to start using it. Localhost cannot receive messages.": "A használat megkezdéséhez adja meg a Zalo adatait. A Localhost nem tud üzeneteket fogadni.", "Enter your 360dialog account settings information.": "Adja meg a 360-dialógus fiókbeállítási adatait.", "Enter your Envato Purchase Code to activate automatic updates and unlock all the features.": "Adja meg Envato vásárlási kódját az automatikus frissítések aktiválásához és az összes funkció feloldásához.", "Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.": "Adja meg Twilio-fiókja adatait. Használhat szöveget és a következő egyesítési mezőket: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.", "Enter your Twilio account settings information.": "<PERSON>ja meg <PERSON>-fiókjának beállítási adatait.", "Enter your WeChat Official Account information.": "Adja meg hivatalos WeChat fiókjának adatait.", "Enter your Zendesk information.": "Adja meg Zendesk adatait.", "Entities": "Entities", "Envato Purchase Code": "Envato vásárlási kód", "Envato purchase code validation": "Envato vásárlási kód érvényesítése", "Exclude products": "Termékek kizárása", "Export all settings.": "Az összes beállítás exportálása.", "Export settings": "Beállítások exportálása", "Facebook pages": "Facebook oldalak", "Fallback message": "Tartalék üzenet", "Filters": "Szűrők", "First chat message": "Első chat üzenet", "First reminder delay (hours)": "Az első emlékeztető késése (óra)", "First ticket form": "Első jegyűrlap", "Flash notifications": "<PERSON>", "Follow up - Email": "<PERSON><PERSON><PERSON> - E-mail", "Follow up email": "Nyomon <PERSON>ési e-mail", "Follow up message": "Nyomon követési üzenet", "Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.": "Követi az emberi ügynök és a végfelhasználó k<PERSON> be<PERSON>, és valós időben válaszjavaslatokat ad az emberi ügynöknek.", "Follow-up email template. You can use text, HTML, and the following merge fields and more: {coupon}, {product_names}, {user_name}.": "Nyomon követési e-mail sablon. Hasz<PERSON><PERSON><PERSON> s<PERSON>öveget, HTML-t és a következő egyesítési mezőket és még sok mást: {coupon}, {product_names}, {user_name}.", "Force language": "Nyelv erőltetése", "Force log out": "Kijelentkezés kényszerítése", "Force the chat to ignore the language preferences, and to use always the same language.": "Kényszerítse a csevegést, hogy figyelmen kívül hagyja a nyelvi beállításokat, és mindig ugyanazt a nyelvet használja.", "Force the loggout of Masi Chat agents if they are not logged in WordPress.": "Kényszerítse ki a Masi Chat ügynökök kijelentkezését, ha nincsenek bejelentkezve a WordPressbe.", "Force users to use a different conversation for each store and hide conversations from other stores from store administrators.": "Kényszerítse a fel<PERSON>z<PERSON><PERSON>ókat, hogy minden üzlethez más beszélgetést használjanak, és elrejtsék a többi üzletben folytatott beszélgetéseket az üzletek rendszergazdái elől.", "Force users to use only one phone country code.": "Kényszerítse a felhasználókat, hogy csak egy telefon országkódj<PERSON><PERSON>.", "Form message": "Űrlap üzenet", "Form title": "Űrlap címe", "Frequency penalty": "Frekvencia büntetés", "Full visitor details": "Részletes látogatói adatok", "Function name": "Funkció neve", "Generate conversations data": "Beszélgetési adatok generálása", "Generate user questions": "Felhasználói kérdések generálása", "Get configuration URL": "Szerezze be a konfigurációs URL-t", "Get it from the APP_KEY value of the file .env located in the root directory of Active eCommerce.": "Szerezze le a Active eCommerce gyökérkönyvtárában található .env fájl APP_KEY értékéből.", "Get it from the APP_KEY value of the file .env located in the root directory of Martfury.": "Szerezze le a Martfury gyökérkönyvtárában található .env fájl APP_KEY értékéből.", "Get Path": "Get Path", "Get Service Worker path": "Szerezzen Service Worker elérési utat", "Get URL": "URL lekérése", "Google and Dialogflow settings.": "A Google és a Dialogflow beállításai.", "Google search": "Google kereső", "Header": "<PERSON><PERSON><PERSON><PERSON>", "Header background image": "<PERSON><PERSON><PERSON><PERSON>", "Header brand image": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>", "Header message": "Fejléc üzenet", "Header title": "<PERSON><PERSON><PERSON><PERSON>", "Header type": "<PERSON><PERSON><PERSON><PERSON> tí<PERSON>", "Header variables": "Fejléc változók", "Hide": "<PERSON><PERSON><PERSON><PERSON>", "Hide agent's profile image": "Az ügynök profilképének elrejtése", "Hide archived tickets": "Az archivált jegyek elrejtése", "Hide archived tickets from users.": "Az archivált jegyek elrejtése a felhasználók elől.", "Hide chat if no agents online": "A csevegés elrej<PERSON>, ha nincs online ügynök", "Hide chat outside of office hours": "A csevegés elrejtése munkaidőn kívül", "Hide conversation details panel": "Beszélgetés részletei panel elrejtése", "Hide conversations of other agents": "Más ügynökök beszélgetéseinek elrejtése", "Hide on mobile": "Elrejtés mobilon", "Hide the agent's profile image within the chat.": "Az ügynök profilképének elrejtése a csevegésben.", "Hide tickets from the chat widget and chats from the ticket area.": "<PERSON><PERSON>ek elrejtése a chat widgetből és csevegés a jegyterületről.", "Hide timetable": "<PERSON><PERSON><PERSON>", "Host": "Házigazda", "Human takeover": "<PERSON><PERSON><PERSON>tvé<PERSON>", "If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.": "Ha egy ügynök sem válaszol a megadott időintervallumon belül, a rendszer egy üzenetet küld, amelyben kéri a felhasz<PERSON> adatait, például az e-mail-címét.", "If the chatbot doesn't understand a user's question, forwards the conversation to an agent.": "Ha a chatbot nem érti a fel<PERSON>z<PERSON><PERSON><PERSON>, továbbítja a beszélgetést egy ügynöknek.", "Image": "<PERSON><PERSON><PERSON>", "Import admins": "Adminisztrátorok importálása", "Import all settings.": "Importálja az összes beállítást.", "Import articles": "Cikkek importálása", "Import contacts": "Névjegyek importálása", "Import customers": "Import ügyfelek", "Import customers into Masi Chat. Only new customers will be imported.": "Importáljon ügyfeleket a Masi Chat webhelyre. Csak új ügyfeleket importálunk.", "Import settings": "Beállítások importálása", "Import users": "Felhasználók importálása", "Import users from a CSV file.": "Felhasználók importálása CSV-fájlból.", "Import vendors": "Import szállítók", "Import vendors into Masi Chat as agents. Only new vendors will be imported.": "Importáljon szállítókat ügynökként a(z) Masi Chat webhelyre. Csak új szállítókat importálunk.", "Improve chat performance with Pusher and WebSockets. This setting stops all AJAX/HTTP real-time requests that slow down your server and use instead the WebSockets.": "Javítsa a csevegési teljesítményt a Pusher és a WebSockets segítségével. Ez a beállítás leállítja az összes valós idejű AJAX/HTTP kérést, amely lelassítja a kiszolgálót, és helyette a WebSocketeket használja.", "Include custom fields": "Tartalmazzon egyéni mezőket", "Include custom fields in the registration form.": "Adja meg az egyéni mezőket a regisztrációs űrlapon.", "Include the password field in the registration form.": "A regisztrációs <PERSON>apon adja meg a j<PERSON><PERSON><PERSON> me<PERSON>.", "Incoming conversations and messages": "<PERSON><PERSON><PERSON><PERSON><PERSON> beszélgetések és üzenetek", "Incoming conversations only": "Csak a bejövő beszélgetések", "Incoming messages only": "Csak bejövő üzenetek", "Increase sales and connect you and sellers with customers in real-time by integrating Active eCommerce with Masi Chat.": "Növelje az eladásokat, és valós időben lépjen kapcsolatba Önnel és eladóival az ügyfelekkel a Active eCommerce támogatási testület integrálásával.", "Increase sales, provide better support, and faster solutions, by integrating WooCommerce with Masi Chat.": "Növelje az eladás<PERSON>t, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> jobb támogatást és gyorsabb megoldásokat a WooCommerce és a Masi Chat integrálásával.", "Info message": "Információs <PERSON>", "Initialize and display the chat widget and tickets only for members.": "Inicializálja és jelenítse meg a csevegőmodult és a jegyeket csak a tagok számára.", "Initialize and display the chat widget only when the user is logged in.": "Csak akkor inicializálja és jelenítse meg a chat widgetet, ha a felhasználó be van j<PERSON>ntkezve.", "Instance ID": "Példányazonosító", "Integrate OpenCart with {R} for real-time syncing of customers, order history access, and customer cart visibility.": "Integrálja az OpenCart a {R} szolgáltatással az ügyfelek valós idejű szinkronizálásához, a rendelési előzményekhez való hozzáféréshez és az ügyfélkosár láthatóságához.", "Interval (sec)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (mp)", "IP banning": "IP tiltás", "Label": "<PERSON><PERSON><PERSON><PERSON>", "Language": "Nyelv", "Language detection": "Nyelvfelismerés", "Language detection message": "Nyelvfelismerési üzenet", "Last name": "Vezetéknév", "Leave it blank if you don't know what this setting is! Entering an incorrect value will break the chat. Sets the main domain where chat is used to enable login and conversations sharing between the main domain and sub domains.": "Ha<PERSON><PERSON> üresen, ha nem tudja, mi ez a beállítás! Hibás érték megadása megszakítja a csevegést. Beállítja a fő tartományt, ahol a csevegés lehetővé teszi a bejelentkezést és a beszélgetések megosztását a fő tartomány és az altartományok között.", "Left": "<PERSON>l", "Left panel": "Bal oldali panel", "Left profile image": "<PERSON><PERSON> <PERSON> profilk<PERSON>p", "Let the bot to search on Google to find answers to user questions.": "Ha<PERSON><PERSON>, hogy a bot keressen a Google-on, hogy választ találjon a felhasználói kérdésekre.", "Let the chatbot search on Google to find answers to user questions.": "<PERSON><PERSON><PERSON>, hogy a chatbot keressen a Google-on, hogy választ találjon a felhasználói kérdésekre.", "Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from {R}.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a felhasználók elérjék Önt a Twitteren keresztül. Olvassa el a Twitter-fiókjába küldött üzeneteket, és válaszoljon rájuk közvetlenül a(z) {R} webhelyről.", "Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from {R}.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a felhasználók elérjék Önt a WeChat segítségével. Olvassa el és válaszoljon az összes üzenetre, amelyet hivatalos WeChat-fiókjába küldött közvetlenül a(z) {R} címről.", "Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from {R}.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, hogy a felhasználók elérjék Önt a WhatsApp-on keresztül. Olvassa el és válaszoljon a WhatsApp Business-fiókjába közvetlenül a(z) {R} címről küldött összes üzenetre.", "Link each agent with the corresponding Slack user, so when an agent replies via Slack it will be displayed as the assigned agent.": "Minden ügynököt kapcsoljon össze a megfelelő Slack-felhasz<PERSON>lóval, így amikor egy ügynök a Slacken keresztül válaszol, akkor hozzárendelt ügynökként jelenik meg.", "Link name": "<PERSON> neve", "Login form": "Bejelentkezési űrlap", "Login initialization": "Bejelentkezés inicializálása", "Login verification URL": "Bejelentkezés ellenőrző URL", "Logit bias": "Logit elfogultság", "Make a backup of your Dialogflow agent first. This operation can take several minutes.": "Először készítsen biztonsági másolatot a Dialogflow ügynökről. Ez a művelet több percig is eltarthat.", "Make the registration phone field mandatory.": "Tegye kötelezővé a regisztrációs telefonszám mezőt.", "Manage": "Kezelése", "Manage here the departments settings.": "Itt kezelheti az osztályok beállításait.", "Manage the tags settings.": "Kezelje a címkék beállításait.", "Manifest file URL": "Manifest fájl URL-je", "Manual": "Kézikönyv", "Manual initialization": "<PERSON><PERSON><PERSON> in<PERSON>", "Martfury root directory path, e.g. /var/www/": "Martfury gyökérkönyvtár elé<PERSON>, pl. /var/www/", "Martfury shop URL, e.g. https://shop.com": "Martfury bolt URL-je, pl. https://shop.com", "Max message limit": "<PERSON><PERSON><PERSON>", "Max tokens": "<PERSON>", "Members only": "Csak tagoknak", "Members with an active paid plan only": "Csak aktív fizetett előfizetéssel rendelkező tagok", "Message": "Üzenet", "Message area": "Üzenetterület", "Message rewrite button": "Üzenet átírás gomb", "Message template": "Üzenet sablon", "Message type": "Üzenet típusa", "Messaging channels": "Üzenetküldő csatornák", "Messenger and Instagram settings": "Messenger és az Instagram beállításait", "Minify JS": "A JS kicsinyítése", "Minimal": "<PERSON><PERSON><PERSON><PERSON>", "Model": "<PERSON><PERSON>", "Multilingual": "Többnyelvű", "Multilingual plugin": "Többnyelvű bővítmény", "Multilingual via translation": "Többnyelvű fordítással", "Multlilingual training sources": "Többnyelvű képzési források", "Name": "Név", "Namespace": "Névtér", "New conversation email": "<PERSON>j beszélgetési e-mail", "New conversation notification": "Értesítés új <PERSON>ől", "New ticket button": "<PERSON><PERSON> jegy gomb", "Newsletter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "No delay": "<PERSON><PERSON><PERSON>", "No results found.": "<PERSON><PERSON><PERSON>.", "No, we don't ship in": "<PERSON><PERSON>, nem s<PERSON>llítun<PERSON> be", "None": "<PERSON><PERSON><PERSON> sem", "Note data scraping": "Megjegyzés adat<PERSON>ás", "Notes": "Megjegyzések", "Notifications icon": "Értesítések ikonra", "Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.": "Értesítse a felhas<PERSON>, ha üzenetét az ütemezett munkaidőn kívül küldték el, vagy minden ügynök offline állapotban van.", "OA secret key": "OA titkos kulcs", "Offline message": "Offline üzenet", "Offset": "Offset", "On chat open": "A chat megnyitása", "On page load": "Az oldal betöltésekor", "One conversation per agent": "Ügynökenként egy beszélgetés", "One conversation per department": "Osztályonként egy beszélgetés", "Online users notification": "Online felhasználók értesítése", "Only desktop": "Csak asztali", "Only general questions": "Csak általános kérdések", "Only mobile devices": "Csak mobil eszközök", "Only questions related to your sources": "Csak a forrásokkal kapcsolatos kérdések", "Open automatically": "Automatikusan megnyílik", "Open chat": "<PERSON><PERSON><PERSON> meg a csevegést", "Open the chat window automatically when a new message is received.": "Új üzenet érkezésekor automatikusan nyissa meg a chat ablakot.", "OpenAI Assistants - Department linking": "OpenAI asszisztensek – <PERSON>szt<PERSON>ly összekapcsolása", "OpenAI settings.": "OpenAI beállítások.", "Optional link": "Op<PERSON><PERSON><PERSON>", "Order webhook": "Webhook rendelése", "Other": "<PERSON><PERSON><PERSON><PERSON>", "Outgoing SMTP server information.": "Kimenő SMTP-szerver információk.", "Page ID": "Oldalazonosító", "Page IDs": "Oldalazonosítók", "Page name": "Az oldal neve", "Page token": "Oldal token", "Panel height": "Panel magasság", "Panel name": "Panel neve", "Panel title": "Panel címe", "Panels arrows": "<PERSON><PERSON>", "Password": "Je<PERSON><PERSON><PERSON>", "Perfex URL": "Perfex URL", "Performance optimization": "Teljesítmény optimalizálás", "Phone": "Telefon", "Phone number ID": "Telefonszám azonosító", "Phone required": "Telefon szükséges", "Place ID": "Helyazonosító", "Placeholder text": "Helyőrz<PERSON> szöveg", "Play a sound for new messages and conversations.": "Hang lejátszása új üzenetekhez és beszélgetésekhez.", "Popup message": "Felugró üzenet", "Port": "Kikötő", "Post Type slugs": "Post Type csigák", "Presence penalty": "<PERSON><PERSON><PERSON><PERSON>", "Prevent admins from receiving email notifications.": "Megakad<PERSON><PERSON>ozza, hogy a rendszergazdák e-mailes értesítéseket kapjanak.", "Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.": "Megakadályozza, hogy az ügynökök megtekintsék a más ügynökökhöz rendelt beszélgetéseket. Ez a beállítás automatikusan engedélyezve van, ha az útválasztás vagy a sor aktív.", "Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.": "A felhasználók általi visszaélések megelőzése érdekében korlátozza az egy eszközről a chatbotnak küldött üzenetek számát.", "Primary color": "Elsődleges szín", "Priority": "<PERSON><PERSON><PERSON><PERSON>", "Privacy link": "Adatvédelmi link", "Privacy message": "Adatvédelmi üzenet", "Private chat": "<PERSON><PERSON><PERSON>", "Private chat linking": "Privát chat összekapcsolása", "Private key": "Priv<PERSON><PERSON> k<PERSON>", "Product IDs": "Termékazonosítók", "Product removed notification": "Értesítés a termék eltávolításáról", "Product removed notification - Email": "Értesítés a termék eltávolításáról – E-mail", "Profile image": "Profilkép", "Project ID": "Projektazonosító", "Project ID or Agent Name": "Projektazonosító vagy ügynök neve", "Prompt": "Gyors", "Prompt - Message rewriting": "Prompt – Üzenet átírása", "Protect the tickets area from spam and abuse with Google reCAPTCHA.": "Védje meg a jegyek területét a spamektől és a Google-lal való visszaélésektől reCAPTCHA.", "Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.": "Nyújtson ügyfélszolgálati támogatást ügyfeleinek azáltal, hogy másodpercek alatt bármely weboldalon jegyet biztosít az összes csevegési funkcióval.", "Provider": "S<PERSON>lgáltató", "Purchase button text": "Vásárlás gomb szövege", "Push notifications": "Értesítések", "Push notifications settings.": "Push értesítések beállításai.", "Queue": "Sor", "Rating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Read and reply to messages sent from Google Search, Maps and brand-owned channels directly in {R}.": "Olvassa el és válaszoljon a Google Keresőből, a Térképből és a márkatulajdonos csatornákból küldött üzenetekre közvetlenül a {R} webhelyen.", "Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from {R}.": "Olvassa el, kezelje és válaszoljon a Facebook-oldalaira és Instagram-fiókjaira küldött összes üzenetre közvetlenül a(z) {R} webhelyről.", "Reconnect": "Csatlakozzon újra", "Redirect the user to the registration link instead of showing the registration form.": "A regisztrációs űrlap megjelenítése helyett irányítsa át a felhasználót a regisztrációs linkre.", "Redirect the user to the specified URL if the registration is required and the user is not logged in. Leave blank to use the default registration form.": "Ha regisztráció s<PERSON>ü<PERSON>, és a felhasználó nincs beje<PERSON>zve, irányítsa át a felhasználót a megadott URL-re. Hagyja üresen az alapértelmezett regisztrációs ű<PERSON>ap használatához.", "Refresh token": "To<PERSON> fris<PERSON>", "Register all visitors": "Regisztr<PERSON><PERSON>jon minden látogatót", "Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered.": "Az összes látogatót automatikusan regisztrálja. Ha ez az opció nem aktív, csak a csevegést indító látogatók lesznek regisztrálva.", "Registration / Login": "Regisztráció / Bejelentkezés", "Registration and login form": "Regisztrációs és bejelentkezési űrlap", "Registration fields": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Registration form": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Registration link": "Regisztrációs link", "Registration redirect": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Rename the chat bot. Default is 'Bot'.": "Nevezze át a csevegőbotot. Az alapértelmezés a &#39;Bot&#39;.", "Rename the visitor name prefix. Default is 'User'.": "Nevezze át a látogatónév előtagját. Az alapértelmezett a „Felhasználó”.", "Repeat": "Is<PERSON><PERSON><PERSON>", "Repeat - admin": "Ismétlés - admin", "Replace the admin login page message.": "Cserélje ki az adminisztrátori bejelentkezési oldal üzenetét.", "Replace the brand logo on the admin login page.": "Cserélje ki a márka logóját az adminisztrátori bejelentkezési oldalon.", "Replace the header title with the user's first name and last name when available.": "Cserélje ki a fejléc címét a felhasználó keresztnevére és vezetéknevére, ha elérhető.", "Replace the top-left brand icon on the admin area and the browser favicon.": "Cserélje ki a bal felső márka ikont az adminisztrációs területen és a böngésző faviconjában.", "Reply to user emails": "Válasz a felhasználói e-mailekre", "Reply to user text messages": "Válasz a felhasználói szöveges üzenetekre", "Reports": "<PERSON><PERSON><PERSON><PERSON>", "Reports area": "Jelentések terület", "Request a valid Envato purchase code for registration.": "Kérjen érvényes Envato vásárlási kódot a regisztrációhoz.", "Request the user to provide their email address and then send a confirmation email to the user.": "<PERSON><PERSON><PERSON><PERSON> meg a fel<PERSON>z<PERSON><PERSON>, hogy adja meg e-mail c<PERSON><PERSON><PERSON>, majd küld<PERSON>n egy megerősítő e-mailt a felhasználónak.", "Require phone": "Telefon szükséges", "Require registration": "Regisztr<PERSON><PERSON><PERSON> szüksé<PERSON>", "Require the user registration or login before start a chat. To enable the login area the password field must be included.": "A csevegés megkezdése előtt felhasználói regisztráció vagy bejelentkezés szükséges. A bejelentkezési terület engedélyezéséhez a j<PERSON><PERSON><PERSON> mezőt meg kell adni.", "Require the user registration or login in order to use the tickets area.": "A jegyterület használatához felhasználói regisztráció vagy bejelentkezés szükséges.", "Required": "<PERSON><PERSON><PERSON><PERSON>", "Response time": "Válaszidő", "Restrict chat access by blocking IPs. List IPs with commas.": "Korlátozza a chat-hozzáférést az IP-címek blokkolásával. Az IP-címeket vesszővel sorolja fel.", "Returning visitor message": "Visszatérő látogatói üzenet", "Rich messages": "Gazdag üzenetek", "Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.": "A bővített üzenetek kódrészletek, am<PERSON><PERSON> fel<PERSON>ználhatók egy csevegőüzenetben. Tartalmazhatnak HTML kódot, és automatikusan megjelennek a csevegésben. A gazdag üzenetek a következő szintaxissal használhatók: [rich-message-name]. Rengeteg beépített gazdag üzenet közül választhat.", "Right": "<PERSON><PERSON>", "Right panel": "Jobb panel", "Routing": "Útvonalválasztás", "Routing if offline": "Útválasztás offline állapotban", "Save useful information like user country and language also for visitors.": "Mentse el a látogatók számára hasznos információkat, például a felhasználó országát és nyelvét.", "Saved replies": "Mentett válaszok", "Scheduled office hours": "Ütemezett munk<PERSON>", "Search engine ID": "Keresőmotor azonosítója", "Second chat message": "Második chat üzenet", "Second reminder delay (hours)": "Második emlékeztető késleltetése (óra)", "Secondary color": "Másodlagos szín", "Secret key": "Titkos k<PERSON>", "Send a message to allow customers to be notified when they can purchase a product they are interested in, but that is currently out of stock. You can use the following merge fields: {user_name}, {product_name}.": "Üzenet küldése, hogy az ügyfelek értesítést kapjanak, amikor megvásárolhatják az őket érdeklő terméket, amely jelenleg nincs raktáron. A következő egyesítési mezőket használhatja: {user_name}, {product_name}.", "Send a message to new users when they create the first ticket. Text formatting and merge fields are supported.": "Üzenet küldése az új felhasználóknak, amikor létrehozzák az első jegyet. A szöveg formázása és egyesítési mezők támogatottak.", "Send a message to new users when they visit the website for the first time.": "Üzenet küldése az új felhasználóknak, amikor először látogatják meg a webhelyet.", "Send a message to the customer after a product has been removed from the cart. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.": "Küldjön üzenetet a vásárlónak, mi<PERSON><PERSON> a terméket eltávolította a kosárból. A következő egyesítési mezőket használhatja: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.", "Send a message to the customers who complete a purchase asking to share the product they just bought. You can use the following merge fields and more: {product_name}, {user_name}.": "Küldjön üzenetet a vásárlást befejező ügyfeleknek, és kérje, hogy osszák meg az éppen vásárolt terméket. A következő egyesítési mezőket használhatja: {product_name}, {user_name}.", "Send a message to the customers who complete a purchase. You can use the following merge fields and more: {coupon}, {product_names}, {user_name}.": "Küldjön üzenetet a vásárlást befejező ügyfeleknek. A következő egyesítési mezőket használhatja: {coupon}, {product_names}, {user_name}.", "Send a message to the user when the agent archive the conversation.": "Üzenet küldése a felhasználónak, amikor az ügynök archiválja a beszélgetést.", "Send a message to users who visit the website again after at least 24 hours. You can use the following merge fields and more: {coupon}, {user_name}. See the docs for more details.": "Üzenet küldése azoknak a felhasználóknak, akik legalább 24 óra elteltével újra felkeresik a webhelyet. A következő egyesítési mezőket használhatja: {coupon}, {user_name}. További részletekért lásd a dokumentumokat.", "Send a test agent notification email to verify email settings.": "Az e-mail beállítások ellenőrzéséhez küldjön egy tesztügynök értesítést.", "Send a test message to your Slack channel. This only tests the sending functionality of outgoing messages.": "Küldj tesztüzenetet a Slack-csatornádra. Ez csak a kimenő üzenetek küldési funkcióját teszteli.", "Send a test user notification email to verify email settings.": "Küldjön tesztfelhasználói értesítő e-mailt az e-mail beállítások ellenőrzéséhez.", "Send a text message to the provided phone number.": "Küldjön szöveges üzenetet a megadott telefonszámra.", "Send a user email notification": "Felhasználói e-mail értesítés küldése", "Send a user text message notifcation": "Felhasználói SMS-értesítés küldése", "Send a user text message notification": "Felhasználói SMS-értesítés küldése", "Send an agent email notification": "Ügynöki e-mail értesítés küldése", "Send an agent text message notification": "Ügynöki SMS-értesítés küldése", "Send an agent user text notification": "Ügynök felhasználói szöveges értesítés küldése", "Send an email notification to the provided email address.": "E-mail értesítés küldése a megadott e-mail címre.", "Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.": "E-mail küldése egy ügynöknek, amikor egy felhasználó <PERSON>, és az ügynök offline állapotban van. A rendszer automatikusan e-mailt küld az összes ügynöknek az új beszélgetésekről.", "Send an email to the user when a new conversation is created.": "E-mail küldése a felhasz<PERSON>ak, amikor ú<PERSON> be<PERSON> j<PERSON>.", "Send an email to the user when a new conversation or ticket is created": "E-mail küldése a fel<PERSON>z<PERSON><PERSON>, amikor új be<PERSON> vagy jegyet hoz létre", "Send an email to the user when an agent replies and the user is offline.": "E-mail küldése a felhasz<PERSON>lónak, amikor egy ügynök válaszol, és a felhasználó offline állapotban van.", "Send email": "Küldjön e-mailt", "Send login details to the specified URL and allow access only if the response is positive.": "Küldje el a bejelentkezési adatokat a megadott URL-re, és csak pozitív válasz esetén engedélyezze a hozzáférést.", "Send message": "Üzenet küldése", "Send message to Slack": "Üzenet küldése a Slacknek", "Send message via enter button": "Üzenet küldése az Enter gombbal", "Send text message": "Szöveges üzenet küldése", "Send the message template to a WhatsApp number.": "Küldje el az üzenetsablont egy WhatsApp számra.", "Send the message via the ENTER keyboard button.": "Küldje el az üzenetet az ENTER billentyűzet gombjával.", "Send the user details of the registration form and email rich messages to Dialogflow.": "Küldje el a regisztrációs űrlap felhasználói adatait és e-mailben gazdag üzeneteket a Dialogflow-nak.", "Send the WhatsApp order details to the URL provided.": "Küldje el a WhatsApp rendelés részleteit a megadott URL-re.", "Send to user's email": "Küldje el a felhasználó e-mail címére", "Send transcript to user's email": "Az átirat elküldése a felhasználó e-mail címére", "Send user details": "Felhasználói adatok elküldése", "Sender": "<PERSON><PERSON><PERSON>", "Sender email": "E-mail küldése", "Sender name": "K<PERSON>ld<PERSON> neve", "Sender number": "<PERSON><PERSON><PERSON>", "Sends a text message if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.": "Szöveges üzenetet küld, ha a WhatsApp üzenet elküldése sikertelen. Használhat szöveget és a következő egyesítési mezőket: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.", "Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.": "WhatsApp sablon értesítést küld, ha a WhatsApp üzenet elküldése sikertelen. Használhat szöveget és a következő egyesítési mezőket: {conversation_url_parameter}, {recipient_name}, {recipient_email}.", "Service": "Szolgáltatás", "Service Worker path": "Service Worker elé<PERSON><PERSON>", "Service Worker URL": "Service Worker URL", "Set a dedicated Dialogflow agent for each department.": "Minden részleghez <PERSON> be egy dedikált Dialogflow ügynököt.", "Set a dedicated OpenAI Assistants for each department.": "<PERSON><PERSON><PERSON><PERSON><PERSON> be külön OpenAI-asszisztenst minden részleghez.", "Set a dedicated Slack channel for each department.": "<PERSON><PERSON><PERSON><PERSON><PERSON> be egy de<PERSON>-csatorn<PERSON>t minden r<PERSON>ez.", "Set a profile image for the chat bot.": "<PERSON><PERSON><PERSON><PERSON><PERSON> be egy profilképet a csevegőbot számára.", "Set the articles panel title. Default is 'Help Center'.": "Állítsa be a cikkek panel címét. Az alapértelmezett a &quot;Súgó&quot;.", "Set the avatar image shown next to the message. It must be a JPG image of 1024x1024px with a maximum size of 50KB.": "Állítsa be az üzenet mellett megjelenő avatar képet. A képnek 1024x1024 képpont méretű JPG-képnek kell lennie, legfeljebb 50 KB méretű.", "Set the chat language or translate it automatically to match the user language. Default is English.": "<PERSON><PERSON><PERSON><PERSON><PERSON> be a csevegés nyelvét, vagy ford<PERSON>tsa le automatikusan a felhasználói nyelvnek megfelelően. Az alapértelmezett az angol.", "Set the currency symbol of the membership prices.": "<PERSON><PERSON><PERSON><PERSON><PERSON> be a tags<PERSON>gi <PERSON>énznemszimbólumát.", "Set the currency symbol used by your system.": "Á<PERSON><PERSON><PERSON><PERSON> be a rendszer által használt pénznem szimbólumot.", "Set the default departments for all tickets. Enter the department ID.": "Állítsa be az alapértelmezett részlegeket az összes jegyhez. <PERSON><PERSON><PERSON> be a részleg azonosítóját.", "Set the default email header that will be prepended to automated emails and direct emails.": "Állítsa be az alapértelmezett e-mail fejlécet, amely az automatikus és a közvetlen e-mailek elé ker<PERSON>.", "Set the default email signature that will be appended to automated emails and direct emails.": "Állítsa be az automatikus e-mailekhez és a közvetlen e-mailekhez csatolandó alapértelmezett e-mail aláírást.", "Set the default form to display if the registraion is required.": "<PERSON><PERSON><PERSON>ts<PERSON> be az alapértelmezett űrlapot, amely akkor jelenik meg, ha regisztráció szükséges.", "Set the default name to use for conversations without a name.": "Á<PERSON><PERSON>ts<PERSON> be az alapértelmezett nevet a név nélküli be<PERSON>.", "Set the default notifications icon. The icon will be used as a profile image if the user doesn't have one.": "Állítsa be az alapértelmezett értesítési ikont. Az ikon profilképként lesz has<PERSON>l<PERSON>, ha a felhasználónak nincs ilyen.", "Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.": "Állítsa be az alapértelmezett munkaidőt, amikor az ügynökök elérhetőként jelennek meg. Ezeket a beállításokat a rendszer minden más, a munkaidőtől függő beállításhoz is használja.", "Set the default username to use in bot messages and emails when the user doesn't have a name.": "Állítsa be az alapértelmezett felhasználónevet a bot üzenetekben és e-mailekben, ha a felhasználónak nincs neve.", "Set the header appearance.": "Á<PERSON><PERSON><PERSON><PERSON> be a fejléc megjelenését.", "Set the maximum height of the tickets panel.": "<PERSON><PERSON><PERSON>ts<PERSON> be a jegyek panel maximális ma<PERSON>.", "Set the multilingual plugin you're using, or leave it disabled if your site uses only one language.": "<PERSON><PERSON><PERSON>tsa be a használt többnyelvű bővítményt, vagy hagy<PERSON> let<PERSON>, ha <PERSON>helye csak egy nyelvet használ.", "Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes.": "Á<PERSON><PERSON>tsa be az offline állapotot automatikusan, ha az ügynök vagy a rendszergazda legalább 10 percig inaktív marad az adminisztrációs területen.", "Set the position of the chat widget.": "<PERSON><PERSON><PERSON><PERSON><PERSON> be a chat widget pozícióját.", "Set the primary color of the admin area.": "Állítsa be az adminisztrációs terület elsődleges színét.", "Set the primary color of the chat widget.": "<PERSON><PERSON><PERSON><PERSON><PERSON> be a csevegési widget elsődleges színét.", "Set the secondary color of the admin area.": "Állítsa be az adminisztrációs terület másodlagos színét.", "Set the secondary color of the chat widget.": "<PERSON><PERSON><PERSON><PERSON><PERSON> be a csevegési widget másodlagos színét.", "Set the tertiary color of the chat widget.": "<PERSON><PERSON><PERSON><PERSON><PERSON> be a csevegési widget harmadlagos színét.", "Set the title of the administration area.": "Állítsa be az adminisztrációs terü<PERSON>í<PERSON>.", "Set the title of the conversations panel.": "Á<PERSON><PERSON>ts<PERSON> be a beszélgetések panel címét.", "Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty.": "Állítsa be az irodai órarend UTC-eltolását. A helyes érték lehet negatív is, és a rendszer automatikusan generálja, ha erre a beviteli mezőre kattint, ha az üres.", "Set which actions to allow agents.": "<PERSON><PERSON><PERSON><PERSON><PERSON> be, hogy mely műveleteket engedélyezze az ügynökök.", "Set which actions to allow supervisors.": "<PERSON><PERSON><PERSON><PERSON><PERSON> be, hogy mely műveleteket engedélyezze a felügyelőknek.", "Set which user details to send to the main channel. Add comma separated values.": "<PERSON><PERSON><PERSON><PERSON><PERSON> be, hogy mely felhasználói adatokat küldje el a fő csatornára. Adjon hozzá vesszővel elválasztott értékeket.", "Settings area": "Beállítások terület", "settings information": "beállítási információk", "Shop": "<PERSON><PERSON><PERSON>", "Show": "Előadás", "Show a browser tab notification when a new message is received.": "A böngésző lapon megjelenő értesítés megjelenítése új üzenet érkezésekor.", "Show a desktop notification when a new message is received.": "Asztali értesítés megjelenítése új üzenet érkezésekor.", "Show a notification and play a sound when a new user is online.": "Értesítés megjelenítése és hangjelzés, amikor egy új felhasználó online állapotban van.", "Show a pop-up notification to all users.": "Előugró <PERSON>sítés megjelenítése minden felhasználó s<PERSON>.", "Show profile images": "Profilképek megjelenítése", "Show sender's name": "A feladó nevének megjelenítése", "Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.": "Jelenítse meg az ügynökök menüt az irányítópulton, és kényszerítse a felhasználót, hogy válasszon ügynököt a beszélgetés elindításához.", "Show the articles panel on the chat dashboard.": "A cikkek panel megjelenítése a csevegési irányítópulton.", "Show the categories instead of the articles list.": "A cikklista helyett a kategóriákat jelenítse meg.", "Show the follow up message when a visitor add an item to the cart. The message is sent only if the user has not provided an email yet.": "A nyomon követési üzenet megjelenítése, amikor a látogató egy terméket a kosárba helyez. Az üzenet csak akkor kerül elküldésre, ha a felhasználó még nem adott meg e-mailt.", "Show the list of all Slack channels.": "Az összes Slack csatorna listájának megjelenítése.", "Show the profile image of agents and users within the conversation.": "Mutassa meg a besz<PERSON>lge<PERSON>sen belüli ügynökök és felhasználók profilképét.", "Show the sender's name in every message.": "Minden üzenetben jelenítse meg a feladó nevét.", "Single label": "Egyetlen címke", "Single phone country code": "Egyetlen telefon országkódja", "Site key": "<PERSON><PERSON><PERSON>", "Slug": "Meztelen csiga", "Smart Reply": "Intelligens válasz", "Social share message": "Közösségi megosztási üzenet", "Sort conversations by date": "A beszélgetések rendezése dátum szerint", "Sound": "Hang", "Sound settings": "Hangbeállítások", "Sounds": "<PERSON><PERSON>", "Sounds - admin": "<PERSON><PERSON> - admin", "Source links": "<PERSON><PERSON><PERSON>", "Speech recognition": "Beszédfelismerés", "Spelling correction": "Helyesírási javítás", "Starred tag": "Csillagozott cí<PERSON>ke", "Start importing": "Kezdje el az importálást", "Store name": "<PERSON>z üzlet neve", "Subject": "<PERSON><PERSON><PERSON><PERSON>", "Subscribe": "Iratkozz fel", "Subscribe users to your preferred newsletter service when they provide an email.": "Iratkozzon fel a felhasználók számára az előnyben részesített hírlevél szolgáltatásra, amikor e-mailt adnak.", "Subtract the offset value from the height value.": "Vonja le az eltolás értékét a magasság értékéből.", "Success message": "Siker üzenet", "Supervisors": "Felügyelők", "Masi Chat path": "Támogatási tábla elérési útja", "Sync admin and staff accounts with Masi Chat. Staff users will be registered as agents, while admins as admins. Only new users will be imported.": "Szinkronizálja az adminisztrátori és személyzeti fiókokat a Masi Chat segítségével. A személyzeti felhasználók ügynökként, míg az adminisztrátorok adminisztrátorként lesznek regisztrálva. Csak új felhasználókat importálunk.", "Sync all contacts of all clients with Masi Chat. Only new contacts will be imported.": "Szinkronizálja az összes ügyfél kapcsolatait a Masi Chat segítségével. Csak az új névjegyeket importálja a rendszer.", "Sync all users with Masi Chat. Only new users will be imported.": "Szinkronizálja az összes felhasználót a Masi Chat segítségével. Csak új felhasználókat importálunk.", "Sync all WordPress users with Masi Chat. Only new users will be imported.": "Szinkronizálja az összes WordPress-felhasználót a Masi Chat segítségével. Csak új felhasználókat importálunk.", "Sync knowledge base articles with Masi Chat. Only new articles will be imported.": "Szinkronizálja a tudásbázis cikkeket a Masi Chat segítségével. Csak az új cikkeket importálja a rendszer.", "Sync mode": "Szinkronizálási mód", "Synchronization": "Szinkronizálás", "Synchronize": "Szinkronizálás", "Synchronize customers, enable ticket and chat support for subscribers only, view subscription plans in the admin area.": "Szinkronizálja az ügyfeleket, engedélyezze a jegy- és csevegési támogatást csak az előfizetőknek, tekintse meg az előfizetési terveket az adminisztrációs területen.", "Synchronize emails": "E-mailek szin<PERSON>ronizálása", "Synchronize Entities": "Entities szinkronizálás", "Synchronize Entities now": "Entities szinkronizálása most", "Synchronize now": "Szinkronizálás most", "Synchronize users": "Felhasználók szinkronizálása", "Synchronize your customers in real-time, chat with them and boost their engagement, or provide a better and faster support.": "Szinkronizálja ügyfeleit valós id<PERSON>, c<PERSON><PERSON>g<PERSON><PERSON> ve<PERSON>, és fokozza elköteleződésüket, v<PERSON>y n<PERSON><PERSON><PERSON><PERSON><PERSON> jobb és gyorsabb támogatást.", "Synchronize your Messenger and Instagram accounts.": "Szinkronizálja Messenger- és Instagram-fiókját.", "Synchronize your Perfex customers in real-time and let them contact you via chat! View profile details, proactively engage them, and more.": "Szinkronizálja Perfex ügyfeleit valós id<PERSON>ben, és hagyja, hogy chaten keresztül lépjenek kapcsolatba Önnel! Tekintse meg a profil részleteit, kezdeményezze velük a kap<PERSON>ola<PERSON>t, és így tovább.", "Synchronize your WhatsApp Cloud API account.": "Szinkronizálja WhatsApp Cloud API-fiókját.", "System requirements": "Rendszerkövetelmények", "Tags": "Címkék", "Tags settings": "Címkék beállításai", "Template default language": "A sablon alapértelmezett nyelve", "Template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "Sablon a felhasználónak küldött e-mailhez, amikor egy ügynök válaszol. Hasz<PERSON>l<PERSON> szöveget, HTML-t és a következő egyesítési mezőket: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Template for the email sent to the user when a new conversation is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.": "Sablon a felhasználónak küldött e-mail<PERSON>z, amikor ú<PERSON> jö<PERSON> l<PERSON>. Használhat szöveget, HTML-t és a következő egyesítési mezőket: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.", "Template for the email sent to the user when a new conversation or ticket is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}.": "Sablon a felhasználónak küldött e-mail<PERSON>z, amikor ú<PERSON> vagy jegy jön l<PERSON>. Hasz<PERSON><PERSON><PERSON> szöveget, HTML-t és a következő egyesítési mezőket: {conversation_url_parameter}, {user_name}, {message}, {attachments}.", "Template languages": "Sablonnyelvek", "Template name": "Sablonnév", "Template of the admin notification email. You can use text, HTML, and the following merge field and more: {carts}. Enter the email you want to send notifications to in the email address field.": "A rendszergazdai értesítő e-mail sablonja. <PERSON>z<PERSON><PERSON><PERSON> szöveget, HTML-t, valamint a következő egyesítési mezőt és egyebeket: {carts}. Í<PERSON><PERSON> be az e-mail cím mezőbe azt az e-mailt, amelyre értesítést szeretne küldeni.", "Template of the email sent to the customer after a product has been removed from the cart. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "A termék kosárból való eltávolítása után a vásárlónak küldött e-mail sablonja. Használhat szöveget, HTML-t, valamint a következő egyesítési mezőket és egyebeket: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the first notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "<PERSON>z első értesítő e-mail sablonja. Hasz<PERSON><PERSON><PERSON> s<PERSON>öveget, HTML-t, valamint a következő egyesítési mezőket és egyebeket: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the second notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "A második értesítő e-mail sablonja. <PERSON>z<PERSON><PERSON><PERSON> s<PERSON>ö<PERSON>get, HTML-t, valamint a következő egyesítési mezőket és egyebeket: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the waiting list notification email. You can use text, HTML, and the following merge field and more: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.": "A várólistáról értesítő e-mail sablonja. Használhat szöveget, HTML-t, valamint a következő egyesítési mezőt és egyebeket: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.", "Terms link": "Feltételek linkje", "Tertiary color": "Harmadlagos szín", "Test Slack": "Tesztelje Slack", "Test template": "Tesztsablon", "Text": "Szöveg", "Text message fallback": "Szöveges üzenet tartalék", "Text message notifications": "SMS-értesítések", "Text messages": "Szöveges üzenetek", "The product is not in the cart.": "A termék nincs a kosárban.", "The workspace name you are using to synchronize Slack.": "A workspace név, amelyet a Slack szinkronizálásához has<PERSON>l.", "This is your main Slack channel ID, which is usually the #general channel. You will get this code by completing the Slack synchronization.": "Ez a fő Slack-csatornaazonosí<PERSON>ód, amely <PERSON>lá<PERSON> az #általános csatorna. Ezt a kódot a Slack szinkronizálás befejezésével kapja meg.", "This returns the Masi Chat path of your server.": "Ez visszaadja a szerver Masi Chat elérési útját.", "This returns your Masi Chat URL.": "Ez visszaadja az Ön Masi Chat URL-jét.", "Ticket custom fields": "<PERSON><PERSON><PERSON> egy<PERSON>i <PERSON>", "Ticket email": "Jegy e-mail", "Ticket field names": "Jegymezők nevei", "Ticket fields": "Jegymezők", "Ticket only": "Csak jegy", "Ticket products selector": "Jegytermék-választó", "Title": "Cím", "Top": "Top", "Top bar": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "Training via cron job": "Képzés cron jobon k<PERSON>ü<PERSON>", "Transcript": "<PERSON><PERSON><PERSON>", "Transcript settings.": "Átírási beállí<PERSON>ok.", "Trigger": "<PERSON><PERSON><PERSON>", "Trigger the Dialogflow Welcome Intent for new visitors when the welcome message is active.": "Indítsa el a Welcome Intent párbeszédablakot az új látogatók számára, amikor az üdvözlő üzenet aktív.", "Troubleshoot": "Hibaelhárítás", "Troubleshoot problems": "Hibák elhárít<PERSON>", "Twilio settings": "<PERSON><PERSON><PERSON>", "Twilio template": "<PERSON><PERSON><PERSON> sablon", "Unsubscribe": "Leiratkozás", "Upload attachments to Amazon S3.": "Töltsön fel mellékleteket a Amazon S3-ba.", "Usage Limit": "Használati korlát", "Use this option to change the PWA icon. See the docs for more details.": "Ezzel az opcióval módosíthatja a PWA ikont. További részletekért lásd a dokumentumokat.", "User details": "Felhasználói adatok", "User details in success message": "Felhasználói adatok a sikeres üzenetben", "User email notifications": "Felhasználói e-mail értesítések", "User login form information.": "Felhasználói bejelentkezési űrlap adatai.", "User message template": "Felhasználói üzenet sablon", "User name as title": "Felhasználónév címként", "User notification email": "Felhasználói értesítő e-mail", "User registration form information.": "Felhasz<PERSON><PERSON><PERSON> regisztr<PERSON><PERSON><PERSON> adata<PERSON>.", "User roles": "Felhaszná<PERSON><PERSON><PERSON>epek", "User system": "<PERSON>lhasz<PERSON><PERSON><PERSON><PERSON> re<PERSON>", "Username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Users and agents": "Felhasználók és ügynökök", "Users area": "Felhasználói terület", "Users only": "Csak felhasználók", "Users table additional columns": "A felhasználók további oszlopokat táblázatba foglalnak", "UTC offset": "UTC eltolás", "Variables": "Változók", "View channels": "Csatornák megtekintése", "View unassigned conversations": "Tekintse meg a hozzá nem rendelt beszélgetéseket", "Visibility": "<PERSON><PERSON><PERSON><PERSON>", "Visitor default name": "<PERSON><PERSON><PERSON><PERSON><PERSON> alapértelmezett neve", "Visitor name prefix": "Látogatónév előtag", "Volume": "<PERSON><PERSON><PERSON>", "Volume - admin": "Kötet - admin", "Waiting list": "Várólista", "Waiting list - Email": "Várólista – E-mail", "Webhook URL": "Webhook URL", "Webhooks": "Webhooks", "Webhooks are information sent in background to a unique URL defined by you when something happens.": "A webhookok a háttérben egy Ön által meghatározott egyedi URL-re küldött információk, amikor valami történik.", "Website": "Weboldal", "WeChat settings": "<PERSON><PERSON><PERSON>", "Welcome message": "Üdvözlő üzenet", "Whmcs admin URL": "Whmcs adminisztrátori URL", "Whmcs admin URL. Ex. https://example.com/whmcs/admin/": "Whmcs adminisztrátori URL. Volt. https://example.com/whmcs/admin/", "WordPress registration": "WordPress regisztr<PERSON><PERSON><PERSON>", "Yes, we ship in": "Igen, szállítjuk", "You haven't placed an order yet.": "Még nem adott le rendelést.", "You will get this code by completing the Dialogflow synchronization.": "Ezt a kódot a Dialogflow szinkronizálás befejezésével kapja meg.", "You will get this code by completing the Slack synchronization.": "Ezt a kódot a Slack szinkronizálás befejezésével kapja meg.", "You will get this information by completing the synchronization.": "Ezt az információt a szinkronizálás befejezésével kapja meg.", "Your cart is empty.": "Az Ön kosara ü<PERSON>.", "Your turn message": "Az Ön körüzenete", "Your username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>eved", "Your WhatsApp catalogue details.": "A WhatsApp katalógus részletei.", "Zendesk settings": "Zendesk beállítások", "Activate the Right-To-Left (RTL) reading layout for the admin area.": "Aktiválja a jobbról balra (RTL) olvasási elrendezést az adminisztrációs területen.", "Activate the Right-To-Left (RTL) reading layout.": "Aktiválja a jobbról balra (RTL) olvasási elrendezést.", "Disable chatbot store integration": "Csevegőrobot-áruház integrációjának let<PERSON>", "Enable logs": "Naplók engedélyezése", "Hide note information": "Jegyzetinformációk elrejtése", "Manage the notes settings.": "A jegyzetek beállításainak kezelése.", "Notes settings": "Jegyzetek beállításai", "One conversation per user": "<PERSON><PERSON> f<PERSON>", "RTL": "<PERSON><PERSON><PERSON><PERSON><PERSON> balra", "Stop the chatbot from directly accessing your store to provide answers.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>d meg, hogy a chatbot közvetlenül hozzáférjen az üzletedhez válaszok adásához."}