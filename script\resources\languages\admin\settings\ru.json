{"{product_name} has no {product_attribute_name} variants.": "{product_name} не имеет {product_attribute_name} вариантов.", "360dialog settings": "Настройки диалога 360", "360dialog template": "Шаблон диалога 360", "Abandoned cart notification": "Уведомление о брошенной корзине", "Abandoned cart notification - Admin email": "Уведомление о брошенной корзине - адрес электронной почты администратора", "Abandoned cart notification - First email": "Уведомление о брошенной корзине - первое электронное письмо", "Abandoned cart notification - Second email": "Уведомление о брошенной корзине - второе письмо", "Accept button text": "Принять текст кнопки", "Account SID": "SID учетной записи", "Activate the Slack integration.": "Активируйте интеграцию со S<PERSON>ck.", "Activate the Zendesk integration": "Активируйте интеграцию Zendesk", "Activate this option if you don't want to translate the settings area.": "Активируйте эту опцию, если вы не хотите переводить область настроек.", "Active": "Активный", "Active - admin": "Активный - администратор", "Active eCommerce CMS URL. Ex. https://shop.com/": "Active eCommerce URL CMS. Бывший. https://shop.com/", "Active eCommerce URL": "Active eCommerce URL", "Active for agents": "Активен для агентов", "Active for users": "Активен для пользователей", "Active webhooks": "Активные вебхуки", "Add a delay (ms) to the bot's responses. Default is 2000.": "Добавьте задержку (мс) к ответам бота. По умолчанию 2000.", "Add and manage additional support departments.": "Добавляйте дополнительные отделы поддержки и управляйте ими.", "Add and manage saved replies that can be used by agents in the chat editor. Saved replies can be printed by typing # followed by the reply name plus space. Use \\n to do a line break.": "Добавляйте сохраненные ответы, которые могут использовать агенты, и управляйте ими в редакторе чата. Сохраненные ответы можно распечатать, набрав #, а затем имя ответа и пробел. Используйте \\n, чтобы сделать разрыв строки.", "Add and manage tags.": "Добавляйте теги и управляйте ими.", "Add comma separated WordPress user roles. The Masi Chat administration area will be available for new roles, in addition to the default one: editor, administrator, author.": "Добавьте разделенные запятыми роли пользователей WordPress. Область администрирования Masi Chat будет доступна для новых ролей, помимо стандартной: редактор, администратор, автор.", "Add custom fields to the new ticket form.": "Добавьте настраиваемые поля в новую форму заявки.", "Add custom fields to the user profile details.": "Добавьте настраиваемые поля к деталям профиля пользователя.", "Add Intents": "Доб<PERSON><PERSON><PERSON><PERSON><PERSON> Intents", "Add Intents to saved replies": "Добавить Intents к сохраненным ответам", "Add WhatsApp phone number details here.": "Добавьте сюда данные номера телефона WhatsApp.", "Adjust the chat button position. Values are in px.": "Отрегулируйте положение кнопки чата. Значения указаны в пикселях.", "Admin icon": "Значок администратора", "Admin IDs": "Идентификаторы администратора", "Admin login logo": "Логотип для входа в систему администратора", "Admin login message": "Сообщение для входа администратора", "Admin notifications": "Уведомления администратора", "Admin title": "Заголовок администратора", "Agent area": "Агентская зона", "Agent details": "Детали агента", "Agent email notifications": "Уведомления агента по электронной почте", "Agent ID": "Идентификатор агента", "Agent linking": "Связывание агента", "Agent message template": "Шаблон сообщения агента", "Agent notification email": "Электронное письмо с уведомлением агента", "Agent privileges": "Привилегии агента", "Agents": "Агенты", "Agents and admins tab": "Вкладка Агенты и администраторы", "Agents menu": "Меню агентов", "Agents only": "Только агенты", "All": "Все", "All channels": "Все каналы", "All messages": "Все сообщения", "All questions": "Все вопросы", "Allow only extended licenses": "Разрешить только расширенные лицензии", "Allow only one conversation": "Разрешить только один разговор", "Allow only one conversation per user.": "Разрешить только один разговор для каждого пользователя.", "Allow the chatbot to reply to the user's emails if the answer is known and email piping is active.": "Разрешить чат боту отвечать на электронные письма пользователя, если ответ известен и активен конвейер электронной почты.", "Allow the chatbot to reply to the user's text messages if the answer is known.": "Разрешить чат боту отвечать на текстовые сообщения пользователя, если ответ известен.", "Allow the user to archive a conversation and hide archived conversations.": "Разрешить пользователю архивировать беседу и скрывать заархивированные беседы.", "Allow users to contact you via their favorite messaging apps.": "Разрешите пользователям связываться с вами через их любимые приложения для обмена сообщениями.", "Allow users to select a product on ticket creation.": "Разрешить пользователям выбирать продукт при создании заявки.", "Always all messages": "Всегда все сообщения", "Always incoming messages only": "Всегда только входящие сообщения", "Always sort conversations by date in the admin area.": "Всегда сортируйте разговоры по дате в админке.", "API key": "API-к<PERSON><PERSON><PERSON>", "Append the registration user details to the success message.": "Добавьте данные о зарегистрированном пользователе в сообщение об успешном завершении.", "Apply a custom background image for the header area.": "Примените собственное фоновое изображение для области заголовка.", "Apply changes": "Применить изменения", "Apply to": "Применить к", "Archive all user channels in the Slack app. This operation may take a long time to complete. Important: All of your slack channels will be archived.": "Архивируйте все пользовательские каналы в приложении Slack. Эта операция может занять много времени. Важно: все ваши резервные каналы будут заархивированы.", "Archive automatically the conversations marked as read every 24h.": "Автоматически архивируйте разговоры, помеченные как прочитанные, каждые 24 часа.", "Archive channels": "Архивные каналы", "Archive channels now": "Архивировать каналы сейчас", "Articles": "Статьи", "Articles area": "Область статей", "Articles button link": "Ссылка на кнопку «Статьи»", "Articles page URL": "URL страницы статей", "Artificial Intelligence": "Искусственный интеллект", "Assign a department to all conversations started from Google Business Messages. Enter the department ID.": "Назначьте отдел всем разговорам, начатым из Google Business Messages. Введите идентификатор отдела.", "Assign a department to all conversations started from Twitter. Enter the department ID.": "Назначьте отдел всем разговорам, начатым в Твиттере. Введите идентификатор отдела.", "Assign a department to all conversations started from Viber. Enter the department ID.": "Назначьте отдел всем разговорам, начатым в Viber. Введите идентификатор отдела.", "Assign a department to all conversations started from WeChat. Enter the department ID.": "Назначьте отдел всем разговорам, начатым в WeChat. Введите идентификатор отдела.", "Assign different departments to conversations started from different Google Business Messages locations. This setting overrides the default department.": "Назначьте разные отделы беседам, начатым из разных местоположений Google Business Messages. Этот параметр переопределяет отдел по умолчанию.", "Assistant": "Ассистент", "Assistant ID": "Идентификатор Ассистента", "Attachments list": "Список вложений", "Audio file URL - admin": "URL-адрес аудиофайла — admin", "Automatic": "Автоматически", "Automatic human takeover": "Автоматический захват человека", "Automatic translation": "Автоматический перевод", "Automatic updates": "Автоматические обновления", "Automatically archive conversations": "Автоматически архивировать разговоры", "Automatically assigns a department based on the user's active plans. Insert -1 as plan ID for users without any plan.": "Автоматически назначает отдел на основе активных планов пользователя. Вставьте -1 в качестве идентификатора плана для пользователей без плана.", "Automatically check and install new updates. A valid Envato Purchase Code and valid apps's license keys are required.": "Автоматически проверять и устанавливать новые обновления. Требуются действующий код покупки Envato и действующие лицензионные ключи приложений.", "Automatically collapse the conversation details panel, and other panels, of the admin area.": "Автоматически сворачивать панель сведений о беседе и другие панели административной области.", "Automatically create a department for each website and route the conversations of each website to the right department. This setting requires a WordPress Multisite installation.": "Автоматически создавайте отдел для каждого веб-сайта и направляйте разговоры с каждого веб-сайта в нужный отдел. Для этого параметра требуется установка WordPress Multisite.", "Automatically hide the conversation details panel.": "Автоматически скрывать панель сведений о разговоре.", "Automatically send cart reminders to customers with products in their carts. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Автоматически отправляйте напоминания о корзине покупателям с товарами в тележках. Вы можете использовать следующие поля слияния и другие: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.", "Automatically sync Zendesk customers with {R}, view Zendesk tickets, or create new ones without leaving {R}.": "Автоматически синхронизируйте клиентов Zendesk с {R}, просматривайте заявки Zendesk или создавайте новых, не выходя из {R}.", "Automatically synchronize products, categories, tags, and more with Dialogflow, and enable the bot to answer autonomously to questions related to your shop.": "Автоматически синхронизируйте продукты, категории, теги и многое другое с Dialogflow и дайте возможность боту автономно отвечать на вопросы, связанные с вашим магазином.", "Automatically translate admin area": "Автоматически переводить админку", "Automatically translate the admin area to match the agent profile language or browser language.": "Автоматический перевод области администрирования в соответствии с языком профиля агента или языком браузера.", "Avatar image": "Изображение аватара", "Away mode": "Режим отсутствия", "Before initiating the chat, the user must accept a privacy message in order to gain access.": "Прежде чем начать чат, пользователь должен принять сообщение о конфиденциальности, чтобы получить доступ.", "Birthday": "День рождения", "Body variables": "Переменные тела", "Bot name": "Имя бота", "Bot profile image": "Изображение профиля бота", "Bot response delay": "Задержка ответа бота", "Bottom": "Нижний", "Brand": "Марка", "Built-in chat button icons": "Встроенные значки кнопок чата", "Business Account ID": "Идентификатор бизнес-аккаунта", "Button action": "Действие кнопки", "Button name": "Название кнопки", "Button text": "Текст кнопки", "Button variables": "Переменные кнопки", "Cancel button text": "Текст кнопки отмены", "Cart": "Корзина", "Cart follow up message": "Сообщение о корзине", "Catalogue details": "Детали каталога", "Catalogue ID": "Идентификатор каталога", "Change the chat button image with a custom one.": "Измените изображение кнопки чата на собственное.", "Change the default field names.": "Измените имена полей по умолчанию.", "Change the message text in the header area of the chat widget. This text will be replaced by the agent headline once the first reply is sent.": "Измените текст сообщения в области заголовка виджета чата. Этот текст будет заменен заголовком агента после отправки первого ответа.", "Change the title text in the header area of the chat widget. This text will be replaced by the agent's name once the first reply is sent.": "Измените текст заголовка в области заголовка виджета чата. Этот текст будет заменен именем агента после отправки первого ответа.", "Channel ID": "ID канала", "Channels": "Каналы", "Channels filter": "<PERSON>иль<PERSON><PERSON> ка<PERSON><PERSON>ов", "Chat": "Чат", "Chat and admin": "Чат и администратор", "Chat background": "Фон чата", "Chat button icon": "Значок кнопки чата", "Chat button offset": "Смещение кнопки чата", "Chat message": "Сообщение в чате", "Chat only": "Только чат", "Chat position": "Позиция в чате", "Chatbot": "Чат бот", "Chatbot mode": "Режим чат бота", "Check Requirements": "Проверить требования", "Check the server configurations and make sure it has all the requirements.": "Проверьте конфигурацию сервера и убедитесь, что он соответствует всем требованиям.", "Checkout": "Проверить", "Choose a background texture for the chat header and conversation area.": "Выберите текстуру фона для заголовка чата и области разговора.", "Choose where to display the chat. Enter the values separated by commas.": "Выберите, где отображать чат. Введите значения, разделенные запятыми.", "Choose which fields to disable from the tickets area.": "Выберите поля, которые нужно отключить в области заявок.", "Choose which fields to include in the new ticket form.": "Выберите, какие поля включить в новую форму заявки.", "Choose which fields to include in the registration form. The name field is included by default.": "Выберите, какие поля включить в форму регистрации. Поле имени включено по умолчанию.", "Choose which user system the front-end chat will use to register and log in users.": "Выберите, какую пользовательскую систему будет использовать интерфейсный чат для регистрации и входа пользователей.", "City": "Город", "Clear flows": "Очистить потоки", "Click the button to start the Dialogflow synchronization.": "Нажмите кнопку, чтобы начать синхронизацию Dialogflow.", "Click the button to start the Slack synchronization. Localhost cannot and does not receive messages. Log in with another account or as a visitor to perform your tests.": "Нажмите кнопку, чтобы начать синхронизацию Slack. Localhost не может и не получает сообщения. Войдите в систему с другой учетной записью или в качестве посетителя, чтобы выполнить свои тесты.", "Client email": "Электронная почта клиента", "Client ID": "ID клиента", "Client token": "То<PERSON>ен клиента", "Close chat": "Закрыть чат", "Close message": "Закрыть сообщение", "Cloud API numbers": "Номера облачных API", "Cloud API settings": "Настройки облачного API", "Cloud API template fallback": "Резервный шаблон Cloud API", "Code": "<PERSON>од", "Collapse panels": "Свернуть панели", "Color": "Цвет", "Communicate with your users right from Slack. Send and receive messages and attachments, use emojis, and much more.": "Общайтесь со своими пользователями прямо из Slack. Отправляйте и получайте сообщения и вложения, используйте смайлики и многое другое.", "Company": "Компания", "Concurrent chats": "Кошки-конкуренты", "Configuration URL": "URL-адрес конфигурации", "Confirm button text": "Подтвердить текст кнопки", "Confirmation message": "Подтверждающее сообщение", "Connect smart chatbots and automate conversations by using one of the most advanced forms of artificial intelligence in the world.": "Подключайте умных чат ботов и автоматизируйте разговоры с помощью одной из самых передовых форм искусственного интеллекта в мире.", "Connect stores to agents.": "Подключайте магазины к агентам.", "Connect your Telegram bot to {R} to read and reply to all messages sent to your Telegram bot directly in {R}.": "Подключите своего бота Telegram к {R}, чтобы читать и отвечать на все сообщения, отправленные вашему боту Telegram, непосредственно в {R}.", "Connect your Viber bot to {R} to read and reply to all messages sent to your Viber bot directly in {R}.": "Подключите своего бота Viber к {R}, чтобы читать и отвечать на все сообщения, отправленные вашему боту Viber, непосредственно в {R}.", "Connect your Zalo Official Account to {R} to read and reply to all messages sent to your Zalo Official Account directly in {R}.": "Подключите свой официальный аккаунт Zalo к {R}, чтобы читать и отвечать на все сообщения, отправленные на ваш официальный аккаунт Zalo, непосредственно в {R}.", "Content": "Содержание", "Content template SID": "SID шаблона контента", "Conversation profile": "Профиль разговора", "Conversations data": "Данные разговоров", "Convert all emails": "Конвертируйте все электронные письма", "Cookie domain": "Домен cookie", "Country": "Страна", "Coupon discount (%)": "Купонная скидка (%)", "Coupon expiration (days)": "Срок действия купона (дни)", "Coupon expiration (seconds)": "Срок действия купона (секунды)", "Create a WordPress user upon registration.": "Создайте пользователя WordPress после регистрации.", "Create Intents now": "Создайте Intents сейчас", "Currency symbol": "Символ валюты", "Custom CSS": "Пользовательские CSS", "Custom fields": "Настраиваемые поля", "Custom JS": "Пользовательский JS", "Custom model ID": "Идентификатор пользовательской модели", "Custom parameters": "Пользовательские параметры", "Customize the link for the 'All articles' button.": "Настройте ссылку для кнопки «Все статьи».", "Dashboard display": "Отображение приборной панели", "Dashboard title": "Заголовок панели инструментов", "Database details": "Детали базы данных", "Database host": "Хост базы данных", "Database name": "Имя базы данных", "Database password": "Пароль базы данных", "Database prefix": "Префикс базы данных", "Database user": "Пользователь базы данных", "Decline button text": "Отклонить текст кнопки", "Declined message": "Отклоненное сообщение", "Default": "По умолчанию", "Default body text": "Основной текст по умолчанию", "Default conversation name": "Название беседы по умолчанию", "Default department": "Отдел по умолчанию", "Default department ID": "Идентификатор отдела по умолчанию", "Default form": "Форма по умолчанию", "Default header text": "Текст заголовка по умолчанию", "Delay (ms)": "Задержка (мс)", "Delete all leads and all messages and conversations linked to them.": "Удалите все лиды и все связанные с ними сообщения и разговоры.", "Delete conversation": "Удалить беседу", "Delete leads": "Удалить лиды", "Delete message": "Удаленное сообщение", "Delete the built-in flows.": "Удалите встроенные потоки.", "Delimiter": "Разделитель", "Department": "Отделение", "Department ID": "ID отдела", "Departments": "Отделы", "Departments settings": "Настройки отделов", "Desktop notifications": "Уведомления на рабочем столе", "Dialogflow - Department linking": "Dialogflow — связывание отделов", "Dialogflow chatbot": "Чат бот Dialogflow", "Dialogflow edition": "Редакция диалогового потока", "Dialogflow Intent detection confidence": "Надежность обнаружения намерений Dialogflow", "Dialogflow location": "Расположение диалогового потока", "Dialogflow spelling correction": "Исправление орфографии диалогового потока", "Dialogflow welcome Intent": "Приветствие Dialogflow Намерение", "Disable agents check": "Отключить проверку агентов", "Disable and hide the chat widget if all agents are offline.": "Отключите и скройте виджет чата, если все агенты не в сети.", "Disable and hide the chat widget outside of scheduled office hours.": "Отключите и скройте виджет чата в нерабочее время.", "Disable any features that you don't need.": "Отключите все функции, которые вам не нужны.", "Disable auto-initialization of the chat widget. When this setting is active you must initialize the chat widget with a custom JavaScript API code written by you. If the chat doesn't appear and this setting is enabled, disable it.": "Отключите автоинициализацию виджета чата. Когда этот параметр активен, вы должны инициализировать виджет чата с помощью написанного вами пользовательского кода JavaScript API. Если чат не появляется и этот параметр включен, отключите его.", "Disable auto-initialization of the tickets area. When this setting is active you must initialize the tickets area with a custom JavaScript API code written by you. If the tickets area doesn't appear and this setting is enabled, disable it.": "Отключить автоинициализацию области тикетов. Когда этот параметр активен, вы должны инициализировать область тикетов с помощью написанного вами пользовательского кода JavaScript API. Если область тикетов не отображается и этот параметр включен, отключите его.", "Disable chatbot": "Отключить чат бота", "Disable cron job": "Отключить задание cron", "Disable dashboard": "Отключить приборную панель", "Disable during office hours": "Отключить в рабочее время", "Disable features": "Отключить функции", "Disable features you don't use and improve the chat performance.": "Отключите функции, которые вы не используете, и улучшите производительность чата.", "Disable file uploading capabilities within the chat.": "Отключите возможность загрузки файлов в чате.", "Disable for messaging channels": "Отключить для каналов обмена сообщениями", "Disable for the tickets area": "Отключить для зоны тикетов", "Disable invitation": "Отключить приглашение", "Disable online status check": "Отключить проверку статуса онлайн", "Disable outside of office hours": "Отключить в нерабочее время", "Disable password": "Отключить пароль", "Disable registration during office hours": "Отключить регистрацию в рабочее время", "Disable registration if agents online": "Отключить регистрацию, если агенты онлайн", "Disable the automatic invitation of agents to the channels.": "Отключить автоматическое приглашение агентов на каналы.", "Disable the channels filter.": "Отключите фильтр каналов.", "Disable the chatbot for the tickets area.": "Отключите чат бота для области тикетов.", "Disable the chatbot for this channel only.": "Отключите чат бота только для этого канала.", "Disable the dashboard, and allow only one conversation per user.": "Отключите панель управления и разрешите только один разговор для каждого пользователя.", "Disable the login and remove the password field from the registration form.": "Отключите логин и удалите поле пароля из регистрационной формы.", "Disable uploads": "Отключить загрузку", "Disable voice message capabilities within the chat.": "Отключите возможности голосовых сообщений в чате.", "Disable voice messages": "Отключить голосовые сообщения", "Disabled": "Отключено", "Display a brand image in the header area. This only applies for the 'brand' header type.": "Отобразите изображение бренда в области заголовка. Это применимо только к типу заголовка «бренд».", "Display categories": "Категории отображения", "Display images": "Отображение изображений", "Display in conversation list": "Отображать в списке разговоров", "Display in dashboard": "Отображение на приборной панели", "Display online agents only": "Отобража<PERSON>ь только онлайн-агентов", "Display the articles section in the right area.": "Отобразите раздел статей в правой области.", "Display the dashboard instead of the chat area on initialization.": "Отображать панель управления вместо области чата при инициализации.", "Display the feedback form to rate the conversation when it is archived.": "Отобразите форму обратной связи, чтобы оценить беседу, когда она будет заархивирована.", "Display the user full name in the left panel instead of the conversation title.": "Отображать полное имя пользователя на левой панели вместо заголовка беседы.", "Display the user's profile image within the chat.": "Отображение изображения профиля пользователя в чате.", "Display user name in header": "Отображать имя пользователя в заголовке", "Display user's profile image": "Показать изображение профиля пользователя", "Displays additional columns in the user table. Enter the name of the fields to add.": "Отображает дополнительные столбцы в пользовательской таблице. Введите название полей для добавления.", "Distribute conversations proportionately between agents and notify visitors of their position within the queue. Response time is in minutes. You can use the following merge fields in the message: {position}, {minutes}. They will be replaced by the real values in real-time.": "Распределяйте разговоры между агентами пропорционально и уведомляйте посетителей об их положении в очереди. Время ответа в минутах. В сообщении можно использовать следующие поля слияния: {position}, {minutes}. Они будут заменены реальными значениями в реальном времени.", "Distribute conversations proportionately between agents, and block an agent from viewing the conversations of the other agents.": "Распределите разговоры между агентами пропорционально и запретите агенту просматривать разговоры других агентов.", "Do not send email notifications to admins": "Не отправлять уведомления по электронной почте администраторам", "Do not show tickets in chat": "Не показывать тикеты в чате", "Do not translate settings area": "Не переводить область настроек", "Download": "Скачать", "Edit profile": "Редактировать профиль", "Edit user": "Изменить пользователя", "Email address": "Адрес электронной почты", "Email and ticket": "Электронная почта и тикет", "Email header": "Заголовок электронного письма", "Email notification delay (hours)": "Задержка уведомления по электронной почте (часы)", "Email notifications via cron job": "Уведомления по электронной почте через задание cron", "Email only": "Только электронная почта", "Email piping": "Электронная почта", "Email piping server information and more settings.": "Информация о почтовом сервере и другие настройки.", "Email request message": "Электронное сообщение с запросом", "Email signature": "Подпись электронной почты", "Email template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "Шаблон письма для письма, отправляемого пользователю при ответе агента. Вы можете использовать текст, HTML и следующие поля слияния: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to an agent when a user sends a new message. You can use text, HTML, and the following merge fields: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "Шаблон письма для письма, отправляемого агенту, когда пользователь отправляет новое сообщение. Вы можете использовать текст, HTML и следующие поля слияния: {conversation_link}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Email template for the email sent to the user after submitting their email through the follow-up message form. You can use text, HTML, and the following merge fields: {user_name}, {user_email}.": "Шаблон электронного письма для отправки пользователю после отправки его электронного письма через форму последующего сообщения. Вы можете использовать текст, HTML и следующие поля слияния: {user_name}, {user_email}.", "Email template for the email sent to the user to verify their email address. Include the {code} merge field within your content, it will be replaced with the one-time code.": "Шаблон письма, отправляемого пользователю для подтверждения адреса электронной почты. Включите поле слияния {code} в свой контент, оно будет заменено одноразовым кодом.", "Email verification": "Подтверждение по электронной почте", "Email verification content": "Содержание проверки электронной почты", "Enable email verification with OTP.": "Включите проверку электронной почты с помощью одноразового пароля.", "Enable logging of agent activity": "Включить ведение журнала активности агента", "Enable the chatbot outside of scheduled office hours only.": "Включайте чат бота только в нерабочее время.", "Enable the registration only if all agents are offline.": "Включите регистрацию, только если все агенты отключены.", "Enable the registration outside of scheduled office hours only.": "Разрешить регистрацию только в нерабочее время.", "Enable this option if email notifications are sent via cron job.": "Включите эту опцию, если уведомления по электронной почте отправляются через задание cron.", "Enable ticket and chat support for subscribers only, view member profile details and subscription details in the admin area.": "Включите поддержку тикетов и чата только для подписчиков, просмотрите детали профиля участника и детали подписки в админке.", "Enter the bot token and click the button to synchronize the Telegram bot. Localhost cannot receive messages.": "Введите токен бота и нажмите кнопку для синхронизации бота Telegram. Localhost не может получать сообщения.", "Enter the bot token and click the button to synchronize the Viber bot. Localhost cannot receive messages.": "Введите токен бота и нажмите кнопку для синхронизации бота Viber. Localhost не может получать сообщения.", "Enter the database details of the Active eCommerce CMS database.": "Введите сведения о базе данных Active eCommerce базы данных CMS.", "Enter the database details of the Martfury database.": "Введите данные базы данных Martfury.", "Enter the database details of the Perfex database.": "Введите данные базы данных Perfex.", "Enter the database details of the WHMCS database.": "Введите данные базы данных WHMCS.", "Enter the default messages used by the chatbot when user question requires a dynamic answer.": "Введите сообщения по умолчанию, используемые чат ботом, когда вопрос пользователя требует динамического ответа.", "Enter the details of your Google Business Messages.": "Введите данные ваших бизнес-сообщений Google.", "Enter the details of your Twitter app.": "Введите данные своего приложения Twitter.", "Enter the LINE details to start using it. Localhost cannot receive messages.": "Введите данные LINE, чтобы начать использовать его. Localhost не может получать сообщения.", "Enter the URL of a .css file, to load it automatically in the admin area.": "Введите URL-адрес файла .css, чтобы он автоматически загружался в админку.", "Enter the URL of a .js file, to load it automatically in the admin area.": "Введите URL-адрес файла .js, чтобы он автоматически загружался в админку.", "Enter the URL of the articles page.": "Введите URL-адрес страницы статей.", "Enter the URLs of your shop": "Введите URL вашего магазина", "Enter the WeChat official account token. See the docs for more details.": "Введите токен официального аккаунта WeChat. См. документы для более подробной информации.", "Enter the Zalo details to start using it. Localhost cannot receive messages.": "Введите данные Zalo, чтобы начать его использовать. Localhost не может получать сообщения.", "Enter your 360dialog account settings information.": "Введите информацию о настройках учетной записи 360dialog.", "Enter your Envato Purchase Code to activate automatic updates and unlock all the features.": "Введите свой код покупки Envato, чтобы активировать автоматические обновления и разблокировать все функции.", "Enter your Twilio account details. You can use text and the following merge fields: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.": "Введите данные своей учетной записи Twilio. Вы можете использовать текст и следующие поля слияния: {message}, {recipient_name}, {sender_name}, {recipient_email}, {sender_email}, {conversation_url_parameter}.", "Enter your Twilio account settings information.": "Введите информацию о настройках своей учетной записи Twilio.", "Enter your WeChat Official Account information.": "Введите информацию о своей официальной учетной записи WeChat.", "Enter your Zendesk information.": "Введите информацию о Zendesk.", "Entities": "Entities", "Envato Purchase Code": "Код покупки Envato", "Envato purchase code validation": "Проверка кода покупки Envato", "Exclude products": "Исключить продукты", "Export all settings.": "Экспортируйте все настройки.", "Export settings": "Настройки экспорта", "Facebook pages": "Страницы в фейсбуке", "Fallback message": "Запасное сообщение", "Filters": "Фильтры", "First chat message": "Первое сообщение в чате", "First reminder delay (hours)": "Задержка первого напоминания (часы)", "First ticket form": "Форма первого тикета", "Flash notifications": "Флэш-уведомления", "Follow up - Email": "Последующие действия - электронная почта", "Follow up email": "Последующее электронное письмо", "Follow up message": "Следующее сообщение", "Follows a conversation between a human agent and an end user and provide response suggestions to the human agent in real-time.": "Следит за разговором между агентом-человеком и конечным пользователем и предоставляет предложения ответа агенту-человеку в режиме реального времени.", "Follow-up email template. You can use text, HTML, and the following merge fields and more: {coupon}, {product_names}, {user_name}.": "Дополнительный шаблон электронного письма. Вы можете использовать текст, HTML, а также следующие поля слияния и многое другое: {coupon}, {product_names}, {user_name}.", "Force language": "Язык силы", "Force log out": "Принудительный выход", "Force the chat to ignore the language preferences, and to use always the same language.": "Заставьте чат игнорировать языковые настройки и всегда использовать один и тот же язык.", "Force the loggout of Masi Chat agents if they are not logged in WordPress.": "Принудительно выйти из системы для агентов <PERSON><PERSON>, если они не вошли в систему WordPress.", "Force users to use a different conversation for each store and hide conversations from other stores from store administrators.": "Заставьте пользователей использовать разные разговоры для каждого магазина и скройте разговоры из других магазинов от администраторов магазинов.", "Force users to use only one phone country code.": "Заставьте пользователей использовать только один телефонный код страны.", "Form message": "Форма сообщения", "Form title": "Заголовок формы", "Frequency penalty": "Штраф за частоту", "Full visitor details": "Полная информация о посетителе", "Function name": "Имя функции", "Generate conversations data": "Создавать данные разговоров", "Generate user questions": "Создавайте вопросы пользователей", "Get configuration URL": "Получить URL-адрес конфигурации", "Get it from the APP_KEY value of the file .env located in the root directory of Active eCommerce.": "Получите его из значения APP_KEY файла .env, расположенного в корневом каталоге Active eCommerce.", "Get it from the APP_KEY value of the file .env located in the root directory of Martfury.": "Получите его из значения APP_KEY файла .env, расположенного в корневом каталоге Martfury.", "Get Path": "Получить путь", "Get Service Worker path": "Получить путь Service Worker", "Get URL": "Получить URL", "Google and Dialogflow settings.": "Настройки Google и Dialogflow.", "Google search": "Поиск Гугл", "Header": "Заголовок", "Header background image": "Фоновое изображение заголовка", "Header brand image": "Изображение бренда в заголовке", "Header message": "Заголовок сообщения", "Header title": "Заголовок заголовка", "Header type": "Тип заголовка", "Header variables": "Переменные заголовка", "Hide": "Скрывать", "Hide agent's profile image": "Скрыть изображение профиля агента", "Hide archived tickets": "Скрыть архивные заявки", "Hide archived tickets from users.": "Скрыть заархивированные заявки от пользователей.", "Hide chat if no agents online": "Скрыть чат, если в сети нет агентов", "Hide chat outside of office hours": "Скрыть чат в нерабочее время", "Hide conversation details panel": "Скрыть панель сведений о разговоре", "Hide conversations of other agents": "Скрыть разговоры других агентов", "Hide on mobile": "Скрыть на мобильном", "Hide the agent's profile image within the chat.": "Скрыть изображение профиля агента в чате.", "Hide tickets from the chat widget and chats from the ticket area.": "Скрыть тикеты из виджета чата и чаты из области тикетов.", "Hide timetable": "Скрыть расписание", "Host": "Х<PERSON><PERSON><PERSON><PERSON><PERSON>", "Human takeover": "Человеческий захват", "If no agents respond within the specified time interval, a message will be sent to request the user's details, such as their email.": "Если ни один агент не ответит в течение указанного интервала времени, будет отправлено сообщение с запросом данных пользователя, например адреса электронной почты.", "If the chatbot doesn't understand a user's question, forwards the conversation to an agent.": "Если чат бот не понимает вопрос пользователя, перенаправляет разговор агенту.", "Image": "Изображение", "Import admins": "Импортировать админов", "Import all settings.": "Импортируйте все настройки.", "Import articles": "Импортировать статьи", "Import contacts": "Импортировать контакты", "Import customers": "Импортировать клиентов", "Import customers into Masi Chat. Only new customers will be imported.": "Импортируйте клиентов в Masi Chat. Будут импортированы только новые клиенты.", "Import settings": "Настройки импорта", "Import users": "Импортировать пользователей", "Import users from a CSV file.": "Импорт пользователей из CSV-файла.", "Import vendors": "Импорт поставщиков", "Import vendors into Masi Chat as agents. Only new vendors will be imported.": "Импортируйте поставщиков в Masi Chat в качестве агентов. Будут импортированы только новые поставщики.", "Improve chat performance with Pusher and WebSockets. This setting stops all AJAX/HTTP real-time requests that slow down your server and use instead the WebSockets.": "Повысьте производительность чата с помощью Pusher и WebSockets. Этот параметр останавливает все запросы AJAX / HTTP в реальном времени, которые замедляют ваш сервер, и вместо этого использует WebSockets.", "Include custom fields": "Включить настраиваемые поля", "Include custom fields in the registration form.": "Включите настраиваемые поля в регистрационную форму.", "Include the password field in the registration form.": "Включите поле пароля в регистрационную форму.", "Incoming conversations and messages": "Входящие разговоры и сообщения", "Incoming conversations only": "Только входящие разговоры", "Incoming messages only": "Только входящие сообщения", "Increase sales and connect you and sellers with customers in real-time by integrating Active eCommerce with Masi Chat.": "Увеличивайте продажи и связывайте себя и продавцов с клиентами в режиме реального времени за счет интеграции Active eCommerce с Советом поддержки.", "Increase sales, provide better support, and faster solutions, by integrating WooCommerce with Masi Chat.": "Увеличьте продажи, обеспечьте лучшую поддержку и более быстрые решения за счет интеграции WooCommerce с Ma<PERSON>.", "Info message": "Информационное сообщение", "Initialize and display the chat widget and tickets only for members.": "Инициализировать и отображать виджет чата и тикеты только для участников.", "Initialize and display the chat widget only when the user is logged in.": "Инициализировать и отображать виджет чата только тогда, когда пользователь вошел в систему.", "Instance ID": "ID экземпляра", "Integrate OpenCart with {R} for real-time syncing of customers, order history access, and customer cart visibility.": "Интегрируйте OpenCart с {R} для синхронизации клиентов в реальном времени, доступа к истории заказов и видимости корзины клиентов.", "Interval (sec)": "Интервал (сек)", "IP banning": "Запрет по IP", "Label": "Ярлык", "Language": "Язык", "Language detection": "Определение языка", "Language detection message": "Сообщение об обнаружении языка", "Last name": "Фамилия", "Leave it blank if you don't know what this setting is! Entering an incorrect value will break the chat. Sets the main domain where chat is used to enable login and conversations sharing between the main domain and sub domains.": "Оставьте поле пустым, если вы не знаете, что это за настройка! Ввод неверного значения нарушит чат. Устанавливает основной домен, в котором используется чат, чтобы разрешить вход в систему и обмен беседами между основным доменом и поддоменами.", "Left": "Левый", "Left panel": "Левая панель", "Left profile image": "Изображение профиля слева", "Let the bot to search on Google to find answers to user questions.": "Позвольте боту искать в Google ответы на вопросы пользователей.", "Let the chatbot search on Google to find answers to user questions.": "Позвольте чат боту искать в Google ответы на вопросы пользователей.", "Lets your users reach you via Twitter. Read and reply to messages sent to your Twitter account directly from {R}.": "Позволяет вашим пользователям связаться с вами через Twitter. Читайте и отвечайте на сообщения, отправленные на вашу учетную запись Twitter, непосредственно из {R}.", "Lets your users reach you via WeChat. Read and reply to all messages sent to your WeChat official account directly from {R}.": "Позволяет вашим пользователям связаться с вами через WeChat. Читайте и отвечайте на все сообщения, отправленные на ваш официальный аккаунт WeChat непосредственно из {R}.", "Lets your users reach you via WhatsApp. Read and reply to all messages sent to your WhatsApp Business account directly from {R}.": "Позволяет вашим пользователям связаться с вами через WhatsApp. Читайте и отвечайте на все сообщения, отправленные на ваш аккаунт WhatsApp Business непосредственно из {R}.", "Link each agent with the corresponding Slack user, so when an agent replies via Slack it will be displayed as the assigned agent.": "Свяжите каждого агента с соответствующим пользователем Slack, чтобы, когда агент отвечает через Slack, он будет отображаться как назначенный агент.", "Link name": "Название ссылки", "Login form": "Форма входа", "Login initialization": "Инициализация входа", "Login verification URL": "URL подтверждения входа", "Logit bias": "Логит-смещение", "Make a backup of your Dialogflow agent first. This operation can take several minutes.": "Сначала сделайте резервную копию своего агента Dialogflow. Эта операция может занять несколько минут.", "Make the registration phone field mandatory.": "Сделайте поле для регистрации телефона обязательным.", "Manage": "Управлять", "Manage here the departments settings.": "Здесь можно управлять настройками отделов.", "Manage the tags settings.": "Управляйте настройками тегов.", "Manifest file URL": "Manifest URL файла", "Manual": "В ручную", "Manual initialization": "Ручная инициализация", "Martfury root directory path, e.g. /var/www/": "Путь к корневому каталогу Martfury, например, /var/www/", "Martfury shop URL, e.g. https://shop.com": "URL-адрес магази<PERSON>, например https://shop.com", "Max message limit": "Максимальный лимит сообщений", "Max tokens": "Максимальное количество токенов", "Members only": "Только для членов", "Members with an active paid plan only": "Только участники с активным платным планом", "Message": "Сообщение", "Message area": "Область сообщений", "Message rewrite button": "Кнопка перезаписи сообщения", "Message template": "Шаблон сообщения", "Message type": "Тип сообщения", "Messaging channels": "Каналы обмена сообщениями", "Messenger and Instagram settings": "Messenger и настройки Instagram", "Minify JS": "Минимизировать JS", "Minimal": "Минимальный", "Model": "Модель", "Multilingual": "Многоязычный", "Multilingual plugin": "Многоязычный плагин", "Multilingual via translation": "Многоязычный через перевод", "Multlilingual training sources": "Многоязычные источники обучения", "Name": "Имя", "Namespace": "Пространство имен", "New conversation email": "Электронная почта для новой беседы", "New conversation notification": "Уведомление о новом разговоре", "New ticket button": "Кнопка нового тикета", "Newsletter": "Новостная рассылка", "No delay": "Без задержки", "No results found.": "Результаты не найдены.", "No, we don't ship in": "Нет, мы не отправляем", "None": "Никто", "Note data scraping": "Обратите внимание на очистку данных", "Notes": "Примечания", "Notifications icon": "Значок уведомлений", "Notify the user when their message is sent outside of the scheduled office hours or all agents are offline.": "Уведомляйте пользователя, если его сообщение отправлено в нерабочие часы или когда все агенты находятся в автономном режиме.", "OA secret key": "Секретный ключ OA", "Offline message": "Автономное сообщение", "Offset": "Компенсировать", "On chat open": "В чате открыт", "On page load": "При загрузке страницы", "One conversation per agent": "Один разговор с агентом", "One conversation per department": "Один разговор на отдел", "Online users notification": "Уведомление онлайн-пользователей", "Only desktop": "Только рабочий стол", "Only general questions": "Только общие вопросы", "Only mobile devices": "Только мобильные устройства", "Only questions related to your sources": "Только вопросы, связанные с вашими источниками", "Open automatically": "Открывать автоматически", "Open chat": "Открыть чат", "Open the chat window automatically when a new message is received.": "Автоматически открывать окно чата при получении нового сообщения.", "OpenAI Assistants - Department linking": "OpenAI Assistants — связывание отделов", "OpenAI settings.": "Настройки OpenAI.", "Optional link": "Необязательная ссылка", "Order webhook": "Заказать вебхук", "Other": "Друг<PERSON>й", "Outgoing SMTP server information.": "Информация об исходящем SMTP-сервере.", "Page ID": "ID страницы", "Page IDs": "Идентификаторы страниц", "Page name": "Название страницы", "Page token": "То<PERSON>ен страницы", "Panel height": "Высота панели", "Panel name": "Название панели", "Panel title": "Заголовок панели", "Panels arrows": "Панели стрелки", "Password": "Пароль", "Perfex URL": "Perfex URL", "Performance optimization": "Оптимизация производительности", "Phone": "Телефон", "Phone number ID": "Идентификатор номера телефона", "Phone required": "Требуется телефон", "Place ID": "Идентификатор места", "Placeholder text": "Текст-заполнитель", "Play a sound for new messages and conversations.": "Воспроизведение звука для новых сообщений и разговоров.", "Popup message": "Всплывающее сообщение", "Port": "Порт", "Post Type slugs": "Post Type пули", "Presence penalty": "Штраф за присутствие", "Prevent admins from receiving email notifications.": "Запретите администраторам получать уведомления по электронной почте.", "Prevent agents from viewing conversations assigned to other agents. This setting is automatically enabled if routing or queue is active.": "Запретить агентам просматривать разговоры, назначенные другим агентам. Этот параметр включается автоматически, если активна маршрутизация или очередь.", "Prevent any abuse from users by limiting the number of messages sent to the chatbot from one device.": "Предотвратите любые злоупотребления со стороны пользователей, ограничив количество сообщений, отправляемых чат боту с одного устройства.", "Primary color": "Основной цвет", "Priority": "Приоритет", "Privacy link": "Ссылка на конфиденциальность", "Privacy message": "Сообщение о конфиденциальности", "Private chat": "Личная переписка", "Private chat linking": "Ссылка на приватный чат", "Private key": "Закрытый ключ", "Product IDs": "Идентификаторы продуктов", "Product removed notification": "Уведомление об удалении продукта", "Product removed notification - Email": "Уведомление об удалении продукта - электронная почта", "Profile image": "Изображение профиля", "Project ID": "Идентификатор проекта", "Project ID or Agent Name": "ID проекта или имя агента", "Prompt": "Быстрый", "Prompt - Message rewriting": "Подсказка - Перезапись сообщения", "Protect the tickets area from spam and abuse with Google reCAPTCHA.": "Защитите область заявок от спама и злоупотреблений с помощью Google reCAPTCHA.", "Provide help desk support to your customers by including a ticket area, with all chat features included, on any web page in seconds.": "Обеспечьте поддержку своих клиентов службой поддержки, разместив на любой веб-странице раздел для обращений со всеми функциями чата за считанные секунды.", "Provider": "Поставщик", "Purchase button text": "Текст кнопки покупки", "Push notifications": "Всплывающие уведомления", "Push notifications settings.": "Настройки push-уведомлений.", "Queue": "Очередь", "Rating": "<PERSON>ей<PERSON>инг", "Read and reply to messages sent from Google Search, Maps and brand-owned channels directly in {R}.": "Читайте и отвечайте на сообщения, отправленные из Google Поиска, Карт и каналов, принадлежащих бренду, непосредственно в {R}.", "Read, manage and reply to all messages sent to your Facebook pages and Instagram accounts directly from {R}.": "Читайте, управляйте и отвечайте на все сообщения, отправленные на ваши страницы Facebook и в учетные записи Instagram, непосредственно из {R}.", "Reconnect": "Восстановить соединение", "Redirect the user to the registration link instead of showing the registration form.": "Перенаправляйте пользователя по ссылке регистрации вместо отображения формы регистрации.", "Redirect the user to the specified URL if the registration is required and the user is not logged in. Leave blank to use the default registration form.": "Перенаправьте пользователя на указанный URL-адрес, если требуется регистрация и пользователь не вошел в систему. Оставьте поле пустым, чтобы использовать форму регистрации по умолчанию.", "Refresh token": "Обновить токен", "Register all visitors": "Зарегистрируйте всех посетителей", "Register all visitors automatically. When this option is not active, only the visitors that start a chat will be registered.": "Регистрируйте всех посетителей автоматически. Когда эта опция не активна, будут зарегистрированы только посетители, которые начинают чат.", "Registration / Login": "Регистрация / Вход", "Registration and login form": "Форма регистрации и входа", "Registration fields": "Поля регистрации", "Registration form": "Форма регистрации", "Registration link": "Ссылка для регистрации", "Registration redirect": "Перенаправление регистрации", "Rename the chat bot. Default is 'Bot'.": "Переименуйте чат бота. По умолчанию - «Бот».", "Rename the visitor name prefix. Default is 'User'.": "Переименуйте префикс имени посетителя. По умолчанию - «Пользователь».", "Repeat": "Повторение", "Repeat - admin": "Повторяю - админ", "Replace the admin login page message.": "Замените сообщение на странице входа администратора.", "Replace the brand logo on the admin login page.": "Замените логотип бренда на странице входа администратора.", "Replace the header title with the user's first name and last name when available.": "Если возможно, замените заголовок заголовка на имя и фамилию пользователя.", "Replace the top-left brand icon on the admin area and the browser favicon.": "Замените верхний левый значок бренда в области администрирования и значок браузера.", "Reply to user emails": "Ответить на электронные письма пользователей", "Reply to user text messages": "Отвечать на текстовые сообщения пользователей", "Reports": "Отчеты", "Reports area": "Область отчетов", "Request a valid Envato purchase code for registration.": "Запросите действительный код покупки Envato для регистрации.", "Request the user to provide their email address and then send a confirmation email to the user.": "Попросите пользователя предоставить свой адрес электронной почты, а затем отправьте ему электронное письмо с подтверждением.", "Require phone": "Требовать телефон", "Require registration": "Требовать регистрацию", "Require the user registration or login before start a chat. To enable the login area the password field must be included.": "Перед тем, как начать чат, потребуйте регистрацию пользователя или авторизацию. Чтобы включить область входа в систему, необходимо включить поле пароля.", "Require the user registration or login in order to use the tickets area.": "Требуйте регистрации пользователя или входа в систему, чтобы использовать область тикетов.", "Required": "Необходимый", "Response time": "Время отклика", "Restrict chat access by blocking IPs. List IPs with commas.": "Ограничьте доступ к чату, заблокировав IP-адреса. Перечислите IP-адреса через запятую.", "Returning visitor message": "Сообщение возвращающегося посетителя", "Rich messages": "Богатые сообщения", "Rich messages are code snippets that can be utilized within a chat message. They can contain HTML code and are automatically rendered in the chat. Rich messages can be used with the following syntax: [rich-message-name]. There are a tonne of built-in rich messages to choose from.": "Богатые сообщения - это фрагменты кода, которые можно использовать в сообщении чата. Они могут содержать HTML-код и автоматически отображаются в чате. Расширенные сообщения могут использоваться со следующим синтаксисом: [rich-message-name]. Есть тонна встроенных многофункциональных сообщений на выбор.", "Right": "Верно", "Right panel": "Правая панель", "Routing": "Маршрутизация", "Routing if offline": "Маршрутизация в автономном режиме", "Save useful information like user country and language also for visitors.": "Сохраните полезную информацию, такую как страна пользователя и язык, также для посетителей.", "Saved replies": "Сохраненные ответы", "Scheduled office hours": "График работы офиса", "Search engine ID": "Идентификатор поисковой системы", "Second chat message": "Второе сообщение чата", "Second reminder delay (hours)": "Задержка второго напоминания (часы)", "Secondary color": "Вторичный цвет", "Secret key": "Секретный ключ", "Send a message to allow customers to be notified when they can purchase a product they are interested in, but that is currently out of stock. You can use the following merge fields: {user_name}, {product_name}.": "Отправьте сообщение, чтобы позволить клиентам получать уведомления, когда они могут приобрести интересующий их продукт, но его в настоящее время нет в наличии. Вы можете использовать следующие поля слияния: {user_name}, {product_name}.", "Send a message to new users when they create the first ticket. Text formatting and merge fields are supported.": "Отправьте сообщение новым пользователям, когда они создадут первый тикет. Поддерживаются поля форматирования текста и слияния.", "Send a message to new users when they visit the website for the first time.": "Отправьте сообщение новым пользователям, когда они впервые посещают сайт.", "Send a message to the customer after a product has been removed from the cart. You can use the following merge fields and more: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.": "Отправьте сообщение покупателю после того, как товар был удален из корзины. Вы можете использовать следующие поля слияния и другие: {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}, {purchase_button}.", "Send a message to the customers who complete a purchase asking to share the product they just bought. You can use the following merge fields and more: {product_name}, {user_name}.": "Отправьте сообщение клиентам, совершившим покупку, с просьбой поделиться только что купленным продуктом. Вы можете использовать следующие поля слияния и многое другое: {product_name}, {user_name}.", "Send a message to the customers who complete a purchase. You can use the following merge fields and more: {coupon}, {product_names}, {user_name}.": "Отправьте сообщение клиентам, совершившим покупку. Вы можете использовать следующие поля слияния и многое другое: {coupon}, {product_names}, {user_name}.", "Send a message to the user when the agent archive the conversation.": "Отправьте сообщение пользователю, когда агент заархивирует беседу.", "Send a message to users who visit the website again after at least 24 hours. You can use the following merge fields and more: {coupon}, {user_name}. See the docs for more details.": "Отправьте сообщение пользователям, которые снова посещают веб-сайт по крайней мере через 24 часа. Вы можете использовать следующие поля слияния и многое другое: {coupon}, {user_name}. См. Документацию для получения более подробной информации.", "Send a test agent notification email to verify email settings.": "Отправьте тестовое уведомление агенту по электронной почте, чтобы проверить настройки электронной почты.", "Send a test message to your Slack channel. This only tests the sending functionality of outgoing messages.": "Отправьте тестовое сообщение на свой канал Slack. Это только проверяет отправку исходящих сообщений.", "Send a test user notification email to verify email settings.": "Отправьте тестовое уведомление пользователю по электронной почте, чтобы проверить настройки электронной почты.", "Send a text message to the provided phone number.": "Отправьте текстовое сообщение на указанный номер телефона.", "Send a user email notification": "Отправить уведомление пользователю по электронной почте", "Send a user text message notifcation": "Отправьте пользователю текстовое сообщение-уведомление", "Send a user text message notification": "Отправьте пользователю текстовое сообщение-уведомление", "Send an agent email notification": "Отправить уведомление агенту по электронной почте", "Send an agent text message notification": "Отправьте агенту SMS-уведомление", "Send an agent user text notification": "Отправка текстового уведомления пользователю агента", "Send an email notification to the provided email address.": "Отправьте уведомление по электронной почте на указанный адрес электронной почты.", "Send an email to an agent when a user replies and the agent is offline. An email is automatically sent to all agents for new conversations.": "Отправьте электронное письмо агенту, когда пользователь отвечает, а агент не в сети. Электронное письмо автоматически отправляется всем агентам для новых разговоров.", "Send an email to the user when a new conversation is created.": "Отправлять электронное письмо пользователю при создании нового разговора.", "Send an email to the user when a new conversation or ticket is created": "Отправлять электронное письмо пользователю при создании нового разговора или заявки", "Send an email to the user when an agent replies and the user is offline.": "Отправьте электронное письмо пользователю, когда агент отвечает, а пользователь не в сети.", "Send email": "Отправить электронное письмо", "Send login details to the specified URL and allow access only if the response is positive.": "Отправьте данные для входа на указанный URL-адрес и разрешите доступ только в случае положительного ответа.", "Send message": "Отправить сообщение", "Send message to Slack": "Отправить сообщение в Slack", "Send message via enter button": "Отправить сообщение с помощью кнопки ввода", "Send text message": "Отправить текстовое сообщение", "Send the message template to a WhatsApp number.": "Отправьте шаблон сообщения на номер WhatsApp.", "Send the message via the ENTER keyboard button.": "Отправьте сообщение с помощью кнопки ENTER на клавиатуре.", "Send the user details of the registration form and email rich messages to Dialogflow.": "Отправьте данные пользователя из регистрационной формы и расширенные сообщения электронной почты в Dialogflow.", "Send the WhatsApp order details to the URL provided.": "Отправьте детали заказа WhatsApp на указанный URL-адрес.", "Send to user's email": "Отправить на электронную почту пользователя", "Send transcript to user's email": "Отправить стенограмму на электронную почту пользователя", "Send user details": "Отправить данные пользователя", "Sender": "Отправитель", "Sender email": "Отправка электронной почты", "Sender name": "Имя отправителя", "Sender number": "Номер отправителя", "Sends a text message if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.": "Отправляет текстовое сообщение, если отправить сообщение WhatsApp не удалось. Вы можете использовать текст и следующие поля слияния: {conversation_url_parameter}, {message}, {recipient_name}, {recipient_email}.", "Sends a WhatsApp Template notification if sending of the WhatsApp message fails. You can use text and the following merge fields: {conversation_url_parameter}, {recipient_name}, {recipient_email}.": "Отправляет уведомление о шаблоне WhatsApp в случае сбоя отправки сообщения WhatsApp. Вы можете использовать текст и следующие поля слияния: {conversation_url_parameter}, {recipient_name}, {recipient_email}.", "Service": "Услуга", "Service Worker path": "Service Worker путь", "Service Worker URL": "Service Worker URL", "Set a dedicated Dialogflow agent for each department.": "Установите выделенного агента Dialogflow для каждого отдела.", "Set a dedicated OpenAI Assistants for each department.": "Установите выделенных помощников OpenAI для каждого отдела.", "Set a dedicated Slack channel for each department.": "Установите выделенный канал Slack для каждого отдела.", "Set a profile image for the chat bot.": "Установите изображение профиля для чат бота.", "Set the articles panel title. Default is 'Help Center'.": "Установите заголовок панели статей. По умолчанию используется «Справочный центр».", "Set the avatar image shown next to the message. It must be a JPG image of 1024x1024px with a maximum size of 50KB.": "Установите изображение аватара, отображаемое рядом с сообщением. Это должно быть изображение в формате JPG размером 1024x1024 пикселей и максимальным размером 50 КБ.", "Set the chat language or translate it automatically to match the user language. Default is English.": "Установите язык чата или переведите его автоматически в соответствии с языком пользователя. По умолчанию — английский.", "Set the currency symbol of the membership prices.": "Установите символ валюты для цен на членство.", "Set the currency symbol used by your system.": "Установите символ валюты, используемый вашей системой.", "Set the default departments for all tickets. Enter the department ID.": "Установите отделы по умолчанию для всех заявок. Введите идентификатор отдела.", "Set the default email header that will be prepended to automated emails and direct emails.": "Установите заголовок электронного письма по умолчанию, который будет добавляться к автоматическим и прямым письмам.", "Set the default email signature that will be appended to automated emails and direct emails.": "Установите подпись электронной почты по умолчанию, которая будет добавляться к автоматическим и прямым электронным письмам.", "Set the default form to display if the registraion is required.": "Установите форму по умолчанию для отображения, если требуется регистрация.", "Set the default name to use for conversations without a name.": "Установите имя по умолчанию, которое будет использоваться для разговоров без имени.", "Set the default notifications icon. The icon will be used as a profile image if the user doesn't have one.": "Установите значок уведомлений по умолчанию. Значок будет использоваться как изображение профиля, если у пользователя его нет.", "Set the default office hours for when agents are shown as available. These settings are also used for all other settings that rely on office hours.": "Установите рабочие часы по умолчанию, когда агенты отображаются как доступные. Эти настройки также используются для всех других настроек, которые зависят от рабочего времени.", "Set the default username to use in bot messages and emails when the user doesn't have a name.": "Установите имя пользователя по умолчанию, которое будет использоваться в сообщениях бота и электронной почте, когда у пользователя нет имени.", "Set the header appearance.": "Установите внешний вид заголовка.", "Set the maximum height of the tickets panel.": "Установите максимальную высоту панели тикетов.", "Set the multilingual plugin you're using, or leave it disabled if your site uses only one language.": "Установите многоязычный плагин, который вы используете, или оставьте его отключенным, если ваш сайт использует только один язык.", "Set the offline status automatically when the agent or admin remains inactive in the admin area for at least 10 minutes.": "Устанавливайте статус офлайн автоматически, когда агент или администратор остается неактивным в админке не менее 10 минут.", "Set the position of the chat widget.": "Установите положение виджета чата.", "Set the primary color of the admin area.": "Установите основной цвет админки.", "Set the primary color of the chat widget.": "Установите основной цвет виджета чата.", "Set the secondary color of the admin area.": "Установите дополнительный цвет административной области.", "Set the secondary color of the chat widget.": "Установите дополнительный цвет виджета чата.", "Set the tertiary color of the chat widget.": "Установите третичный цвет виджета чата.", "Set the title of the administration area.": "Задайте заголовок области администрирования.", "Set the title of the conversations panel.": "Задайте заголовок панели разговоров.", "Set the UTC offset of the office hours timetable. The correct value can be negative, and it's generated automatically once you click this input field, if it's empty.": "Установите смещение по всемирному координированному времени расписания рабочего времени. Правильное значение может быть отрицательным, и оно генерируется автоматически, когда вы щелкаете это поле ввода, если оно пустое.", "Set which actions to allow agents.": "Установите, какие действия разрешать агентам.", "Set which actions to allow supervisors.": "Установите, какие действия разрешать супервизорам.", "Set which user details to send to the main channel. Add comma separated values.": "Установите, какие данные пользователя отправлять на основной канал. Добавьте значения, разделенные запятыми.", "Settings area": "Область настроек", "settings information": "информация о настройках", "Shop": "Мага<PERSON>ин", "Show": "Показать", "Show a browser tab notification when a new message is received.": "Показывать уведомление на вкладке браузера при получении нового сообщения.", "Show a desktop notification when a new message is received.": "Показывать уведомление на рабочем столе при получении нового сообщения.", "Show a notification and play a sound when a new user is online.": "Показывать уведомление и воспроизводить звук, когда новый пользователь находится в сети.", "Show a pop-up notification to all users.": "Показывать всплывающее уведомление всем пользователям.", "Show profile images": "Показать изображения профиля", "Show sender's name": "Показать имя отправителя", "Show the agents menu in the dashboard and force the user to choose an agent to start a conversation.": "Показать меню агентов на панели инструментов и заставить пользователя выбрать агента для начала разговора.", "Show the articles panel on the chat dashboard.": "Покажите панель статей на панели инструментов чата.", "Show the categories instead of the articles list.": "Показывать категории вместо списка статей.", "Show the follow up message when a visitor add an item to the cart. The message is sent only if the user has not provided an email yet.": "Показывать последующее сообщение, когда посетитель добавляет товар в корзину. Сообщение отправляется только в том случае, если пользователь еще не отправил электронное письмо.", "Show the list of all Slack channels.": "Показать список всех каналов Slack.", "Show the profile image of agents and users within the conversation.": "Показывать изображения профиля агентов и пользователей в беседе.", "Show the sender's name in every message.": "Показывать имя отправителя в каждом сообщении.", "Single label": "Один ярлык", "Single phone country code": "Единый телефонный код страны", "Site key": "<PERSON><PERSON><PERSON>ч сайта", "Slug": "Слаг", "Smart Reply": "Умный ответ", "Social share message": "Сообщение для публикации в соцсетях", "Sort conversations by date": "Сортировать разговоры по дате", "Sound": "Звук", "Sound settings": "Настройки звука", "Sounds": "Звуки", "Sounds - admin": "Звуки - админ", "Source links": "Ссылки на источники", "Speech recognition": "Распознавание речи", "Spelling correction": "Орфографическая коррекция", "Starred tag": "Помеченный тег", "Start importing": "Начать импорт", "Store name": "Название магазина", "Subject": "Тема", "Subscribe": "Подписаться", "Subscribe users to your preferred newsletter service when they provide an email.": "Подписывайте пользователей на предпочитаемую вами службу рассылки новостей, когда они отправляют электронное письмо.", "Subtract the offset value from the height value.": "Вычтите значение смещения из значения высоты.", "Success message": "Сообщение об успехе", "Supervisors": "Руководители", "Masi Chat path": "Путь к доске поддержки", "Sync admin and staff accounts with Masi Chat. Staff users will be registered as agents, while admins as admins. Only new users will be imported.": "Синхронизируйте учетные записи администратора и персонала с Советом поддержки. Штатные пользователи будут зарегистрированы как агенты, а администраторы - как администраторы. Будут импортированы только новые пользователи.", "Sync all contacts of all clients with Masi Chat. Only new contacts will be imported.": "Синхронизируйте все контакты всех клиентов с Советом поддержки. Будут импортированы только новые контакты.", "Sync all users with Masi Chat. Only new users will be imported.": "Синхронизируйте всех пользователей с Советом поддержки. Будут импортированы только новые пользователи.", "Sync all WordPress users with Masi Chat. Only new users will be imported.": "Синхронизируйте всех пользователей WordPress с помощью Masi Chat. Будут импортированы только новые пользователи.", "Sync knowledge base articles with Masi Chat. Only new articles will be imported.": "Синхронизируйте статьи базы знаний с Советом поддержки. Будут импортированы только новые статьи.", "Sync mode": "Режим синхронизации", "Synchronization": "Синхронизация", "Synchronize": "Синхронизировать", "Synchronize customers, enable ticket and chat support for subscribers only, view subscription plans in the admin area.": "Синхронизируйте клиентов, включите поддержку тикетов и чата только для подписчиков, просматривайте планы подписок в админке.", "Synchronize emails": "Синхронизировать электронную почту", "Synchronize Entities": "Синхронизировать Entities", "Synchronize Entities now": "Синхронизируйте Entities сейчас", "Synchronize now": "Синхронизировать сейчас", "Synchronize users": "Синхронизировать пользователей", "Synchronize your customers in real-time, chat with them and boost their engagement, or provide a better and faster support.": "Синхронизируйте своих клиентов в режиме реального времени, общайтесь с ними и повышайте их вовлеченность или предоставляйте более качественную и быструю поддержку.", "Synchronize your Messenger and Instagram accounts.": "Синхронизируйте свои аккаунты Messenger и Instagram.", "Synchronize your Perfex customers in real-time and let them contact you via chat! View profile details, proactively engage them, and more.": "Синхронизируйте своих клиентов Perfex в режиме реального времени и позвольте им связываться с вами в чате! Просматривайте детали профиля, активно привлекайте их и т. Д.", "Synchronize your WhatsApp Cloud API account.": "Синхронизируйте свою учетную запись WhatsApp Cloud API.", "System requirements": "Системные Требования", "Tags": "Теги", "Tags settings": "Настройки тегов", "Template default language": "Язык шаблона по умолчанию", "Template for the email sent to a user when an agent replies. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.": "Шаблон электронного письма, отправляемого пользователю при ответе агента. Вы можете использовать текст, HTML и следующие поля слияния: {conversation_url_parameter}, {recipient_name}, {sender_name}, {sender_profile_image}, {message}, {attachments}.", "Template for the email sent to the user when a new conversation is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.": "Шаблон для электронного письма, отправляемого пользователю при создании нового разговора. Вы можете использовать текст, HTML и следующие поля слияния: {conversation_url_parameter}, {user_name}, {message}, {attachments}, {conversation_id}.", "Template for the email sent to the user when a new conversation or ticket is created. You can use text, HTML, and the following merge fields: {conversation_url_parameter}, {user_name}, {message}, {attachments}.": "Шаблон для электронного письма, отправляемого пользователю при создании нового разговора или заявки. Вы можете использовать текст, HTML и следующие поля слияния: {conversation_url_parameter}, {user_name}, {message}, {attachments}.", "Template languages": "Языки шаблонов", "Template name": "Имя Шаблона", "Template of the admin notification email. You can use text, HTML, and the following merge field and more: {carts}. Enter the email you want to send notifications to in the email address field.": "Шаблон уведомления администратора по электронной почте. Вы можете использовать текст, HTML, следующее поле слияния и многое другое: {carts}. Введите адрес электронной почты, на который вы хотите отправлять уведомления, в поле адреса электронной почты.", "Template of the email sent to the customer after a product has been removed from the cart. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Шаблон письма, отправляемого покупателю после удаления товара из корзины. Вы можете использовать текст, HTML и следующие поля слияния и многое другое: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the first notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Шаблон первого уведомления по электронной почте. Вы можете использовать текст, HTML и следующие поля слияния и многое другое: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the second notification email. You can use text, HTML, and the following merge fields and more: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name}.": "Шаблон второго уведомления по электронной почте. Вы можете использовать текст, HTML и следующие поля слияния и многое другое: {html_products_list}, {coupon}, {discount_price}, {original_price}, {product_names}, {user_name} .", "Template of the waiting list notification email. You can use text, HTML, and the following merge field and more: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.": "Шаблон электронного письма с уведомлением о листе ожидания. Вы можете использовать текст, HTML, следующее поле слияния и многое другое: {html_product_card}, {product_description}, {product_image}, {product_name}, {product_link}.", "Terms link": "Ссылка на условия", "Tertiary color": "Третичный цвет", "Test Slack": "Тестовый Slack", "Test template": "Шаблон теста", "Text": "Текст", "Text message fallback": "Резервное текстовое сообщение", "Text message notifications": "Уведомления о текстовых сообщениях", "Text messages": "Текстовые сообщения", "The product is not in the cart.": "Товара нет в корзине.", "The workspace name you are using to synchronize Slack.": "Имя workspace, которое вы используете для синхронизации Slack.", "This is your main Slack channel ID, which is usually the #general channel. You will get this code by completing the Slack synchronization.": "Это ваш основной идентификатор канала Slack, обычно это #general channel. Вы получите этот код, выполнив синхронизацию со Slack.", "This returns the Masi Chat path of your server.": "Это возвращает путь Ma<PERSON> вашего сервера.", "This returns your Masi Chat URL.": "Это возвращает ваш Masi Chat URL.", "Ticket custom fields": "Пользовательские поля тикета", "Ticket email": "Электронная почта для тикетов", "Ticket field names": "Имена полей заявки", "Ticket fields": "Поля тикета", "Ticket only": "Только тикет", "Ticket products selector": "Селектор тикетов", "Title": "Заголовок", "Top": "Верхний", "Top bar": "Верхняя панель", "Training via cron job": "Обучение через cron-задание", "Transcript": "Стенограмма", "Transcript settings.": "Настройки стенограммы.", "Trigger": "Триггер", "Trigger the Dialogflow Welcome Intent for new visitors when the welcome message is active.": "Запускайте диалоговое окно Welcome Intent для новых посетителей, когда активно приветственное сообщение.", "Troubleshoot": "Устранение неполадок", "Troubleshoot problems": "Решение проблем", "Twilio settings": "Настройк<PERSON> Twilio", "Twilio template": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unsubscribe": "Отписаться", "Upload attachments to Amazon S3.": "Загрузить вложения в Amazon S3.", "Usage Limit": "Лимит использования", "Use this option to change the PWA icon. See the docs for more details.": "Используйте эту опцию, чтобы изменить значок PWA. Более подробную информацию смотрите в документации.", "User details": "Данные пользователя", "User details in success message": "Сведения о пользователе в сообщении об успешном завершении", "User email notifications": "Уведомления пользователя по электронной почте", "User login form information.": "Информация о форме входа пользователя.", "User message template": "Шаблон сообщения пользователя", "User name as title": "Имя пользователя как заголовок", "User notification email": "Электронное письмо с уведомлением пользователя", "User registration form information.": "Информация о форме регистрации пользователя.", "User roles": "Роли пользователей", "User system": "Пользовательская система", "Username": "Имя пользователя", "Users and agents": "Пользователи и агенты", "Users area": "Область пользователей", "Users only": "Только для пользователей", "Users table additional columns": "Дополнительные столбцы в таблице пользователей", "UTC offset": "Смещение UTC", "Variables": "Переменные", "View channels": "Просмотр ка<PERSON><PERSON>ов", "View unassigned conversations": "Просмотр неназначенных разговоров", "Visibility": "Видимость", "Visitor default name": "Имя посетителя по умолчанию", "Visitor name prefix": "Префикс имени посетителя", "Volume": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Volume - admin": "Том - админ", "Waiting list": "Список ожидания", "Waiting list - Email": "Список ожидания - электронная почта", "Webhook URL": "URL-адрес веб-перехватчика", "Webhooks": "Вебхуки", "Webhooks are information sent in background to a unique URL defined by you when something happens.": "Веб-перехватчики — это информация, отправляемая в фоновом режиме на уникальный URL-адрес, указанный вами, когда что-то происходит.", "Website": "Веб-сайт", "WeChat settings": "Настройки WeChat", "Welcome message": "Приветствие", "Whmcs admin URL": "URL-адрес администратора Whmcs", "Whmcs admin URL. Ex. https://example.com/whmcs/admin/": "URL-адрес администратора Whmcs. Бывший. https://example.com/whmcs/admin/", "WordPress registration": "Вордпресс регистрация", "Yes, we ship in": "Да, мы отправляем", "You haven't placed an order yet.": "Вы еще не разместили заказ.", "You will get this code by completing the Dialogflow synchronization.": "Вы получите этот код, выполнив синхронизацию Dialogflow.", "You will get this code by completing the Slack synchronization.": "Вы получите этот код, выполнив синхронизацию со S<PERSON>ck.", "You will get this information by completing the synchronization.": "Вы получите эту информацию, завершив синхронизацию.", "Your cart is empty.": "Ваша корзина пуста.", "Your turn message": "Ваше сообщение поворота", "Your username": "Ва<PERSON> логин", "Your WhatsApp catalogue details.": "Детали вашего каталога WhatsApp.", "Zendesk settings": "Настройки Zendesk", "Activate the Right-To-Left (RTL) reading layout for the admin area.": "Активируйте макет чтения справа налево (RTL) для административной области.", "Activate the Right-To-Left (RTL) reading layout.": "Активируйте макет чтения справа налево (RTL).", "Disable chatbot store integration": "Отключить интеграцию магазина чат-ботов", "Enable logs": "Включить журналы", "Hide note information": "Скрыть информацию о заметке", "Manage the notes settings.": "Управляйте настройками заметок.", "Notes settings": "Настройки заметок", "One conversation per user": "Один разговор на пользователя", "RTL": "РТЛ", "Stop the chatbot from directly accessing your store to provide answers.": "Не позволяйте чат-боту напрямую обращаться в ваш магазин для предоставления ответов."}