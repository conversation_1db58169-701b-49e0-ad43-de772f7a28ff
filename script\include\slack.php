<?php

/*
 * ==========================================================
 * SLACK.PHP
 * ==========================================================
 *
 * Slack response listener. This file receive the Slack messages of the agents forwarded by masichat.com. This file requires the Slack App. 
 * � 2017-2025 masichat.com. All rights reserved.
 *
 */

if (file_exists('../apps/slack/functions.php')) {
    require_once('functions.php');
    require_once('../apps/slack/functions.php');
    $response = json_decode(file_get_contents('php://input'), true);
    mc_slack_listener($response);
}

?>