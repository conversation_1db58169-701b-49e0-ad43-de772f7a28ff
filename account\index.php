<?php
use <PERSON><PERSON>nere\Value;

global $cloud_settings;
require_once('functions.php');
mc_cloud_load();
$account = account();
$cloud_settings = super_get_settings();
$rtl = defined('MC_CLOUD_DEFAULT_RTL') || mc_is_rtl();
$custom_code = db_get('SELECT value FROM settings WHERE name = "custom-code-admin"');
if (!function_exists('sup' . 'er_adm' . 'in_con' . 'fig')) {
    die();
}
if (isset($_GET['login_email'])) {
    $account = false;
}
if (mc_isset($_GET, 'payment_type') == 'credits' && PAYMENT_PROVIDER == 'stripe') {
    $required_action = super_get_user_data('stripe_next_action', $account['user_id']);
    if ($required_action) {
        $required_action = explode('|', $required_action);
        if ($required_action[0] > (time() - 86400)) {
            super_delete_user_data($account['user_id'], 'stripe_next_action');
            header('Location: ' . $required_action[1]);
            die();
        }
    }
}
?>
<html lang="en-US">
<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1.0, user-scalable=no" />
    <title>
        <?php echo MC_CLOUD_BRAND_NAME ?>
    </title>
    <script src="../script/js/min/jquery.min.js"></script>
    <script id="mcinit" src="../script/js/<?php echo mc_is_debug() ? 'main' : 'min/main.min' ?>.js?v=<?php echo MC_VERSION ?>"></script>
    <link rel="stylesheet" href="../script/css/admin.css?v=<?php echo MC_VERSION ?>" type="text/css" media="all" />
    <link rel="stylesheet" href="../script/css/responsive-admin.css?v=<?php echo MC_VERSION ?>" media="(max-width: 464px)" />
    <?php
    if ($rtl) {
        echo '<link rel="stylesheet" href="../script/css/rtl-admin.css?v=' . MC_VERSION . '" />';
    }
    ?>
    <link rel="stylesheet" href="css/skin.min.css?v=<?php echo MC_VERSION ?>" type="text/css" media="all" />
    <link rel="shortcut icon" href="<?php echo MC_CLOUD_BRAND_ICON ?>" />
    <link rel="apple-touch-icon" href="<?php echo MC_CLOUD_BRAND_ICON_PNG ?>" />
    <link rel="manifest" href="<?php echo MC_CLOUD_MANIFEST_URL ?>" />
    <?php account_js() ?>
</head>
<body class="on-load<?php echo $rtl ? ' mc-rtl' : '' ?>">
    <div id="preloader"></div>
    <?php
    cloud_custom_code();
    if ($account && !empty($_COOKIE['mc-cloud'])) {
        if (empty($account['owner']) && db_get('SELECT id FROM agents WHERE admin_id = ' . db_escape($account['user_id'], true) . ' AND email = "' . $account['email'] . '"')) { // Deprecated. Remove && db_get('....
            echo '<script>document.location = "' . CLOUD_URL . '"</script>';
        } else {
            box_account();
        }
    } else {
        $GLOBALS['MC_LANGUAGE'] = [mc_defined('MC_CLOUD_DEFAULT_LANGUAGE_CODE'), 'front'];
        box_registration_login();
    }
    ?>
    <footer>
        <script src="js/cloud<?php echo (mc_is_debug() ? '' : '.min') ?>.js?v=<?php echo MC_VERSION ?>"></script>
        <?php mc_cloud_css_js() ?>
    </footer>
</body>
</html>

<?php function box_account() {
    global $cloud_settings;
    $membership = membership_get_active(false);
    $expiration = DateTime::createFromFormat('d-m-y', $membership['expiration']);
    $expired = $membership['price'] != 0 && (!$expiration || time() > $expiration->getTimestamp());
    $shopify = defined('SHOPIFY_CLIENT_ID') ? super_get_user_data('shopify_shop', get_active_account_id()) : false;
    echo '<script>var messages_volume = [' . implode(',', membership_volume()) . ']; var membership = { quota: ' . $membership['quota'] . ', count: ' . $membership['count'] . ', expired: ' . ($expired ? 'true' : 'false') . (isset($membership['quota_agents']) ? (', quota_agents: ' . $membership['quota_agents'] . ', count_agents: ' . $membership['count_agents']) : '') . ', credits: ' . $membership['credits'] . ' }; var CLOUD_USER_ID = ' . account()['user_id'] . '; var CLOUD_CURRENCY = "' . strtoupper(membership_currency()) . '"; var TWILIO_SMS = ' . (defined('CLOUD_TWILIO_SID') && !empty(CLOUD_TWILIO_SID) ? 'true' : 'false') . '; var external_integration = "' . ($shopify ? 'shopify' : '') . '";</script>' . PHP_EOL; ?>
    <div class="mc-account-box mc-admin mc-loading">
        <div class="mc-top-bar">
            <div>
                <h2>
                    <img src="<?php echo MC_CLOUD_BRAND_ICON ?>" />
                    <?php mc_e('Account') ?>
                </h2>
            </div>
            <div>
                <a class="mc-btn mc-btn-dashboard" href="../">
                    <?php mc_e('Dashboard') ?>
                </a>
            </div>
        </div>
        <div class="mc-tab">
            <div class="mc-nav">
                <div>
                    <?php mc_e('Installation') ?>
                </div>
                <ul>
                    <li id="nav-installation" class="mc-active">
                        <?php mc_e('Installation') ?>
                    </li>
                    <li id="nav-membership">
                        <?php mc_e('Membership') ?>
                    </li>
                    <li id="nav-invoices">
                        <?php mc_e(PAYMENT_PROVIDER == 'stripe' ? 'Invoices' : 'Payments') ?>
                    </li>
                    <li id="nav-profile">
                        <?php mc_e('Profile') ?>
                    </li>
                    <?php
                    if (!empty($cloud_settings['referral-commission'])) {
                        echo '<li id="nav-referral">' . mc_('Refer a friend') . '</li>';
                    }
                    ?>
                    <li id="nav-logout">
                        <?php mc_e('Logout') ?>
                    </li>
                </ul>
                <?php
                if (defined('MC_CLOUD_DOCS')) {
                    echo '<a href=" ' . MC_CLOUD_DOCS . '" target="_blank" class="mc-docs mc-btn-text"><i class="mc-icon-help"></i> ' . mc_('Help') . '</a>';
                }
                ?>
            </div>
            <div class="mc-content mc-scroll-area">
                <div id="tab-installation" class="mc-active">
                    <h2 class="addons-title first-title">
                        <?php mc_e($shopify ? 'Installation' : 'Embed code') ?>
                    </h2>
                    <?php
                    if ($shopify) {
                        echo '<p>' . str_replace('{R}', MC_CLOUD_BRAND_NAME, mc_('Customize your store and enable {R} in the app embeds section.')) . '</p><a class="mc-btn mc-btn-white" href="https://' . $shopify . '/admin/themes/current/editor?context=apps&activateAppId=' . SHOPIFY_APP_ID . '/mc" target="_blank">' . mc_('Preview in theme') . '</a>';
                    } else {
                        echo '<p>' . htmlspecialchars(mc_(mc_isset($cloud_settings, 'text_embed_code', 'To add the chat to your website, paste this code before the closing </body> tag on each page. Then, reload your website to see the chat in the bottom-right corner. Click the dashboard button in the top-right to access the admin area.'))) . '</p><div class="mc-setting"><textarea id="embed-code" readonly></textarea></div>';
                    }
                    if (defined('DIRECT_CHAT_URL')) {
                        $link = DIRECT_CHAT_URL . '/' . account_chat_id(account()['user_id']);
                        echo '<h2 class="addons-title">' . mc_('Chat direct link') . '</h2><p>' . mc_('Use this unique URL to access the chat widget directly. Include the attribute ?ticket in the URL to view the tickets area.') . '</p><div class="mc-setting mc-direct-link"><input onclick="window.open(\'' . $link . '\')" value="' . $link . '" readonly /></div>';
                    }
                    if (defined('ARTICLES_URL')) {
                        $link = ARTICLES_URL . '/' . account_chat_id(account()['user_id']);
                        echo '<h2 class="addons-title">' . mc_('Articles link') . '</h2><p>' . mc_('Use this unique URL to access the articles page. See the docs for other display options.') . '</p><div class="mc-setting mc-direct-link"><input onclick="window.open(\'' . $link . '\')" value="' . $link . '" readonly /></div>';
                    }
                    ?>
                    <h2 class="addons-title">
                        <?php mc_e('API token') ?>
                    </h2>
                    <p>
                        <?php echo str_replace('{R}', MC_CLOUD_BRAND_NAME, mc_('The API token is a required for using the {R} WEB API.')) ?>
                    </p>
                    <div class="mc-setting">
                        <input value="<?php echo account()['token'] ?>" readonly />
                    </div>
                </div>
                <div id="tab-membership">
                    <h2 class="addons-title first-title">
                        <?php mc_e('Membership') ?>
                    </h2>
                    <?php box_membership($membership) ?>
                    <hr />
                    <?php box_membership_plans($membership['id'], $expired) ?>
                    <?php box_credits(!$shopify) ?>
                    <?php box_addons() ?>
                    <?php box_chart() ?>
                    <hr />
                    <hr />
                    <?php
                    button_cancel_membership($membership);
                    ?>
                </div>
                <div id="tab-invoices" class="mc-loading">
                    <h2 class="addons-title first-title">
                        <?php mc_e(PAYMENT_PROVIDER == 'stripe' ? 'Invoices' : 'Payments') ?>
                    </h2>
                    <p>
                        <?php mc_e(PAYMENT_PROVIDER == 'stripe' ? 'Download your invoices here.' : 'View your payments here.') ?>
                    </p>
                    <table class="mc-table">
                        <tbody></tbody>
                    </table>
                </div>
                <div id="tab-profile">
                    <h2 class="addons-title first-title">
                        <?php mc_e('Manage profile') ?>
                    </h2>
                    <p>
                        <?php mc_e('Update here your profile information.') ?>
                    </p>
                    <div id="first_name" data-type="text" class="mc-input">
                        <span>
                            <?php mc_e('First name') ?>
                        </span>
                        <input type="text" />
                    </div>
                    <div id="last_name" data-type="text" class="mc-input">
                        <span>
                            <?php mc_e('Last name') ?>
                        </span>
                        <input type="text" />
                    </div>
                    <div id="email" data-type="text" class="mc-input mc-type-input-button">
                        <span>
                            <?php mc_e('Email') ?>
                        </span>
                        <input type="email" readonly />
                        <a class="mc-btn btn-verify-email">
                            <?php mc_e('Verify') ?>
                        </a>
                    </div>
                    <div id="phone" data-type="text" class="mc-input mc-type-input-button">
                        <span>
                            <?php mc_e('Phone') ?>
                        </span>
                        <input type="tel" />
                        <a class="mc-btn btn-verify-phone">
                            <?php mc_e('Verify') ?>
                        </a>
                    </div>
                    <div id="password" data-type="text" class="mc-input mc-type-input-button">
                        <span>
                            <?php mc_e('Password') ?>
                        </span>
                        <input type="password" value="12345678" />
                    </div>
                    <div id="company_details" data-type="text" class="mc-input">
                        <span>
                            <?php mc_e('Company details') ?>
                        </span>
                        <input type="text" />
                    </div>
                    <hr />
                    <div class="mc-flex">
                        <a id="save-profile" class="mc-btn mc-btn-white mc-icon">
                            <i class="mc-icon-check"></i>
                            <?php mc_e('Save changes') ?>
                        </a>
                        <a id="delete-account" class="mc-btn-text">
                            <?php mc_e('Delete account') ?>
                        </a>
                    </div>

                </div>
                <?php box_referral() ?>
            </div>
        </div>
    </div>

<?php } ?>

<?php

function box_membership($membership) {
    $membership_type = mc_defined('MC_CLOUD_MEMBERSHIP_TYPE', 'messages');
    $membership_type_ma = $membership_type == 'messages-agents';
    $name = $membership_type_ma ? 'messages' : $membership_type;
    $box_two = $membership_type_ma ? '<div><span>' . $membership['count_agents'] . ' / <span class="membership-quota">' . ($membership['quota_agents'] == 9999 ? '∞' : $membership['quota_agents']) . '</span></span> <span>' . mc_('Agents') . '</span></div>' : '';
    $price_string = $membership['price'] == 0 ? '' : (substr($membership['expiration'], -2) == '37' ? '<span id="membership-appsumo" data-id="' . account_get_payment_id() . '"></span>' : (mb_strtoupper($membership['currency']) . ' ' . $membership['price'] . ' ' . membership_get_period_string($membership['period'])));
    echo '<div class="box-maso box-membership"><div class="box-black"><h2>' . mc_(date('F')) . ', ' . date('Y') . '</h2><div><div><span>' . $membership['count'] . ' / <span class="membership-quota">' . $membership['quota'] . '</span></span> <span>' . mc_($name) . '</span></div>' . $box_two . '</div></div><div class="box-black"><h2>' . mc_('Active Membership') . '</h2><div><div><span class="membership-name">' . mc_($membership['name']) . '</span> <span class="membership-price" data-currency="' . $membership['currency'] . '">' . $price_string . '</span></div></div></div></div>';
}

function box_membership_plans($active_membership_id, $expired = false) {
    $plans = memberships();
    $code = '<div id="plans" class="plans-box">';
    $menu_items = [];
    $membership_type = mc_defined('MC_CLOUD_MEMBERSHIP_TYPE', 'messages');
    $membership_type_ma = $membership_type == 'messages-agents';
    for ($i = 1; $i < count($plans); $i++) {
        $plan = $plans[$i];
        $plan_period = $plan['period'];
        $menu = $plan_period == 'month' ? 'Monthly' : ($plan_period == 'year' ? 'Annually' : 'More');

        $period_top = membership_get_period_string($plan_period);
        $period = $membership_type_ma || $membership_type == 'messages' ? $period_top : '';
        if (!in_array($menu, $menu_items)) {
            array_push($menu_items, $menu);
        }
        $code .= '<div data-menu="' . $menu . '" data-id="' . $plan['id'] . '"' . ($active_membership_id == $plan['id'] ? ' data-active-membership="true"' : '') . ($active_membership_id == $plan['id'] && $expired ? ' data-expired="true"' : '') . '>' . ($active_membership_id == $plan['id'] ? '<div class="active-membership-info">' . mc_('Active Membership') . ($expired ? ' ' . mc_('Expired') : '') . '</div>' : '') . '<h3>' . mb_strtoupper($plan['currency']) . ' ' . $plan['price'] . ' ' . $period_top . '</h3><h4>' . $plan['name'] . '</h4><p>' . $plan['quota'] . ' ' . mc_($membership_type_ma ? 'messages' : $membership_type) . ' ' . $period . ($membership_type_ma ? ('<br>' . ($plan['quota_agents'] == 9999 ? mc_('unlimited') : $plan['quota_agents']) . ' ' . mc_('agents')) : '') . '<br>' . cloud_embeddings_chars_limit($plan) . ' ' . mc_('characters to train the chatbot') . '</p></div>';
    }
    $code .= '</div>';
    if (count($menu_items) > 1) {
        $menu = ['Monthly', 'Annually', 'More'];
        $code_menu = '<div class="plans-box-menu mc-menu-wide"><div>' . mc_($menu_items[0]) . '</div><ul>';
        for ($i = 0; $i < count($menu); $i++) {
            if (in_array($menu[$i], $menu_items)) {
                $code_menu .= '<li data-type="' . $menu[$i] . '">' . mc_($menu[$i]) . '</li>';
            }
        }
        $code = $code_menu . '</ul></div>' . $code;
    }
    echo $code;
}

function box_credits($auto_recharge = true) {
    if (!mc_defined('GOOGLE_CLIENT_ID') && !mc_defined('OPEN_AI_KEY') && !mc_defined('WHATSAPP_APP_ID')) {
        return false;
    }
    $prices = [5, 10, 20, 50, 100, 250, 500, 1000, 3000];
    $code_prices = '';
    $currency = strtoupper(membership_currency());
    $exchange_rate = $currency == 'USD' ? 1 : mc_usd_rates($currency);
    for ($i = 0; $i < count($prices); $i++) {
        $prices[$i] = intval($prices[$i] * $exchange_rate);
        $code_prices .= '<option value="' . $prices[$i] . '">' . $currency . ' ' . $prices[$i] . '</option>';
    }
    $user_id = db_escape(account()['user_id'], true);
    $credits = mc_isset(db_get('SELECT credits FROM users WHERE id = ' . $user_id), 'credits', 0);
    $checked = super_get_user_data('auto_recharge', $user_id) ? ' checked' : '';
    echo '<h2 id="credits" class="addons-title">' . mc_('Credits') . '</h2><p>' . str_replace('{R}', '<a href="' . (defined('MC_CLOUD_DOCS') ? MC_CLOUD_DOCS : '') . '#cloud-credits" target="_blank" class="mc-link-text">' . mc_('here') . '</a>', mc_('Credits are required to use some features in automatic sync mode. If you don\'t want to buy credits, switch to manual sync mode and use your own API key. For more details click {R}.')) . '</p><div class="box-maso maso-box-credits"><div class="box-black"><h2>' . mc_('Active credits') . '</h2><div>' . ($credits ? $credits : '0') . '</div></div><div><h2>' . mc_('Add credits') . '</h2><div><div id="add-credits" data-type="text" class="mc-input"><select><option></option>' . $code_prices . '</select></div></div></div>' . (in_array(PAYMENT_PROVIDER, ['stripe', 'yoomoney']) && $auto_recharge ? '<div><h2>' . mc_('Auto recharge') . '</h2><div><div id="credits-recharge" data-type="checkbox" class="mc-input"><input type="checkbox"' . $checked . '></div></div></div>' : '') . '</div>';
}

function box_addons() {
    $white_label_price = super_get_white_label();
    $addons = mc_defined('CLOUD_ADDONS');
    if ($white_label_price || $addons) {
        $account = account();
        $code = '<h2 class="addons-title">' . mc_('Add-ons') . '</h2><p>' . mc_('Add-ons are optional features with a fixed subscription cost.') . '</p><div id="addons" class="plans-box">';
        if ($white_label_price) {
            $code .= '<div class="mc-visible' . (membership_is_white_label($account['user_id']) ? ' mc-plan-active' : '') . '" id="purchase-white-label"><h3>' . strtoupper(membership_currency()) . ' ' . $white_label_price . ' ' . mc_('a year') . '</h3><h4>' . mc_('White Label') . '</h4><p>' . mc_('Remove our branding and logo from the chat widget.') . '</p></div>';
        }
        if ($addons) {
            for ($i = 0; $i < count($addons); $i++) {
                $addon = $addons[$i];
                $code .= '<div class="mc-visible mc-custom-addon" data-index="' . $i . '" data-id="' . mc_string_slug($addon['title']) . '"><h3>' . strtoupper(membership_currency()) . ' ' . $addon['price'] . '</h3><h4>' . mc_($addon['title']) . '</h4><p>' . mc_($addon['description']) . '</p></div>';
            }
        }
        echo $code . '</div>';
    }
}

function button_cancel_membership($membership) {
    if ($membership['price'] != 0) {
        if (super_get_user_data('subscription_cancelation', get_active_account_id())) {
            echo '<p>' . mc_('Your membership renewal has been canceled. Your membership is set to expire on') . ' ' . membership_get_active()['expiration'] . '.</p>';
        } else {
            echo '<a id="cancel-subscription" class="mc-btn-text mc-icon mc-btn-red"><i class="mc-icon-close"></i>' . mc_('Cancel subscription') . '</a>';
        }
    }
}

function account_js() {
    global $cloud_settings;
    $account = account();
    $reset_code = '<script>document.cookie="mc-login=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/;";document.cookie="mc-cloud=;expires=Thu, 01 Jan 1970 00:00:01 GMT;path=/;";location.reload();</script>';
    if ($account) {
        $path = '../script/config/config_' . $account['token'] . '.php';
        if (file_exists($path)) {
            require_once($path);
        } else {
            die($reset_code);
        }
    } else {
        echo '<script>var MC_URL = "' . CLOUD_URL . '/script"; var MC_CLOUD_SW = true; var MC_DISABLED = true; (function() { MCF.serviceWorker.init(); }())</script>';
    }
    if ($cloud_settings) {
        unset($cloud_settings['js']);
        unset($cloud_settings['js-front']);
        unset($cloud_settings['css']);
        unset($cloud_settings['css-front']);
    }
    if (isset($_GET['appsumo']) && mc_is_agent()) {
        die($reset_code);
    }
    $language = mc_get_admin_language();
    $translations = ($language && $language != 'en' ? file_get_contents(MC_PATH . '/resources/languages/admin/js/' . $language . '.json') : '[]');
    echo '<script>var CLOUD_URL = "' . CLOUD_URL . '"; var BRAND_NAME = "' . MC_CLOUD_BRAND_NAME . '"; var PUSHER_KEY = "' . mc_pusher_get_details()[0] . '"; var LANGUAGE = "' . mc_get_admin_language() . '"; var SETTINGS = ' . ($cloud_settings ? json_encode($cloud_settings, JSON_INVALID_UTF8_IGNORE) : '{}') . '; var MC_TRANSLATIONS = ' . ($translations ? $translations : '[]') . '; var PAYMENT_PROVIDER = "' . PAYMENT_PROVIDER . '"; var MEMBERSHIP_TYPE = "' . mc_defined('MC_CLOUD_MEMBERSHIP_TYPE', 'messages') . '";' . (defined('PAYMENT_MANUAL_LINK') ? 'var PAYMENT_MANUAL_LINK = "' . PAYMENT_MANUAL_LINK . '"' : '') . '</script>';
}

function box_chart() {
    if (in_array(mc_defined('MC_CLOUD_MEMBERSHIP_TYPE', 'messages'), ['messages', 'messages-agents'])) {
        echo '<div class="chart-box"><div><h2 class="addons-title">' . mc_('Monthly usage in') . ' ' . date('Y') . '</h2><p>' . mc_('The number of messages sent monthly, all messages are counted, including messages from agents, administrators and chatbot.') . '</p></div></div><canvas id="chart-usage" class="mc-loading" height="100"></canvas>';
    }
}

?>

<?php function box_registration_login() {
    $appsumo = base64_decode(mc_isset($_GET, 'appsumo'));
    global $cloud_settings; ?>
    <div class="mc-registration-box mc-cloud-box mc-admin-box<?php echo !isset($_GET['login']) && !isset($_GET['reset']) ? ' active' : '' ?>">
        <div class="mc-info"></div>
        <div class="mc-top-bar">
            <img src="<?php echo MC_CLOUD_BRAND_LOGO ?>" />
            <div class="mc-title">
                <?php mc_e('New account') ?>
            </div>
            <div class="mc-text">
                <?php mc_e($appsumo ? 'Complete the AppSumo registration.' : 'Create your free account. No payment information required.') ?>
            </div>
        </div>
        <div class="mc-main">
            <div id="first_name" class="mc-input">
                <span>
                    <?php mc_e('First name') ?>
                </span>
                <input type="text" required />
            </div>
            <div id="last_name" class="mc-input">
                <span>
                    <?php mc_e('Last name') ?>
                </span>
                <input type="text" required />
            </div>
            <div id="email" class="mc-input">
                <span>
                    <?php mc_e('Email') ?>
                </span>
                <input type="email" <?php echo $appsumo ? 'value="' . $appsumo . '" readonly="true" style="color:#989898"' : '' ?> required />
            </div>
            <div id="password" class="mc-input">
                <span>
                    <?php mc_e('Password') ?>
                </span>
                <input type="password" required />
            </div>
            <div id="password_2" class="mc-input">
                <span>
                    <?php mc_e('Repeat password') ?>
                </span>
                <input type="password" required />
            </div>
            <?php
            $code = '';
            for ($i = 1; $i < 5; $i++) {
                $name = mc_isset($cloud_settings, 'registration-field-' . $i);
                if ($name) {
                    $code .= '<div id="' . mc_string_slug($name) . '" class="mc-input"><span>' . mc_($name) . '</span><input type="text" required /></div>';
                }
            }
            echo $code;
            ?>
            <div class="mc-bottom">
                <div class="mc-btn btn-register">
                    <?php mc_e('Create account') ?>
                </div>
                <div class="mc-text">
                    <?php mc_e('Already have an account?') ?>
                </div>
                <div class="mc-text mc-btn-login-box">
                    <?php mc_e('Log in') ?>
                </div>
            </div>
            <div class="mc-errors-area"></div>
        </div>
        <div class="loading-screen">
            <i class="mc-loading"></i>
            <p>
                <?php mc_e('We are creating your account...') ?>
            </p>
        </div>
    </div>
    <div class="mc-login-box mc-cloud-box mc-admin-box<?php if (isset($_GET['login']))
        echo ' active' ?>">
            <div class="mc-info"></div>
            <div class="mc-top-bar">
                <img src="<?php echo MC_CLOUD_BRAND_LOGO ?>" />
            <div class="mc-title">
                <?php mc_e('Sign in to your account') ?>
            </div>
            <div class="mc-text">
                <?php echo mc_('To continue to') . ' ' . MC_CLOUD_BRAND_NAME ?>
            </div>
        </div>
        <div class="mc-main">
            <div id="email" class="mc-input">
                <span>
                    <?php mc_e('Email') ?>
                </span>
                <input type="email" required />
            </div>
            <div id="password" class="mc-input">
                <span>
                    <?php mc_e('Password') ?>
                </span>
                <input type="password" required />
            </div>
            <div class="mc-text btn-forgot-password">
                <?php mc_e('Forgot your password?') ?>
            </div>
            <div class="mc-bottom">
                <div class="mc-btn btn-login">
                    <?php mc_e('Sign in') ?>
                </div>
                <div class="mc-text">
                    <?php mc_e('Need new account?') ?>
                </div>
                <div class="mc-text btn-registration-box">
                    <?php mc_e('Sign up free') ?>
                </div>
            </div>
            <div class="mc-errors-area"></div>
        </div>
    </div>
    <div class="mc-reset-password-box mc-cloud-box mc-admin-box">
        <div class="mc-info"></div>
        <div class="mc-top-bar">
            <img src="<?php echo MC_CLOUD_BRAND_LOGO ?>" />
            <div class="mc-title">
                <?php mc_e('Reset password') ?>
            </div>
            <div class="mc-text">
                <?php mc_e('Enter your email below, you will receive an email with instructions on how to reset your password.') ?>
            </div>
        </div>
        <div class="mc-main">
            <div class="mc-input">
                <span>
                    <?php mc_e('Email') ?>
                </span>
                <input id="reset-password-email" type="email" required />
            </div>
            <div class="mc-bottom">
                <div class="mc-btn btn-reset-password">
                    <?php mc_e('Reset password') ?>
                </div>
                <div class="mc-text btn-cancel-reset-password">
                    <?php mc_e('Cancel') ?>
                </div>
            </div>
        </div>
    </div>
    <div class="mc-reset-password-box-2 mc-cloud-box mc-admin-box<?php if (isset($_GET['reset']))
        echo ' active' ?>">
            <div class="mc-info"></div>
            <div class="mc-top-bar">
                <img src="<?php echo MC_CLOUD_BRAND_LOGO ?>" />
            <div class="mc-title">
                <?php mc_e('Reset password') ?>
            </div>
            <div class="mc-text">
                <?php mc_e('Enter your new password here.') ?>
            </div>
        </div>
        <div class="mc-main">
            <div class="mc-input">
                <span>
                    <?php mc_e('Password') ?>
                </span>
                <input id="reset-password-1" type="password" required />
            </div>
            <div class="mc-input">
                <span>
                    <?php mc_e('Repeat password') ?>
                </span>
                <input id="reset-password-2" type="password" required />
            </div>
            <div class="mc-bottom">
                <div class="mc-btn btn-reset-password-2">
                    <?php mc_e('Reset password') ?>
                </div>
            </div>
        </div>
    </div>
    <p class="disclaimer">
        <?php mc_e(mc_isset($cloud_settings, 'disclaimer', 'By creating an account you agree to our <a target="_blank" href="https://masichat.com/terms-of-service">Terms Of Service</a> and <a target="_blank" href="https://masichat.com/privacy">Privacy Policy</a>.<br />&copy; 2022-2024 masichat.com. All rights reserved.')) ?>
    </p>
<?php } ?>

<?php function box_referral() {
    global $cloud_settings;
    if (isset($cloud_settings['referral-commission'])) { ?>
        <div id="tab-referral">
            <h2 class="addons-title first-title">
                <?php mc_e('Refer a friend') ?>
            </h2>
            <p>
                <?php echo mc_isset($cloud_settings, 'referral-text', '') ?>
            </p>
            <div class="mc-input">
                <input value="<?php echo CLOUD_URL . '?ref=' . mc_encryption('encrypt', account()['user_id']) ?>" type="text" readonly />
            </div>
            <hr class="space" />
            <h2 class="addons-title">
                <?php mc_e('Your current earnings') ?>
            </h2>
            <div class="text-earnings">
                <?php echo strtoupper(membership_currency()) . ' ' . super_get_user_data('referral', account()['user_id'], 0) ?>
            </div>
            <hr class="space" />
            <h2 class="addons-title">
                <?php mc_e('Your payment information') ?>
            </h2>
            <div data-type="text" class="mc-input">
                <span><?php mc_e('Method') ?></span>
                <select id="payment_method">
                    <option></option>
                    <option value="paypal">PayPal</option>
                    <option value="bank"><?php mc_e('Bank Transfer') ?></option>
                </select>
            </div>
            <div data-type="text" class="mc-input mc-input">
                <span id="payment_information_label"></span>
                <textarea id="payment_information"></textarea>
            </div>
            <hr class="space-sm" />
            <a id="save-payment-information" class="mc-btn mc-btn-white mc-icon">
                <i class="mc-icon-check"></i><?php mc_e('Save changes') ?>
            </a>
        </div>
    <?php }
} ?>