<?php
// Simple test to check what's being returned by the AJAX endpoint

$url = 'https://crm.masichat.com/account/ajax.php';
$data = array(
    'function' => 'debug-test',
    'test' => 'hello'
);

$options = array(
    'http' => array(
        'header'  => "Content-type: application/x-www-form-urlencoded\r\n",
        'method'  => 'POST',
        'content' => http_build_query($data)
    )
);

$context  = stream_context_create($options);
$result = file_get_contents($url, false, $context);

echo "Raw response:\n";
echo $result;
echo "\n\nResponse length: " . strlen($result);
echo "\nFirst 100 characters: " . substr($result, 0, 100);

// Try to decode as JSON
$json = json_decode($result, true);
if ($json === null) {
    echo "\nJSON decode error: " . json_last_error_msg();
} else {
    echo "\nJSON decoded successfully: " . print_r($json, true);
}
?>
