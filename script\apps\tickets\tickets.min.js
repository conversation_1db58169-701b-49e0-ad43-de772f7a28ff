"use strict";!function(e){function t(){c.addClass("mc-collapsing"),setTimeout(function(){c.removeClass("mc-collapsing")},1e3)}function s(e){if(void 0===e)return window.mc_current_user;window.mc_current_user=e}function i(e){return MCF.translate(e)}function n(){0!=s()&&e(m).setProfile("#"==s().get("last_name").charAt(0)?i("Account"):s().name)}function a(e=!1){let t=e&&"title"in e.details&&!MCF.null(e.details.title)?e.get("title"):MCF.setting("tickets_conversation_name");r(t&&-1!=t?t:s().name)}function o(){let t="",n=MCForm.getAll(f),a="department"in n?n.department[0]:null,o=[];MCChat.clear(),g.mcActive(!1);for(var l in n)n[l][1]&&n[l][0]&&(t+=`*${i(n[l][1])}*\n${"department"==l?f.find("#department li.mc-active").html():n[l][0]}\n\n`);t+=g.find("textarea").val().trim(),f.find(".mc-attachments > div").each(function(){o.push([e(this).attr("data-name"),e(this).attr("data-value")])}),s()?MCChat.newConversation(2,-1,t,o,a,null,function(){F.welcome()}):MCChat.addUserAndLogin(()=>{MCChat.newConversation(2,-1,t,o,a,null,function(){F.welcome()})})}function l(){c.on("click",".mc-btn-collapse",function(){t(),c.find(".mc-panel-"+(e(this).hasClass("mc-left")?"left":"right")).toggleClass("mc-collapsed"),e(this).toggleClass("mc-active")}),g.on("focus focusout","textarea",function(){e(this).parent().parent().toggleClass("mc-focus")}),w||(g.on("click",".mc-btn-emoji",function(){let t="new-ticket"==p?[f,"padding-top",415]:[c,"margin-top",335];if(g.find(".mc-emoji").mcActive()){let s=e(this).offset().top+e(t[0])[0].scrollTop-window.scrollY,i=c.offset().top-window.scrollY;s-i<380&&e(t[0]).css(t[1],t[2]-(s-i)+"px")}else e(t[0]).css(t[1],"")}),g.on("click",".mc-emoji-list > ul > li",function(){let t="new-ticket"==p?[f,"padding-top",415]:[c,"margin-top",335];e(t[0]).css(t[1],"")})),d.on("click","> .mc-top .mc-close",function(){F.showPanel()}),d.on("click",".mc-create-ticket",function(){let t=!1;if(g.removeClass("mc-error"),MCForm.errors(f)&&(MCForm.showErrorMessage(f,"Please fill in all the required fields."),t=!0),g.find("textarea").val().trim()||g.hasClass("mc-audio-message-active")||(t||MCForm.showErrorMessage(f,"Please write a message."),g.addClass("mc-error"),t=!0),!t&&!MCChat.is_busy){if(MCChat.busy(!0),MCF.setting("tickets_recaptcha"))return void grecaptcha.ready(function(){grecaptcha.execute(MCF.setting("tickets_recaptcha"),{action:"submit"}).then(function(t){MCF.ajax({function:"recaptcha",token:t},t=>{!0===t?(o(),e(".grecaptcha-badge").hide()):MCChat.busy(!1)})})});o()}}),c.on("click",".mc-new-ticket",function(){F.showPanel("new-ticket")}),u.on("click","li",function(){MCChat.clear(),F.selectConversation(e(this).attr("data-conversation-id")),b.mcLoading(!0),w&&c.find(".mc-panel-left").addClass("mc-collapsed")}),c.find(".mc-panel-left .mc-search-btn input").on("input",function(){let t=e(this).val(),n=e(this).prev();MCF.search(t,()=>{if(t.length>1){if(function(t){return!!e(t).mcLoading()||(e(t).mcLoading(!0),!1)}(n))return;MCF.ajax({function:"search-user-conversations",search:t},e=>{n.mcLoading(!1);let t=[],a=e.length;for(var o=0;o<a;o++)MCF.setting("tickets_close")||t.push(new MCConversation([new MCMessage(e[o])],e[o]));u.html(a?s().getConversationsCode(t):"<p>"+i("No results found.")+"</p>")})}else MCChat.populateConversations()})}),c.on("click",".mc-panel-left .mc-search-btn i",function(){MCF.searchClear(this,()=>{MCChat.populateConversations()})}),f.on("click",".mc-login-area",function(){F.showPanel("login")}),f.on("click",".mc-registration-area",function(){F.showPanel("registration")}),c.on("click",'.mc-profile-menu [data-value="edit-profile"]',function(){F.showPanel("edit-profile")}),c.on("click",'.mc-profile-menu [data-value="logout"]',function(){MCF.logout(!1),F.showPanel("login")}),f.on("click","> .mc-buttons .mc-submit",function(){if(!e(this).mcLoading()){let i=MCForm.getAll(f),a=MCForm.getAll(f.find(".mc-form-extra")),o="edit-profile"==p;for(var t in i)i[t]=i[t][0];MCForm.errors(f)?MCForm.showErrorMessage(f,MCForm.getRegistrationErrorMessage(f)):(e(this).mcLoading(!0),i.user_type="user",MCF.ajax({function:o||s()?"update-user":"add-user-and-login",settings:i,settings_extra:a},t=>{if(t&&!MCF.errorValidation(t)){if(MCF.loginCookie(t[1]),s()){for(var l in i)s().set(l,i[l][0]);for(var l in a)s().setExtra(l,a[l][0])}else{s(new MCUser(t[0]));for(var l in a)s().setExtra(l,a[l][0]);MCPusher.start(),MCChat.initChat()}n(),o?F.showPanel():(MCF.event("MCRegistrationForm",{user:i}),MCF.event("MCNewEmailAddress",{name:s().name,email:s().get("email")}),F.showPanel("new-ticket")),MCF.setting("wp_registration")&&"email"in i&&"password"in i?(console.log(i),MCApps.wordpress.ajax("wp_registration",{user_id:t[0].id,first_name:t[0].first_name,last_name:t[0].last_name,password:i.password[0],email:i.email[0]})):"wp"==MCF.setting("wp_users_system")&&MCApps.wordpress.ajax("wp_login",{user:i.email[0],password:i.password[0]})}else MCForm.showErrorMessage(f,MCForm.getRegistrationErrorMessage(t,"response"));e(this).mcLoading(!1)}))}}),f.on("click",".mc-submit-login",function(){MCF.loginForm(this,f,e=>{s(new MCUser(e[0])),n(),MCChat.populateConversations(e=>{0==e.length?(c.addClass("mc-no-conversations"),F.showPanel("new-ticket")):(MCChat.openConversation(e[0].id),F.showPanel())})})})}function r(t){e(h).html(t).mcActive(t)}var c,d,b,f,g,p,u,h,v,m,S,B={},C={},w=e(window).width()<426,k=!1,F={showPanel:function(t="",s=!1){let n=p;switch(p=t,c.addClass("mc-panel-active mc-load").removeClass("mc-panel-form").attr("data-panel",t),k&&k.hide(),t){case"privacy":MCF.ajax({function:"get-block-setting",value:"privacy"},e=>{r(i(e.title)),f.append(`<div class="mc-privacy mc-init-form" data-decline="${i(e.decline.replace(/"/g,""))}"><div class="mc-text">${i(e.message)}</div>`+(""!=e.link?`<a target="_blank" href="${e.link}">${i(e["link-name"])}</a>`:"")+`<div class="mc-buttons"><a class="mc-btn mc-approve">${i(e["btn-approve"])}</a><a class="mc-btn mc-decline">${i(e["btn-decline"])}</a></div></div>`)}),this.showSidePanels(!1);break;case"articles":r(i(0==s?"Articles":s)),this.showSidePanels(!1);break;case"edit-profile":case"login":case"registration":let l="edit-profile"==t;this.showSidePanels(!1),c.addClass("mc-panel-form"),t in C?(f.html(C[t]),r(i(f.find(".mc-top").html()))):(MCF.ajax({function:"get-rich-message",name:(l?"registration":t)+"-tickets"},e=>{f.html(e);let s=f.find(".mc-top").html();l&&f.find(".mc-top").html(i("Edit profile")),r(i(s)),setTimeout(function(){r(i(s))},300),f.find(".mc-link-area").insertAfter(".mc-buttons"),f.find(".mc-info").insertBefore(".mc-buttons"),C[t]=f.html()}),f.html('<div class="mc-loading"></div>'));break;case"new-ticket":let u={title:"Title",message:"Message",panel:"Create a new ticket",button:"Create a new ticket"};if(this.showSidePanels(!1),MCF.setting("tickets_names")){let e=MCF.setting("tickets_names");for(var o in e)e[o]&&(u[o.replace("tickets-names-","")]=e[o])}r(i(u.panel)),f.html(`<div class="mc-info"></div><div class="mc-input mc-input-text mc-ticket-title"><span>${i(u.title)}</span><input type="text" required></div>${c.find(".mc-ticket-fields").html()}<div class="mc-input mc-editor-cnt"><span>${i(u.message)}</span></div><div class="mc-btn mc-icon mc-create-ticket"><i class="mc-icon-plus"></i>${i(u.button)}</div>`),d.find(".mc-editor-cnt").append(g),MCF.setting("tickets_recaptcha")&&(k?k.show():e.getScript("https://www.google.com/recaptcha/api.js?render="+MCF.setting("tickets_recaptcha"),()=>{setTimeout(()=>{k=e(".grecaptcha-badge")},500)}));break;default:this.showSidePanels(!0),"new-ticket"==n&&(g.find("textarea").val(""),g.mcActive(!1).removeClass("mc-error"),b.after(g)),f.html(""),c.removeClass("mc-panel-active mc-load").removeAttr("data-panel"),a(MCChat.conversation)}MCF.event("MCPanelActive",t),setTimeout(function(){c.removeClass("mc-load")},300)},showSidePanels:function(s=!0){let i=c.find(".mc-btn-collapse");if(t(),!s||S>800){let e=c.find(".mc-panel-left,.mc-panel-right");s?e.removeClass("mc-collapsed"):e.addClass("mc-collapsed")}else S<=800&&e(i).mcActive(!0);e(i).css("display",s?"":"none")},setAgent:function(t){let s=c.find(".mc-agent-label").mcActive(!1);if(MCChat.agent_id=t,t in B){let i=B[t];e(v).setProfile(i.name,i.image).mcActive(!0),MCChat.updateUsersActivity(),"details"in i&&MCF.getLocationTimeString(i.extra,t=>{e(s).html((i.get("flag")?`<img src="${MC_URL}/media/flags/${i.get("flag")}">`:'<i class="mc-icon mc-icon-marker"></i>')+t).mcActive(!0)})}else MCF.ajax({function:"get-agent",agent_id:t},e=>{0!=e&&(B[t]=new MCUser(e),this.setAgent(t))})},activateConversation:function(t){if(t instanceof MCConversation){let n=MCChat.lastAgent(),o=["id","creation_time","last_update"],l="";this.selectConversation(t.id),a(t),c.find(".mc-panel-right .mc-scroll-area > div").mcActive(!1),n?(e(v).setProfile(n.full_name,n.profile_image),this.setAgent(n.user_id),setTimeout(()=>{MCChat.updateUsersActivity()},300)):e(v).mcActive(!1),""!=t.get("department")&&MCChat.getDepartmentCode(t.get("department"),t=>{let s=c.find(".mc-department");e(s).html(`<span class="mc-title">${e(s).data("label")}</span>${t}`).mcActive(!0)});for(s=0;s<o.length;s++){let e;switch(o[s]){case"id":e=["padlock",i("Ticket ID"),t.id];break;case"creation_time":e=["calendar",i("Creation time"),MCF.beautifyTime(t.get("creation_time"),!0)];break;case"last_update":e=["reload",i("Last update"),MCF.beautifyTime(t.getLastMessage()?t.getLastMessage().get("creation_time"):t.get("last_update_time"),!0)]}l+=`<div data-id="${o[s]}"><i class="mc-icon mc-icon-${e[0]}"></i><span>${e[1]}</span><div>${e[2]}</div></div>`}c.find(".mc-ticket-details").html(l);let r=t.getAttachments();l="";for(var s=0;s<r.length;s++)l+=`<a href="${r[s][1]}" target="_blank"><i class="mc-icon mc-icon-file"></i>${r[s][0]}</a>`;c.find(".mc-conversation-attachments").html((l?`<div class="mc-title">${i("Attachments")}</div>`:"")+l),b.mcLoading(!1)}else MCF.error("Value not of type MCConversation","activateConversation")},selectConversation:function(e){let t=u.find(`[data-conversation-id="${e}"]`);u.find("> li").mcActive(!1),1==t.attr("data-conversation-status")&&t.attr("data-conversation-status",0),t.find("[data-count]").remove(),t.mcActive(!0)},getActiveConversation:function(e=""){let t=u.find(" > .mc-active");return!!t.length&&("ID"==e?t.attr("data-conversation-id"):t)},welcome:function(){let e=MCF.setting("tickets_welcome_message");e&&!MCF.storage("tickets-welcome")&&setTimeout(()=>{MCChat.sendMessage(MCF.setting("bot_id"),e),MCF.storage("tickets-welcome",!0)},1e3)},init:function(){if(c=e("body").find(".mc-tickets"),d=c.find(" > div > .mc-panel-main"),f=d.find(".mc-panel"),g=d.find(".mc-editor"),h=d.find(" > .mc-top .mc-title"),u=c.find(".mc-user-conversations"),b=d.find(".mc-list"),v=c.find(".mc-profile-agent"),m=c.find(".mc-panel-right > .mc-top .mc-profile"),S=c.width(),B[MCF.setting("bot_id")]={name:MCF.setting("bot_name"),image:MCF.setting("bot_image")},l(),!c.length)return;if(!MCF.setting("tickets_registration_required")||s()&&!["visitor","lead"].includes(s().type))s()&&s().conversations.length?F.getActiveConversation()||MCChat.openConversation(MCF.getURL("conversation")?MCF.getURL("conversation"):s().conversations[0].id):(c.addClass("mc-no-conversations"),MCF.setting("privacy")&&!MCF.storage("privacy_approved")?F.showPanel("privacy"):MCF.setting("tickets_disable_first")?a():F.showPanel("new-ticket"));else{let e=MCF.setting("tickets_registration_redirect");if(e)return void(document.location=e+(e.includes("?")?"&":"?")+"mc=true");c.addClass("mc-no-conversations"),F.showPanel(MCF.setting("tickets_default_form"))}let t=parseInt(MCF.null(c.data("height"))?e(window).height():c.data("height")),i=parseInt(MCF.null(c.data("offset"))?0:c.data("offset"));S<=800?(c.addClass("mc-800"),c.find(".mc-panel-left,.mc-panel-right").addClass("mc-collapsed"),c.find(".mc-btn-collapse").mcActive(!0)):S<=1e3?c.addClass("mc-1000"):S<=1300&&c.addClass("mc-1300"),n(),c.removeClass("mc-loading").find(".mc-tickets-area").attr("style",`height: ${t-i}px`),setTimeout(function(){c.removeClass("mc-load")},300),MCChat.startRealTime(),MCF.event("MCTicketsInit")},onMessageSent:function(){if("new-ticket"==p){let e=d.find(".mc-ticket-title input").val();MCChat.updateConversations(),c.find(".mc-panel-right .mc-scroll-area > div").mcActive(!1),c.find(".mc-conversation-attachments,.mc-ticket-details").html(""),c.removeClass("mc-no-conversations"),F.showPanel(),r(e)}},onConversationReceived:function(e){e.id==MCChat.conversation.id&&F.getActiveConversation("ID")!=e.id&&setTimeout(()=>{F.activateConversation(MCChat.conversation),u.length&&u.scrollTop(u[0].scrollHeight)},300)},onNewMessageReceived:function(t,s){if(t instanceof MCMessage&&s==MCChat.conversation.id){let s=MCChat.lastAgent(),i=MCChat.conversation.getCode(),n=F.getActiveConversation();n?e(n).html(i):u.append(`<li data-conversation-id="${MCChat.conversation.id}" class="mc-active">${i}</li>`).find(" > p").remove(),s&&MCF.isAgent(t.get("user_type"))&&s.id!=t.get("user_id")&&F.setAgent(t.get("user_id"))}}};window.MCTickets=F}(jQuery);