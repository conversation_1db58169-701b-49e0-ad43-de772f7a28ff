"use strict";!function(e){function t(e,t=!1){return dt.infoBottom(e,t)}function i(e,t="info",i=!1,s="",a="",n=!1,o=!1,r=!1){return dt.infoPanel(e,t,i,s,a,n,o,r)}function s(e){return dt.activeUser(e)}function a(e){return dt.loading(e)}function n(e=!0,t=!0){return dt.loadingGlobal(e,t)}function o(e){return MC_TRANSLATIONS&&e in MC_TRANSLATIONS?MC_TRANSLATIONS[e]:e}function r(){typeof caches!==Ke&&caches.delete("mc-pwa-cache")}function l(e,t){dt.collapse(e,t)}function c(t,i){let s=e(t).parent().find("i"),a=e(t).val();MCF.search(a,()=>{s.mcLoading(!0),i(a,s)})}function d(t,i=!1,s=0){if(i)return e(t).scrollTop()+e(t).innerHeight()>=e(t)[0].scrollHeight-1;e(t).scrollTop(e(t)[0].scrollHeight-s)}function u(t=!1){if(!it){if(!1===tt)return tt=!0,void e.getScript(MC_URL+"/vendor/editorjs.js",()=>{u(t)});p(),it=!0,tt=new EditorJS({data:h(t)?{time:Date.now(),blocks:[t?{id:"mc",type:"raw",data:{html:t}}:{id:"mc",type:"paragraph",data:{text:""}}]}:t,i18n:{messages:{ui:{blockTunes:{toggler:{"Click to tune":o("Click to tune")}},inlineToolbar:{converter:{"Convert to":o("Convert to")}},toolbar:{toolbox:{Add:o("Add")}}},toolNames:{Text:o("Text"),Heading:o("Heading"),List:o("List"),Image:o("Image"),Code:o("Code"),"Raw HTML":o("Raw HTML"),Bold:o("Bold"),Italic:o("Italic"),Link:o("Link")},tools:{list:{Ordered:o("Ordered"),Unordered:o("Unordered")}},blockTunes:{delete:{Delete:o("Delete")},moveUp:{"Move up":o("Move up")},moveDown:{"Move down":o("Move down")}}},direction:k.hasClass("mc-rtl")?"rtl":"ltr"},tools:{list:{class:List,inlineToolbar:!0},image:{class:ImageTool,config:{uploader:{uploadByFile(t){let i=new FormData;return i.append("file",t),new Promise(t=>{e.ajax({url:MC_URL+"/include/upload.php",cache:!1,contentType:!1,processData:!1,data:i,type:"POST",success:function(e){"success"==(e=JSON.parse(e))[0]?t({success:1,file:{url:e[1]}}):console.log(e)}})})}}}},header:Header,code:CodeTool,raw:RawTool},onReady:()=>{it=!1,qe=!0,Pe=!1},onChange:()=>{qe?qe=!1:Pe=!0},minHeight:50})}}function p(){typeof tt.destroy!==Ke&&(tt.destroy(),tt=!1)}function h(e){return"string"==typeof e}function f(e){et||window.history.pushState("","",e)}function g(){return MC_ADMIN_SETTINGS.cloud?"&cloud="+MC_ADMIN_SETTINGS.cloud.token:""}function b(e){return e.replace("https://","").replace("http://","").replace("www.","").replace(/\/$/,"")}function v(e){MCF.search(e,()=>{let t="",i=!(e.length>1),s=Fe.find(".mc-replies-list > ul"),a=`<li class="mc-no-results">${o("No results found.")}</li>`;for(var n=0;n<Ne.length;n++){let s=Ne[n]["reply-name"];(i||s.toLowerCase().includes(e)||s.replaceAll("-"," ").toLowerCase().includes(e)||Ne[n]["reply-text"].toLowerCase().includes(e))&&(t+=`<li><div>${s}</div><div>${Ne[n]["reply-text"]}</div></li>`)}if(s.html(t),i||!MC_ADMIN_SETTINGS.dialogflow&&!MC_ADMIN_SETTINGS.chatbot_features)t||s.html(a);else{let i=s.closest(".mc-popup").find(".mc-icon-search");i.mcLoading(!0),MC_ADMIN_SETTINGS.dialogflow?st.dialogflow.getIntents(n=>{let o=st.dialogflow.searchIntents(e,!0),r="";for(var l=0;l<o.length;l++){let e=o[l].messages[0].text;e&&e.text&&(r+=`<li><div>${o[l].displayName}</div><div>${o[l].messages[0].text.text[0]}</div></li>`)}s.html(r?t+r:t||a),i.mcLoading(!1)}):MCF.ajax({function:"open-ai-message",model:MC_ADMIN_SETTINGS.open_ai_model,message:e},e=>{s.html(!e[1]||e[5]&&e[5].unknow_answer?t||a:`${t}<li><div></div><div>${e[1]}</div></li>`),i.mcLoading(!1)})}})}function m(e){let i=k.find("#mc-whatsapp-send-template-box");n(),MCF.ajax({function:"whatsapp-get-templates"},s=>{let a='<option value=""></option>',o=s[0],r="twilio"==o;if((s=s[1]).error)return t(s.error.message,"error");if(!Array.isArray(s))return t(s,"error");for(var l=0;l<s.length;l++)"official"!=o||"APPROVED"!=s[l].status||MC_ACTIVE_AGENT.department&&s[l].department.length&&!s[l].department.includes(MC_ACTIVE_AGENT.department)||(a+=`<option value="${s[l].name}" data-languages="${s[l].languages}" data-phone-id="${s[l].phone_number_id}">${s[l].name} (${s[l].label})</option>`),r&&(a+=`<option value="${s[l].sid}">${s[l].friendly_name}</option>`);i.attr("data-provider",o),i.find("#mc-whatsapp-send-template-list").html(a),MCForm.clear(i),i.find(".mc-direct-message-users").val(e.length?e.join(","):"all"),i.find(".mc-bottom > div").html(""),i.find(".mc-loading").mcLoading(!1),n(!1),i.mcShowLightbox()})}function _(e,t,s){window.open(e),i(`${o("For security reasons, delete the file after downloading it. Close this window to automatically delete it. File location:")}<pre>${e}</pre>`,"info",!1,t,s)}function S(){xe=!1,Be=!1,setTimeout(()=>{ke&&(ke[1].css("transform",""),ke[1].removeClass("mc-touchmove"))},100)}function w(e){return C.find(`[data-conversation-id="${e}"]`)}function y(){MCF.ajax({function:"get-conversations"},e=>{e.length||A.find(".mc-board").addClass("mc-no-conversation"),lt.populateList(e),We&&A.find(".mc-admin-list").mcActive(!0),MCF.getURL("conversation")?(A.mcActive()||x.find(".mc-admin-nav #mc-conversations").click(),lt.openConversation(MCF.getURL("conversation"))):We||MCF.getURL("user")||MCF.getURL("setting")||MCF.getURL("report")||MCF.getURL("area")&&"conversations"!=MCF.getURL("area")||setTimeout(()=>{lt.clickFirst()},100),lt.startRealTime(),lt.datetime_last_conversation=MC_ADMIN_SETTINGS.now_db,n(!1)}),MCF.serviceWorker.init(),MC_ADMIN_SETTINGS.push_notifications&&MCF.serviceWorker.initPushNotifications(),setInterval(function(){MCF.ajax({function:"get-active-user",db:!0},e=>{e||MCF.reset()})},36e5)}var k,x,B,A,T,I,$,C,F,N,E,L,D,M,R,j,G,U,P,q,O,V,H,W,z,J,Y,X,Q,Z,K,ee,te,ie,se,ae,ne,oe,re,le,ce,de,ue,pe,he,fe,ge,be,ve,me,_e,Se,we,ye,ke,xe,Be,Ae,Te,Ie,$e,Ce=[],Fe=!1,Ne=!1,Ee=!1,Le=!1,De=1,Me=1,Re={},je=1,Ge=1,Ue=!0,Pe=!1,qe=!0,Oe=!1,Ve=[],He=[],We=e(window).width()<465,ze={last:0,header:!0,always_hidden:!1},Je="localhost"===location.hostname||"127.0.0.1"===location.hostname,Ye=new Date,Xe=!1,Qe=!0,Ze=!1,Ke="undefined",et=!1,tt=!1,it=!1;e.fn.miniTip=function(t){var i=e.extend({title:"",content:!1,delay:300,anchor:"n",event:"hover",fadeIn:200,fadeOut:200,aHide:!0,maxW:"250px",offset:5,stemOff:0,doHide:!1},t);k&&0==k.find("#miniTip").length&&k.append('<div id="miniTip" class="mc-tooltip"><div></div></div>');var s=k.find("#miniTip"),a=s.find("div");return i.doHide?(s.stop(!0,!0).fadeOut(i.fadeOut),!1):this.each(function(){var t=e(this),n=i.content?i.content:t.attr("title");if(""!=n&&void 0!==n){window.delay=!1;var o=!1,r=!0;i.content||t.removeAttr("title"),"hover"==i.event?(t.hover(function(){s.removeAttr("click"),r=!0,l.call(this)},function(){r=!1,c()}),i.aHide||s.hover(function(){o=!0},function(){o=!1,setTimeout(function(){!r&&!s.attr("click")&&c()},20)})):"click"==i.event&&(i.aHide=!0,t.click(function(){return s.attr("click","t"),s.data("last_target")!==t?l.call(this):"none"==s.css("display")?l.call(this):c(),s.data("last_target",t),e("html").unbind("click").click(function(t){"block"==s.css("display")&&!e(t.target).closest("#miniTip").length&&(e("html").unbind("click"),c())}),!1}));var l=function(){i.show&&i.show.call(this,i),i.content&&""!=i.content&&(n=i.content),a.html(n),i.render&&i.render(s),s.hide().width("").width(s.width()).css("max-width",i.maxW);var o=t.is("area");if(o){var r,l=[],c=[],d=t.attr("coords").split(",");function u(e,t){return e-t}for(r=0;r<d.length;r++)l.push(d[r++]),c.push(d[r]);var p=t.parent().attr("name"),h=e("img[usemap=\\#"+p+"]").offset(),f=parseInt(h.left,10)+parseInt((parseInt(l.sort(u)[0],10)+parseInt(l.sort(u)[l.length-1],10))/2,10),g=parseInt(h.top,10)+parseInt((parseInt(c.sort(u)[0],10)+parseInt(c.sort(u)[c.length-1],10))/2,10)}else g=parseInt(t.offset().top,10),f=parseInt(t.offset().left,10);var b=o?0:parseInt(t.outerWidth(),10),v=o?0:parseInt(t.outerHeight(),10),m=s.outerWidth(),_=s.outerHeight(),S=Math.round(f+Math.round((b-m)/2)),w=Math.round(g+v+i.offset+8),y=Math.round(m-16)/2-parseInt(s.css("borderLeftWidth"),10),k=0,x=f+b+m+i.offset+8>parseInt(e(window).width(),10),B=m+i.offset+8>f,A=_+i.offset+8>g-e(window).scrollTop(),T=g+v+_+i.offset+8>parseInt(e(window).height()+e(window).scrollTop(),10),I=i.anchor;B||"e"==i.anchor&&!x?"w"!=i.anchor&&"e"!=i.anchor||(I="e",k=Math.round(_/2-8-parseInt(s.css("borderRightWidth"),10)),y=-8-parseInt(s.css("borderRightWidth"),10),S=f+b+i.offset+8,w=Math.round(g+v/2-_/2)):(x||"w"==i.anchor&&!B)&&("w"!=i.anchor&&"e"!=i.anchor||(I="w",k=Math.round(_/2-8-parseInt(s.css("borderLeftWidth"),10)),y=m-parseInt(s.css("borderLeftWidth"),10),S=f-m-i.offset-8,w=Math.round(g+v/2-_/2))),T||"n"==i.anchor&&!A?"n"!=i.anchor&&"s"!=i.anchor||(I="n",k=_-parseInt(s.css("borderTopWidth"),10),w=g-(_+i.offset+8)):(A||"s"==i.anchor&&!T)&&("n"!=i.anchor&&"s"!=i.anchor||(I="s",k=-8-parseInt(s.css("borderBottomWidth"),10),w=g+v+i.offset+8)),"n"==i.anchor||"s"==i.anchor?m/2>f?(S=S<0?y+S:y,y=0):f+m/2>parseInt(e(window).width(),10)&&(S-=y,y*=2):A?(w+=k,k=0):T&&(w-=k,k*=2),delay&&clearTimeout(delay),delay=setTimeout(function(){s.css({"margin-left":S+"px","margin-top":w+"px"}).stop(!0,!0).fadeIn(i.fadeIn)},i.delay),s.attr("class","mc-tooltip "+I)},c=function(){(!i.aHide&&!o||i.aHide)&&(delay&&clearTimeout(delay),delay=setTimeout(function(){d()},i.delay))},d=function(){!i.aHide&&!o||i.aHide?(s.stop(!0,!0).fadeOut(i.fadeOut),i.hide&&i.hide.call(this)):setTimeout(function(){c()},200)}}})},e.fn.mcLanguageSwitcher=function(t=[],i="",s=!1){let a=`<div class="mc-language-switcher" data-source="${i}">`,n=[],r=e(this).hasClass("mc-language-switcher-cnt")?e(this):e(this).find(".mc-language-switcher-cnt");for(var l=0;l<t.length;l++){let e=h(t[l])?t[l]:t[l][0],i=!(h(t[l])||!t[l][1])&&t[l][1];n.includes(e)||(a+=`<span ${s==e?'class="mc-active" ':""}data-language="${e}"${i?' data-id="'+i+'"':""}><i class="mc-icon-close"></i><img src="${MC_URL}/media/flags/${e.toLowerCase()}.png" /></span>`,n.push(e))}return r.find(".mc-language-switcher").remove(),r.append(a+`<i data-mc-tooltip="${o("Add translation")}" class="mc-icon-plus"></i></div>`),r.mcInitTooltips(),this},e.fn.mcShowLightbox=function(t=!1,i=""){return k.find(".mc-lightbox").mcActive(!1),we.mcActive(!0),e(this).mcActive(!0),t?e(this).addClass("mc-popup-lightbox").attr("data-action",i):e(this).css({"margin-top":e(this).outerHeight()/-2+"px","margin-left":e(this).outerWidth()/-2+"px"}),e("body").addClass("mc-lightbox-active"),setTimeout(()=>{dt.open_popup=this},500),this.preventDefault,this},e.fn.mcHideLightbox=function(){return e(this).find(".mc-lightbox,.mc-popup-lightbox").mcActive(!1).removeClass("mc-popup-lightbox").removeAttr("data-action"),we.mcActive(!1),e("body").removeClass("mc-lightbox-active"),dt.open_popup=!1,this},e.fn.mcInitTooltips=function(){return e(this).find("[data-mc-tooltip]").each(function(){e(this).miniTip({content:e(this).attr("data-mc-tooltip"),anchor:"s",delay:500})}),this};var st={dialogflow:{intents:!1,qea:[],token:MCF.storage("dialogflow-token"),dialogflow_languages:[],original_response:!1,smart_reply_busy:!1,smartReply:function(e=!1){let t=MCChat.conversation.id;this.smart_reply_busy!=t&&(this.smart_reply_busy=t,MCF.ajax({function:"dialogflow-smart-reply",message:e,token:this.token,conversation_id:t,dialogflow_languages:this.dialogflow_languages},e=>{if(this.smart_reply_busy=!1,MCChat.conversation.id&&t===MCChat.conversation.id){let t=e.suggestions,s="",a=T[0].scrollTop===T[0].scrollHeight-T[0].offsetHeight,n=!(!MCChat.conversation||!MCChat.conversation.getLastMessage())&&MCChat.conversation.getLastMessage().message;e.token&&(this.token=e.token,MCF.storage("dialogflow-token",e.token));for(var i=0;i<t.length;i++)t[i]!=n&&(s+=`<span>${MCF.escape(t[i])}</span>`);R.html(s),a&&MCChat.scrollBottom()}e.dialogflow_languages&&(this.dialogflow_languages=e.dialogflow_languages)}))},showCreateIntentBox:function(e){let t="",i=MCChat.conversation.getMessage(e),s=i.message;if(MCF.isAgent(i.get("user_type")))t=MCChat.conversation.getLastUserMessage(i.get("index")),t=t&&t.payload("mc-human-takeover")?MCChat.conversation.getLastUserMessage(t.get("index")).message:t.message;else{t=s;let e=MCChat.conversation.getNextMessage(i.id,"agent");e&&(s=e.message)}M.hasClass("mc-dialogflow-disabled")?(MCF.ajax({function:"open-ai-get-qea-training"},e=>{let t='<option value="">'+o("New Q&A")+"</option>";for(var i=0;i<e.length;i++)this.qea=e,t+=`<option value="${i}">${e[i][0][0]}</option>`;M.find("#mc-qea-select").html(t)}),st.openAI.generateQuestions(t)):this.getIntents(e=>{let i='<option value="">'+o("New Intent")+"</option>";for(var s=0;s<e.length;s++)i+=`<option value="${e[s].name}">${e[s].displayName}</option>`;M.find("#mc-intents-select").html(i),st.openAI.generateQuestions(t)}),M.attr("data-message-id",i.id),M.find(".mc-type-text:not(.mc-first)").remove(),M.find(".mc-type-text input").val(t),M.find("#mc-intents-select,#mc-qea-select").val(""),M.find(".mc-search-btn").mcActive(!1).find("input").val(""),this.searchIntents(""),this.original_response=s,M.find("textarea").val(s),M.mcShowLightbox()},submitIntent:function(i){if(a(i))return;let s=[],n=M.find("textarea").val(),o=M.find("#mc-intents-select,#mc-qea-select").val(),r=M.find("#mc-train-chatbots").val(),l="open-ai"==r||M.hasClass("mc-dialogflow-disabled");if(M.find(".mc-type-text input").each(function(){e(this).val()&&s.push(e(this).val())}),!n&&!o||0==s.length)MCForm.showErrorMessage(M,"Please insert the bot response and at least one user expression."),e(i).mcLoading(!1);else{let a;l&&(o?(this.qea[o][0]=this.qea[o][0].concat(s),a=this.qea[o]):a=[[s,n]]),MCF.ajax({function:l?"open-ai-qea-training":o?"dialogflow-update-intent":"dialogflow-create-intent",questions_answers:a,expressions:s,response:n,agent_language:M.find(".mc-dialogflow-languages select").val(),conversation_id:MCChat.conversation.id,intent_name:o,update_index:o,services:r,language:M.find(".mc-dialogflow-languages select").val()},s=>{e(i).mcLoading(!1),!0===s?(k.mcHideLightbox(),t("Training completed")):MCForm.showErrorMessage(M,s.error&&s.error.message?s.error&&s.error.message:"Error")})}},getIntents:function(e){!1===this.intents?MCF.ajax({function:"dialogflow-get-intents"},t=>{this.intents=Array.isArray(t)?t:[],e(this.intents)}):e(this.intents)},searchIntents:function(e,t=!1){if(M.hasClass("mc-dialogflow-disabled")){let t=!(e.length>1),s=t?`<option value="">${o("New Q&A")}</option>`:"";e=e.toLowerCase();for(i=0;i<this.qea.length;i++)(t||this.qea[i][0].join("").toLowerCase().includes(e)||this.qea[i][1].toLowerCase().includes(e))&&(s+=`<option value="${i}">${this.qea[i][0][0]}</option>`);M.find("#mc-qea-select").html(s).change(),t&&M.find("textarea").val(this.original_response)}else{let n=!(e.length>1),r=n?`<option value="">${o("New Intent")}</option>`:"",l=this.intents,c=[];e=e.toLowerCase();for(var i=0;i<l.length;i++){let o=n||l[i].displayName.toLowerCase().includes(e);if(!o&&l[i].trainingPhrases){let t=l[i].trainingPhrases;for(var s=0;s<t.length;s++){for(var a=0;a<t[s].parts.length;a++)if(t[s].parts[a].text.toLowerCase().includes(e)){o=!0;break}if(o)break}}o&&(t?c.push(l[i]):r+=`<option value="${l[i].name}">${l[i].displayName}</option>`)}if(t)return c;M.find("#mc-intents-select").html(r).change(),e||M.find("textarea").val(this.original_response)}},previewIntentDialogflow:function(e){let t="",s=this.getIntent(e);if(s){let e=s.trainingPhrases?s.trainingPhrases:[],o=e.length;if(o>1){for(var a=0;a<o;a++)for(var n=0;n<e[a].parts.length&&(t+=`<span>${e[a].parts[n].text}</span>`,15!=n);n++);i(t,"info",!1,"intent-preview-box","",o>10)}}},previewIntent:function(e){if(e){let s="",a=this.qea[e];if(a[0].length>1){let e=a[0].length>15?15:a[0].length;for(var t=0;t<e;t++)s+=`<span>${a[0][t]}</span>`;i(s,"info",!1,"qea-preview-box","",e>10)}}},getIntent:function(e){for(var t=0;t<this.intents.length;t++)if(this.intents[t].name==e)return this.intents[t];return!1},translate:function(e,t,i,s,a){e.length&&MCF.ajax({function:"google-translate",strings:e,language_code:t,token:this.token,message_ids:s,conversation_id:a},e=>{if(this.token=e[1],!Array.isArray(e[0]))return MCF.error(JSON.stringify(e[0]),"MCApps.dialogflow.translate"),!1;i(e[0])})}},openAI:{urls_history:[],progress:1,rewriteButton:function(e){j.length&&j.mcActive(e.length>2&&e.indexOf(" "))},rewrite:function(e,t){MCF.ajax({function:"open-ai-message",model:MC_ADMIN_SETTINGS.open_ai_model,message:(MC_ADMIN_SETTINGS.open_ai_prompt_rewrite?MC_ADMIN_SETTINGS.open_ai_prompt_rewrite:"Make the following sentence more friendly and professional")+` and use ${MC_LANGUAGE_CODES[MC_ADMIN_SETTINGS.active_agent_language]} language: """${e.replace('"',"'")}"""`,extra:"rewrite"},i=>{i[0]||console.error("OpenAI: "+JSON.stringify(i[1])),lt.previous_editor_text=e,t(i)})},troubleshoot:function(){let e=MC_ADMIN_SETTINGS.open_ai_chatbot_status;return!0!==e&&t("inactive"==e?"Enable the chatbot in Settings > Artificial Intelligence > OpenAI > Chatbot.":"key"==e?"Enter the OpenAI API key in Settings > Artificial Intelligence > OpenAI > API key.":"The training data is ignored. Change the chatbot mode in Settings > Artificial Intelligence > OpenAI > Chatbot mode.","error"),e},getCode:{set_data:function(e){let t="",i=this.select_user_details();e&&e.length||(e=[["",""]]);for(var s=0;s<e.length;s++)t+=`<div class="repeater-item"><div>${i.replace(`"${e[s][0]}"`,`"${e[s][0]}" selected`)}<div class="mc-setting"><input type="url" placeholder="${o("Enter the value")}" value="${e[s][1]}"></div></div><i class="mc-icon-close"></i></div>`;return this.repeater_("Data",t)},actions:function(e){let t=[["tags","Assign tags"],["department","Assign a department"],["agent","Assign an agent"],["redirect","Go to URL"],["open_article","Show an article"],["transcript","Download transcript"],["transcript_email","Email transcript"],["send_email","Send email to user"],["send_email_agents","Send email to agents"],["archive_conversation","Archive the conversation"],["human_takeover","Human takeover"]],i="",s="";e&&e.length||(e=[["tags",""]]);for(a=0;a<t.length;a++)i+=`<option value="${t[a][0]}">${o(t[a][1])}</option>`;for(var a=0;a<e.length;a++)s+=`<div class="repeater-item"><div><div class="mc-setting"><select>${i.replace(`"${e[a][0]}"`,`"${e[a][0]}" selected`)}</select></div>${this.action(e[a][0],e[a][1])}</div><i class="mc-icon-close"></i></div>`;return this.repeater_("Actions",s)},action:function(e,t){let i={department:"number",agent:"number",redirect:"url"};return["send_email_agents","send_email","open_article","redirect","agent","department","tags"].includes(e)?`<div class="mc-setting"><input type="${i[e]?i[e]:"text"}" value="${t}" placeholder="${o({tags:"Enter tag names, separated by commas",department:"Enter the department ID",agent:"Enter the agent ID",redirect:"Enter the URL",open_article:"Enter the article ID",send_email:"Enter a message",send_email_agents:"Enter a message"}[e])}" value="${t}"></div>`:""},select_user_details:function(){let e='<div class="mc-setting"><select>',t=[["full_name","Name"],["email","Email"],["password","Password"]].concat(rt.getExtraDetailsList());for(var i=0;i<t.length;i++)e+=`<option value="${t[i][0]}">${o(t[i][1])}</option>`;return e+"</select></div>"},repeater_:function(e,t){return`<div class="mc-title">${o(e)}</div><div data-type="repeater" class="mc-setting mc-type-repeater"><div class="input"><div class="mc-repeater mc-repeater-block-${MCF.stringToSlug(e)}">${t}</div><div class="mc-btn mc-btn-white mc-repeater-add mc-icon"><i class="mc-icon-plus"></i>${o("Add new item")}</div></div></div>`}},generateQuestions:function(e){if(MC_ADMIN_SETTINGS.open_ai_user_expressions&&e){let t=M.find('[data-value="add"]');t.mcLoading(!0),M.find(".mc-open-ai-intent").remove(),MCF.ajax({function:"open-ai-user-expressions",message:e},e=>{let i="";for(var s=0;s<e.length;s++)e[s]&&(i+=`<div class="mc-setting mc-type-text mc-open-ai-intent"><input type="text" value="${e[s].replace(/"/g,"")}"></div>`);i&&M.find("> div > .mc-type-text").last().after(i),t.mcLoading(!1)})}},flows:{flows:[],set:function(e){if("string"==typeof e)(e=e.trim().replaceAll('"',"").replaceAll("_","-"))&&(e={name:e,steps:[[[{type:"start",start:"message",message:"",conditions:[],disabled:!1}]],[[]]]});else for(var t=0;t<this.flows.length;t++)if(this.flows[t].name==e.name)return this.flows[t]=e,this.show(e.name),!0;this.flows.push(e),re.find(".mc-active").mcActive(!1),re.append(this.navCode(e.name,!0)),this.show(e.name)},get:function(e=!1){e||(e=this.getActiveName());for(var t=0;t<this.flows.length;t++)if(this.flows[t].name==e)return this.flows[t].steps||(this.flows[t].steps=[]),this.flows[t];return!1},show:function(t=!1){t||(t=this.getActiveName());let i,s=this.get(t),a="";if(s){let t=[],d=[];for(var n=0;n<s.steps.length;n++){let e=s.steps[n],u=0;t=d,d=[],a+="<div><div>";for(var r=0;r<e.length;r++){let s=e[r];a+='<div class="mc-flow-block-cnt">',t[r]&&(a+=`<div class="mc-flow-block-cnt-name">${t[r]}</div>`);for(var l=0;l<s.length;l++){"start"!=s[l].type||Array.isArray(s[l].message)||(s[l].message=[{message:s[l].message}]);let e=s[l].message?Array.isArray(s[l].message)?s[l].message.length?s[l].message[0].message:"":s[l].message:"";switch(e&&(e=`<div>${e.length>45?e.substring(0,45)+"...":e}</div>`),a+=`<div class="mc-flow-block" data-type="${s[l].type}"><div>${o(MCF.slugToString(s[l].type))}</div>${e}`,s[l].type){case"get_user_details":d.push(!1);break;case"condition":case"button_list":let t="condition"==s[l].type,n=t?[o("True"),o("False")]:s[l].options;if(t){a+="<div>",i=s[l].conditions;for(c=0;c<i.length;c++)a+=`<div>${MCF.slugToString(i[c][0]+" "+i[c][1])}${i[c][2]?": "+i[c][2]:""}</div>`;a+="</div>"}a+='<div class="mc-flow-connectors">';for(c=0;c<n.length;c++)d.push("ABCDEFGHIJKLMNOPQRSTUVWXYZ"[u]+(c+1)),a+=`<div>${n[c]}<span>${d[d.length-1]}</span></div>`;a+="</div>",u++;break;case"video":a+=`<div>${s[l].url}</div>`;break;case"action":case"set_data":a+="<div>",i=s[l]["set_data"==s[l].type?"data":"actions"];for(var c=0;c<i.length;c++)a+=`<div>${MCF.slugToString(i[c][0])}${i[c][1]?": "+i[c][1]:""}</div>`;a+="</div>";break;case"rest_api":a+=`<div>${s[l].url}</div>`}a+="</div>"}s.length<4&&(a+='<div class="mc-flow-add-block mc-icon-plus"></div>'),a+="</div>"}a+='</div><div class="mc-flow-add-step mc-icon-plus"></div></div>'}le.html(a),e(k).find(".mc-flow-scroll").mcActive(250*s.steps.length>le.outerWidth())}},delete:function(e){for(var t=0;t<this.flows.length;t++)if(this.flows[t].name==e){let i=re.find(`[data-value="${e}"]`);return this.flows[t].steps.forEach(e=>{e.forEach(e=>{e.forEach(e=>{e.attachments&&e.attachments.forEach(e=>{MCF.ajax({function:"delete-file",path:e})})})})}),this.flows.splice(t,1),i.mcActive()&&(i.prev().length?i.prev().click():i.next().length?i.next().click():le.html("")),i.remove(),!0}return!1},save:function(e=!1){MCF.ajax({function:"open-ai-flows-save",flows:JSON.stringify(this.flows)},t=>{e(t)})},navCode:function(e,t=!1){return`<li${t?' class="mc-active"':""} data-value="${e}">${e}<i class="mc-icon-delete"></i></li>`},getActive:function(){return re.find(".mc-active")},getActiveName:function(){return this.getActive().attr("data-value")},getActiveIndex:function(){return this.getActive().index()},steps:{get:function(e=!1,t=!1){return st.openAI.flows.get(e).steps[t||this.getActiveIndex()]},getActiveIndex:function(){return st.openAI.flows.blocks.getActive().parent().parent().parent().index()}},blocks:{set:function(e,t=!1,i=!1,s=!1,a=!1){let n=this.getIndexes(t,i,s,a),o=st.openAI.flows.get(t);if(o){let i=!1;if(o.steps.length>n.step){let t=o.steps[n.step];if(t.length>n.cnt){let s=t[n.cnt];s.length>n.block&&(s[n.block]=e,i=!0)}i||(o.steps[n.step][n.cnt].push(e),i=!0)}i||o.steps.push([[e]]);let s=o.steps[n.step],a=o.steps[st.openAI.flows.steps.getActiveIndex()+1],c=0;for(l=0;l<s.length;l++)for(var r=0;r<s[l].length;r++)switch(s[l][r].type){case"get_user_details":c+=1;break;case"button_list":c+=s[l][r].options.length;break;case"condition":c+=2}if(a)if(a.length>c)a.splice(-1*(c-1),c);else{let e=a.length;for(e;e<c;e++)a.splice(n.cnt,0,[])}else{a=[];for(var l=0;l<c;l++)a.push([]);a.length&&o.steps.push(a)}return st.openAI.flows.show(t),!0}return!1},get:function(e=!1,t=!1,i=!1,s=!1){let a=st.openAI.flows.get(e);if(a){let n=this.getIndexes(e,t,i,s);return a.steps[n.step][n.cnt][n.block]}return!1},add:function(e,t=!1,i=!1,s=!1,a=!1){let n=this.getIndexes(t,i,s,a);this.set(Object.assign({button_list:{message:"",options:[]},message:{message:""},video:{message:"",url:""},get_user_details:{message:"",details:[]},set_data:{data:[]},action:{actions:[]},rest_api:{url:"",method:"",headers:[],body:"",save_response:[]},condition:{conditions:[]}}[e],{type:e}),n.name,n.step,n.cnt,n.block),st.openAI.flows.show(t),setTimeout(()=>{le.find("> div").eq(n.step).find(".mc-flow-block-cnt").eq(n.cnt).find(".mc-flow-block").eq(n.block).click()},100)},delete:function(e=!1,t=!1,i=!1,s=!1){let a=this.getIndexes(e,t,i,s);for(var n=0;n<st.openAI.flows.flows.length;n++){let e=st.openAI.flows.flows[n];if(a.name==e.name){if(["get_user_details","button_list","condition"].includes(e.steps[a.step][a.cnt][a.block].type)){let t=this.delete_(n,a.step,a.cnt),i=Object.assign({},e);for(o=0;o<t.length;o++)e.steps[t[o][0]][t[o][1]]=!1;e.steps[a.step][a.cnt].splice(a.block,1),i.steps=[];for(var o=0;o<e.steps.length;o++){let t=[];for(var r=0;r<e.steps[o].length;r++)e.steps[o][r]&&t.push(e.steps[o][r]);t.length&&i.steps.push(t)}st.openAI.flows.flows[n]=i}else e.steps[a.step][a.cnt].splice(a.block,1);return st.openAI.flows.show(a.name),!0}}return!1},delete_:function(e,t,i,s=[]){let a=st.openAI.flows.blocks.getNextCntIndexes(e,t,i);for(var n=0;n<a.length;n++)s.push([t+1,a[n]]),this.delete_(e,t+1,a[n],s);return s},getActive:function(){return le.find(".mc-flow-add-block.mc-active, .mc-flow-block.mc-active")},getActiveIndex:function(){let e=this.getActiveCnt().find(".mc-flow-block"),t=e.index(this.getActive());return-1===t?e.length:t},getActiveCnt:function(){return this.getActive().parent()},getActiveCntIndex:function(){return this.getActive().parent().index()},getNextCnt:function(e,t,i,s=0){let a=this.getNextCntIndexes(e,t,i,s);return a.length>s&&st.openAI.flows.flows[e].steps[t+1][a[s]]},getNextCntIndexes:function(e,t,i){let s=st.openAI.flows.flows[e],a=[];if(s&&s.steps[t+1]){let e=0;for(var n=0;n<=i;n++){let l=s.steps[t][n];for(var o=0;o<l.length;o++)if("button_list"==l[o].type)for(r=0;r<l[o].options.length;r++)n==i&&a.push(e),e++;else if("get_user_details"==l[o].type)n==i&&a.push(e),e++;else if("condition"==l[o].type)for(var r=0;r<2;r++)n==i&&a.push(e),e++}}return a},getPreviousCntIndex:function(e,t,i){let s=st.openAI.flows.flows[e].steps[t-1];if(s){let e=0;for(var a=0;a<s.length;a++){let t=s[a];for(var n=0;n<t.length;n++)e+="button_list"==t[n].type?t[n].options.length:"get_user_details"==t[n].type?1:"condition"==t[n].type?2:0;if(e>i)return a}return e}},getIndexes:function(e,t,i,s){return{name:e||st.openAI.flows.getActiveName(),step:t||st.openAI.flows.steps.getActiveIndex(),cnt:i||this.getActiveCntIndex(),block:s||this.getActiveIndex()}},activateLinkedCnts:function(t){let i=e(t).parent(),s=st.openAI.flows.getActiveIndex(),a=i.parent().parent().index(),n=le.find("> div"),o=n.eq(a-1).find(".mc-flow-block-cnt").eq(st.openAI.flows.blocks.getPreviousCntIndex(s,a,i.index()));if(!Oe){let e=n.eq(a+1).find(".mc-flow-block-cnt"),t=st.openAI.flows.blocks.getNextCntIndexes(s,a,i.index());for(var r=0;r<t.length;r++)e.eq(t[r]).mcActive(!0)}le.find(".mc-flow-connectors > div").mcActive(!1),o.mcActive(!0).find(".mc-flow-block-cnt-name").mcActive(!0),i.find(".mc-flow-block-cnt-name").length&&o.find(".mc-flow-connectors > div").mcActive(!1).eq(parseInt(i.find(".mc-flow-block-cnt-name").html().substring(1))-1).mcActive(!0)}}},train:{urls:[],errors:[],base_url:!1,start_urls:[],sitemap_processed_urls:[],active_source:!1,history:[],training_button:!1,extract_url:[],skip_files:[],files:function(e,t=0){let i=de.prop("files");if(t>=i.length)return e(!0);let s=i[t].name;this.isFile(s)&&!this.skip_files.includes(s)?(k.find("#mc-embeddings-box p").html(o("We are processing the source")+"<pre>"+s+"</pre><span>"+o("Only {R} sources left to complete.").replace("{R}",i.length-t)+"</span>"),de.mcUploadFiles(i=>{"success"==(i=JSON.parse(i))[0]&&MCF.ajax({function:"open-ai-file-training",url:i[1]},i=>{this.isError(i)||this.files(e,t+1)})},t)):this.files(e,t+1)},website:function(e,t=0){if(t>=this.urls.length)return e(!0);let i=this.urls[t];i&&i.includes("http")?(k.find("#mc-embeddings-box p").html(o("We are processing the source")+"<pre>"+i+"</pre><span>"+o("Only {R} sources left to complete.").replace("{R}",this.urls.length-t)+"</span>"),i.includes(".xml")?MCF.ajax({function:"get-sitemap-urls",sitemap_url:i},i=>{Array.isArray(i)?this.urls=this.urls.concat(i):this.errors.push(i),this.website(e,t+1)}):!this.sitemap_processed_urls.includes(i)&&this.extract_url[t]?(this.sitemap_processed_urls.push(i),this.sitemap(i,i=>{this.urls=this.urls.concat(i),this.website(e,t+1)})):MCF.ajax({function:"open-ai-url-training",url:i},i=>{this.isError(i)||(i[0]||i[1].includes("http-error-404")||i[1].includes("http-error-302")||(i[1].includes("http-error")&&0===t?this.errors.push(i[2]):!0!==i[0][0]&&this.errors.push(i)),this.website(e,t+1))})):(i&&this.errors.push(o("Use a valid URL starting with http. The URL {R} is not valid.").replace("{R}",i)),this.website(e,t+1))},sitemap:function(e,t,i=[]){k.find("#mc-embeddings-box p").html(o("We are generating the sitemap")+"<pre>"+e+"</pre>"),MCF.ajax({function:"generate-sitemap",url:e},e=>{t(e)})},qea:function(e){k.find("#mc-embeddings-box p").html(o("We are processing the Q&A."));let t=at.repeater.get(ae.find(".mc-repeater").eq(0).find("> .repeater-item")).map(e=>[e["open-ai-faq-questions"].map(e=>e.question),e["open-ai-faq-answer"],e["open-ai-faq-function-calling-url"],e["open-ai-faq-function-calling-method"],e["open-ai-faq-function-calling-headers"],!!e["open-ai-faq-function-calling-properties"].length&&e["open-ai-faq-function-calling-properties"].map(e=>[e.name,e.description,e.allowed]),e["open-ai-faq-set-data"].map(e=>[e.id,e.value])]);MCF.ajax({function:"open-ai-qea-training",questions_answers:t,reset:!0},t=>{e(t)})},articles:function(e){k.find("#mc-embeddings-box p").html(o("We are processing the articles.")),MCF.ajax({function:"open-ai-articles-training"},t=>{e(t)})},isFile:function(e){return e.includes(".pdf")||e.includes(".txt")||e.includes(".json")||e.includes(".csv")},isError:function(e){let t="chars-limit-exceeded"==e[1],s=t||e[1]&&e[1][0]&&e[1][0].error;return s&&(te.find("#mc-train-chatbot").mcLoading(!1),i(t?o("The chatbot cannot be trained with these sources because the limit of your plan is {R} characters. Upgrade your plan to increase the number of characters.").replace("{R}",e[2]):e[1][0].error.message)),s}},playground:{messages:[],last_response:!1,addMessage:function(e,t="user",i=[]){oe.append(`<div data-type="${t}"><div>${o("user"==t?"User":"Assistant")}<div><i class="mc-icon-close mc-btn-icon mc-btn-red"></i></div></div><div>${new MCMessage({id:1,message:MCF.escape(e),creation_time:"0000-00-00 00:00:00",status_code:0,user_type:"agent",attachments:JSON.stringify(i)}).getCode()}</div></div>`),oe[0].scrollTop=oe[0].scrollHeight,this.messages.push([t,e])}},init:function(){MCF.ajax({function:"open-ai-get-training-files"},e=>{let t=["",""];for(var i=0;i<e.length;i++)if(!["mc-conversations","mc-articles","mc-database","mc-flows"].includes(e[i])){let s=this.train.isFile(e[i]);t[s?1:0]+=`<tr data-url="${e[i]}"><td><input type="checkbox" /></td><td>${s?MCF.beautifyAttachmentName(e[i].split("/").pop()):b(e[i])}</td><td></td><td><i class="mc-icon-delete"></i></td></tr>`}ie.html(t[1]).mcLoading(!1),se.html(t[0]).mcLoading(!1),te.find("#mc-chatbot-delete-website").setClass("mc-hide",!t[0])}),MCF.ajax({function:"open-ai-get-qea-training"},t=>{for(var i=0;i<t.length;i++)t[i][0]&&!Array.isArray(t[i][0])&&(t[i][0]=[t[i][0]]);let s=t.map(e=>({"open-ai-faq-questions":e[0]?e[0].map(e=>({question:e})):[""],"open-ai-faq-answer":e[1],"open-ai-faq-function-calling-url":e[2],"open-ai-faq-function-calling-method":e[3],"open-ai-faq-function-calling-headers":e[4],"open-ai-faq-function-calling-properties":e[5]&&e[5].length?e[5].map(e=>({name:e[0],description:e[1],allowed:e[2]})):[["","",""]],"open-ai-faq-set-data":e[6]&&e[6].length?e[6].map(e=>({id:e[0],value:e[1]})):[["",""]]}));s.length&&(ae.find("> div > .mc-repeater").html(at.repeater.set(s,ae.find("> div > .mc-repeater > .repeater-item:last-child"))),ae.find(".mc-enlarger").each(function(){let t=e(this).find("select"),i=["transcript","transcript_email","human_takeover","archive_conversation"];(e(this).find("input").val()||i.includes(t.val()))&&(e(this).mcActive(!0),e(this).hasClass("mc-enlarger-function-calling")&&e(this).closest(".repeater-item").find(".mc-qea-repeater-answer").addClass("mc-hide")),t.each(function(){i.includes(e(this).val())&&e(this).parent().next().find("input").addClass("mc-hide")})}))})}},messenger:{check:function(e){return["fb","ig"].includes(e.get("source"))},send:function(e,t,i="",s=[],a,n=!1,o=!1){MCF.ajax({function:"messenger-send-message",psid:e,facebook_page_id:t,message:i,message_id:n,attachments:s,metadata:a},e=>{o&&o(e),st.unsupportedRichMessages(i,"Messenger")})}},whatsapp:{check:function(e){return"wa"==e.get("source")},send:function(e,t="",s=[],a=!1,n=!1){MCF.ajax({function:"whatsapp-send-message",to:e,message:t,attachments:s,phone_id:a},e=>{e.error&&i(e.error.message,"info",!1,"error-wa"),n&&n(e),st.unsupportedRichMessages(t,"WhatsApp")})},activeUserPhone:function(e=s()){return!!e.getExtra("phone")&&e.getExtra("phone").value.replace("+","")}},telegram:{check:function(e){return"tg"==e.get("source")},send:function(e,t="",i=[],s=!1,a=!1){MCF.ajax({function:"telegram-send-message",chat_id:e,message:t,attachments:i,conversation_id:s},e=>{a&&a(e),st.unsupportedRichMessages(t,"Telegram")})}},viber:{check:function(e){return"vb"==e.get("source")},send:function(e,t="",i=[],s=!1){MCF.ajax({function:"viber-send-message",viber_id:e,message:t,attachments:i},e=>{s&&s(e),st.unsupportedRichMessages(t,"Viber")})}},zalo:{check:function(e){return"za"==e.get("source")},send:function(e,t="",i=[],s=!1){MCF.ajax({function:"zalo-send-message",zalo_id:e,message:t,attachments:i},e=>{s&&s(e),st.unsupportedRichMessages(t,"Zalo")})}},twitter:{check:function(e){return"tw"==e.get("source")},send:function(e,t="",i=[],s=!1){MCF.ajax({function:"twitter-send-message",twitter_id:e,message:t,attachments:i},e=>{s&&s(e),st.unsupportedRichMessages(t,"Twitter")})}},line:{check:function(e){return"ln"==e.get("source")},send:function(e,t="",i=[],s=!1,a=!1){MCF.ajax({function:"line-send-message",line_id:e,message:t,attachments:i,conversation_id:s},e=>{a&&a(e),st.unsupportedRichMessages(t,"LINE")})}},wechat:{token:!1,check:function(e){return"wc"==e.get("source")},send:function(e,t="",i=[],s=!1){MCF.ajax({function:"wechat-send-message",open_id:e,message:t,attachments:i,token:this.token},e=>{Array.isArray(e)&&(this.token=e[1],e=e[0]),s&&s(e),st.unsupportedRichMessages(t,"WeChat")})}},aecommerce:{conversationPanel:function(){let t="",i=s().getExtra("aecommerce-id");this.panel||(this.panel=A.find(".mc-panel-aecommerce")),i&&!a(this.panel)&&MCF.ajax({function:"aecommerce-get-conversation-details",aecommerce_id:i.value},i=>{t=`<h3>${MC_ADMIN_SETTINGS.aecommerce_panel_title}</h3><div><div class="mc-split"><div><div class="mc-title">${o("Number of orders")}</div><span>${i.orders_count} ${o("orders")}</span></div><div><div class="mc-title">${o("Total spend")}</div><span>${i.total} ${i.currency_symbol}</span></div></div><div class="mc-title">${o("Cart")}</div><div class="mc-list-items mc-list-links mc-aecommerce-cart">`;for(s=0;s<i.cart.length;s++){let e=i.cart[s];t+=`<a href="${e.url}" target="_blank" data-id="${e.id}"><span>#${e.id}</span> <span>${e.name}</span> <span>x ${e.quantity}</span></a>`}if(t+=(i.cart.length?"":"<p>"+o("The cart is currently empty.")+"</p>")+"</div>",i.orders.length){t+=`<div class="mc-title">${o("Orders")}</div><div class="mc-list-items mc-list-links mc-aecommerce-orders">`;for(var s=0;s<i.orders.length;s++){let e=i.orders[s],a=e.id;t+=`<a data-id="${a}" href="${e.url}" target="_blank"><span>#${e.id}</span> <span>${MCF.beautifyTime(e.time,!0)}</span> <span>${e.price} ${i.currency_symbol}</span></a>`}t+="</div>"}e(this.panel).html(t).mcLoading(!1),l(this.panel,160)}),e(this.panel).html(t)}},martfury:{conversationPanel:function(){let t=s().getExtra("martfury-id");this.panel||(this.panel=A.find(".mc-panel-martfury")),t&&!a(this.panel)&&MCF.ajax({function:"martfury-get-conversation-details",martfury_id:t.value},t=>{e(this.panel).html(t).mcLoading(!1),l(this.panel,160)}),e(this.panel).html("")}},whmcs:{conversationPanel:function(){let t="",i=s().getExtra("whmcs-id");this.panel||(this.panel=A.find(".mc-panel-whmcs")),i&&!a(this.panel)&&MCF.ajax({function:"whmcs-get-conversation-details",whmcs_id:i.value},i=>{let s=["products","addons","domains"];t=`<h3>WHMCS</h3><div><div class="mc-split"><div><div class="mc-title">${o("Number of services")}</div><span>${i.services_count} ${o("services")}</span></div><div><div class="mc-title">${o("Total spend")}</div><span>${i.total} ${i.currency_symbol}</span></div></div></div>`;for(var a=0;a<s.length;a++){let e=i[s[a]];if(e.length){t+=`<div class="mc-title">${o(MCF.slugToString(s[a]))}</div><div class="mc-list-items">`;for(var n=0;n<e.length;n++)t+=`<div>${e[n].name}</div>`;t+="</div>"}}t+=`<a href="${MC_ADMIN_SETTINGS.whmcs_url}/clientssummary.php?userid=${i["client-id"]}" target="_blank" class="mc-btn mc-whmcs-link">${o("View on WHMCS")}</a>`,e(this.panel).html(t).mcLoading(!1),l(this.panel,160)}),e(this.panel).html(t)}},perfex:{conversationPanel:function(){let e=s().getExtra("perfex-id");A.find(".mc-panel-perfex").html(e?`<a href="${MC_ADMIN_SETTINGS.perfex_url}/admin/clients/client/${e.value}" target="_blank" class="mc-btn mc-perfex-link">${o("View on Perfex")}</a>`:"")}},ump:{conversationPanel:function(){if(a(this.panel))return;this.panel||(this.panel=A.find(".mc-panel-ump"));let t,i="";MCF.ajax({function:"ump-get-conversation-details"},s=>{if((t=s.subscriptions).length){i='<i class="mc-icon-refresh"></i><h3>Membership</h3><div class="mc-list-names">';for(var a=0;a<t.length;a++){let e=t[a].expired;i+=`<div${e?' class="mc-expired"':""}><span>${t[a].label}</span><span>${o(e?"Expired on":"Expires on")} ${MCF.beautifyTime(t[a].expire_time,!1,!e)}</span></div>`}i+=`</div><span class="mc-title">${o("Total spend")} ${s.total} ${s.currency_symbol}</span>`}e(this.panel).html(i).mcLoading(!1),l(this.panel,160)})}},armember:{conversationPanel:function(){let t=s().getExtra("wp-id");if(this.panel||(this.panel=A.find(".mc-panel-armember")),MCF.null(t)||a(this.panel))e(this.panel).html("");else{let i,a="";t=t.value,MCF.ajax({function:"armember-get-conversation-details",wp_user_id:t},t=>{if((i=t.subscriptions).length){a=`<i class="mc-icon-refresh"></i><h3>${o("Plans")}</h3><div class="mc-list-names">`;for(var n=0;n<i.length;n++){let e=i[n].expired;a+=`<div${e?' class="mc-expired"':""}><span>${i[n].arm_current_plan_detail.arm_subscription_plan_name}</span><span>${"never"==i[n].expire_time?"":o(e?"Expired on":"Expires on")+" "+MCF.beautifyTime(i[n].expire_time,!1,!e)}</span></div>`}a+=`</div><span class="mc-title">${o("Total spend")} ${t.total} ${t.currency_symbol}<a href="${window.location.href.substr(0,window.location.href.lastIndexOf("/"))+"?page=arm_manage_members&member_id="+s().getExtra("wp-id").value}" target="_blank" class="mc-btn-text"><i class="mc-icon-user"></i> ${o("View member")}</a></span>`}e(this.panel).html(a).mcLoading(!1),l(this.panel,160)})}}},zendesk:{conversationPanel:function(){if(!MC_ADMIN_SETTINGS.zendesk_active)return;let t=s().getExtra("zendesk-id"),i=s().getExtra("phone"),n=s().get("email"),o=A.find(".mc-panel-zendesk");(t||i||n)&&!a(o)?MCF.ajax({function:"zendesk-get-conversation-details",conversation_id:MCChat.conversation.id,zendesk_id:!!t&&t.value,phone:!!i&&i.value,email:n},t=>{e(o).html(t).mcLoading(!1),o.find(".mc-zendesk-date").each(function(){e(this).html(MCF.beautifyTime(e(this).html()))}),l(o,160)}):e(o).html("")}},woocommerce:{timeout:!1,conversationPanel:function(){if(a(this.panel))return;this.panel||(this.panel=A.find(".mc-panel-woocommerce"));let t="";MCF.ajax({function:"woocommerce-get-conversation-details"},i=>{t=`<i class="mc-icon-refresh"></i><h3>WooCommerce</h3><div><div class="mc-split"><div><div class="mc-title">${o("Number of orders")}</div><span>${i.orders_count} ${o("orders")}</span></div><div><div class="mc-title">${o("Total spend")}</div><span>${i.total} ${i.currency_symbol}</span></div></div><div class="mc-title">${o("Cart")}<i class="mc-add-cart-btn mc-icon-plus"></i></div><div class="mc-list-items mc-list-links mc-woocommerce-cart">`;for(s=0;s<i.cart.length;s++){let e=i.cart[s];t+=`<a href="${e.url}" target="_blank" data-id="${e.id}"><span>#${e.id}</span> <span>${e.name}</span> <span>x ${e.quantity}</span><i class="mc-icon-close"></i></a>`}if(t+=(i.cart.length?"":"<p>"+o("The cart is currently empty.")+"</p>")+"</div>",i.orders.length){t+=`<div class="mc-title">${o("Orders")}</div><div class="mc-list-items mc-woocommerce-orders mc-accordion">`;for(var s=0;s<i.orders.length;s++){let e=i.orders[s],a=e.id;t+=`<div data-id="${a}"><span><span>#${a}</span> <span>${MCF.beautifyTime(e.date,!0)}</span><a href="${ye}/wp-admin/post.php?post=${a}&action=edit" target="_blank" class="mc-icon-next"></a></span><div></div></div>`}t+="</div>"}e(this.panel).html(t).mcLoading(!1),l(this.panel,160)})},conversationPanelOrder:function(e){let t=this.panel.find(`[data-id="${e}"] > div`);t.html(""),MCF.ajax({function:"woocommerce-get-order",order_id:e},e=>{let i="",s=this.panel.find(".mc-collapse-btn:not(.mc-active)");if(e){let t=e.products;i+=`<div class="mc-title">${o("Order total")}: <span>${e.total} ${e.currency_symbol}<span></div><div class="mc-title">${o("Order status")}: <span>${MCF.slugToString(e.status.replace("wc-",""))}<span></div><div class="mc-title">${o("Date")}: <span>${MCF.beautifyTime(e.date,!0)}<span></div><div class="mc-title">${o("Products")}</div>`;for(a=0;a<t.length;a++)i+=`<a href="${ye}?p=${t[a].id}" target="_blank"><span>#${t[a].id}</span> <span>${t[a].quantity} x</span> <span>${t[a].name}</span></a>`;for(var a=0;a<2;a++){let t=0==a?"shipping":"billing";e[t+"_address"]&&(i+=`<div class="mc-title">${o((0==a?"Shipping":"Billing")+" address")}</div><div class="mc-multiline">${e[t+"_address"].replace(/\\n/g,"<br>")}</div>`)}}s.length&&s.click(),t.html(i)})},conversationPanelUpdate:function(e,t="added"){let i=!1,s=0;this.timeout=setInterval(()=>{i||(MCF.ajax({function:"woocommerce-get-conversation-details"},a=>{let n=!0;for(var o=0;o<a.cart.length;o++)a.cart[o].id==e&&("added"==t?s=61:n=!1);(s>60||n)&&(this.conversationPanel(),A.find(".mc-add-cart-btn,.mc-woocommerce-cart > a i").mcLoading(!1),clearInterval(this.timeout)),s++,i=!1}),i=!0)},1e3)}},opencart:{conversationPanel:function(){let e=A.find(".mc-panel-opencart"),t=s().getExtra("opencart_id"),i=s().getExtra("opencart_store_url");if(!t)return e.html("");a(e)||MCF.ajax({function:"opencart-panel",opencart_id:t.value,store_url:!!i&&i.value},t=>{e.html(t).mcLoading(!1),l(this.panel,160)})},openOrder:function(e){MCF.ajax({function:"opencart-order-details",order_id:e},t=>{dt.infoPanel(t,"info",!1,"opencart-order-details",o("Order")+" #"+e,!0)})}},wordpress:{ajax:function(t,i,s){e.ajax({method:"POST",url:MC_WP_AJAX_URL,data:e.extend({action:"mc_wp_ajax",type:t},i)}).done(e=>{!1!==s&&s(e)})}},is:function(e){if(typeof MC_VERSIONS==Ke)return!1;switch(e){case"opencart":case"zendesk":case"twitter":case"wechat":case"line":case"viber":case"zalo":case"telegram":case"armember":case"aecommerce":case"martfury":case"whmcs":case"perfex":case"ump":case"messenger":case"whatsapp":case"woocommerce":case"dialogflow":case"slack":case"tickets":return typeof MC_VERSIONS[e]!=Ke&&MC_VERSIONS[e];case"wordpress":return typeof MC_WP!=Ke;case"mc":return!0}return!1},unsupportedRichMessages:function(e,i,s=[]){s.push("timetable","registration","table","inputs"),["Messenger","WhatsApp"].includes(i)||s.push("email");for(var a=0;a<s.length;a++)e.includes("["+s[a])&&t("The {R} rich message is not supported by {R2}. The rich message was not sent to {R2}.".replace(/{R}/g,MCF.slugToString(s[a])).replace(/{R2}/g,i),"error")},getName:function(e){let t={fb:"Facebook",wa:"WhatsApp",tm:"Text message",ig:"Instagram",tg:"Telegram",tk:"Tickets",wc:"WeChat",em:"Email",tw:"Twitter",bm:"Business Messages",vb:"Viber",ln:"LINE",za:"Zalo"};return e in t?t[e]:e},itemsPanel:{pagination_reference:1,panel_language:"",code:function(e,t,i=!0){let s="";for(var a=0;a<e.length;a++)s+=`<li data-id="${e[a].id.split("/").pop()}"><div class="mc-image" style="background-image:url('${e[a].image?"shopify"==t?e[a].image.replace(".jpg","_small.jpg"):e[a].image:MC_URL+"/media/thumb.svg"}')"></div><div><span>${e[a].name?e[a].name:e[a].title}</span><span>${e[a].price?e[a].price:e[a].variants.edges[0].node.price} ${MC_ADMIN_SETTINGS.currency}</span></div></li>`;return i?s||`<p class="mc-no-results">${o("No products found")}</p>`:s},getAppInfo:function(e){switch(e){case"woocommerce":return{area:Ee,ul:Le,search:"woocommerce-search-products",filter:"woocommerce-get-products",populate:"woocommerce-products-popup",pagination:"woocommerce-get-products"};case"shopify":return{area:MCCloud.shopify_products_box,ul:MCCloud.shopify_products_box_ul,search:"shopify-get-products",filter:"shopify-get-products",populate:"shopify-get-products",pagination:"shopify-get-products"}}},search:function(t,i){let s=this.getAppInfo(i);c(t,(t,a)=>{t?(this.pagination_reference=1,MCF.ajax({function:s.search,search:t},t=>{"shopify"==i&&(this.pagination_reference=t[1],t=t[0]),this.getAppInfo(i).ul.html(this.code(t,i)),e(a).mcLoading(!1)})):this.populate(i,function(){e(a).mcLoading(!1)})})},filter:function(t,i){let s=this.getAppInfo(i),n=e(t).data("value");a(s.ul)||(s.ul.html(""),this.pagination_reference=1,MCF.ajax({function:s.filter,user_language:this.panel_language,filters:{taxonomy:n},collection:n},e=>{"shopify"==i&&(this.pagination_reference=e[1],e=e[0]),s.ul.html(this.code(e,i)).mcLoading(!1)}))},populate:function(e,t=!1){let i=this.getAppInfo(e);this.panel_language=s()&&MC_ADMIN_SETTINGS.languages&&MC_ADMIN_SETTINGS.languages.includes(s().language)?s().language:"",this.pagination_reference=1,i.ul.html("").mcLoading(!0),MCF.ajax({function:i.populate,user_language:this.panel_language},s=>{let a="",n=i.area.find(".mc-select");for(var r=0;r<s[1].length;r++)a+=`<li data-value="${s[1][r].id}">${s[1][r].name}</li>`;s[2]&&(this.pagination_reference=s[2]),s[3]&&(MC_ADMIN_SETTINGS.currency=s[3]),n.find("> p").html(o("All")),n.find("ul").html(`<li data-value="" class="mc-active">${o("All")}</li>`+a),i.ul.html(this.code(s[0],e)).mcLoading(!1),!1!==t&&t()})},pagination:function(t,i){let s=this.getAppInfo(i),a=e(t).parent().find(".mc-select p").attr("data-value");this.pagination_reference&&(s.ul.mcLoading(t),MCF.ajax({function:s.pagination,filters:{taxonomy:a},collection:a,pagination:this.pagination_reference,user_language:this.panel_language},e=>{"shopify"==i?(this.pagination_reference=e[1],e=e[0]):this.pagination_reference++,s.ul.append(this.code(e,i,!1)).mcLoading(!1),e.length||(this.pagination_reference=0)}))}}},at={init:!1,save:function(i=!1){if(i&&a(i))return;let s={},n={};switch(H.find(" > .mc-tab > .mc-nav .mc-active").attr("id")){case"tab-automations":let a=J.find(".mc-active").attr("data-id");at.automations.save(s=>{t(!0===s?"Automations saved":s),at.automations.populate(),J.find(`[data-id="${a}"]`).click(),i&&e(i).mcLoading(!1)});break;case"tab-translations":this.translations.updateActive(),MCF.ajax({function:"save-translations",translations:JSON.stringify(this.translations.to_update)},()=>{t("Translations saved"),i&&e(i).mcLoading(!1)});break;default:H.find(".mc-setting").each((t,i)=>{let a=this.get(i),o=e(i).data("setting");if(a[0])if(typeof o!=Ke){let t=!1;if(e(i).find("[data-language]").length){let s=e(i).find("[data-language].mc-active");if(t=a[0]in this.translations.originals&&this.translations.originals[a[0]],this.translations.save(i,!!s.length&&s.attr("data-language")),t&&"string"!=typeof t)for(var r in t)t[r]=[t[r],a[1][r][1]]}o in s||(s[o]={}),s[o][a[0]]=[t||a[1],a[2]]}else n[a[0]]=[a[1],a[2]]}),MCF.ajax({function:"save-settings",settings:JSON.stringify(n),external_settings:s,external_settings_translations:this.translations.translations},()=>{i&&(t("Settings saved. Reload to apply the changes."),e(i).mcLoading(!1)),MCF.event("MCSettingsSaved",{settings:n,external_settings:s,external_settings_translations:this.translations.translations})})}},get:function(t){let i=(t=e(t)).attr("id"),s=t.data("type");switch(s){case"upload":case"range":case"number":case"text":case"password":case"color":case"upload-file":return[i,t.find("input").val(),s];case"textarea":return[i,t.find("textarea").val(),s];case"select":return[i,t.find("select").val(),s];case"checkbox":return[i,t.find("input").is(":checked"),s];case"radio":let a=t.find("input:checked").val();return MCF.null(a)&&(a=""),[i,a,s];case"upload-image":let n=t.find(".image").attr("data-value");return MCF.null(n)&&(n=""),[i,n,s];case"multi-input":let o={};return t.find(".input > div").each((e,t)=>{let i=this.get(t);i[0]&&(o[i[0]]=[i[1],i[2]])}),[i,o,s];case"select-images":return[i,t.find(".input > .mc-active").data("value"),s];case"repeater":return[i,this.repeater.get(t.find(".repeater-item")),s];case"double-select":let r={};return t.find(".input > div").each(function(){let t=e(this).find("select").val();-1!=t&&(r[e(this).attr("data-id")]=[t])}),[i,r,s];case"select-checkbox":return[i,t.find(".mc-select-checkbox input:checked").map(function(){return e(this).attr("id")}).get(),s];case"timetable":let l={};return t.find(".mc-timetable > [data-day]").each(function(){let t=e(this).attr("data-day"),i=[];e(this).find("> div > div").each(function(){let t=e(this).html(),s=e(this).attr("data-value");MCF.null(s)?i.push(["",""]):"closed"==s?i.push(["closed","Closed"]):i.push([s,t])}),l[t]=i}),[i,l,s];case"color-palette":return[i,t.attr("data-value"),s]}return["","",""]},set:function(t,i){let s=e(i)[1],a=e(i)[0];switch(t=`#${t}`,s){case"color":case"upload":case"number":case"text":case"password":case"upload-file":H.find(`${t} input`).val(a);break;case"textarea":H.find(`${t} textarea`).val(a);break;case"select":H.find(`${t} select`).val(a);break;case"checkbox":H.find(`${t} input`).prop("checked","false"!=a&&a);break;case"radio":H.find(`${t} input[value="${a}"]`).prop("checked",!0);break;case"upload-image":a&&H.find(t+" .image").attr("data-value",a).css("background-image",`url("${a}")`);break;case"multi-input":for(var n in a)this.set(n,a[n]);break;case"range":let i=a;H.find(t+" input").val(i),H.find(t+" .range-value").html(i);break;case"select-images":H.find(t+" .input > div").mcActive(!1),H.find(t+` .input > [data-value="${a}"]`).mcActive(!0);break;case"select-checkbox":for(o=0;o<a.length;o++)H.find(`input[id="${a[o]}"]`).prop("checked",!0);H.find(t+" .mc-select-checkbox-input").val(a.join(", "));break;case"repeater":let r=this.repeater.set(a,H.find(t+" .repeater-item:last-child"));r&&H.find(t+" .mc-repeater").html(r);break;case"double-select":for(var n in a)H.find(`${t} .input > [data-id="${n}"] select`).val(a[n]);break;case"timetable":for(var n in a){let i=H.find(`${t} [data-day="${n}"] > div > div`);for(var o=0;o<i.length;o++)e(i[o]).attr("data-value",a[n][o][0]).html(a[n][o][1])}break;case"color-palette":a&&H.find(t).attr("data-value",a)}},repeater:{set:function(t,i){var s="";if(this.clear(e(i)),i=e(i).html(),t.length){e(i).find("> .mc-icon-close").remove();for(var a=0;a<t.length;a++){let o=e(e.parseHTML(`<div>${i}</div>`));for(var n in t[a])at.input.set(o.find(`[data-id="${n}"]`),t[a][n]);s+=`<div class="repeater-item">${o.html().replaceAll('<i class="mc-icon-close"></i>',"")}<i class="mc-icon-close"></i></div>`}}return s},get:function(t){let i=[];return e(t).each(function(){let t={},s=!0;e(this).find("[data-id]").removeClass("mc-exclude"),e(this).find(".mc-repeater [data-id]").addClass("mc-exclude"),e(this).find("[data-id]:not(.mc-exclude)").each(function(){let i=at.input.get(this);s&&i&&"hidden"!=e(this).attr("type")&&"auto-id"!=e(this).attr("data-type")&&(s=!1),t[e(this).attr("data-id")]=i}),s||i.push(t)}),i},add:function(t){let i=e(t).parent();t=e(e.parseHTML(`<div>${i.find("> .mc-repeater > .repeater-item:last-child").html()}</div>`)),this.clear(t),t.find(".repeater-item:not(:first-child)").remove(),t.find("[data-id]").each(function(){if(at.input.reset(this),"auto-id"==e(this).data("type")){let t=1;i.find('[data-type="auto-id"]').each(function(){let i=parseInt(e(this).val());i>t&&(t=i)}),e(this).attr("value",t+1)}}),i.find("> .mc-repeater").append(`<div class="repeater-item">${t.html()}</div>`)},delete:function(t){let i=e(t).parent(),s=i.parent();s.parent().find(".mc-repeater-upload").length&&MCF.ajax({function:"delete-file",path:i.find("input").val()}),s.find("> .repeater-item").length>1?i.remove():i.find('[data-id]:not([data-type="auto-id"]').each((e,t)=>{at.input.reset(t)})},clear:function(e){e.find(".mc-active").mcActive(!1),e.find('input:not([data-type="auto-id"]').removeAttr("value checked"),e.find("option").removeAttr("selected"),e.find(".mc-hide").removeClass("mc-hide")}},input:{set:function(t,i){if(t=e(t),"object"!=typeof i&&(i=e.trim(i)),t.is("select"))t.find(`option[value="${i}"]`).attr("selected","");else if(t.is(":checkbox")&&i&&"false"!=i)t.attr("checked","");else if(t.is("textarea"))t.html(i);else{let e=t.is("div");t.hasClass("mc-repeater")?t.html(at.repeater.set(i,"<div>"+t.find("> .repeater-item").eq(0).html()+"</div>").replaceAll("mc-icon-close","mc-icon-close mc-sub-repeater-close")):e||t.is("i")||t.is("li")?(t.attr("data-value",i),e&&t.hasClass("image")&&t.css("background-image",i?`url("${i}")`:"")):t.attr("value",i)}},get:function(t){if((t=e(t)).is(":checkbox"))return t.is(":checked");if(t.hasClass("mc-repeater"))return at.repeater.get(t.find("> .repeater-item"));if(t.is("div")||t.is("i")||t.is("li")){let e=t.attr("data-value");return e||""}return t.val()},reset:function(t){(t=e(t)).is("select")?t.val("").find("[selected]").removeAttr("selected"):t.is(":checkbox")?t.removeAttr("checked").prop("checked",!1):t.is("textarea")?(t.val(""),t.html("")):t.hasClass("mc-repeater")?t.find(".repeater-item:not(:first-child)").remove():t.removeAttr("value style data-value").val("")}},initColorPicker:function(t=!1){e(t||H).find(".mc-type-color input").colorPicker({renderCallback:function(t,i){e(t.context).closest(".input").find("input").css("background-color",t.text)}})},getSettingObject:function(t){return e(t)[0].hasAttribute("data-setting")?e(t):e(t).closest("[data-setting]")},visibility:function(e,t){let i=[["#push-notifications-onesignal-sw-url, #push-notifications-onesignal-app-id, #push-notifications-onesignal-api-key, #push-notifications-sw-path","#push-notifications-id, #push-notifications-key"],["#messenger-key, #messenger-path-btn","#messenger-sync-btn"],["#open-ai-assistant-id","#open-ai-prompt,#open-ai-model, #open-ai-tokens, #open-ai-temperature, #open-ai-presence-penalty, #open-ai-frequency-penalty, #open-ai-logit-bias, #open-ai-custom-model, #open-ai-source-links"]];H.find(i[e][0]).mcActive(!t),H.find(i[e][1]).setClass("mc-hide",!t)},open:function(e,t=!1){x.find(".mc-admin-nav #mc-settings").click(),t&&setTimeout(()=>{H.find("#tab-"+e).click().get(0).scrollIntoView()},300)},automations:{items:{messages:[],emails:[],sms:[],popups:[],design:[],more:[]},translations:{},conditions:function(){let e={datetime:["Date time",["Is between","Is exactly"],"dd/mm/yyy hh:mm - dd/mm/yyy hh:mm"],repeat:["Repeat",["Every day","Every week","Every month","Every year"]],browsing_time:["Browsing time",[],"seconds"],scroll_position:["Scroll position",[],"px"],url:["Current URL",["Contains","Does not contain"],"URLs parts separated by commas"],referring:["Referring URL",["Contains","Does not contain"],"URLs parts separated by commas"],user_type:["User type",["Is visitor","Is lead","Is user","Is not visitor","Is not lead","Is not user"]],returning_visitor:["Returning visitor",["First time visitor","Returning visitor"]],countries:["Country",["Is included","Is not included","Is set","Is not set"],"Country codes separated by commas"],languages:["Language",["Is included","Is not included","Is set","Is not set"],"Language codes separated by commas"],cities:["City",["Is included","Is not included","Is set","Is not set"],"Cities separated by commas"],website:["Website",["Contains","Does not contain","Is set","Is not set"],"URLs parts separated by commas"],birthdate:["Birthdate",["Is between","Is exactly","Is set","Is not set"],"dd/mm - dd/mm"],company:["Company",["Is included","Is not included","Is set","Is not set"],"Company names separated by commas"],postal_code:["Postal code",["Is included","Is not included","Is set","Is not set"],"Postal codes separated by commas"],email:["Email",["Contains","Does not contain","Is set","Is not set"],"Email addresses separated by commas"],phone:["Phone",["Contains","Does not contain","Is set","Is not set"],"Phone numbers separated by commas"],creation_time:["Creation time",["Is between","Is exactly"],"dd/mm/yyy hh:mm - dd/mm/yyy hh:mm"],custom_variable:["Custom variable",[],"variable=value"]},t=rt.getExtraDetailsList(!0);for(var i=0;i<t.length;i++)e[t[i][0]]=[t[i][1],["Contains","Does not contain","Is set","Is not set"],"Values separated by commas"];return e},get:function(e){MCF.ajax({function:"automations-get"},t=>{this.items=t[0],this.translations=Array.isArray(t[1])&&!t[1].length?{}:t[1],e(t)})},save:function(e=!1){this.updateActiveItem(),MCF.ajax({function:"automations-save",automations:this.items,translations:this.translations},t=>{e&&e(t)})},show:function(e=!1,t=!1){this.updateActiveItem();let i=t?t in this.translations?this.translations[t]:[]:this.items,s=W.find(" > .mc-tab > .mc-content");!1===e&&(e=this.activeID()),this.hide(!1);for(var a in i)for(var n=0;n<i[a].length;n++){let o=i[a][n];if(o.id==e){for(var a in o){let e=s.find(`[data-id="${a}"]`);e.hasClass("image")?(e.css("background-image",`url(${o[a]})`).attr("data-value",o[a]),o[a]||e.removeAttr("data-value")):"checkbox"==e.attr("type")?e.prop("checked",o[a]):e.val(o[a])}return this.setConditions(o.conditions,Y),Y.parent().setClass("mc-hide",t),s.mcLanguageSwitcher(this.getTranslations(e),"automations",t),!0}}return!1},add:function(){let e=MCF.random(),t=`${o("Item")} ${J.find("li:not(.mc-no-results)").length+1}`;this.updateActiveItem(),this.items[this.activeType()].push(this.itemArray(this.activeType(),e,t)),this.hide(!1),J.find(".mc-active").mcActive(!1),J.find(".mc-no-results").remove(),J.append(`<li class="mc-active" data-id="${e}">${t}<i class="mc-icon-delete"></i></li>`),W.find(".mc-automation-values").find("input, textarea").val(""),W.mcLanguageSwitcher([],"automations"),Y.html("")},delete:function(t){this.items[this.activeType()].splice(e(t).parent().index(),1),e(t).parent().remove(),this.hide(),0==this.items[this.activeType()].length&&J.html(`<li class="mc-no-results">${o("No results found.")}</li>`)},populate:function(e=!1){!1===e&&(e=this.activeType());let t="",i=this.items[e];if(this.updateActiveItem(),i.length)for(s=0;s<i.length;s++)t+=`<li data-id="${i[s].id}">${i[s].name}<i class="mc-icon-delete"></i></li>`;else t=`<li class="mc-no-results">${o("No results found.")}</li>`;switch(J.html(t),t="",e){case"emails":t=`<h2>${o("Subject")}</h2><div class="mc-setting mc-type-text"><div><input data-id="subject" type="text"></div></div>`;break;case"popups":t=`<h2>${o("Title")}</h2><div class="mc-setting mc-type-text"><div><input data-id="title" type="text"></div></div><h2>${o("Profile image")}</h2><div data-type="upload-image" class="mc-setting mc-type-upload-image"><div class="input"><div data-id="profile_image" class="image"><i class="mc-icon-close"></i></div></div></div><h2>${o("Message fallback")}</h2><div class="mc-setting mc-type-checkbox"><div><input data-id="fallback" type="checkbox"></div></div>`;break;case"design":t=`<h2>${o("Header title")}</h2><div class="mc-setting mc-type-text"><div><input data-id="title" type="text"></div></div>`;for(s=1;s<4;s++)t+=`<h2>${o((1==s?"Primary":2==s?"Secondary":"Tertiary")+" color")}</h2><div data-type="color" class="mc-setting mc-type-color"><div class="input"><input data-id="color_${s}" type="text"><i class="mc-close mc-icon-close"></i></div></div>`;for(var s=1;s<4;s++)t+=`<h2>${o(1==s?"Header background image":2==s?"Header brand image":"Chat button icon")}</h2><div data-type="upload-image" class="mc-setting mc-type-upload-image"><div class="input"><div data-id="${1==s?"background":2==s?"brand":"icon"}" class="image"><i class="mc-icon-close"></i></div></div></div>`;break;case"more":t=`<h2>${o("Department ID")}</h2><div class="mc-setting mc-type-number"><div><input data-id="department" type="number"></div></div><h2>${o("Agent ID")}</h2><div class="mc-setting mc-type-number"><div><input data-id="agent" type="number"></div></div><h2>${o("Tags")}</h2><div class="mc-setting mc-type-text"><div><input data-id="tags" type="text"></div></div><h2>${o("Article IDs")}</h2><div class="mc-setting mc-type-number"><div><input data-id="articles" type="text"></div></div><h2>${o("Articles category")}</h2><div class="mc-setting mc-type-number"><div><input data-id="articles_category" type="text"></div></div>`}W.find(".mc-automation-extra").html(t),W.attr("data-automation-type",e),at.initColorPicker(W),this.hide()},updateActiveItem:function(){let t=this.activeID();if(t){let s=W.find(".mc-language-switcher [data-language].mc-active").attr("data-language"),a=this.activeType(),n=s?s in this.translations?this.translations[s][a]:[]:this.items[a];for(var i=0;i<n.length;i++)if(n[i].id==t){n[i]={id:t,conditions:[]},W.find(".mc-automation-values").find('input,textarea,[data-type="upload-image"] .image').each(function(){n[i][e(this).attr("data-id")]=e(this).hasClass("image")&&e(this)[0].hasAttribute("data-value")?e(this).attr("data-value"):"checkbox"==e(this).attr("type")?e(this).is(":checked"):e(this).val()}),n[i].conditions=this.getConditions(Y),MCF.null(n[i].name)&&this.delete(J.find(`[data-id="${t}"] i`));break}}},getConditions:function(t){let i=[];return t.find(" > div").each(function(){let t=[];e(this).find("input,select").each(function(){t.push(e(this).val())}),t[0]&&t[1]&&(2==t.length||t[2]||["is-set","is-not-set"].includes(t[1]))&&i.push(t)}),i},setConditions:function(e,t){if(t.html(""),e)for(var i in e){this.addCondition(t);let s=t.find(" > div:last-child");s.find("select").val(e[i][0]),this.updateCondition(s.find("select")),s.find(" > div").eq(1).find("select,input").val(e[i][1]),e[i].length>2&&(["is-set","is-not-set"].includes(e[i][1])?s.find(" > div").eq(2).addClass("mc-hide"):s.find(" > div").eq(2).find("input").val(e[i][2]))}},addCondition:function(e){e.append(`<div><div class="mc-setting mc-type-select mc-condition-1"><select>${this.getAvailableConditions()}</select></div></div>`)},updateCondition:function(t){e(t).parent().siblings().remove();let i=e(t).parents().eq(1);if(e(t).val()){let a=this.conditions()[e(t).val()],n="";if(a[1].length){n='<div class="mc-setting mc-type-select mc-condition-2"><select>';for(var s=0;s<a[1].length;s++)n+=`<option value="${MCF.stringToSlug(a[1][s])}">${o(a[1][s])}</option>`;n+="</select></div>"}i.append(n+(a.length>2?`<div class="mc-setting mc-type-text"><input placeholder="${o(a[2])}" type="text"></div>`:"")),i.siblings().find(".mc-condition-1 select").each(function(){let t=e(this).val();e(this).html(at.automations.getAvailableConditions([t])),e(this).val(t)})}else i.remove()},getAvailableConditions:function(t=[],i=[]){let s='<option value=""></option>',a=[],n=this.conditions();Y.find(".mc-condition-1 select").each(function(){a.push(e(this).val())});for(var r in n)i.includes(r)||a.includes(r)&&!t.includes(r)||(s+=`<option value="${r}">${o(n[r][0])}</option>`);return s},addTranslation:function(e=!1,t=!1,i){if(!1===e&&(e=this.activeID()),!1===t&&(t=this.activeType()),this.getTranslations(e).includes(e))return console.warn("Automation translation already in array.");i in this.translations||(this.translations[i]={messages:[],emails:[],sms:[],popups:[],design:[]}),t in this.translations[i]||(this.translations[i][t]=[]),this.translations[i][t].push(this.itemArray(t,e))},getTranslations:function(e=!1){let t=[];!1===e&&(e=this.activeID());for(var i in this.translations){let n=this.translations[i];for(var s in n){let o=n[s];for(var a=0;a<o.length;a++)if(o[a].id==e){t.push(i);break}}}return t},deleteTranslation:function(e=!1,t){if(!1===e&&(e=this.activeID()),t in this.translations){let a=this.translations[t];for(var i in a){let n=a[i];for(var s=0;s<n.length;s++)if(n[s].id==e)return this.translations[t][i].splice(s,1),!0}}return!1},activeID:function(){let e=J.find(".mc-active");return!!e.length&&e.attr("data-id")},activeType:function(){return z.find("li.mc-active").data("value")},itemArray:function(t,i,s="",a=""){return e.extend({id:i,name:s,message:a},"emails"==t?{subject:""}:"popups"==t?{title:"",profile_image:""}:"design"==t?{title:"",color_1:"",color_2:"",color_3:"",background:"",brand:"",icon:""}:{})},hide:function(e=!0){W.find(" > .mc-tab > .mc-content").setClass("mc-hide",e)}},translations:{translations:{},originals:{},to_update:{},add:function(e){let t=at.getSettingObject(fe),i=t.attr("id"),s=fe.find("[data-language].mc-active");this.save(t,!!s.length&&s.attr("data-language")),t.find('textarea,input[type="text"]').val(""),this.save(t,e),fe.remove(),t.mcLanguageSwitcher(this.getLanguageCodes(i),"settings",e)},delete:function(e,t){let i=(e=at.getSettingObject(e)).attr("id");delete this.translations[t][i],e.find(`.mc-language-switcher [data-language="${t}"]`).remove(),this.activate(e)},activate:function(e,t=!1){let i=(e=at.getSettingObject(e)).attr("id"),s=t?this.translations[t][i]:this.originals[i];if(h(s))e.find("input, textarea").val(s);else for(var a in s)e.find("#"+a).find("input, textarea").val(h(s[a])?s[a]:s[a][0])},updateActive:function(){let t=H.find(".mc-translations-list"),i={front:{},admin:{},"admin/js":{},"admin/settings":{}},s=t.attr("data-value");if(!MCF.null(s)){for(var a in i)t.find(' > [data-area="'+a+'"] .mc-setting:not(.mc-new-translation)').each(function(){i[a][e(this).find("label").html()]=e(this).find("input").val()}),t.find('> [data-area="'+a+'"] .mc-new-translation').each(function(){let t=e(this).find("input:first-child").val(),s=e(this).find("input:last-child").val();t&&s&&(i[a][t]=s)});this.to_update[s]=i}},save:function(t,i=!1){t=at.getSettingObject(t);let s={},a=e(t).attr("id");"multi-input"==t.data("type")?t.find(".multi-input-textarea,.multi-input-text").each(function(){s[e(this).attr("id")]=e(this).find("input, textarea").val()}):s=t.find("input, textarea").val(),i?(i in this.translations||(this.translations[i]={}),this.translations[i][a]=s):this.originals[a]=s},load:function(e){let t=H.find(".mc-translations > .mc-content");t.find(" > .mc-hide").removeClass("mc-hide"),this.updateActive(),MCF.ajax({function:"get-translation",language_code:e},i=>{e in this.to_update&&(i=this.to_update[e]);let s="",a=["front","admin","admin/js","admin/settings"];for(var n=0;n<a.length;n++){let e=i[a[n]];s+=`<div${n?"":' class="mc-active"'} data-area="${a[n]}">`;for(var o in e)s+=`<div class="mc-setting mc-type-text"><label>${o}</label><div><input type="text" value="${e[o]}"></div></div>`;s+="</div>"}t.find(".mc-translations-list").attr("data-value",e).html(s),t.find(".mc-menu-wide li").mcActive(!1).eq(0).mcActive(!0),t.mcLoading(!1)}),t.mcLoading(!0)},getLanguageCodes:function(e){let t=[];for(var i in this.translations)e in this.translations[i]&&t.push(i);return t}}},nt={category_list:[],page_url:!1,get:function(e,t=!1,i=!1,s=!0,a=!1){MCF.ajax({function:"get-articles",id:t,categories:i,articles_language:a,full:s},t=>{e(t)})},save:function(e=!1){let t,i=this.activeID();if(!(t={id:i,title:X.find(".mc-article-title input").val(),content:X.find(".mc-article-content textarea").val(),link:X.find(".mc-article-link input").val(),parent_category:ee.val(),category:K.val(),language:X.find(".mc-language-switcher [data-language].mc-active").attr("data-language")}).title&&!t.content)return e(!1);t.language&&(t.parent_id=this.activeID(!0)),tt&&typeof tt.save!==Ke?tt.save().then(i=>{t.editor_js=i,t.content=function(e){let t="";return e.map(e=>{switch(e.type){case"header":t+=`<h${e.data.level}>${e.data.text}</h${e.data.level}>`;break;case"paragraph":t+=`<p>${e.data.text}</p>`;break;case"image":t+=`<img class="img-fluid" src="${e.data.file.url}" title="${e.data.caption}" /><em>${e.data.caption}</em>`;break;case"list":t+='<ul class="mc-ul-'+e.data.style+'">',e.data.items.forEach(function(e){t+=`<li>${e}</li>`}),t+="</ul>";break;case"code":t+=`<code>${e.data.code}</code>`;break;case"raw":t+=`<div class="bxc-raw-html">${e.data.html}</div>`}}),t}(i.blocks),this.save_2(t,e)}).catch(e=>{console.log(e)}):this.save_2(t,e)},save_2:function(e,i=!1){MCF.ajax({function:"save-article",article:JSON.stringify(e)},s=>{let a=!0!==s&&!isNaN(s);if(Pe=!1,a&&(Q.attr("data-id",s),e.id=s,this.viewButton(s),e.language)){let t=nt.translations.get(e.parent_id);Q.find(`.mc-language-switcher [data-language="${e.language}"]`).attr("data-id",s);for(var n=0;n<t.length;n++)if(t[n][0]==e.language){t[n][1]=e.id,nt.translations.list[e.parent_id]=t;break}}e.language||X.find(".ul-articles .mc-active").html(e.title+'<i class="mc-icon-delete"></i>').attr("data-id",e.id),i&&i(s),t(!0===s||a?"Article saved":s)})},show:function(e){e&&(a(Q),this.get(t=>{Q.mcLoading(!1),t=t[0],Q.mcLanguageSwitcher(this.translations.get(t.parent_id?t.parent_id:e),"articles",t.language),Q.attr("data-id",e),X.find(".mc-article-title input").val(t.title),X.find(".mc-article-link input").val(t.link),X.find("#mc-article-id").html(`ID <span>${e}</span>`),X.find(".mc-article-categories").setClass("mc-hide",t.language),tt||X.find("#editorjs").length?u(t.editor_js?h(t.editor_js)?JSON.parse(t.editor_js):t.editor_js:t.content):X.find(".mc-article-content textarea").val(t.content),t.language||(ee.val(t.parent_category),K.val(t.category)),this.viewButton(e),Pe=!1},e))},add:function(){let e=X.find(".ul-articles");e.find(".mc-active").mcActive(!1),e.append('<li class="mc-active"></li>'),Q.mcLanguageSwitcher([],"articles"),this.clear()},clear:function(){Q.removeAttr("data-id").removeClass("mc-hide"),Q.find("input, textarea, select").val(""),Q.find("input").prop("checked",!1),u(),this.viewButton(),Pe=!1},delete:function(e,t=!1){MCF.ajax({function:"save-article",article:JSON.stringify({id:e,delete:!0})},e=>{this.clear(),t&&t(e)})},populate:function(t,i=!1){let s="";for(var a=0;a<t.length;a++)s+=`<li data-id="${t[a].id}">${t[a].title}<i class="mc-icon-delete"></i></li>`;X.find(i?".ul-categories":".ul-articles").html(s),X.find(i?".ul-categories > li":".ul-articles > li").eq(0).click(),t.length||e(i?Z:Q).mcLoading(!1).addClass("mc-hide")},activeID:function(e=!1){return e?X.find(".ul-articles .mc-active").attr("data-id"):Q.attr("data-id")},viewButton:function(e=!1){if(this.page_url){X.find(".mc-view-article").attr("href",e?this.page_url+(this.is_url_rewrite?("/"==this.page_url.charAt(this.page_url.length-1)?"":"/")+(this.cloud_chat_id?this.cloud_chat_id+"/":""):"?article_id=")+e:"")}},categories:{list:[],save:function(e=!1){this.updateActive(),MCF.ajax({function:"save-articles-categories",categories:JSON.stringify(this.list)},i=>{e&&e(i),t(!0===i?"Categories saved":i)})},show:function(t,i=!1){let s=this.getIndex(t);if(!1!==s){let t=i?this.list[s].languages[i]:this.list[s],a=Z.find("#category-image");this.updateActive(),Z.find("#category-title").val(t.title),Z.find("#category-description").val(t.description),Z.find("#category-parent").prop("checked",!!t.parent),Z.mcLanguageSwitcher(e.map(this.list[s].languages,function(e,t){return t}),"article-categories",i),t.image?a.attr("data-value",t.image).css("background-image",`url("${t.image}")`):a.removeAttr("data-value style"),Z.find(".category-parent").setClass("mc-hide",i)}Z.mcLoading(!1)},add:function(){let e=MCF.random();this.list.push({id:e,title:"",description:"",image:"",languages:[]});let t=`<li data-id="${e}">${o("New category")}<i class="mc-icon-delete"></i></li>`;X.find(".ul-categories").append(t),X.find(".ul-categories li").eq(X.find(".ul-categories li").length-1).click(),Z.removeClass("mc-hide")},delete:function(e){let t=this.getIndex(e),i=X.find(".ul-categories");return!1!==t&&(this.list.splice(t,1),i.find(`[data-id="${e}"]`).remove(),i.find("li").eq(0).click(),!0)},update:function(){let e=K.val(),t=ee.val(),i=["","<option></option>"],s=this.list.map(function(e){return e.id});for(var a=0;a<this.list.length;a++)i[this.list[a].parent?0:1]+=`<option value="${this.list[a].id}">${this.list[a].title}</option>`;ee.html(i[0]),K.html(i[1]),this.list.length&&(ee.val(s.includes(t)?t:ee[0].selectedIndex>-1?this.list[ee[0].selectedIndex].id:""),K.val(s.includes(e)?e:K[0].selectedIndex>-1?this.list[K[0].selectedIndex].id:""))},updateActive:function(){let e=this.activeID();if(e){let t=this.getIndex(e),i=Z.find(".mc-language-switcher .mc-active").attr("data-language"),s={title:Z.find("#category-title").val(),description:Z.find("#category-description").val(),image:Z.find("#category-image").attr("data-value")};i?this.list[t].languages[i]=s:(s.id=MCF.stringToSlug(s.title),s.parent=Z.find("#category-parent").is(":checked"),s.languages=this.list[t].languages,this.list[t]=s,X.find(".ul-categories .mc-active").html(s.title+'<i class="mc-icon-delete"></i>').attr("data-id",s.id))}},clear:function(){Z.find("input, textarea").val(""),Z.find("#category-image").removeAttr("data-value style")},getIndex:function(e){for(var t=0;t<this.list.length;t++)if(this.list[t].id==e)return t;return!1},activeID:function(){return X.find(".ul-categories .mc-active").attr("data-id")},translations:{add:function(t,i=!1){nt.categories.updateActive(),i||(i=nt.categories.activeID());let s=nt.categories.getIndex(i);MCF.null(nt.categories.list[s].languages)&&(nt.categories.list[s].languages={}),nt.categories.list[s].languages[t]={title:"",description:"",image:""},Z.mcLanguageSwitcher(e.map(nt.categories.list[s].languages,function(e,t){return t}),"article-categories",t),nt.categories.clear()},delete:function(e,t=!1){let i=nt.categories.activeID();t||(t=nt.categories.activeID(i)),delete nt.categories.list[nt.categories.getIndex(t)].languages[e],nt.categories.show(i)}}},translations:{list:{},add:function(e,t=!1){t||(t=nt.activeID(!0));let i=this.get(t);i.push([e,!1]),this.list[t]=i,Q.mcLanguageSwitcher(i,"articles",e),nt.clear()},delete:function(e,t=!1){t||(t=nt.activeID(!0));let i=this.get(t);for(var s=0;s<i.length;s++)if(i[s][0]==e){nt.delete(i[s][1],e=>{!0===e&&(i.splice(s,1),this.list[t]=i,nt.show(t))});break}return!1},get:function(e){return e in this.list?this.list[e]:[]}}},ot={chart:!1,active_report:!1,active_date_range:!1,initChart:function(e,t="line",i=1){let s=[],a=[],n=MC_ADMIN_SETTINGS.color?[MC_ADMIN_SETTINGS.color]:["#049CFF","#74C4F7","#B9E5FF","#0562A0","#003B62","#1F74C4","#436786"];for(var o in e)s.push(e[o][0]),a.push(o);if("line"!=t&&s.length>6)for(var r=0;r<s.length;r++)n.push("hsl(210, "+Math.floor(100*Math.random())+"%, "+Math.floor(100*Math.random())+"%)");this.chart&&this.chart.destroy(),this.chart=new Chart(Te.find("canvas"),{type:t,data:{labels:a,datasets:s&&Array.isArray(s[0])?[{data:s.map(e=>e[0]),backgroundColor:"#13ca7e"},{data:s.map(e=>e[1]),backgroundColor:"#ca3434"}]:[{data:s,backgroundColor:"line"==t?MC_ADMIN_SETTINGS.color?"#cbcbcb82":"#028be530":n,borderColor:"line"==t?MC_ADMIN_SETTINGS.color?MC_ADMIN_SETTINGS.color:"#049CFF":"#FFFFFF",borderWidth:0}]},options:{legend:{display:!1},scales:{yAxes:[{ticks:{callback:function(e,t,s){return 1==i?e:2==i?new Date(1e3*e).toISOString().substr(11,8):e},beginAtZero:!0}}],xAxes:[{ticks:{beginAtZero:!0}}]},tooltips:{callbacks:{label:function(e,t){let a=e.index,n=t.datasets[e.datasetIndex].data[a];switch(i){case 1:return n;case 2:return new Date(1e3*s[a]).toISOString().substr(11,8);case 3:return n+"%";case 4:let e=Te.find(".mc-table tbody tr").eq(a).find("td");return e.eq(0).text()+" "+e.eq(1).text()}}},displayColors:!1}}})},initTable:function(e,t,i=!1){let s="<thead><tr>",a=t[Object.keys(t)[0]].length-1,n=[];for(r=0;r<e.length;r++)s+=`<th>${e[r]}</th>`;s+="</tr></thead><tbody>";for(var o in t)0!=t[o][a]&&n.push([o,t[o][a]]);i&&n.reverse();for(var r=0;r<n.length;r++)s+=`<tr><td><div>${n[r][0]}</div></td><td>${n[r][1]}</td></tr>`;s+="</tbody>",Te.find("table").html(s)},initReport:function(e=!1,t=!1){let i=Te.find(".mc-tab > .mc-content");t=MCF.null(t)?[!1,!1]:t.split(" - "),i.mcLoading(!0),e&&(this.active_report=e),this.active_report&&(this.active_date_range=t,this.getData(this.active_report,t[0],t[1],e=>{0==e?i.addClass("mc-no-results-active"):(i.removeClass("mc-no-results-active"),this.initChart(e.data,e.chart_type,e.label_type),this.initTable(e.table,e.data,e.table_inverse),Te.find(".mc-reports-title").html(e.title),Te.find(".mc-reports-text").html(e.description),Te.find(".mc-collapse-btn").remove(),We||l(Te.find(".mc-collapse"),Te.find("canvas").outerHeight()-135)),i.mcLoading(!1)}))},getData:function(e,t=!1,i=!1,s){MCF.ajax({function:"reports",name:e,date_start:t,date_end:i,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone},e=>{s(e)})},initDatePicker:function(){let e={ranges:{},locale:{format:"DD/MM/YYYY",separator:" - ",applyLabel:o("Apply"),cancelLabel:o("Cancel"),fromLabel:o("From"),toLabel:o("To"),customRangeLabel:o("Custom"),weekLabel:o("W"),daysOfWeek:[o("Su"),o("Mo"),o("Tu"),o("We"),o("Th"),o("Fr"),o("Sa")],monthNames:[o("January"),o("February"),o("March"),o("April"),o("May"),o("June"),o("July"),o("August"),o("September"),o("October"),o("November"),o("December")],firstDay:1},showCustomRangeLabel:!0,alwaysShowCalendars:!0,autoApply:!0,opens:k.hasClass("mc-rtl")?"left":"right"};e.ranges[o("Today")]=[moment(),moment()],e.ranges[o("Yesterday")]=[moment().subtract(1,"days"),moment().subtract(1,"days")],e.ranges[o("Last 7 Days")]=[moment().subtract(6,"days"),moment()],e.ranges[o("Last 30 Days")]=[moment().subtract(29,"days"),moment()],e.ranges[o("This Month")]=[moment().startOf("month"),moment().endOf("month")],e.ranges[o("Last Month")]=[moment().subtract(1,"month").startOf("month"),moment().subtract(1,"month").endOf("month")],Te.find("#mc-date-picker").daterangepicker(e).val("")},export:function(e){MCF.ajax({function:"reports-export",name:this.active_report,date_start:this.active_date_range[0],date_end:this.active_date_range[1],timezone:Intl.DateTimeFormat().resolvedOptions().timeZone},t=>{e(t)})},open:function(e){x.find(".mc-admin-nav #mc-reports").click(),setTimeout(()=>{Te.find("#"+e).click().get(0).scrollIntoView()},300)}},rt={real_time:null,datetime_last_user:"2000-01-01 00:00:00",sorting:["creation_time","DESC"],user_types:["visitor","lead","user"],user_main_fields:["id","first_name","last_name","email","password","profile_image","user_type","creation_time","token","last_activity","department"],search_query:"",init:!1,busy:!1,table_extra:!1,history:[],get:function(e,t=!1,i=!1){let s=[];for(var n=0;n<q.length;n++)s.push(q.eq(n).find("li.mc-active").data("value"));i||a(U),MCF.ajax({function:t?"get-online-users":"get-users",sorting:t?this.sorting[0]:this.sorting,pagination:!!i&&je,user_types:this.user_types,search:this.search_query,extra:this.table_extra,department:s[0],source:s[1],tag:s[2]},t=>{e(t),U.mcLoading(!1)})},filter:function(e){e="all"==e?["visitor","lead","user"]:"agent"==e?["agent","admin"]:[e],this.user_types=e,je=1,Ge=1,this.get(e=>{this.populate(e)},"online"==e[0])},sort:function(e,t="DESC"){this.sorting=[e,t],je=1,Ge=1,this.get(e=>{this.populate(e)})},search:function(t){c(t,(t,i)=>{je=1,Ge=1,this.search_query=t,this.get(t=>{this.user_types=["visitor","lead","user"],this.populate(t),e(i).mcLoading(!1),P.find("li").mcActive(!1).eq(0).mcActive(!0)})})},populate:function(t){let i="",s=t.length;if(s)for(var a=0;a<s;a++)i+=this.getRow(new MCUser(t[a],t[a].extra));else i=`<p class="mc-no-results">${o("No users found.")}</p>`;U.parent().scrollTop(0),U.find("tbody").html(i),this.user_types.includes("agent")&&MCF.ajax({function:"get-online-users",agents:!0},t=>{let i=[];for(var s=0;s<t.length;s++)i.push(t[s].id);U.find("[data-user-id]").each(function(){e(this).find(".mc-td-profile").addClass("mc-"+(i.includes(e(this).attr("data-user-id"))?"online":"offline"))})})},update:function(){if(!this.busy){let e=["user","visitor","lead","agent"],t=e.includes(this.user_types[0])&&!this.search_query,i=P.find(".mc-active").data("type");"online"==i?this.filter(i):(this.busy=!0,MCF.ajax({function:"get-new-users",datetime:this.datetime_last_user},s=>{let a=s.length;if(this.busy=!1,a>0){let o="";for(n=0;n<a;n++){let e=new MCUser(s[n]);Re[e.id]=e,this.updateMenu("add",e.type),t&&(o+=this.getRow(e))}if(t&&(U.find("tbody").prepend(o),e.includes(i))){let t="";for(var n=0;n<e.length;n++)t+=e[n]==i?"":`[data-user-type="${e[n]}"],`;U.find(t.slice(0,-1)).remove()}this.datetime_last_user=s[0].creation_time}}))}},getRow:function(e){if(e instanceof MCUser){let i="";for(var t=0;t<this.table_extra.length;t++){let s=this.table_extra[t];i+=`<td class="mc-td-${s}">${this.user_main_fields.includes(s)?e.get(s):e.getExtra(s)}</td>`}return`<tr data-user-id="${e.id}" data-user-type="${e.type}"><td><input type="checkbox" /></td><td class="mc-td-profile"><a class="mc-profile"><img loading="lazy" src="${e.image}" /><span>${e.name}</span></a></td>${i}<td class="mc-td-email">${e.get("email")}</td><td class="mc-td-ut">${o(e.type)}</td><td>${MCF.beautifyTime(e.get("last_activity"),!0)}</td><td>${MCF.beautifyTime(e.get("creation_time"))}</td></tr>`}return MCF.error("User not of type MCUser","MCUsers.getRow"),!1},updateRow:function(e){let t=U.find(`[data-user-id="${e.id}"]`);if(t.length){let i=P.find(".mc-active").data("type");if(e.type==i||"admin"==e.type&&"agent"==i||"all"==i)t.replaceWith(this.getRow(e));else{let i=k.find(`[data-type="${"admin"==e.type?"agent":e.type}"] span`),s=parseInt(i.attr("data-count"));i.html(s+1).attr("data-count",s+1),t.remove()}}else U.find("tbody").append(this.getRow(e))},updateMenu:function(e="all",t=!1){let i=["all","user","lead","visitor"];"all"==e?MCF.ajax({function:"count-users"},e=>{for(var t=0;t<i.length;t++)this.updateMenuItem("set",i[t],e[i[t]])}):this.updateMenuItem(e,t)},updateMenuItem:function(e="set",t=!1,i=1){let s=P.find(`[data-type="${t}"] span`),a=["user","lead","visitor"];"set"!=e&&(i=parseInt(s.attr("data-count"))+1*("add"==e?1:-1)),s.html(`(${i})`).attr("data-count",i),i=0;for(var n=0;n<a.length;n++)i+=parseInt(P.find(`[data-type="${a[n]}"] span`).attr("data-count"));P.find('[data-type="all"] span').html(`(${i})`).attr("data-count",i)},delete:function(e){if(a(U),Array.isArray(e)){if(MC_ADMIN_SETTINGS.cloud&&!(e=MCCloud.removeAdminID(e)).length)return;MCF.ajax({function:"delete-users",user_ids:e},()=>{for(var i=0;i<e.length;i++)delete Re[e[i]],U.find(`[data-user-id="${e[i]}"]`).remove(),C.find(`[data-user-id="${e[i]}"]`).remove(),MCF.event("MCUserDeleted",e[i]);0==U.find("[data-user-id]").length&&this.filter(P.find(".mc-active").data("type")),t("Users deleted"),this.updateMenu(),U.mcLoading(!1)})}else Re[e].delete(()=>{let i=C.find(`[data-user-id="${e}"]`);s().id==e&&s(!1),i.mcActive()&&(MCChat.conversation=!1,setTimeout(()=>{lt.clickFirst()},300)),delete Re[e],U.find(`[data-user-id="${e}"]`).remove(),i.remove(),k.mcHideLightbox(),t("User deleted"),this.updateMenu(),U.mcLoading(!1)})},startRealTime:function(){MCPusher.active||(this.stopRealTime(),this.real_time=setInterval(()=>{this.update()},1e3))},stopRealTime:function(){clearInterval(this.real_time)},csv:function(){MCF.ajax({function:"csv-users",users_id:rt.getSelected()},e=>{_(e,"mc-export-users-close","Users exported"),window.open(e)})},updateUsersActivity:function(){MCF.updateUsersActivity(Qe?MC_ACTIVE_AGENT.id:-1,s()?s().id:-1,function(e){rt.setActiveUserStatus("online"==e)})},setActiveAgentStatus:function(e=!0){let t=e?"online":"offline";Qe=e,x.find('[data-value="status"]').html(o(MCF.slugToString(t))).attr("class","mc-"+t),MCPusher.active&&(e?(MCPusher.presence(),"queue"!=MC_ADMIN_SETTINGS.routing&&"routing"!=MC_ADMIN_SETTINGS.routing||MCF.ajax({function:"assign-conversations-active-agent",is_queue:"queue"==MC_ADMIN_SETTINGS.routing},()=>{lt.update()})):MCPusher.presenceUnsubscribe()),MC_ADMIN_SETTINGS.reports_disabled||MCF.ajax({function:"reports-update",name:t})},setActiveUserStatus:function(e=!0){let t=A.find(".mc-conversation .mc-top > .mc-labels");t.find(".mc-status-online").remove(),e&&t.prepend(`<span class="mc-status-online">${o("Online")}</span>`),MCChat.user_online=e},onlineUserNotification:function(e){let t=MC_ADMIN_SETTINGS.online_users_notification;if(t){let i=e.info.first_name+" "+e.info.last_name,s=this.userProfileImage(e.info.profile_image);MC_ADMIN_SETTINGS.push_notifications&&e.info.id&&!this.history.includes(e.info.id)?MCF.ajax({function:"push-notification",title:t,message:i,icon:s,interests:MC_ACTIVE_AGENT.id,user_id:e.info.id}):lt.desktop_notifications&&MCChat.desktopNotification(t,i,s,!1,e.info.id),this.history.push(e.info.id)}},userProfileImage:function(e){return!e||e.indexOf("user.svg")?MC_ADMIN_SETTINGS.notifications_icon:e},getSelected:function(){let t=[];return U.find("tr").each(function(){e(this).find('td input[type="checkbox"]').is(":checked")&&t.push(e(this).attr("data-user-id"))}),t},getExtraDetailsList:function(t=!1){return V.find(".mc-additional-details .mc-edit-box > "+(t?".mc-custom-detail":".mc-input")).map(function(){return[[e(this).attr("id"),e(this).find("span").html().trim()]]}).get()}},lt={real_time:null,datetime_last_conversation:!1,user_typing:!1,desktop_notifications:!1,flash_notifications:!1,busy:!1,busy_2:!1,is_search:!1,menu_count_ajax:!1,previous_editor_text:!1,open:function(e=-1,t){-1!=e&&this.openConversation(e,t),k.mcHideLightbox(),x.find(".mc-admin-nav a").mcActive(!1).parent().find("#mc-conversations").mcActive(!0),k.find(" > main > div").mcActive(!1),A.mcActive(!0).find(".mc-board").removeClass("mc-no-conversation"),Ie.find(" > p").attr("data-id","").attr("data-value","").html(o("None")),this.notes.update([]),this.tags.update([]),this.startRealTime()},openConversation:function(i,a=!1,n=!0){if(MCChat.label_date.mcActive(!1),MCChat.label_date_show=!1,this.busy_2!=i)if(!1===a&&i)this.busy_2=i,MCF.ajax({function:"get-user-from-conversation",conversation_id:i},e=>{this.busy_2=!1,MCF.null(e.id)?MCF.error("Conversation not found","MCAdmin.openConversation"):this.openConversation(i,e.id,n)});else{let r=MCF.null(Re[a])||!Re[a].details.email,l=A.find(`[data-conversation-id="${i}"]`),c=C.find("li");T.html(""),T.mcLoading(!0),r?(s(new MCUser({id:a})),s().update(()=>{Re[a]=s(),this.updateUserDetails()})):(s(Re[a]),this.updateCurrentURL()),MCPusher.active&&(MCPusher.event("client-typing",e=>{e.user_id==s().id&&(lt.typing(!0),clearTimeout(me),me=setTimeout(()=>{lt.typing(!1)},1e3))}),MCPusher.event("new-message",()=>{MCChat.update()}),MCPusher.event("agent-active-conversation-changed",e=>{e.previous_conversation_id==i&&A.find(".mc-conversation-busy").remove()},"agents"),MCPusher.event("init",e=>{lt.updateCurrentURL(e.current_url)}),MCPusher.event("message-status-update",e=>{MCChat.conversation&&MCChat.conversation.updateMessagesStatus(e.message_ids)})),MC_ADMIN_SETTINGS.smart_reply&&R.html(""),c.mcActive(!1),MC_ADMIN_SETTINGS.departments_show||c.attr("data-color",""),l.mcActive(!0),-1!=i?(this.busy_2=i,s().getFullConversation(i,a=>{let r=a.status_code,d=F.eq(0),u=d.find(".mc-active").attr("data-value");if(this.busy_2=!1,MCChat.setConversation(a),MCChat.populate(),this.setReadIcon(r),A.find(".mc-conversation-busy").remove(),this.updateUserDetails(),A.find(".mc-top > a").html(a.get("title")),dt.must_translate=MC_ADMIN_SETTINGS.translation&&s().language&&MC_ADMIN_SETTINGS.active_agent_language!=s().language,dt.must_translate){let e=[],t=[],s=[],n=[];for(f=0;f<a.messages.length;f++){let i=a.messages[f];i.message&&(i.payload("original-message")&&(!i.payload("original-message-language")||i.payload("original-message-language")==MC_ADMIN_SETTINGS.active_agent_language)||i.payload("translation")&&(!i.payload("translation-language")||i.payload("translation-language")==MC_ADMIN_SETTINGS.active_agent_language)?n.push(i):(e.push(i.message),t.push(i.id),s.push(i.get("user_type"))))}e.length?st.dialogflow.translate(e,MC_ADMIN_SETTINGS.active_agent_language,e=>{if(e)for(var i=0;i<e.length;i++){let a=MCChat.conversation.getMessage(t[i]);a.payload("translation",e[i]),a.payload("translation-language",MC_ADMIN_SETTINGS.active_agent_language),this.openConversation_2(t[i],s[i])}MC_ADMIN_SETTINGS.smart_reply&&this.openConversation_1(MCChat.conversation,R)},t,i):MC_ADMIN_SETTINGS.smart_reply&&this.openConversation_1(MCChat.conversation,R);for(f=0;f<n.length;f++)this.openConversation_2(n[f].id,n[f].get("user_type"))}if(Ie.length){let e=!!a.get("department")&&this.getDepartments(a.get("department")),t=e?e["department-color"]:"";MC_ADMIN_SETTINGS.departments_show||c.attr("data-color",""),w(i).attr("data-color",t),Ie.find(" > p").attr("data-id",e?e["department-id"]:"").attr("data-value",t).html(e?e["department-name"]+"<span></span>":o("None"))}let p=A.find("#conversation-agent");if(p.length){let e=p.find(`[data-id="${a.get("agent_id")}"]`);p.find(" > p").attr("data-value",e.data("id")).html(e.html())}[1,2,"1","2"].includes(r)&&(r=0),u==r||e($).find(".mc-search-btn").mcActive()||lt.filters()[1]||lt.filters()[3]||(d.find(`[data-value="${r}"]`).click(),d.find("ul").mcActive(!1)),We&&this.mobileOpenConversation(),l.length||u!=r&&(0!=u||1!=r)||C.prepend(lt.getListCode(a)),C.find("li").mcActive(!1),l.mcActive(!0),n&&this.scrollTo(),this.notificationsCounterReset(i,l),T.mcInitTooltips();let h=a.get("busy");h&&A.find(".mc-editor > .mc-labels").prepend(`<span data-agent="${h.id}" class="mc-status-warning mc-conversation-busy">${h.first_name} ${h.last_name} ${o("is replying to this conversation")}</span>`),st.is("woocommerce")&&st.woocommerce.conversationPanel(),st.is("ump")&&st.ump.conversationPanel(),st.is("perfex")&&st.perfex.conversationPanel(),st.is("whmcs")&&st.whmcs.conversationPanel(),st.is("aecommerce")&&st.aecommerce.conversationPanel(),st.is("martfury")&&st.martfury.conversationPanel(),st.is("armember")&&st.armember.conversationPanel(),st.is("zendesk")&&st.zendesk.conversationPanel(),st.is("opencart")&&st.opencart.conversationPanel(),s()&&MC_ADMIN_SETTINGS.cloud&&(MCCloud.shopify.panel&&MCCloud.shopify.panel.html(""),MCCloud.shopify.conversationPanel()),this.notes.update(a.details.notes),this.tags.update(a.details.tags),this.attachments(),MC_ADMIN_SETTINGS.smart_reply&&!dt.must_translate&&this.openConversation_1(a,R);for(f=a.messages.length-1;f>0;f--){let e=a.messages[f].get("payload");if(e.rating){A.find(".mc-profile-list > ul").append(`<li data-id="rating"><i class="mc-icon mc-icon-${1==e.rating?"like":"dislike"}"></i><span>${o("User rating")}</span><label>${o(1==e.rating?"Helpful":"Not helpful")}${e.message?" - "+e.message:""}</label></li>`);break}}if("em"==a.get("source")&&this.cc(a.get("extra").split(",")),"wa"==a.get("source")){let e=a.getLastUserMessage();e&&new Date-new Date(e.get("creation_time").replace(" ","T"))>864e5&&t(o("You can't send a WhatsApp message more than 24 hours after the user's last message—use a template instead.")+'<i id="mc-whatsapp-alert-btn" class="mc-icon-social-wa"</i>',"info")}if(k.on("click","#mc-whatsapp-alert-btn",function(){lt.showDirectMessageBox("whatsapp",[s().id])}),s().getConversations(function(e){A.find(".mc-user-conversations").html(1==e.length?"":s().getConversationsCode(e)).prev().setClass("mc-hide",1==e.length)}),this.is_search){let t=$.find(".mc-search-btn input").val();for(var f=0;f<a.messages.length;f++)if(a.messages[f].message.toLowerCase().includes(t)){let t=a.messages[f].id;setTimeout(()=>{let i="";T.find("> div").each(function(){let s=e(this);s.attr("data-id")==t?(s.addClass("mc-highlight"),setTimeout(()=>{s.removeClass("mc-highlight")},3600),MCChat.label_date.html(i),MCChat.label_date_show=!0,s.index()?s.prev()[0].scrollIntoView():s[0].scrollIntoView()):s.hasClass("mc-label-date")&&(i=s.html())})},300)}}T.mcLoading(!1)})):(MCChat.clear(),C.find("li").mcActive(!1),T.mcLoading(!1),A.find(".mc-top > a").html(""),r||this.updateUserDetails()),A.find(".mc-board").removeClass("mc-no-conversation"),rt.updateUsersActivity(),this.startRealTime(),MCF.getURL("conversation")!=i&&-1!=i&&f("?conversation="+i)}},openConversation_1:function(e,t){let i=e.getLastUserMessage();t.html(""),i&&i.payload("mc-human-takeover")&&(i=e.getLastUserMessage(i.get("index"))),i&&st.dialogflow.smartReply(i.message)},openConversation_2:function(e,t){let i=MCChat.conversation.getMessage(e);if(i){let s=MCF.isAgent(t)&&"bot"!=t;T.find(`[data-id="${e}"]`).replaceWith(i.getCode()),T.find(`[data-id="${e}"] .mc-menu`).prepend(`<li data-value="${s?"translation":"original"}">${o(s?"View translation":"View original message")}</li>`)}},populate:function(e,t,i){this.openConversation(e,t,i)},populateList:function(e){let t="";Ce=[];for(var i=0;i<e.length;i++)t+=this.getListCode(e[i]),Ce.push(new MCConversation([new MCMessage(e[i])],e[i]));t||(t=`<p class="mc-no-results">${o("No conversations found.")}</p>`),C.html(t),this.updateMenu(),MCF.event("MCAdminConversationsLoaded",{conversations:e})},update:function(){if(!this.busy&&0==F.eq(0).find("p").attr("data-value")&&this.datetime_last_conversation){let t=lt.filters();if(this.busy=!0,MCF.ajax({function:"get-new-conversations",datetime:this.datetime_last_conversation,department:t[1],source:t[2],tag:t[3],agent_id:t[4]},t=>{if(this.busy=!1,t.length){let n,o="",r="",l=MCChat.conversation?MCChat.conversation.id:-1,c=!1,d=[];t[0].last_update_time&&(this.datetime_last_conversation=t[0].last_update_time);for(i=0;i<t.length;i++)if(!d.includes(t[i].id)){let e=new MCMessage(t[i]),a=new MCConversation([e],t[i]),u=a.status_code,p=2==u||MC_ADMIN_SETTINGS.order_by_date&&(0==u||1==u),h=a.user_id,f=a.id,g=w(f),b=g.length,v=e.get("user_type"),m=a.get("message"),_=l==f,S=!MCF.isAgent(v);if(!m&&e.payload("preview")&&(m=e.payload("preview")),p&&(!_||"hidden"==MCF.visibility_status)&&(S||e.payload("human-takeover-message-confirmation"))){let t=MCF.storage("notifications-counter");t||(t={}),t[f]||(t[f]=[]),t[f].includes(e.id)||(t[f].push(e.id),MCF.storage("notifications-counter",t))}let y=this.getListCode(a,null);_?(this.updateUserDetails(),b?(m&&g.replaceWith(y),this.setStatus(u,f)):c=!0):b&&(Ce[g.index()]=a,w(f).remove()),h in Re||(Re[h]=new MCUser({id:h,first_name:a.get("first_name"),last_name:a.get("last_name"),profile_image:a.get("profile_image"),user_type:v})),_&&b||(p?(o+=y,Ce.unshift(a)):0!=u&&1!=u||((n=C.find('[data-conversation-status="2"]').last()).length?(Ce.splice(n.index()+1,0,a),r+=y):o+=y),s()&&h==s().id&&s().getConversations(e=>{A.find(".mc-user-conversations").html(s().getConversationsCode(e))}),MCF.event("MCAdminNewConversation",{conversation:a})),!s()||"update-user"!=e.payload("event")&&Re[h].type==v||s().update(()=>{this.updateUserDetails(),Re[s().id]=s()});let k=e.payload("preview");if(!MCChat.tab_active&&2==u&&(S||k)&&(m||a.getAttachments().length||k)){if(this.desktop_notifications){let e=[Re[h].nameBeautified,Re[h].image];MCChat.desktopNotification(e[0],k||m,e[1],f,h)}this.flash_notifications&&MCChat.flashNotification(),MCChat.audio&&MC_ADMIN_SETTINGS.sound&&MCChat.playSound()}d.push(f)}lt.is_search||(o&&C.prepend(o),r&&e(r).insertAfter(n),c&&this.scrollTo(),this.updateMenu());for(var i=0;i<MCChat.notifications.length;i++){let e=!1;for(var a=0;a<Ce.length;a++)if(Ce[a].id==MCChat.notifications[i][0]){e=2==Ce[a].status_code;break}e||(MCChat.notifications.splice(i,1),i--)}}}),MC_ADMIN_SETTINGS.assign_conversation_to_agent||MC_ACTIVE_AGENT.department){let t=C.find(" > li").map(function(){return e(this).attr("data-conversation-id")}).get();t.length&&MCF.ajax({function:"check-conversations-assignment",conversation_ids:t,agent_id:!!MC_ADMIN_SETTINGS.assign_conversation_to_agent&&MC_ACTIVE_AGENT.id,department:MC_ACTIVE_AGENT.department},e=>{if(e)for(var t=0;t<e.length;t++)w(e[t]).remove()})}}},updateMenu:function(){let e=C.find('[data-conversation-status="2"]').length,t=F.eq(0),i=t.find(" > p span");if(100==e||this.menu_count_ajax||MC_ADMIN_SETTINGS.order_by_date){let e=t.find("li.mc-active").data("value");this.menu_count_ajax=!0,MCF.ajax({function:"count-conversations",status_code:0==e?2:e},e=>{i.html(`(${e})`)})}else i.html(`(${e})`)},messageMenu:function(e,t=!1,i=!1){return`<i class="mc-menu-btn mc-icon-menu"></i><ul class="mc-menu">${(t&&MC_ADMIN_SETTINGS.chatbot_features?`<li data-value="bot">${o("Train chatbot")}</li>`:"")+(e&&!MC_ADMIN_SETTINGS.supervisor&&MC_ADMIN_SETTINGS.allow_agent_delete_message||MC_ADMIN_SETTINGS.supervisor&&MC_ADMIN_SETTINGS.allow_supervisor_delete_message?`<li data-value="delete">${o("Delete")}</li>`:"")+(i?`<li data-value="reply">${o("Reply")}</li>`:"")}</ul>`},updateUserDetails(){s()&&(A.find(`[data-user-id="${s().id}"] .mc-name`).html(s().name),A.find(".mc-top > a").html(MCChat.conversation?MCChat.conversation.title:s().name),I.find(".mc-profile").setProfile(),ct.populate(s(),A.find(".mc-profile-list")))},setReadIcon(e){let t=2==e;A.find('.mc-top [data-value="read"],.mc-top [data-value="unread"]').mcActive([0,1,2].includes(parseInt(e))).attr("data-value",t?"read":"unread").attr("data-mc-tooltip",o(t?"Mark as read":"Mark as unread")).parent().mcInitTooltips().find("i").attr("class",t?"mc-icon-check-circle":"mc-icon-circle")},setStatus(e,t=!1,i=!1){t||(t=MCChat.conversation.id),t&&(Ce[w(t).index()].set("status_code",e),this.setReadIcon(e),i&&C.find(`[data-conversation-id="${t}"]`).attr("data-conversation-status",e))},getListCode:function(e,t){e instanceof MCConversation||(e=new MCConversation([new MCMessage(e)],e));let i=e.getCode(!0),s=MC_ADMIN_SETTINGS.tags_show?e.get("tags"):"",a=e.get("department"),n="",o=e.get("last_update_time"),r=MCChat.conversation&&MCChat.conversation.id==e.id;return MCF.null(t)&&(t=e.status_code),s&&(s=lt.tags.codeLeft(s)),MCChat.conversation&&r&&"hidden"!=MCF.visibility_status||((n=MCF.storage("notifications-counter"))&&n[e.id]&&n[e.id].length?2==t?n=`<span class="mc-notification-counter">${n[e.id].length}</span>`:(n[e.id]=[],MCF.storage("notifications-counter",n),n=""):n=""),o||(o=e.getLastMessage()?e.getLastMessage().get("creation_time"):e.get("creation_time")),`<li${r?' class="mc-active"':""} data-user-id="${e.get("user_id")}" data-conversation-id="${e.id}" data-conversation-status="${t}"${a?` data-department="${a}"${MC_ADMIN_SETTINGS.departments_show?' data-color="'+this.getDepartments(a)["department-color"]+'"':""}`:""}${MCF.null(e.get("source"))?"":` data-conversation-source="${e.get("source")}"`}>${""+n}<div class="mc-profile"><img loading="lazy" src="${e.get("profile_image")}"><span class="mc-name">${e.get("first_name")+" "+e.get("last_name")}</span>${s}<span class="mc-time">${MCF.beautifyTime(o)}</span></div><p>${i}</p></li>`},startRealTime:function(){MCPusher.active||(this.stopRealTime(),this.real_time=setInterval(()=>{this.update(),this.updateCurrentURL()},1e4),MCChat.startRealTime())},stopRealTime:function(){clearInterval(this.real_time),MCChat.stopRealTime()},transcript:function(e,t,i=!1,a=!1){MCF.ajax({function:"transcript",conversation_id:e},e=>{"email"==i?s()&&s().id==t&&!s().get("email")||MCChat.sendEmail(MC_ADMIN_SETTINGS.transcript_message,[[e,e]],t,t=>{a&&a(!0===t?e:t)}):(a&&a(e),window.open(e))})},typing:function(e){e?(MCChat.user_online||rt.setActiveUserStatus(!0),this.user_typing||(A.find(".mc-conversation .mc-top > .mc-labels").append('<span class="mc-status-typing">'+o("Typing")+"</span>"),this.user_typing=!0)):this.user_typing&&(A.find(".mc-conversation .mc-top .mc-status-typing").remove(),this.user_typing=!1)},scrollTo:function(){let e=C.find(".mc-active"),t=e.length?e[0].offsetTop:0;C.parent().scrollTop(t-(We?120:80))},search:function(t){t&&c(t,(t,i)=>{if(Me=1,t.length>1)MCF.ajax({function:"search-conversations",search:t},t=>{lt.populateList(t),e(i).mcLoading(!1),this.scrollTo(),this.is_search=!0});else{let t=lt.filters();De=1,MCF.ajax({function:"get-conversations",status_code:t[0],department:t[1],source:t[2],tag:t[3],agent_id:t[4]},t=>{lt.populateList(t),e(i).mcLoading(!1),this.is_search=!1,MCChat.conversation&&(w(MCChat.conversation.id).mcActive(!0),this.scrollTo())})}})},notificationsCounterReset:function(e,t=!1){let i=MCF.storage("notifications-counter");if(i&&i[e]){t||(t=C.find('[data-conversation-id="'+e+'"]'));let s=t.find(".mc-notification-counter");i[e]=[],MCF.storage("notifications-counter",i),s.addClass("mc-fade-out"),setTimeout(()=>{s.remove()},200)}},updateCurrentURL:function(e=!1){e?this.ucurl(e):MCChat.user_online&&s()&&s().getExtra("current_url")&&MCF.ajax({function:"current-url"},e=>{e&&this.ucurl(e)})},ucurl(e){let t=s().getExtra("current_url");e=b(e),A.find('.mc-profile-list [data-id="current_url"] label').attr("data-value",e).html(e),t&&(t.value=e,s().setExtra("current_url",t))},assignDepartment:function(e,t,i){MCF.ajax({function:"update-conversation-department",conversation_id:e,department:t,message:MCChat.conversation.getLastMessage().message},e=>{i(e)})},assignAgent:function(e,t,i=!1){MCF.ajax({function:"update-conversation-agent",conversation_id:e,agent_id:t,message:MCChat.conversation.getLastMessage().message},e=>{i&&i(e)})},setActiveDepartment:function(e){if(MCChat.conversation&&MCChat.conversation.get("department")==e)return;let i=!!e&&this.getDepartments(e),s=i?i["department-color"]:"",a=w(MCChat.conversation.id),n=lt.filters()[1];Ie.find(" > p").attr("data-id",i?i["department-id"]:"").attr("data-value",s).html((i?i["department-name"]:o("None"))+"<span></span>").next().mcActive(!1),MCChat.conversation.set("department",e),n&&n!=e||"agent"==MC_ACTIVE_AGENT.user_type&&MC_ACTIVE_AGENT.department&&MC_ACTIVE_AGENT.department!=e?(a.remove(),lt.clickFirst()):a.attr("data-color",s),t("Department updated. The agents have been notified.")},getDepartments:function(e=!1){if(e){for(var t=0;t<MC_ADMIN_SETTINGS.departments.length;t++)if(MC_ADMIN_SETTINGS.departments[t]["department-id"]==e)return MC_ADMIN_SETTINGS.departments[t];return!1}return MC_ADMIN_SETTINGS.departments},setActiveAgent:function(e){let i=A.find("#conversation-agent"),s=i.find(`[data-id="${e}"]`);MCChat.conversation.set("agent_id",e),i.find(" > p").attr("data-value",s.data("id")).html(s.html()).next().mcActive(!1),"agent"!=MC_ACTIVE_AGENT.user_type||MC_ADMIN_SETTINGS.assign_conversation_to_agent&&!e||(w(MCChat.conversation.id).remove(),lt.clickFirst()),e&&t("Agent assigned. The agent has been notified.")},getSelectedConversations:function(){return C.find(".mc-active").map(function(){return{id:e(this).attr("data-conversation-id"),user_id:e(this).attr("data-user-id"),status_code:e(this).attr("data-conversation-status")}}).toArray()},mobileOpenConversation:function(){A.find(".mc-admin-list").mcActive(!1),B.mcActive(!0),x.addClass("mc-hide")},mobileCloseConversation:function(){C.find("li.mc-active").mcActive(!1),A.find(".mc-admin-list").mcActive(!0),A.find(".mc-conversation,.mc-user-details").removeClass("mc-active"),k.find('.mc-menu-mobile [data-value="panel"]').mcActive(!1),x.removeClass("mc-hide"),window.history.replaceState({},document.title,MCF.URL())},clickFirst:function(e=!1){e||(e=C.find("li:first-child")),e.length?(e.click(),lt.scrollTo()):(A.find(".mc-board").addClass("mc-no-conversation"),C.find("li").length||C.html(`<p class="mc-no-results">${o("No conversations found.")}</p>`),MCF.getURL("conversation")&&window.history.replaceState({},document.title,MCF.URL().replace("?conversation="+MCF.getURL("conversation"),"")))},savedReplies:function(t,i){let s=i.charAt(t.selectionStart-1),a=i.substr(0,i.lastIndexOf("#"));if("#"==s){if(i.length>1&&"#"==i.charAt(t.selectionStart-2))return e(t).val(a.substr(0,a.length-1)),B.find(".mc-btn-saved-replies").click();MCChat.editor_listening=!0}if(MCChat.editor_listening&&" "==s){let s=i.substr(i.lastIndexOf("#")+1).replace(" ","");MCChat.editor_listening=!1;for(var n=0;n<Ne.length;n++)if(Ne[n]["reply-name"]==s)return void e(t).val(a+Ne[n]["reply-text"])}},attachments:function(){if(L.length){let i=MCChat.conversation.getAttachments(),s="",a="",n=[];for(t=i.length-1;t>-1;t--){let e=MCF.getFileType(i[t][1]);s+=`<a href="${i[t][1]}" target="_blank"><i class="mc-icon mc-icon-download"></i>${i[t][0]}</a>`,n.includes(e)||n.push(e)}if(i.length>4&&n.length>1){a=`<div id="mc-attachments-filter" class="mc-select"><p>${o("All")}</p><ul><li data-value="">${o("All")}</li>`;for(var t=0;t<n.length;t++)a+=`<li data-value="${n[t]}">${o(MCF.slugToString(n[t])+"s")}</li>`;a+="</ul></div>"}e(L).html(s?`<h3${a?' class="mc-flex"':""}>${o("Attachments")}${a}</h3><div class="mc-list-items mc-list-links mc-list-icon">${s}</div>`:""),l(L,160)}},filters:function(){let e=[];for(var t=0;t<F.length;t++)e.push(F.eq(t).find("li.mc-active").data("value"));return e},notes:{busy:!1,add:function(e,t,i,s,a=!1,n=!1){MCF.ajax({function:n?"update-note":"add-note",conversation_id:e,user_id:t,note_id:n,name:i,message:s},e=>{a&&a(e)})},update:function(e,t=!1){if(N.length){let s="",a=N.find(" > div");if(e){for(var i=0;i<e.length;i++){let t=e[i];s+=`<div data-id="${t.id}"><span${MC_ADMIN_SETTINGS.notes_hide_information?' class="mc-note-hide-info"':""}>${MC_ADMIN_SETTINGS.notes_hide_information?"":t.name}${MC_ADMIN_SETTINGS.notes_hide_information||!t.date?"":`<span>${MCF.beautifyTime(t.date,!0)}</span>`}${MC_ACTIVE_AGENT.id==t.user_id?'<i class="mc-edit-note mc-icon-edit"></i><i class="mc-delete-note mc-icon-close"></i>':""}</span><span class="mc-note-text">${t.message.replace(/\n/g,"<br>")}</span></div>`}s=s.autoLink({target:"_blank"})}t?a.append(s):a.html(s),a.attr("style",""),N.find(".mc-collapse-btn").remove(),l(N,155),this.busy=!1}},delete:function(e,t,i=!1){this.busy||(this.busy=!0,MCF.ajax({function:"delete-note",conversation_id:e,note_id:t},e=>{this.busy=!1,i&&i(e)}))}},tags:{busy:!1,update:function(e){if(E.length){let i="",s=E.find(" > div");for(var t=0;t<e.length;t++){let s=this.get(e[t]);s&&(i+=this.code(s))}s.html(i),this.busy=!1}},get:function(e){for(var t=0;t<MC_ADMIN_SETTINGS.tags.length;t++)if(e==MC_ADMIN_SETTINGS.tags[t]["tag-name"])return MC_ADMIN_SETTINGS.tags[t]},getAll:function(e=[]){let t="";for(var i=0;i<MC_ADMIN_SETTINGS.tags.length;i++)t+=this.code(i,e.includes(MC_ADMIN_SETTINGS.tags[i]["tag-name"]));return t},code:function(e,t){let i=isNaN(e)?e:MC_ADMIN_SETTINGS.tags[e];if(i){let e=i["tag-name"];return`<span data-value="${e}" data-color="${i["tag-color"]}"${t?' class="mc-active"':""}>${o(e)}</span>`}return""},codeLeft:function(e){let t='<span class="mc-tags-area">';for(var i=0;i<e.length;i++){let s=this.get(e[i]);s&&(t+=`<i class="mc-icon-tag" data-color-text="${s["tag-color"]}"></i>`)}return t+"</span>"}},showDirectMessageBox:function(e,t=[]){if("whatsapp"==e)m(t);else{let i="custom_email"==e;MCForm.clear(D),D.find(".mc-direct-message-users").val(t.length?t.join(","):"all"),D.find(".mc-bottom > div").html(""),D.find(".mc-top-bar > div:first-child").html(o(`Send a ${{sms:"text message",custom_email:"email",message:"chat message"}[e]}`)),D.find(".mc-loading").mcLoading(!1),D.find(".mc-direct-message-subject").mcActive(i).find("input").attr("required",i),D.attr("data-type",e),D.mcShowLightbox()}},getDeliveryFailedMessage:function(e){return`<i class="mc-icon-warning mc-delivery-failed" data-mc-tooltip="${o("Message not delivered to {R}.").replace("{R}",st.getName(e))}" data-mc-tooltip-init></i>`},cc:function(e){let t="";for(var i=0;i<e.length;i++)e[i]&&(t+=`<li data-id="cc"><i class="mc-icon mc-icon-envelope"></i><span>CC</span><label>${e[i]}</label></li>`);A.find('[data-id="cc"]').remove(),A.find('[data-id="email"]').attr("data-em","true").after(t)}},ct={getAll:function(e){return MCForm.getAll(e)},get:function(e){return MCForm.get(e)},set:function(e,t){return MCForm.set(e,t)},show:function(e){n(),s(new MCUser({id:e})),s().update(()=>{this.populate(s(),O.find(".mc-profile-list")),O.find(".mc-profile").setProfile(),s().getConversations(t=>{let i=s().type,a=s().getConversationsCode(t);MCF.isAgent(i)&&this.agentData(),O.find(".mc-user-conversations").html(a).prev().setClass("mc-hide",!a),O.find(".mc-top-bar [data-value]").mcActive(!1),MCF.null(s().get("email"))||O.find('.mc-top-bar [data-value="email"]').mcActive(!0),s().getExtra("phone")&&(MC_ADMIN_SETTINGS.sms&&O.find('.mc-top-bar [data-value="sms"]').mcActive(!0),O.find('.mc-top-bar [data-value="whatsapp"]').mcActive(!0)),this.boxClasses(O,i),O.attr("data-user-id",s().id).mcShowLightbox(),n(!1,!1),MCF.event("MCProfileBoxOpened",{user_id:e})}),Re[e]=s(),MCF.getURL("user")==e||MCF.getURL("conversation")||f("?user="+e)})},showEdit:function(e){if(!(e instanceof MCUser))return MCF.error("User not of type MCUser","MCUsers.showEdit"),!1;{let i=V.find("#password input"),s=e.type,a=V.find("#user_type select");V.removeClass("mc-user-new").attr("data-user-id",e.id),V.find(".mc-top-bar .mc-save").html(`<i class="mc-icon-check"></i>${o("Save changes")}`),V.find(".mc-profile").setProfile(),V.find(".mc-unlisted-detail").remove(),V.find("input,select,textara").removeClass("mc-error");let n="",r=V.find(".mc-additional-details [id]").map(function(){return this.id}).get().concat(["wp-id","perfex-id","whmcs-id","aecommerce-id","facebook-id","ip","os","current_url","country_code","browser_language","browser","martfury-id","martfury-session"]);for(var t in e.extra)r.includes(t)||(n+=`<div id="${t}" data-type="text" class="mc-input mc-unlisted-detail"><span>${o(e.extra[t].name)}</span><input type="text"></div>`);V.find(".mc-additional-details .mc-edit-box").append(n),this.populateEdit(e,V),this.updateRequiredFields(s),"admin"==MC_ACTIVE_AGENT.user_type&&MCF.isAgent(s)&&a.html(`<option value="agent">${o("Agent")}</option><option value="admin"${"admin"==s?" selected":""}>${o("Admin")}</option>`),i.val()&&i.val("********"),MC_ADMIN_SETTINGS.cloud&&V.setClass("mc-cloud-admin",1==e.id),this.boxClasses(V,s),V.mcShowLightbox(),MCF.event("MCProfileEditBoxOpened",{user_id:e.id})}},populate:function(e,t){let i=["first_name","last_name","password","profile_image"],s="";if(t.hasClass("mc-profile-list-conversation")&&MCChat.conversation){let e=MCChat.conversation.get("source");s=this.profileRow("conversation-id",MCChat.conversation.id,o("Conversation ID")),MCF.null(e)||(s+=this.profileRow("conversation-source",st.getName(e),o("Source")))}"admin"!=MC_ACTIVE_AGENT.user_type&&i.push("token");for(var a in e.details)i.includes(a)||(s+=this.profileRow(a,e.get(a),"id"==a?"User ID":a));if(t.html(`<ul>${s}</ul>`),s="",e.isExtraEmpty())MCF.ajax({function:"get-user-extra",user_id:e.id},i=>{for(var a=0;a<i.length;a++){let t=i[a].slug;e.setExtra(t,i[a]),s+=this.profileRow(t,i[a].value,i[a].name)}t.find("ul").append(s),l(t,145),MCF.event("MCUserLoaded",e)});else{for(var a in e.extra){let t=e.getExtra(a);s+=this.profileRow(a,t.value,t.name)}t.find("ul").append(s),l(t,145),MCF.event("MCUserLoaded",e)}},profileRow:function(e,t,i=e){if(!t)return"";let s,a={id:"user",full_name:"user",email:"envelope",phone:"phone",user_type:"user",last_activity:"calendar",creation_time:"calendar",token:"shuffle",currency:"currency",location:"marker",country:"marker",address:"marker",city:"marker",postal_code:"marker",browser:"desktop",os:"desktop",current_url:"next",timezone:"clock"},n=`<i class="mc-icon mc-icon-${e in a?a[e]:"plane"}"></i>`,r=!1;switch(e){case"last_activity":case"creation_time":t=MCF.beautifyTime(t);break;case"user_type":t=MCF.slugToString(t);break;case"country":case"country_code":case"language":case"browser_language":let i=t;"ar"!=t||"language"!=e&&"browser_language"!=e||(i="arc"),n=`<img src="${MC_URL}/media/flags/${i.toLowerCase()}.png" />`;break;case"browser":(s=t.toLowerCase()).includes("chrome")?r="chrome":s.includes("edge")?r="edge":s.includes("firefox")?r="firefox":s.includes("opera")?r="opera":s.includes("safari")&&(r="safari");break;case"os":(s=t.toLowerCase()).includes("windows")?r="windows":s.includes("mac")||s.includes("apple")||s.includes("ipad")||s.includes("iphone")?r="apple":s.includes("android")?r="android":s.includes("linux")?r="linux":s.includes("ubuntu")&&(r="ubuntu");break;case"conversation-source":r=t.toLowerCase();case"browser":case"os":case"conversation-source":r&&(n=`<img src="${MC_URL}/media/${"conversation-source"==e?"apps":"devices"}/${r}.svg" />`);break;case"current_url":t=b(t)}return`<li data-id="${e}">${n}<span>${o(MCF.slugToString(i))}</span><label>${t}</label></li>`},populateEdit:function(t,i){i.find(".mc-details .mc-input").each((i,s)=>{this.set(s,t.details[e(s).attr("id")])}),i.find(".mc-additional-details .mc-input").each((i,s)=>{let a=e(s).attr("id");a in t.extra?this.set(s,t.extra[a].value):this.set(s,"")})},clear:function(e){MCForm.clear(e)},errors:function(e){return MCForm.errors(e.find(".mc-details"))},showErrorMessage:function(e,t){MCForm.showErrorMessage(e,t)},agentData:function(){let e=`<div class="mc-title">${o("Feedback rating")}</div><div class="mc-rating-area mc-loading"></div>`,t=O.find(".mc-agent-area");t.html(e),MCF.ajax({function:"get-rating"},i=>{if(0==i[0]&&0==i[1])e=`<p class="mc-no-results">${o("No ratings yet.")}</p>`;else{let t=i[0]+i[1],s=100*i[0]/t,a=100*i[1]/t;e=`<div><div>${o("Helpful")}</div><span data-count="${i[0]}" style="width: ${Math.round(2*s)}px"></span><div>${s.toFixed(2)} %</div></div><div><div>${o("Not helpful")}</div><span data-count="${i[1]}" style="width: ${Math.round(2*a)}px"></span><div>${a.toFixed(2)} %</div></div><p class="mc-rating-count">${t} ${o("Ratings")}</p>`}t.find(".mc-rating-area").html(e).mcLoading(!1)})},boxClasses:function(t,i=!1){e(t).removeClass("mc-type-admin mc-type-agent mc-type-lead mc-type-user mc-type-visitor").addClass(`${0!=i?`mc-type-${i}`:""} mc-agent-${MC_ACTIVE_AGENT.user_type}`)},updateRequiredFields:function(e){let t=MCF.isAgent(e);V.find("#password input").prop("required",t),V.find("#email input").prop("required",t)}},dt={infoBottom:function(e,t=!1){var i=k.find(".mc-info-card");t?"error"==t?i.addClass("mc-info-card-error"):i.addClass("mc-info-card-info"):(i.removeClass("mc-info-card-error mc-info-card-warning mc-info-card-info"),clearTimeout(ge),ge=setTimeout(()=>{i.mcActive(!1)},5e3)),i.html(`<h3>${o(e)}</h3>`).mcActive(!0)},infoPanel:function(t,i="info",s=!1,a="",n="",r=!1,l=!1,c=!1){if(l&&s)return s();let d=k.find(".mc-dialog-box").attr("data-type",i),u=d.find("p");d.attr("id",a).setClass("mc-scroll-area",r).css("height",r?parseInt(e(window).height())-200+"px":""),d.find(".mc-title").html(o(n)),u.html(("alert"==i?o("Are you sure?")+" ":"")+o(t)),d.mcActive(!0).css({"margin-top":d.outerHeight()/-2+"px","margin-left":d.outerWidth()/-2+"px"}),we.mcActive(!0),be=s,ve=c,setTimeout(()=>{dt.open_popup=d},500)},genericPanel:function(e,t,i,s=[],a="",n=!1){let r="",l=k.find("#mc-generic-panel");for(var c=0;c<s.length;c++)s[c]=h(s[c])||s[c]instanceof String?[s[c],!1]:s[c],r+=`<a id="mc-${MCF.stringToSlug(s[c][0])}" class="mc-btn${s[c][1]?" mc-icon":""}">${s[c][1]?`<i class="mc-icon-${s[c][1]}"></i>`:""} ${o(s[c][0])}</a>`;l.html(`<div class="mc-lightbox mc-${e}-box"${a}><div class="mc-info"></div><div class="mc-top-bar"><div>${o(t)}</div>\n                        <div>\n                            ${r}\n                            <a class="mc-close mc-btn-icon mc-btn-red">\n                                <i class="mc-icon-close"></i>\n                            </a>\n                        </div>\n                    </div>\n                    <div class="mc-main${n?" mc-scroll-area":""}">\n                        ${i}\n                    </div>\n             </div>`),l.find("> div").mcShowLightbox()},activeUser:function(e){if(typeof e==Ke)return window.mc_current_user;window.mc_current_user=e},loadingGlobal:function(t=!0,i=!0){k.find(".mc-loading-global").mcActive(t),i&&(we.mcActive(t),e("body").setClass("mc-lightbox-active",t))},loading:function(t){return!!e(t).mcLoading()||(e(t).mcLoading(!0),!1)},collapse(t,i){let s=(t=e(t)).find("> div, > ul");s.css({height:"","max-height":""}),t.find(".mc-collapse-btn").remove(),t.hasClass("mc-collapse")&&e(s).prop("scrollHeight")>i&&(t.mcActive(!0).attr("data-height",i),t.append(`<a class="mc-btn-text mc-collapse-btn">${o("View more")}</a>`),s.css({height:i+"px","max-height":i+"px"}))},open_popup:!1,must_translate:!1,is_logout:!1,conversations:lt,users:rt,settings:at,profile:ct,apps:st};window.MCAdmin=dt,e(document).ready(function(){function c(e,i,s,a=0,n=[],r,l=!1,d=!1,u=!1,p=!1,h=!1){let f={whatsapp:["whatsapp-send-template","messages"," a phone number.","direct-whatsapp"],email:["create-email","emails"," an email address.",!1],custom_email:["send-custom-email","emails"," an email address.","direct-emails"],sms:["send-sms","text messages"," a phone number.","direct-sms"]}[r];MCF.ajax({function:f[0],to:e[i][a].value,recipient_id:e[i][a].id,sender_name:MC_ACTIVE_AGENT.full_name,sender_profile_image:MC_ACTIVE_AGENT.profile_image,subject:l,message:s,template_name:d,parameters:u,template_languages:p,template:!1,phone_id:h,user_name:!!e.first_name&&(e.first_name[a].value+" "+e.last_name[a].value).trim(),user_email:!!e.email&&e.email[a].value},g=>{let b=e[i].length,v="whatsapp"==r?k.find("#mc-whatsapp-send-template-box"):D;if(v.find(".mc-bottom > div").html(`${o("Sending")} ${o(f[1])}... ${a+1} / ${b}`),g){if(!0!==g&&("status"in g&&400==g.status||"error"in g&&![131030,131009].includes(g.error.code)))return MCForm.showErrorMessage(v,g.error?g.error.message:`${g.message} Details at ${g.more_info}`),console.error(g),v.find(".mc-loading").mcLoading(!1),void v.find(".mc-bottom > div").html("");if(a<b-1)return c(e,i,s,a+1,n,r,l,d,u,p,h);n.length?c(n,i,s,0,[],"sms",!1):(k.mcHideLightbox(),f[3]&&MCF.ajax({function:"reports-update",name:f[3],value:s.substr(0,18)+" | "+b})),t(1==b?"The message has been sent.":o("The message was sent to all users who have"+f[2]))}else console.warn(g)})}if(k=e(".mc-admin"),x=k.find("> .mc-header"),A=k.find(".mc-area-conversations"),B=A.find(".mc-conversation"),T=A.find(".mc-conversation .mc-list"),$=A.find(".mc-admin-list"),C=$.find(".mc-scroll-area ul"),F=$.find(".mc-select"),I=A.find(".mc-user-details"),G=k.find(".mc-area-users"),U=G.find(".mc-table-users"),P=G.find(".mc-menu-users"),q=G.find(".mc-filter-btn .mc-select"),O=k.find(".mc-profile-box"),V=k.find(".mc-profile-edit-box"),H=k.find(".mc-area-settings"),W=H.find(".mc-automations-area"),Y=W.find(".mc-conditions"),z=W.find(" > .mc-select"),J=W.find(" > .mc-tab > .mc-nav > ul"),Te=k.find(".mc-area-reports"),X=k.find(".mc-area-articles"),Q=X.find(".mc-content-articles"),Z=X.find(".mc-content-categories"),ee=k.find("#article-parent-categories"),K=k.find("#article-categories"),Fe=A.find(".mc-replies"),we=k.find(".mc-lightbox-overlay"),ye=typeof MC_URL!=Ke?MC_URL.substr(0,MC_URL.indexOf("-content")-3):"",Ee=A.find(".mc-woocommerce-products"),Le=Ee.find(" > div > ul"),N=A.find(".mc-panel-notes"),E=A.find(".mc-panel-tags"),L=A.find(".mc-panel-attachments"),D=k.find(".mc-direct-message-box"),et=st.is("wordpress")&&e(".wp-admin").length,M=k.find(".mc-dialogflow-intent-box"),R=A.find(".mc-editor > .mc-suggestions"),j=A.find(".mc-btn-open-ai"),Ie=A.find("#conversation-department"),de=k.find(".mc-upload-form-admin .mc-upload-files"),te=k.find(".mc-area-chatbot"),ie=te.find("#mc-table-chatbot-files"),se=te.find("#mc-table-chatbot-website"),ae=te.find("#mc-chatbot-qea"),ne=te.find(".mc-playground-editor"),oe=te.find(".mc-playground .mc-scroll-area"),le=te.find('[data-id="flows"] > .mc-content'),re=te.find("#mc-flows-nav"),window.onpopstate=function(){k.mcHideLightbox(),We&&A.mcActive()&&B.mcActive()&&lt.mobileCloseConversation(),MCF.getURL("user")?(G.mcActive()||x.find(".mc-admin-nav #mc-users").click(),ct.show(MCF.getURL("user"))):MCF.getURL("area")?x.find(".mc-admin-nav #mc-"+MCF.getURL("area")).click():MCF.getURL("conversation")?(A.mcActive()||x.find(".mc-admin-nav #mc-conversations").click(),lt.openConversation(MCF.getURL("conversation"))):MCF.getURL("setting")?(H.mcActive()||x.find(".mc-admin-nav #mc-settings").click(),H.find("#tab-"+MCF.getURL("setting")).click()):MCF.getURL("report")&&(Te.mcActive()||x.find(".mc-admin-nav #mc-reports").click(),Te.find("#"+MCF.getURL("report")).click())},MCF.getURL("area")&&setTimeout(()=>{x.find(".mc-admin-nav #mc-"+MCF.getURL("area")).click()},300),typeof MC_ADMIN_SETTINGS==Ke){let t=k.find(".mc-intall"),i=window.location.href.replace("/admin","").replace(".php","").replace(/#$|\/$/,"");return e(k).on("click",".mc-submit-installation",function(){if(a(this))return;let s=!1,n=t.find("#first-name").length;MCForm.errors(t)?s=n?"All fields are required. Minimum password length is 8 characters. Be sure you've entered a valid email.":"All fields are required.":n&&t.find("#password input").val()!=t.find("#password-check input").val()?s="The passwords do not match.":(MCF.cookie("SA_VGCKMENS",0,0,"delete"),i.includes("?")&&(i=i.substr(0,i.indexOf("?"))),e.ajax({method:"POST",url:i+"/include/ajax.php",data:{function:"installation",details:e.extend(MCForm.getAll(t),{url:i})}}).done(a=>{if(h(a)&&(a=JSON.parse(a)),0!=a){if(!0===(a=a[1]))return void setTimeout(()=>{window.location.href=i+"/admin.php?refresh=true"},1e3);switch(a){case"connection-error":s="Masi Chat cannot connect to the database. Please check the database information and try again.";break;case"missing-details":s="Missing database details! Please check the database information and try again.";break;case"missing-url":s="Masi Chat cannot get the plugin URL.";break;default:s=a}}else s=a;!1!==s&&(MCForm.showErrorMessage(t,s),e("html, body").animate({scrollTop:0},500)),e(this).mcLoading(!1)})),!1!==s&&(MCForm.showErrorMessage(t,s),e("html, body").animate({scrollTop:0},500),e(this).mcLoading(!1))}),void fetch("https://masichat.com/synch/verification.php?x="+i)}if(!k.length)return;if(n(),k.removeAttr("style"),(window.matchMedia("(display-mode: standalone)").matches||window.navigator.standalone||document.referrer.includes("android-app://"))&&k.addClass("mc-pwa"),Je&&r(),k.find(" > .mc-rich-login").length)return;MCF.storage("notifications-counter",[]),MC_ADMIN_SETTINGS.pusher?(MCPusher.active=!0,MCPusher.init(()=>{MCPusher.presence(1,()=>{rt.updateUsersActivity()}),MCPusher.event("update-conversations",()=>{lt.update()},"agents"),MCPusher.event("set-agent-status",e=>{e.agent_id==MC_ACTIVE_AGENT.id&&(rt.setActiveAgentStatus("online"==e.status),Ue=!1)},"agents"),y()})):(y(),setInterval(function(){rt.updateUsersActivity()},1e4)),rt.table_extra=U.find("th[data-extra]").map(function(){return e(this).attr("data-field")}).get(),typeof MC_CLOUD_FREE!=Ke&&MC_CLOUD_FREE&&setTimeout(()=>{location.reload()},36e5),e(window).on("beforeunload",function(){s()&&e.ajax({method:"POST",url:MC_AJAX_URL,data:{function:"on-close"}})}),e(window).keydown(function(e){let t=e.which,i=!1;if(Ae=t,[13,27,32,37,38,39,40,46,90].includes(t)){if(k.find(".mc-dialog-box").mcActive()){let e=k.find(".mc-dialog-box");switch(t){case 46:case 27:e.find(".mc-cancel").click();break;case 32:case 13:e.find("info"!=e.attr("data-type")?".mc-confirm":".mc-close").click()}i=!0}else if([38,40,46,90].includes(t)&&A.mcActive()&&!k.find(".mc-lightbox").mcActive()){let s=A.find(".mc-editor textarea"),a=s.is(":focus");if(46==t){if(a||"INPUT"==e.target.tagName)return;let t=A.find(" > div > .mc-conversation");t.find('.mc-top [data-value="'+(3==t.attr("data-conversation-status")?"delete":"archive")+'"]').click(),i=!0}else if(e.ctrlKey){let e=C.find(".mc-active");40==t?e.next().click():38==t?e.prev().click():90==t&&a&&lt.previous_editor_text&&(s.val(lt.previous_editor_text),lt.previous_editor_text=!1,i=!0),38!=t&&40!=t||(i=!0,lt.scrollTo())}}else if([37,39].includes(t)&&G.mcActive()&&k.find(".mc-lightbox").mcActive()){let e=U.find(`[data-user-id="${s().id}"]`);(e=39==t?e.next():e.prev()).length&&(k.mcHideLightbox(),ct.show(e.attr("data-user-id"))),i=!0}else if(27==t&&k.find(".mc-lightbox").mcActive())k.mcHideLightbox(),i=!0;else if(46==t){let e=k.find(".mc-search-btn.mc-active");e.length&&(e.find("i").click(),i=!0)}else 13==t&&te.find(".mc-playground-editor textarea").is(":focus")&&(te.find('.mc-playground-editor [data-value="send"]').click(),i=!0);i&&e.preventDefault()}}),e(window).keyup(function(e){Ae=!1}),e(document).on("click keydown mousemove",function(){MCF.debounce(()=>{MCChat.tab_active||MCF.visibilityChange(),MCChat.tab_active=!0,clearTimeout(Ze),Ze=setTimeout(()=>{MCChat.tab_active=!1},1e4)},"#3",8e3),!We&&MC_ADMIN_SETTINGS.away_mode&&MCF.debounce(()=>{Ue&&(rt.setActiveAgentStatus(),clearTimeout(_e),_e=setTimeout(()=>{rt.setActiveAgentStatus(!1)},6e5))},"#4",558e3)}),document.onpaste=function(e){let t=e.clipboardData.items[0];if(0===t.type.indexOf("image")){var i=new FileReader;i.onload=function(e){let t=e.target.result.split(","),i=t[0].indexOf("base64")>=0?atob(t[1]):decodeURI(t[1]),s=new Uint8Array(i.length);for(let e=0;e<i.length;e++)s[e]=i.charCodeAt(e);let a=new FormData;a.append("file",new Blob([s],{type:t[0].split(":")[1].split(";")[0]}),"image_print.jpg"),MCF.upload(a,function(e){MCChat.uploadResponse(e)})},i.readAsDataURL(t.getAsFile())}};let u=[o("Please go to Settings > Miscellaneous and enter the Envato Purchase Code of Masi Chat."),`${o("Your license key is expired. Please purchase a new license")} <a href="https://masichat.com/shop/{R}" target="_blank">${o("here")}</a>.`];e(x).on("click",".mc-version",function(){let e=k.find(".mc-updates-box");MCF.ajax({function:"get-versions"},t=>{let i="",s={mc:"Masi Chat",slack:"Slack",dialogflow:"Artificial Intelligence",tickets:"Tickets",woocommerce:"Woocommerce",ump:"Ultimate Membership Pro",perfex:"Perfex",whmcs:"WHMCS",aecommerce:"Active eCommerce",messenger:"Messenger",whatsapp:"WhatsApp",armember:"ARMember",telegram:"Telegram",viber:"Viber",line:"LINE",wechat:"WeChat",zalo:"Zalo",twitter:"Twitter",zendesk:"Zendesk",martfury:"Martfury",opencart:"OpenCart",zalo:"Zalo"},a=!1;for(var r in t)if(st.is(r)){let e=MC_VERSIONS[r]==t[r];e||(a=!0),i+=`<div class="mc-input"><span>${s[r]}</span><div${e?' class="mc-green"':""}>${o(e?"You are running the latest version.":"Update available! Please update now.")} ${o("Your version is")} V ${MC_VERSIONS[r]}.</div></div>`}a?e.find(".mc-update").removeClass("mc-hide"):e.find(".mc-update").addClass("mc-hide"),n(!1),e.find(".mc-main").prepend(i),e.mcShowLightbox()}),n(),e.mcActive(!1),e.find(".mc-input").remove()}),e(k).on("click",".mc-updates-box .mc-update",function(){if(a(this))return;let i=k.find(".mc-updates-box");MCF.ajax({function:"update",domain:MC_URL},s=>{let a="";if(MCF.errorValidation(s,"envato-purchase-code-not-found"))a=u[0];else if(MCF.errorValidation(s))a=MCF.slugToString(s[1]);else{let e=!0;for(var n in s)if("success"!=s[n]){e=!1,"expired"==s[n]&&(a=u[1].replace("{R}",n)),"license-key-not-found"==s[n]&&(a=o("License key for the {R} app missing. Add it in Settings > Apps.").replace("{R}",MCF.slugToString(n.replace("dialogflow","Artificial Intelligence"))));break}e||a||(a=JSON.stringify(s))}r(),a?MCForm.showErrorMessage(i,a):(t("Update completed."),location.reload()),e(this).mcLoading(!1)})}),setTimeout(function(){let e=MCF.storage("last-update-check"),i=[Ye.getMonth(),Ye.getDate()];MC_ADMIN_SETTINGS.cloud||(0==e||i[0]!=e[0]||i[1]>e[1]+10)&&(MCF.storage("last-update-check",i),MC_ADMIN_SETTINGS.auto_updates?MCF.ajax({function:"update",domain:MC_URL},e=>{h(e)||Array.isArray(e)||(t("Automatic update completed. Reload the admin area to apply the update."),r())}):"admin"==MC_ACTIVE_AGENT.user_type&&MCF.ajax({function:"updates-available"},e=>{!0===e&&t(`${o("Update available.")} <span onclick="$('.mc-version').click()">${o("Click here to update now")}</span>`,"info")}))},1e3),e(k).on("click",".mc-apps > div:not(.mc-disabled)",function(){let t=k.find(".mc-app-box"),i=e(this).data("app"),s=MC_ADMIN_SETTINGS.cloud,a=st.is(i)&&(!s||MC_CLOUD_ACTIVE_APPS.includes(i)),n="?utm_source=plugin&utm_medium=admin_area&utm_campaign=plugin";s||MCF.ajax({function:"app-get-key",app_name:i},e=>{t.find("input").val(e)}),t.setClass("mc-active-app",a),t.find("input").val(""),t.find(".mc-top-bar > div:first-child").html(e(this).find("h2").html()),t.find("p").html(e(this).find("p").html()),t.attr("data-app",i),t.find(".mc-btn-app-setting").mcActive(a),t.find(".mc-btn-app-puchase").attr("href","https://masichat.com/shop/"+i+n),t.find(".mc-btn-app-details").attr("href",(s?WEBSITE_URL:"https://masichat.com/")+i+n),t.mcShowLightbox()}),e(k).on("click",".mc-app-box .mc-activate",function(){let i=k.find(".mc-app-box"),s=i.find("input").val(),n=i.attr("data-app");if(s||MC_ADMIN_SETTINGS.cloud){if(a(this))return;MCF.ajax({function:"app-activation",app_name:n,key:s},a=>{if(MCF.errorValidation(a)){let t="";t="envato-purchase-code-not-found"==(a=a[1])?u[0]:"invalid-key"==a?"It looks like your license key is invalid. If you believe this is an error, please contact support.":"expired"==a?u[1].replace("{R}",s):"app-purchase-code-limit-exceeded"==a?MCF.slugToString(n)+" app purchase code limit exceeded.":"Error: "+a,MCForm.showErrorMessage(i,t),e(this).mcLoading(!1)}else t("Activation complete! Page reload in progress..."),setTimeout(function(){location.reload()},1e3)})}else MCForm.showErrorMessage(i,"Please insert the license key.")}),e(k).on("click",".mc-app-box .mc-btn-app-setting",function(){H.find("#tab-"+e(this).closest("[data-app]").attr("data-app")).click(),k.mcHideLightbox()}),typeof Notification===Ke||"all"!=MC_ADMIN_SETTINGS.desktop_notifications&&"agents"!=MC_ADMIN_SETTINGS.desktop_notifications||MC_ADMIN_SETTINGS.push_notifications||(lt.desktop_notifications=!0),["all","agents"].includes(MC_ADMIN_SETTINGS.flash_notifications)&&(lt.flash_notifications=!0),Ye.getDate()!=MCF.storage("admin-clean")&&setTimeout(function(){MCF.ajax({function:"cron-jobs"}),MCF.storage("admin-clean",Ye.getDate())},1e4),e(k).on("click",".mc-collapse-btn",function(){let t=e(this).mcActive(),i=t?e(this).parent().data("height")+"px":"";e(this).html(o(t?"View more":"Close")),e(this).parent().find("> div, > ul").css({height:i,"max-height":i}),e(this).mcActive(!t)}),e(k).on("click",".mc-popup-close",function(){k.mcHideLightbox()}),We?(I.find("> .mc-scroll-area").prepend('<div class="mc-profile"><img><span class="mc-name"></span></div>'),e(k).on("click",".mc-menu-mobile > i",function(){e(this).toggleClass("mc-active"),dt.open_popup=e(this).parent()}),e(k).on("click",".mc-menu-mobile a",function(){e(this).closest(".mc-menu-mobile").find(" > i").mcActive(!1)}),e(k).on("click",".mc-menu-wide,.mc-nav",function(){e(this).toggleClass("mc-active")}),e(k).on("click",".mc-menu-wide > ul > li, .mc-nav > ul > li",function(t){let i=e(this).parent().parent();return i.find("li").mcActive(!1),i.find("> div:not(.mc-menu-wide):not(.mc-btn)").html(e(this).html()),i.mcActive(!1),i.find("> .mc-menu-wide").length&&i.closest(".mc-scroll-area").scrollTop(i.next()[0].offsetTop-(k.hasClass("mc-header-hidden")?70:130)),t.preventDefault(),!1}),e(k).find(".mc-admin-list .mc-scroll-area, main > div > .mc-scroll-area,.mc-area-settings > .mc-tab > .mc-scroll-area,.mc-area-reports > .mc-tab > .mc-scroll-area").on("scroll",function(){let t=e(this).scrollTop();ze.last<t-10&&ze.header?(k.addClass("mc-header-hidden"),ze.header=!1):ze.last>t+10&&!ze.header&&!ze.always_hidden&&(k.removeClass("mc-header-hidden"),ze.header=!0),ze.last=t}),e(k).on("click",".mc-search-btn i,.mc-filter-btn i",function(){e(this).parent().mcActive()?(k.addClass("mc-header-hidden"),ze.always_hidden=!0):(ze.always_hidden=!1,C.parent().scrollTop()<10&&k.removeClass("mc-header-hidden"))}),e(k).on("click",".mc-top .mc-btn-back",function(){lt.mobileCloseConversation()}),e(U).find("th:first-child").html(o("Order by")),e(U).on("click","th:first-child",function(){e(this).parent().toggleClass("mc-active")}),document.addEventListener("touchstart",e=>{xe=e.changedTouches[0].clientX,Be=e.changedTouches[0].clientY},!1),document.addEventListener("touchend",()=>{S()},!1),document.addEventListener("touchmove",e=>{var t=e.changedTouches[0].clientX,i=xe-t,a=e.changedTouches[0].clientY,n=Be-a;if(Math.abs(i)>Math.abs(n)){var o=[];ke=A.mcActive()?[C.find(".mc-active"),T,1]:[U.find(`[data-user-id="${s().id}"]`),O,2],i>150?(1==ke[2]?ke[0].next().click():o=ke[0].next(),S()):i<-150&&(1==ke[2]?ke[0].prev().click():o=ke[0].prev(),S()),2==ke[2]&&o.length&&(k.mcHideLightbox(),ct.show(o.attr("data-user-id"))),(i>80||i<-80)&&(ke[1].css("transform","translateX("+-1*i+"px)"),ke[1].addClass("mc-touchmove"))}},!1)):MC_ADMIN_SETTINGS.hide_conversation_details?A.find('.mc-menu-mobile [data-value="panel"]').mcActive(!0):I.mcActive(!0),e(window).width()<913&&e(A).on("click","> .mc-btn-collapse",function(){e(this).toggleClass("mc-active"),A.find(e(this).hasClass("mc-left")?".mc-admin-list":".mc-user-details").toggleClass("mc-active")}),e(x).on("click"," .mc-admin-nav a",function(){switch($e=e(this).attr("id").substr(3),dt.active_admin_area=$e,x.find(".mc-admin-nav a").mcActive(!1),k.find(" > main > div").mcActive(!1),k.find(".mc-area-"+$e).mcActive(!0),e(this).mcActive(!0),MCF.deactivateAll(),$e){case"conversations":We||MCF.getURL("conversation")||lt.clickFirst(),lt.update(),lt.startRealTime(),rt.stopRealTime();break;case"users":rt.startRealTime(),lt.stopRealTime(),rt.init||(n(),je=1,Ge=1,rt.get(e=>{rt.populate(e),rt.updateMenu(),rt.init=!0,rt.datetime_last_user=MCF.dateDB("now"),n(!1)}));break;case"settings":at.init||(n(),MCF.ajax({function:"get-all-settings"},i=>{if(i){let e=i["external-settings-translations"];if(i["slack-agents"]){let e="";for(var s in i["slack-agents"][0])e+=`<div data-id="${s}"><select><option value="${i["slack-agents"][0][s]}"></option></select></div>`;H.find("#slack-agents .input").html(e)}at.translations.translations=Array.isArray(e)&&!e.length?{}:e,delete i["external-settings-translations"];for(var s in i)at.set(s,i[s])}MCF.getURL("refresh_token")&&(k.find("#google-refresh-token input").val(MCF.getURL("refresh_token")),at.save(),t("Synchronization completed."),k.find("#google")[0].scrollIntoView()),H.find("textarea").each(function(){e(this).autoExpandTextarea(),e(this).manualExpandTextarea()}),H.find("[data-setting] .mc-language-switcher-cnt").each(function(){e(this).mcLanguageSwitcher(at.translations.getLanguageCodes(e(this).closest("[data-setting]").attr("id")),"settings")}),at.init=!0,n(!1),i&&!MC_ADMIN_SETTINGS.cloud&&at.visibility(0,i["push-notifications"]&&"pusher"==i["push-notifications"][0]["push-notifications-provider"][0]),at.visibility(1,!i.messenger||"manual"!=i.messenger[0]["messenger-sync-mode"][0]),at.visibility(2,!i["open-ai"]||"assistant"!=i["open-ai"][0]["open-ai-mode"][0]),MCF.event("MCSettingsLoaded",i)})),rt.stopRealTime(),lt.stopRealTime();break;case"reports":Te.mcLoading()&&e.getScript(MC_URL+"/vendor/moment.min.js",()=>{e.getScript(MC_URL+"/vendor/daterangepicker.min.js",()=>{e.getScript(MC_URL+"/vendor/chart.min.js",()=>{ot.initDatePicker(),ot.initReport("conversations"),Te.mcLoading(!1)})})}),rt.stopRealTime(),lt.stopRealTime();break;case"articles":let i=X.find(".mc-menu-wide li").eq(0);X.mcLoading()?(i.mcActive(!0).next().mcActive(!1),MCF.ajax({function:"init-articles-admin"},e=>{nt.categories.list=e[1],nt.translations.list=e[2],nt.page_url=e[3],nt.is_url_rewrite=e[4],nt.cloud_chat_id=e[5],nt.populate(e[0]),nt.populate(e[1],!0),nt.categories.update(),X.mcLoading(!1)})):i.click(),rt.stopRealTime(),lt.stopRealTime();break;case"chatbot":ie.mcLoading()&&st.openAI.init(),st.openAI.troubleshoot()}MCF.getURL("area")!=$e&&("conversations"==$e&&!MCF.getURL("conversation")||"users"==$e&&!MCF.getURL("user")||"settings"==$e&&!MCF.getURL("setting")||"reports"==$e&&!MCF.getURL("report")||"articles"==$e&&!MCF.getURL("article")||"chatbot"==$e&&!MCF.getURL("chatbot"))&&f("?area="+$e)}),e(x).on("click",".mc-profile",function(){e(this).next().toggleClass("mc-active")}),e(x).on("click",'[data-value="logout"],.logout',function(){dt.is_logout=!0,MCF.ajax({function:"on-close"}),rt.stopRealTime(),lt.stopRealTime(),setTimeout(()=>{MCF.logout()},300)}),e(x).on("click",'[data-value="edit-profile"],.edit-profile',function(){n();let e=new MCUser({id:MC_ACTIVE_AGENT.id});e.update(()=>{s(e),A.find(".mc-board").addClass("mc-no-conversation"),C.find(".mc-active").mcActive(!1),ct.showEdit(e)})}),e(x).on("click",'[data-value="status"]',function(){let t=!e(this).hasClass("mc-online");rt.setActiveAgentStatus(t),Ue=t}),e(x).find(".mc-account").setProfile(MC_ACTIVE_AGENT.full_name,MC_ACTIVE_AGENT.profile_image),e(C).on("click","li",function(){17==Ae?e(this).mcActive(!e(this).mcActive()):(lt.openConversation(e(this).attr("data-conversation-id"),e(this).attr("data-user-id"),!1),MCF.deactivateAll())}),e(k).on("click",".mc-user-conversations li",function(){lt.openConversation(e(this).attr("data-conversation-id"),s().id,e(this).attr("data-conversation-status"))}),e(A).on("click",".mc-top ul a",function(){let t,n=lt.getSelectedConversations(),r=n.length,l=r>1?"All the selected conversations will be ":"The conversation will be ",c=(t,i)=>{let s=t.includes(".txt");e(this).mcLoading(!1),"email"==i&&actioninfoBottom(s?o("Transcript sent to user's email.")+' <a href="'+t+'" target="_blank">'+o("View transcript")+"</a>":"Transcript sending error: "+t,s?"":"error")};switch(e(this).attr("data-value")){case"inbox":t=1,l+="restored.";break;case"archive":l+="archived.",t=3;break;case"delete":l+="deleted.",t=4;break;case"empty-trash":t=5,l="All conversations in the trash (including their messages) will be deleted permanently.";break;case"transcript":let i=e(this).attr("data-action");"email"!=i||s()&&s().get("email")||(i=""),lt.transcript(n[0].id,n[0].user_id,i,e=>c(e,i)),a(this);break;case"read":t=1,l+="marked as read.";break;case"unread":t=2,l+="marked as unread.";break;case"panel":e([I,this]).toggleClass("mc-active")}t&&i(l,"alert",function(){let e=F.eq(0).find("p").attr("data-value"),i=n[r-1].id;for(var s=0;s<r;s++){let a=n[s];MCF.ajax({function:"update-conversation-status",conversation_id:a.id,status_code:t},()=>{let s=C.find(`[data-conversation-id="${a.id}"]`);if([0,3,4].includes(t))for(var n=0;n<Ce.length;n++)if(Ce[n].id==a.id){Ce[n].set("status_code",t);break}if(MC_ADMIN_SETTINGS.close_message&&3==t&&(MCF.ajax({function:"close-message",conversation_id:a.id,bot_id:MC_ADMIN_SETTINGS.bot_id}),MC_ADMIN_SETTINGS.close_message_transcript&&lt.transcript(a.id,a.user_id,"email",e=>c(e))),[0,1,2].includes(t)&&(s.attr("data-conversation-status",t),lt.updateMenu()),MCChat.conversation&&st.is("slack")&&[3,4].includes(t)&&MCF.ajax({function:"archive-slack-channels",conversation_user_id:MCChat.conversation.get("user_id")}),0==e&&[3,4].includes(t)||3==e&&[0,1,2,4].includes(t)||4==e&&4!=t){let e=!1;lt.updateMenu(),MCChat.conversation&&MCChat.conversation.id==a.id&&(e=s.prev(),MCChat.conversation=!1),s.remove(),a.id==i&&lt.clickFirst(e)}4==e&&5==t&&(C.find("li").remove(),lt.updateMenu(),lt.clickFirst())}),MCChat.conversation&&MCChat.conversation.id==a.id&&(MCChat.conversation.set("status_code",t),lt.setReadIcon(t))}})}),MCF.ajax({function:"saved-replies"},e=>{let t=`<p class="mc-no-results">${o("No saved replies found. Add new saved replies via Settings > Admin.")}</p>`;if(Array.isArray(e)&&e.length&&e[0]["reply-name"]){t="",Ne=e;for(var i=0;i<e.length;i++)t+=`<li><div>${e[i]["reply-name"]}</div><div>${e[i]["reply-text"].replace(/\\n/g,"\n")}</div></li>`}Fe.find(".mc-replies-list > ul").html(t).mcLoading(!1)}),e(A).on("click",".mc-btn-saved-replies",function(){Fe.mcTogglePopup(this),Fe.find(".mc-search-btn").mcActive(!0).find("input").get(0).focus()}),e(Fe).on("click",".mc-replies-list li",function(){MCChat.insertText(e(this).find("div:last-child").text().replace(/\\n/g,"\n")),MCF.deactivateAll(),k.removeClass("mc-popup-active")}),e(Fe).on("input",".mc-search-btn input",function(){v(e(this).val().toLowerCase())}),e(Fe).on("click",".mc-search-btn i",function(){MCF.searchClear(this,()=>{v("")})}),e(k).on("click",".mc-btn-open-ai",function(){if(!MCChat.conversation||a(this))return;let t=e(this).hasClass("mc-btn-open-ai-editor"),i=t?A.find(".mc-editor textarea"):M.find("textarea");st.openAI.rewrite(i.val(),s=>{e(this).mcLoading(!1),s[0]&&(i.val(t?"":s[1]),t&&MCChat.insertText(s[1]))})}),e($).find(".mc-scroll-area").on("scroll",function(){if(!Xe&&!lt.is_search&&d(this,!0)&&Me){let e=A.find(".mc-admin-list"),t=lt.filters();Xe=!0,e.append('<div class="mc-loading-global mc-loading"></div>'),MCF.ajax({function:"get-conversations",pagination:De,status_code:t[0],department:t[1],source:t[2],tag:t[3],agent_id:t[4]},t=>{if(setTimeout(()=>{Xe=!1},500),Me=t.length){let e="";for(var i=0;i<Me;i++){let s=new MCConversation([new MCMessage(t[i])],t[i]);e+=lt.getListCode(s),Ce.push(s)}De++,C.append(e)}e.find(" > .mc-loading").remove(),MCF.event("MCAdminConversationsLoaded",{conversations:t})})}}),e(document).on("MCMessageDeleted",function(){let e=MCChat.conversation.getLastMessage();0!=e?C.find("li.mc-active p").html(e.message):(C.find("li.mc-active").remove(),lt.clickFirst(),lt.scrollTo())}),e(document).on("MCMessageSent",function(s,a){let n=a.conversation_id,r=(w(n),o("Error. Message not sent to")),l=a.conversation,c=a.user;a.conversation_status_code&&lt.updateMenu(),st.messenger.check(l)&&st.messenger.send(c.getExtra("facebook-id").value,l.get("extra"),a.message,a.attachments,a.message_id,a.message_id,e=>{for(var t=0;t<e.length;t++)e[t]&&e[t].error&&i(r+" Messenger: "+e[t].error.message,"info",!1,"error-fb")}),st.whatsapp.check(l)&&st.whatsapp.send(st.whatsapp.activeUserPhone(c),a.message,a.attachments,l.get("extra"),e=>{(e.ErrorCode||e.meta&&!e.meta.success)&&i(r+" WhatsApp: "+("ErrorCode"in e?e.errorMessage:e.meta.developer_message),"info",!1,"error-wa")}),st.telegram.check(l)&&st.telegram.send(l.get("extra"),a.message,a.attachments,n,e=>{e&&e.ok||i(r+" Telegram: "+JSON.stringify(e),"info",!1,"error-tg")}),st.viber.check(l)&&st.viber.send(c.getExtra("viber-id").value,a.message,a.attachments,e=>{e&&"ok"==e.status_message||i(r+" Viber: "+JSON.stringify(e),"info",!1,"error-vb")}),st.zalo.check(l)&&st.zalo.send(c.getExtra("zalo-id").value,a.message,a.attachments,e=>{e&&e.error.error&&i(r+" Zalo: "+e.error.message?e.error.message:e.message,"info",!1,"error-za")}),st.twitter.check(l)&&st.twitter.send(c.getExtra("twitter-id").value,a.message,a.attachments,e=>{e&&!e.event?i(JSON.stringify(e),"info",!1,"error-tw"):a.attachments.length>1&&t("Only the first attachment was sent to Twitter.")}),st.line.check(l)&&st.line.send(c.getExtra("line-id").value,a.message,a.attachments,n,e=>{e.error&&i(r+" LINE: "+JSON.stringify(e),"info",!1,"error-ln")}),st.wechat.check(l)&&st.wechat.send(c.getExtra("wechat-id").value,a.message,a.attachments,e=>{e&&"ok"==e.errmsg||i(r+" WeChat: "+JSON.stringify(e),"info",!1,"error-wc")}),MC_ADMIN_SETTINGS.smart_reply&&R.html(""),MC_ADMIN_SETTINGS.assign_conversation_to_agent&&MCF.null(l.get("agent_id"))&&lt.assignAgent(n,MC_ACTIVE_AGENT.id,()=>{MCChat.conversation.id==n&&(MCChat.conversation.set("agent_id",MC_ACTIVE_AGENT.id),e(A).find("#conversation-agent > p").attr("data-value",MC_ACTIVE_AGENT.id).html(MC_ACTIVE_AGENT.full_name))})}),e(document).on("MCNewMessagesReceived",function(e,s){let a=s.messages;for(var n=0;n<a.length;n++){let e=a[n],r=e.payload(),l=MCF.isAgent(e.get("user_type"));if(setTimeout(function(){B.find(".mc-top .mc-status-typing").remove()},300),dt.must_translate){let t=B.find(`[data-id="${e.id}"]`),i=!!r["original-message"]&&r["original-message"];i?(t.replaceWith(e.getCode()),B.find(`[data-id="${e.id}"] .mc-menu`).prepend(`<li data-value="translation">${o("View translation")}</li>`),MC_ADMIN_SETTINGS.smart_reply&&st.dialogflow.smartReply(MCF.escape(i))):e.message&&st.dialogflow.translate([e.message],MC_ADMIN_SETTINGS.active_agent_language,i=>{i&&(e.payload("translation",i[0]),e.payload("translation-language",MC_ADMIN_SETTINGS.active_agent_language),t.replaceWith(e.getCode()),B.find(`[data-id="${e.id}"] .mc-menu`).prepend(`<li data-value="original">${o("View original message")}</li>`)),MC_ADMIN_SETTINGS.smart_reply&&st.dialogflow.smartReply(i[0]),C.find(`[data-conversation-id="${s.conversation_id}"] p`).html(i[0])},[e.id],MCChat.conversation.id)}else MC_ADMIN_SETTINGS.smart_reply&&st.dialogflow.smartReply(e.message);r&&(r.department&&lt.setActiveDepartment(r.department),r.agent&&lt.setActiveAgent(r.agent)),("ErrorCode"in r||r.errors&&r.errors.length)&&i("Error. Message not sent to WhatsApp. Error message: "+(r.ErrorCode?r.ErrorCode:r.errors[0].title)),"whatsapp-templates"in r&&t(`Message sent as text message.${"whatsapp-template-fallback"in r?" The user has been notified via WhatsApp Template notification.":""}`),"whatsapp-template-fallback"in r&&!("whatsapp-templates"in r)&&t("The user has been notified via WhatsApp Template notification."),l||MCChat.conversation.id!=s.conversation_id||MCChat.user_online||rt.setActiveUserStatus()}lt.update()}),e(document).on("MCNewConversationCreated",function(){lt.update()}),e(document).on("MCEmailSent",function(){t("The user has been notified by email.")}),e(document).on("MCSMSSent",function(){t("The user has been notified by text message.")}),e(document).on("MCNotificationsSent",function(e,i){t(`The user ${i.includes("cron")?"will be":"has been"} notified by email${i.includes("sms")?" and text message":""}.`)}),e(document).on("MCTyping",function(e,t){lt.typing(t)}),e($).on("input",".mc-search-btn input",function(){lt.search(this)}),e(A).on("click",".mc-admin-list .mc-search-btn i",function(){MCF.searchClear(this,()=>{lt.search(e(this).next())})}),e(F).on("click","li",function(t){let i=C.parent();if(a(i))return t.preventDefault(),!1;setTimeout(()=>{let t=lt.filters();De=1,Me=1,MCF.ajax({function:"get-conversations",status_code:t[0],department:t[1],source:t[2],tag:t[3],agent_id:t[4]},s=>{if(lt.populateList(s),B.attr("data-conversation-status",t[0]),s.length){if(!We){if(MCChat.conversation){let e=w(MCChat.conversation.id);e.length?e.mcActive(!0):t[0]==MCChat.conversation.status_code?C.prepend(lt.getListCode(MCChat.conversation)):lt.clickFirst()}else lt.clickFirst();lt.scrollTo()}}else A.find(".mc-board").addClass("mc-no-conversation"),MCChat.conversation=!1;e(this).closest(".mc-filter-btn").attr("data-badge",F.slice(1).toArray().reduce((t,i)=>t+!!e(i).find("li.mc-active").data("value"),0)),i.mcLoading(!1)})},100)}),e(A).on("click",".mc-user-details .mc-profile,.mc-top > a",function(){let e=C.find(".mc-active").attr("data-user-id");s().id!=e&&s(Re[e]),ct.show(s().id)}),e(k).on("click",".mc-profile-list-conversation li",function(){let a=e(this).find("label"),r=a.html();switch(e(this).attr("data-id")){case"location":i('<iframe src="https://maps.google.com/maps?q='+r.replace(", ","+")+'&output=embed"></iframe>',"map");break;case"timezone":MCF.getLocationTimeString(s().extra,e=>{n(!1),i(e)});break;case"current_url":window.open("//"+(MCF.null(a.attr("data-value"))?r:a.attr("data-value")));break;case"conversation-source":let c=r.toLowerCase();"whatsapp"==c&&s().getExtra("phone")?window.open("https://wa.me/"+st.whatsapp.activeUserPhone()):"facebook"==c?window.open("https://www.facebook.com/messages/t/"+MCChat.conversation.get("extra")):"instagram"==c?window.open("https://www.instagram.com/direct/inbox/"):"twitter"==c&&window.open("https://twitter.com/messages/");break;case"wp-id":window.open(window.location.href.substr(0,window.location.href.lastIndexOf("/"))+"/user-edit.php?user_id="+s().getExtra("wp-id").value);break;case"envato-purchase-code":n(),MCF.ajax({function:"envato",purchase_code:r},e=>{let s="";if(e&&e.item){e.name=e.item.name;for(var a in e)!h(e[a])&&isNaN(e[a])||(s+=`<b>${MCF.slugToString(a)}</b> ${e[a]} <br>`);n(!1),i(s,"info",!1,"mc-envato-box")}else t(MCF.slugToString(e))});break;case"email":case"cc":if(MCChat.conversation&&"em"==MCChat.conversation.get("source")){let e=MCChat.conversation.get("extra").split(","),t='<div data-type="repeater" class="mc-setting mc-type-repeater"><div class="input"><div class="mc-repeater">';for(var l=0;l<e.length;l++)t+=`<div class="repeater-item"><div><input data-id="cc" type="text" value="${e[l]}"></div><i class="mc-icon-close"></i></div>`;t+=`</div><div class="mc-btn mc-btn-white mc-repeater-add mc-icon"><i class="mc-icon-plus"></i>${o("Add new item")}</div></div></div>`,dt.genericPanel("cc","Manage CC",t,["Save changes"],"",!0)}}}),e(I).on("click",".mc-user-details-close",function(){A.find('.mc-menu-mobile [data-value="panel"]').click().mcActive(!0)}),e(M).on("click",'.mc-intent-add [data-value="add"]',function(){M.find("> div > .mc-type-text").last().after('<div class="mc-setting mc-type-text"><input type="text"></div>')}),e(M).on("click",'.mc-intent-add [data-value="previous"],.mc-intent-add [data-value="next"]',function(){let t=M.find(".mc-first input"),i=t.val(),s="next"==e(this).attr("data-value"),a=MCChat.conversation.getUserMessages(),n=a.length;for(var o=0;o<n;o++)if(a[o].message==i&&(s&&o<n-1||!s&&o>0)){o+=s?1:-1,t.val(a[o].message),M.attr("data-message-id",a[o].id),st.openAI.generateQuestions(a[o].message);break}}),e(M).on("click",".mc-send",function(){st.dialogflow.submitIntent(this)}),e(M).on("input",".mc-search-btn input",function(){st.dialogflow.searchIntents(e(this).val())}),e(M).on("click",".mc-search-btn i",function(){MCF.searchClear(this,()=>{st.dialogflow.searchIntents(e(this).val())})}),e(M).on("click","#mc-intent-preview",function(){st.dialogflow.previewIntentDialogflow(M.find("#mc-intents-select").val())}),e(M).on("click","#mc-qea-preview",function(){st.dialogflow.previewIntent(M.find("#mc-qea-select").val())}),e(M).on("change","#mc-intents-select",function(){let t=e(this).val();M.find(".mc-bot-response").css("opacity",t?.5:1).find("textarea").val(t?st.dialogflow.getIntent(t).messages[0].text.text[0]:st.dialogflow.original_response),M.find("#mc-train-chatbots").val(t?"dialogflow":"")}),e(M).on("change","#mc-qea-select",function(){let t=e(this).val();M.find(".mc-bot-response").setClass("mc-disabled",t).find("textarea").val(t?st.dialogflow.qea[t][1]:st.dialogflow.original_response),M.find("#mc-train-chatbots").val(t?"dialogflow":"")}),e(M).on("change","textarea",function(){clearTimeout(ge),ge=setTimeout(()=>{st.dialogflow.original_response=M.find("textarea").val()},500)}),e(M).on("change","#mc-train-chatbots",function(){M.find(".mc-type-text:not(.mc-first)").setClass("mc-hide","open-ai"==e(this).val())}),e(Ie).on("click","li",function(t){let s=e(this).parent().parent();return e(this).data("id")==s.find(" > p").attr("data-id")?(setTimeout(()=>{e(this).mcActive(!1)},100),!0):MCChat.conversation?(s.mcLoading()||i(`${o("All agents assigned to the new department will be notified. The new department will be")} ${e(this).html()}.`,"alert",()=>{let t=e(this).data("id");s.mcLoading(!0),lt.assignDepartment(MCChat.conversation.id,t,()=>{lt.setActiveDepartment(t),s.mcLoading(!1)})}),t.preventDefault(),!1):(e(this).parent().mcActive(!1),t.preventDefault(),!1)}),e(A).on("click","#conversation-agent li",function(t){let s=e(this).parent().parent(),a=e(this).data("id");return a==s.find(" > p").attr("data-value")||(MCChat.conversation?(s.mcLoading()||i(`${o("The new agent will be")} ${e(this).html()}.`,"alert",()=>{s.mcLoading(!0);lt.getSelectedConversations().forEach(e=>{lt.assignAgent(e.id,a,()=>{MCChat.conversation&&MCChat.conversation.id==e.id&&lt.setActiveAgent(a)})}),s.mcLoading(!1)}),t.preventDefault(),!1):(e(this).parent().mcActive(!1),t.preventDefault(),!1))}),N.on("click","> i,.mc-edit-note",function(t){let i=!!e(this).hasClass("mc-edit-note")&&e(this).closest("[data-id]");if(dt.genericPanel("notes",i?"Edit note":"Add new note",`<div class="mc-setting mc-type-textarea"><textarea${i?' data-id="'+i.attr("data-id")+'"':""} placeholder="${o("Write here your note...")}">${i?i.find(".mc-note-text").html().replace(/<a\s+href="([^"]*)".*?>(.*?)<\/a>/gi,"$1").replaceAll("<br>","\n"):""}</textarea></div>`,[[i?"Update note":"Add note",i?"check":"plus"]]),st.is("dialogflow")&&MC_ADMIN_SETTINGS.note_data_scrape){let e="";for(var s in MC_ADMIN_SETTINGS.note_data_scrape)e+=`<option value="${s}">${MC_ADMIN_SETTINGS.note_data_scrape[s]}</option>`;k.find("#mc-add-note").parent().prepend(`<div id="note-ai-scraping" class="mc-setting mc-type-select"><select><option value="">${o("Data scraping")}</option>${e}</select></div>`)}return t.preventDefault(),!1}),e(k).on("change","#note-ai-scraping select",function(){let i=e(this).val();i&&!a(e(this).parent())&&MCF.ajax({function:"data-scraping",conversation_id:MCChat.conversation.id,prompt_id:i},i=>{if(i&&i.error)return console.error(i),t(i.error.message,"error");e(this).parent().mcLoading(!1);let s=k.find(".mc-notes-box textarea");s.val((s.val()+"\n"+i).trim())})}),N.on("click",".mc-delete-note",function(){let t=e(this).parents().eq(1);lt.notes.delete(MCChat.conversation.id,t.attr("data-id"),e=>{!0===e?t.remove():MCF.error(e)})}),e(k).on("click","#mc-add-note, #mc-update-note",function(){let i=e(this).parent().parents().eq(1).find("textarea"),s=i.val(),n=i.attr("data-id");if(0==s.length)MCForm.showErrorMessage(k.find(".mc-notes-box"),"Please write something...");else{if(a(this))return;s=MCF.escape(s),lt.notes.add(MCChat.conversation.id,MC_ACTIVE_AGENT.id,MC_ACTIVE_AGENT.full_name,s,a=>{Number.isInteger(a)||!0===a?(e(this).mcLoading(!1),k.mcHideLightbox(),n&&N.find(`[data-id="${n}"]`).remove(),lt.notes.update([{id:n||a,conversation_id:MCChat.conversation.id,user_id:MC_ACTIVE_AGENT.id,name:MC_ACTIVE_AGENT.full_name,message:s}],!0),i.val(""),t(n?"Note successfully updated.":"New note successfully added.")):MCForm.showErrorMessage(a)},n)}}),E.on("click","> i",function(e){let t=lt.tags.getAll(MCChat.conversation.details.tags);MCChat.conversation.details.tags;return dt.genericPanel("tags","Manage tags",t?'<div class="mc-tags-cnt">'+t+"</div>":"<p>"+o("Add tags from Settings > Admin > Tags.")+"</p>",["Save tags"]),e.preventDefault(),!1}),e(k).on("click",".mc-tags-cnt > span",function(){e(this).toggleClass("mc-active")}),e(k).on("click","#mc-add-tag",function(){e('<input type="text">').insertBefore(this)}),e(k).on("click","#mc-save-tags",function(){if(a(this))return;let i=k.find(".mc-tags-box").find("span.mc-active").map(function(){return e(this).attr("data-value")}).toArray(),s=MCChat.conversation.id;MCF.ajax({function:"update-tags",conversation_id:s,tags:i},a=>{if(e(this).mcLoading(!1),!0===a&&(lt.tags.update(i),MCChat.conversation&&s==MCChat.conversation.id)){let t=lt.filters()[3],a=w(s);if(MCChat.conversation.set("tags",i),t&&!i.includes(t))a.remove(),lt.clickFirst();else if(MC_ADMIN_SETTINGS.tags_show){let t=a.find(".mc-tags-area"),s=lt.tags.codeLeft(i);t.length?t.replaceWith(s):e(s).insertAfter(a.find(".mc-name"))}}k.mcHideLightbox(),t(!0===a?"Tags have been successfully updated.":a)})}),e(R).on("click","span",function(){MCChat.insertText(e(this).text()),R.html("")}),e(R).on("mouseover","span",function(){ge=setTimeout(()=>{e(this).addClass("mc-suggestion-full")},2500)}),e(R).on("mouseout","span",function(){clearTimeout(ge),R.find("span").removeClass("mc-suggestion-full")}),e(A).on("click",".mc-list .mc-menu > li",function(){let t=e(this).closest("[data-id]"),i=t.attr("data-id"),s=MCChat.conversation.getMessage(i),a=s.get("user_type"),n=e(this).attr("data-value");switch(n){case"delete":MCChat.user_online?MCF.ajax({function:"update-message",message_id:i,message:"",attachments:[],payload:{event:"delete-message"}},()=>{MCChat.conversation.deleteMessage(i),t.remove()}):MCChat.deleteMessage(i);break;case"translation":case"original":let r="translation"==n,l=["translation","translation-language","original-message","original-message-language"],c=l.map(e=>s.payload(e)).concat(s.details.message);s.set("message",r?s.payload("translation")||s.get("message"):s.payload("original-message")||s.get("message")),l.forEach(e=>delete s.details.payload[e]),t.replaceWith(s.getCode().replace('mc-menu">',`mc-menu"><li data-value="${r?"original":"translation"}">${o(r?"View original message":"View translation")}</li>`)),l.forEach((e,t)=>s.payload(e,c[t])),s.details.message=c[4];break;case"bot":st.dialogflow.showCreateIntentBox(e(this).closest("[data-id]").attr("data-id"));break;case"reply":let d=MCF.isAgent(a),u=s.get("message");u||s.attachments.forEach(e=>{u+='<i class="mc-icon-clip"></i>'+e[0]}),A.find(".mc-editor [data-reply]").remove(),A.find(".mc-editor").prepend(`<div data-reply="${i}"${d?' class="mc-reply-color"':""}><span>${d&&k||!d&&!k?o("You"):s.get("full_name")}</span>${u}<i class="mc-icon-close" onclick="MCChat.cancelReply()"></i></div>`),T.addClass("mc-reply-active")}}),e(A).on("click",".mc-filter-btn i",function(){e(this).parent().toggleClass("mc-active")}),e(A).on("click",".mc-filter-star",function(){e(this).parent().find("li").mcActive(!1),e(this).parent().find(`li[data-value="${e(this).mcActive()?"":e(this).attr("data-value")}"]`).last().mcActive(!0).click(),e(this).toggleClass("mc-active")}),L.on("click","#mc-attachments-filter li",function(){let t=L.find("a:not(.mc-collapse-btn)"),i=e(this).attr("data-value");t.each(function(){e(this).setClass("mc-hide",i&&MCF.getFileType(e(this).attr("href"))!=i)}),l(L,160)}),e(k).on("click",".mc-cc-box #mc-save-changes",function(){let t=k.find(".mc-cc-box .repeater-item input").map(function(){return e(this).val()}).get().join(",");a(this),MCF.ajax({function:"update-conversation-extra",conversation_id:MCChat.conversation.id,extra:t||"NULL"},()=>{MCChat.conversation.set("extra",t),lt.cc(t.split(",")),e(this).mcLoading(!1),k.mcHideLightbox()})}),MCF.getURL("user")&&(x.find(".mc-admin-nav #mc-users").click(),setTimeout(()=>{ct.show(MCF.getURL("user"))},500)),e(U).on("click","th :checkbox",function(){U.find("td :checkbox").prop("checked",e(this).prop("checked"))}),e(U).on("click",":checkbox",function(){let e=G.find('[data-value="delete"]');U.find("td input:checked").length?e.removeAttr("style"):e.hide()}),e(P).on("click","li",function(){rt.filter(e(this).data("type"))}),e(q).on("click","li",function(){let t=q.closest(".mc-filter-btn");setTimeout(()=>{rt.get(e=>{rt.populate(e)}),t.attr("data-badge",q.toArray().reduce((t,i)=>t+!!e(i).find("li.mc-active").data("value"),0))},100),t.mcActive(!1)}),e(G).on("input",".mc-search-btn input",function(){rt.search(this)}),e(G).on("click",".mc-search-btn i",function(){MCF.searchClear(this,()=>{rt.search(e(this).next())})}),e(U).on("click","th:not(:first-child)",function(){let t=e(this).hasClass("mc-order-asc")?"DESC":"ASC";e(this).toggleClass("mc-order-asc"),e(this).siblings().mcActive(!1),e(this).mcActive(!0),rt.sort(e(this).data("field"),t)}),e(U).parent().on("scroll",function(){!Xe&&!rt.search_query&&d(this,!0)&&Ge&&(Xe=!0,G.append('<div class="mc-loading-global mc-loading mc-loading-pagination"></div>'),rt.get(e=>{if(setTimeout(()=>{Xe=!1},500),Ge=e.length){let i="";for(var t=0;t<Ge;t++){let s=new MCUser(e[t],e[t].extra);i+=rt.getRow(s),Re[s.id]=s}je++,U.find("tbody").append(i)}G.find(" > .mc-loading-pagination").remove()},!1,!0))}),e(V).on("click",".mc-delete",function(){if(MC_ACTIVE_AGENT.id==s().id)return t("You cannot delete yourself.","error");i("This user will be deleted permanently including all linked data, conversations, and messages.","alert",function(){rt.delete(s().id)})}),e(U).on("click","td:not(:first-child)",function(){ct.show(e(this).parent().attr("data-user-id"))}),e(O).on("click",".mc-top-bar .mc-edit",function(){ct.showEdit(s())}),e(G).on("click",".mc-new-user",function(){V.addClass("mc-user-new"),V.find(".mc-top-bar .mc-profile span").html(o("Add new user")),V.find(".mc-top-bar .mc-save").html(`<i class="mc-icon-check"></i>${o("Add user")}`),V.find("input,select,textara").removeClass("mc-error"),V.removeClass("mc-cloud-admin"),"admin"==MC_ACTIVE_AGENT.user_type&&V.find("#user_type").find("select").html(`<option value="user">${o("User")}</option><option value="agent">${o("Agent")}</option><option value="admin">${o("Admin")}</option>`),ct.clear(V),ct.boxClasses(V),ct.updateRequiredFields("user"),V.mcShowLightbox()}),e(V).on("click",".mc-save",function(){if(a(this))return;let i=!!V.hasClass("mc-user-new"),n=V.attr("data-user-id"),r=ct.getAll(V.find(".mc-details")),l=ct.getAll(V.find(".mc-additional-details"));return e.map(r,function(e,t){r[t]=e[0]}),ct.errors(V)?(ct.showErrorMessage(V,MCF.isAgent(V.find("#user_type :selected").val())?"First name, last name, password and a valid email are required. Minimum password length is 8 characters.":V.find("#password").val().length<8?"Minimum password length is 8 characters.":"First name is required."),void e(this).mcLoading(!1)):MC_ACTIVE_AGENT.id==s().id&&"agent"==r.user_type[0]&&"admin"==MC_ACTIVE_AGENT.user_type?(ct.showErrorMessage(V,"You cannot change your status from admin to agent."),void e(this).mcLoading(!1)):(r.user_type||(r.user_type="user"),void MCF.ajax({function:i?"add-user":"update-user",user_id:n,settings:r,settings_extra:l},a=>{if(MCF.errorValidation(a,"duplicate-email")||MCF.errorValidation(a,"duplicate-phone"))return ct.showErrorMessage(V,`This ${MCF.errorValidation(a,"duplicate-email")?"email":"phone number"} is already in use.`),void e(this).mcLoading(!1);i&&(n=a,s(new MCUser({id:n}))),s().update(()=>{Re[n]=s(),i?(ct.clear(V),rt.update()):(rt.updateRow(s()),A.mcActive()&&lt.updateUserDetails(),n==MC_ACTIVE_AGENT.id&&(MCF.loginCookie(a[1]),MC_ACTIVE_AGENT.full_name=s().name,MC_ACTIVE_AGENT.profile_image=s().image,x.find(".mc-account").setProfile())),i&&V.find(".mc-profile").setProfile(o("Add new user")),e(this).mcLoading(!1),i||k.mcHideLightbox(),t(i?"New user added":"User updated")}),MCF.event("MCUserUpdated",{new_user:i,user_id:n})}))}),e(V).on("change","#user_type",function(){let t=e(this).find("option:selected").val();ct.boxClasses(V,t),ct.updateRequiredFields(t)}),e(O).on("click",".mc-user-conversations li",function(){lt.open(e(this).attr("data-conversation-id"),e(this).find("[data-user-id]").attr("data-user-id"))}),e(O).on("click",".mc-start-conversation",function(){lt.open(-1,s().id),lt.openConversation(-1,s().id),We&&lt.mobileOpenConversation()}),e(O).on("click",".mc-top-bar [data-value]",function(){lt.showDirectMessageBox(e(this).attr("data-value"),[s().id])}),e(G).on("click",".mc-top-bar [data-value]",function(){let s=e(this).data("value"),a=rt.getSelected();switch(s){case"whatsapp":m(a);break;case"message":case"custom_email":case"sms":lt.showDirectMessageBox(s,a);break;case"csv":rt.csv();break;case"delete":if(a.includes(MC_ACTIVE_AGENT.id))return t("You cannot delete yourself.","error");i("All selected users will be deleted permanently including all linked data, conversations, and messages.","alert",()=>{rt.delete(a),e(this).hide(),U.find("th:first-child input").prop("checked",!1)})}}),e(k).on("click",".mc-send-direct-message",function(){let i=e(this).attr("data-type")?e(this).attr("data-type"):D.attr("data-type"),s="whatsapp"==i,n=s?k.find("#mc-whatsapp-send-template-box"):D,o=n.find(".mc-direct-message-subject input").val(),r=s?"":n.find("textarea").val(),l=n.find(".mc-direct-message-users").val().replace(/ /g,""),d=!1,u=!1,p=[],h=!1;if(s){let e=n.find("#mc-whatsapp-send-template-list");d=e.val(),u=e.find("option:selected").attr("data-languages"),h=e.find("option:selected").attr("data-phone-id"),p=["header","body","button"].map(e=>n.find(`#mc-whatsapp-send-template-${e}`).val())}if(MCForm.errors(n))MCForm.showErrorMessage(n,"Please complete the mandatory fields.");else{if(a(this))return;let s=[];if((r.includes("recipient_name")||p.join("").includes("recipient_name"))&&s.push("first_name","last_name"),(r.includes("recipient_email")||p.join("").includes("recipient_email"))&&s.push("email"),"message"==i)MCF.ajax({function:"direct-message",user_ids:l,message:r},a=>{e(this).mcLoading(!1);let d=MC_ADMIN_SETTINGS.notify_user_email,u=MC_ADMIN_SETTINGS.sms_active_users;if(MCF.errorValidation(a))return MCForm.showErrorMessage(n,"An error has occurred. Please make sure all user ids are correct.");(d||u)&&MCF.ajax({function:"get-users-with-details",user_ids:l,details:s.concat(d&&u?["email","phone"]:[d?"email":"phone"])},e=>{d&&e.email.length?c(e,"email",r,0,u?e.phone:[],"email",o):u&&e.phone.length?c(e,"phone",r,0,[],"sms"):k.mcHideLightbox()}),t(`${MCF.slugToString(i)} sent to all users.`)});else{let t="custom_email"==i?"email":"phone";MCF.ajax({function:"get-users-with-details",user_ids:l,details:s.concat([t])},s=>{if(!s[t].length)return e(this).mcLoading(!1),MCForm.showErrorMessage(n,"No users found.");c(s,t,r,0,[],i,o,d,p,u,h)})}}}),e(G).on("click",".mc-filter-btn > i",function(){e(this).parent().toggleClass("mc-active")}),MCF.getURL("setting")&&at.open(MCF.getURL("setting"),!0),e(H).on("click"," > .mc-tab > .mc-nav [id]",function(){let t=e(this).attr("id").substr(4);MCF.getURL("setting")!=t&&f("?setting="+t)}),e(k).on("click",'.mc-repeater-upload, [data-type="upload-image"] .image, [data-type="upload-file"] .mc-btn, #mc-chatbot-add-files, #mc-import-settings a, #mc-import-users a',function(){let s="";ue=this,"mc-chatbot-add-files"==e(this).attr("id")?(s=".pdf,.txt,.json,.csv",ie.find(".mc-pending").remove(),st.openAI.train.skip_files=[],pe=function(){let e=de.prop("files"),t="";for(var i=0;i<e.length;i++){let s=parseInt(e[i].size/1e3);t+=`<tr class="mc-pending" data-name="${e[i].name}"><td><input type="checkbox" /></td><td>${e[i].name}<label>${o("Pending")}</label></td><td>${s||1} KB</td><td><i class="mc-icon-delete"></i></td></tr>`}ie.append(t)}):"mc-import-settings"==e(this).parent().parent().attr("id")?(s=".json",he=(s=>{a(this)||(MCF.ajax({function:"import-settings",file_url:s},s=>{s?t("Settings saved. Reload to apply the changes."):i(s),e(this).mcLoading(!1)}),he=!1)})):"mc-import-users"==e(this).parent().parent().attr("id")?(s=".csv",he=(s=>{a(this)||(MCF.ajax({function:"import-users",file_url:s},s=>{s?t("Users imported successfully."):i(s),e(this).mcLoading(!1)}),he=!1)})):e(this).hasClass("image")?s=".png,.jpg,.jpeg,.gif,.webp":e(this).hasClass("mc-repeater-upload")&&(he=(t=>{let i=e(this).parent();i.find(".repeater-item:last-child input").val()&&at.repeater.add(this),i.find(".repeater-item:last-child input").val(t)})),de.attr("accept",s).prop("value","").click()}),e(H).on("click",'[data-type="upload-image"] .image > i',function(t){return MCF.ajax({function:"delete-file",path:e(this).parent().attr("data-value")}),e(this).parent().removeAttr("data-value").css("background-image",""),t.preventDefault(),!1}),e(k).on("click",".mc-repeater-add",function(){at.repeater.add(this)}),e(k).on("click",".repeater-item > i",function(){setTimeout(()=>{at.repeater.delete(this)},100)}),at.initColorPicker(),e(H).find('[data-type="color"]').focusout(function(){let t=e(this).find("input").val();"rgb(255, 255, 255)"==t&&["color-admin-1","color-1"].includes(e(this).attr("id"))&&(t=""),setTimeout(()=>{e(this).find("input").val(t),e(this).find(".color-preview").css("background-color",t)},300),at.set(e(this).attr("id"),[t,"color"])}),e(H).on("click",".mc-type-color .input i",function(t){e(this).parent().find("input").removeAttr("style").val("")}),e(H).on("click",".mc-color-palette span",function(){let t=e(this).mcActive();e(this).closest(".mc-repeater").find(".mc-active").mcActive(!1),e(this).mcActive(!t)}),e(H).on("click",".mc-color-palette ul li",function(){e(this).parent().parent().attr("data-value",e(this).data("value")).find("span").mcActive(!1)}),e(H).on("click",'[data-type="select-images"] .input > div',function(){e(this).siblings().mcActive(!1),e(this).mcActive(!0)}),e(H).on("click",".mc-select-checkbox-input",function(){e(this).toggleClass("mc-active")}),e(H).on("click",".mc-select-checkbox input",function(){let t=e(this).closest("[data-type]");t.find(".mc-select-checkbox-input").val(at.get(t)[1].join(", "))}),e(H).on("click",".mc-save-changes",function(){at.save(this)}),e(H).on("change",'#saved-replies [data-id="reply-name"], [data-id="rich-message-name"]',function(){e(this).val(e(this).val().replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-+/,"").replace(/-+$/,"").replace(/ /g,""))}),e(H).on("change",'#user-additional-fields [data-id="extra-field-name"]',function(){e(this).parent().next().find("input").val(MCF.stringToSlug(e(this).val()))}),e(H).on("click","#timetable-utc input",function(){e(this).val()||e(this).val(Ye.getTimezoneOffset()/60)}),e(H).on("click","#dialogflow-sync-btn a",function(e){let t="https://accounts.google.com/o/oauth2/auth?scope=https%3A%2F%2Fwww.googleapis.com/auth/dialogflow%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcloud-translation%20https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fcloud-language&response_type=code&access_type=offline&redirect_uri="+MC_URL+"/apps/dialogflow/functions.php&client_id={client_id}&prompt=consent";if(!MC_ADMIN_SETTINGS.cloud||"auto"!=H.find("#google-sync-mode select").val()){let s=H.find("#google-client-id input").val();return s&&H.find("#google-client-secret input").val()?window.open(t.replace("{client_id}",s)):i("Before continuing enter Client ID and Client secret. Check the docs for more details."),e.preventDefault(),!1}if(MC_ADMIN_SETTINGS.credits)return window.open(t.replace("{client_id}",MC_ADMIN_SETTINGS.google_client_id)),e.preventDefault(),!1}),e(H).on("click","#dialogflow-redirect-url-btn a",function(e){return i(`<pre>${MC_URL}/apps/dialogflow/functions.php</pre>`),e.preventDefault(),!1}),e(H).on("click","#dialogflow-saved-replies a",function(t){return i("","alert",()=>{a(this)||MCF.ajax({function:"dialogflow-saved-replies"},()=>{e(this).mcLoading(!1)})}),t.preventDefault(),!1}),e(H).on("click","#test-email-user a, #test-email-agent a",function(){let t=e(this).parent().find("input").val();t&&t.indexOf("@")>0&&!a(this)&&MCF.ajax({function:"send-test-email",to:t,email_type:"test-email-user"==e(this).parent().parent().attr("id")?"user":"agent"},t=>{i(!0===t?"The message has been sent.":t,"info"),e(this).mcLoading(!1)})}),e(H).on("click","#email-server-troubleshoot a",function(e){return H.find("#test-email-user input").val(MC_ACTIVE_AGENT.email).next().click()[0].scrollIntoView({behavior:"smooth"}),e.preventDefault(),!1}),e(H).on("click","#test-sms-user a, #test-sms-agent a",function(){let t=e(this).parent().find("input").val();t&&!a(this)&&MCF.ajax({function:"send-sms",message:"Hello World!",to:t},t=>{i(t&&["sent","queued"].includes(t.status)?"The message has been sent.":`<pre>${JSON.stringify(t)}</pre>`),e(this).mcLoading(!1)})}),e(H).on("click",".mc-timetable > div > div > div",function(){let t=e(this).closest(".mc-timetable"),i=e(this).mcActive();if(e(t).find(".mc-active").mcActive(!1),i)e(this).mcActive(!1).find(".mc-custom-select").remove();else{let i=e(t).find("> .mc-custom-select").html();e(t).find(" > div .mc-custom-select").remove(),e(this).append(`<div class="mc-custom-select">${i}</div>`).mcActive(!0)}}),e(H).on("click",".mc-timetable .mc-custom-select span",function(){let t=[e(this).html(),e(this).attr("data-value")];e(this).closest(".mc-timetable").find("> div > div > .mc-active").html(t[0]).attr("data-value",t[1]),e(this).parent().mcActive(!1)}),e(H).on("click","#system-requirements a",function(e){let t="";return MCF.ajax({function:"system-requirements"},e=>{for(var i in e)t+=`<div class="mc-input"><span>${o(MCF.slugToString(i))}</span><div${e[i]?' class="mc-green"':""}>${o(e[i]?"Success":"Error")}</div></div>`;n(!1),dt.genericPanel("requirements","System requirements",t)}),n(),e.preventDefault(),!1}),e(H).on("click","#mc-path a",function(e){return MCF.ajax({function:"path"},e=>{i(`<pre>${e}</pre>`)}),e.preventDefault(),!1}),e(H).on("click","#mc-url a",function(e){return i(`<pre>${MC_URL}</pre>`),e.preventDefault(),!1}),e(H).on("click","#delete-leads a",function(t){return e(this).mcLoading()||i("All leads, including all the linked conversations and messages, will be deleted permanently.","alert",()=>{e(this).mcLoading(!0),MCF.ajax({function:"delete-leads"},()=>{i("Leads and conversations successfully deleted."),e(this).mcLoading(!1)})}),t.preventDefault(),!1}),e(H).on("click","#mc-export-settings a",function(t){if(t.preventDefault(),!a(this))return MCF.ajax({function:"export-settings"},t=>{_(t,"mc-export-settings-close","Settings exported"),e(this).mcLoading(!1)}),!1}),e(k).on("click","#mc-export-settings-close .mc-close, #mc-export-users-close .mc-close, #mc-export-report-close .mc-close",function(){MCF.ajax({function:"delete-file",path:k.find(".mc-dialog-box p pre").html()})}),MC_ADMIN_SETTINGS.cloud||(e(H).on("change","#push-notifications-provider select",function(){let t="pusher"==e(this).val();at.visibility(0,t),MCF.ajax({function:"update-sw",url:t?"https://js.pusher.com/beams/service-worker.js":"https://cdn.onesignal.com/sdks/web/v16/OneSignalSDK.sw.js"})}),e(H).on("click","#push-notifications-sw-path a",function(e){let t=b(location.href.replace(location.host,"")).replace("admin.php","");return t.includes("?")&&(t=t.substring(0,t.indexOf("?"))),i("<pre>"+t+"</pre>"),e.preventDefault(),!1})),e(H).on("click","#push-notifications-btn a",function(e){return MC_ADMIN_SETTINGS.cloud||"onesignal"==H.find("#push-notifications-provider select").val()?typeof OneSignal!=Ke?OneSignal.Slidedown.promptPush({force:!0}):MCF.serviceWorker.initPushNotifications():Notification.requestPermission(),e.preventDefault(),!1}),e(H).on("input","#mc-search-settings",function(){let t=e(this).val().toLowerCase();MCF.search(t,()=>{let i="",s=H.find(".mc-search-dropdown-items");if(t.length>2){let s=H.find("> .mc-tab > .mc-nav li").map(function(){return e(this).text().trim()}).get();H.find(".mc-setting").each(function(){let a=e(this).attr("data-keywords"),n=e(this).attr("id");if(a&&a.includes(t)||n&&n.replaceAll("-","-").includes(t)||e(this).find(".mc-setting-content").text().toLowerCase().includes(t)){let t=e(this).parent().index();i+=`<div data-tab-index="${t}" data-setting="${e(this).attr("id")}">${s[t]} > ${e(this).find("h2").text()}</div>`}})}s.html(i),s.outerHeight()>e(window).height()-100?(s.css("max-height",e(window).height()-100),s.addClass("mc-scroll-area")):s.removeClass("mc-scroll-area")})}),e(H).on("click",".mc-search-dropdown-items div",function(){let t=e(this).attr("data-tab-index"),i=H.find("> .mc-tab > .mc-nav li").eq(t);i.click(),i.get(0).scrollIntoView(),H.find("#"+e(this).attr("data-setting"))[0].scrollIntoView()}),e(H).on("click","#slack-button a",()=>(window.open("https://masichat.com/synch/?service=slack&plugin_url="+MC_URL+g()),!1)),e(H).on("click","#slack-test a",function(t){if(!a(this))return MCF.ajax({function:"send-slack-message",user_id:!1,full_name:MC_ACTIVE_AGENT.full_name,profile_image:MC_ACTIVE_AGENT.profile_image,message:"Lorem ipsum dolor sit amete consectetur adipiscing elite incidido labore et dolore magna aliqua.",attachments:[["Example link",MC_URL+"/media/user.svg"],["Example link two",MC_URL+"/media/user.svg"]],channel:H.find("#slack-channel input").val()},t=>{i(MCF.errorValidation(t)?"slack-not-active"==t[1]?"Please first activate Slack, then save the settings and reload the admin area.":"Error. Response: "+JSON.stringify(t):"success"==t[0]?"Slack message successfully sent. Check your Slack app!":JSON.stringify(t)),e(this).mcLoading(!1)}),t.preventDefault(),!1}),e(H).on("click","#tab-slack",function(){let e=H.find("#slack-agents .input");e.html('<div class="mc-loading"></div>'),MCF.ajax({function:"slack-users"},t=>{let i="";if(MCF.errorValidation(t,"slack-token-not-found"))i=`<p>${o("Synchronize Slack and save changes before linking agents.")}</p>`;else{let e='<option value="-1"></option>';for(s=0;s<t.agents.length;s++)e+=`<option value="${t.agents[s].id}">${t.agents[s].name}</option>`;for(var s=0;s<t.slack_users.length;s++)i+=`<div data-id="${t.slack_users[s].id}"><label>${t.slack_users[s].name}</label><select>${e}</select></div>`}e.html(i),at.set("slack-agents",[t.saved,"double-select"])})}),e(H).on("click","#slack-archive-channels a",function(t){t.preventDefault(),a(this)||MCF.ajax({function:"archive-slack-channels"},t=>{!0===t&&i("Slack channels archived successfully!"),e(this).mcLoading(!1)})}),e(H).on("click","#slack-channel-ids a",function(t){t.preventDefault(),a(this)||MCF.ajax({function:"slack-channels",code:!0},t=>{i(t,"info",!1,"","",!0),e(this).mcLoading(!1)})}),e(H).on("click",'#whatsapp-twilio-btn a, #whatsapp-twilio-get-configuartion-btn a, #sms-btn a, #wechat-btn a, #twitter-callback a, #viber-webhook a, #zalo-webhook a, [data-id="line-webhook"], #messenger-path-btn a',function(t){let s=e(this).closest("[id]").attr("id"),a="";if(t.preventDefault(),"line"==s){if(!(a=e(this).closest(".repeater-item").find('[data-id="line-secret"]').val()))return;a="?line_secret="+a}return i(`<pre>${MC_URL+("sms-btn"==s?"/include/api.php":"/apps/"+(s.includes("-")?s.substring(0,s.indexOf("-")):s)+"/post.php")+a+g().replace("&",a?"&":"?")}</pre>`),!1}),e(H).on("click",'[data-id="telegram-numbers-button"], #viber-button a, #whatsapp-360-button a',function(t){let s,n={"telegram-button":["#telegram-token input","telegram-synchronization",["result",!0]],"viber-button":["#viber-token input","viber-synchronization",["status_message","ok"]],"whatsapp-360-button":["#whatsapp-360-key input","whatsapp-360-synchronization",["success",!0]]},o=e(this).parent().attr("id"),r=!1;if(o)s=H.find(n[o][0]).val().trim();else{o={"telegram-numbers-button":"telegram-button"}[e(this).attr("data-id")],s=e(this).closest(".repeater-item").find(`[data-id="${{"telegram-button":"telegram-numbers-token"}[o]}"]`).val().trim(),r=!0}return n=n[o],t.preventDefault(),!(!s||a(this))&&(MCF.ajax({function:n[1],token:s,cloud_token:g(),is_additional_number:r},t=>{i(n[2][0]in t&&t[n[2][0]]==n[2][1]?"Synchronization completed.":JSON.stringify(t)),e(this).mcLoading(!1)}),!1)}),e(H).on("click","#whatsapp-test-template a",function(t){t.preventDefault();let s=e(this).parent().find("input").val();if(s&&!a(this))return MCF.ajax({function:"whatsapp-send-template",to:s},t=>{i(t?"error"in t?t.error.message?t.error.message:t.error:"Message sent, check your WhatsApp!":t),e(this).mcLoading(!1)}),!1}),e(H).on("click","#twitter-subscribe a",function(t){return t.preventDefault(),!a(this)&&(MCF.ajax({function:"twitter-subscribe",cloud_token:g()},t=>{i(!0===t?"Synchronization completed.":JSON.stringify(t)),e(this).mcLoading(!1)}),!1)}),MC_ADMIN_SETTINGS.cloud||e(H).on("click","#messenger-sync-btn a",()=>(window.open("https://masichat.com/synch/?service=messenger&plugin_url="+MC_URL+g()),!1)),e(H).on("change","#messenger-sync-mode select",function(){at.visibility(1,"manual"!=e(this).val())}),e(H).on("click","#messenger-unsubscribe a",function(t){return t.preventDefault(),i("","alert",()=>{if(a(this))return!1;MCF.ajax({function:"messenger-unsubscribe"},t=>{e(this).mcLoading(!1),t?i(JSON.stringify(t)):(H.find("#messenger-pages .repeater-item > i").click(),setTimeout(()=>{at.save(),i("Operation successful.")},300))})}),!1}),e(H).on("change","#open-ai-mode select",function(){at.visibility(2,"assistant"!=e(this).val())}),e(H).on("click","#wp-sync a",function(t){return t.preventDefault(),!a(this)&&(st.wordpress.ajax("wp-sync",{},t=>{!0===t||"1"===t?(rt.update(),i("WordPress users successfully imported.")):i("Error. Response: "+JSON.stringify(t)),e(this).mcLoading(!1)}),!1)}),e("body").on("click","#wp-admin-bar-logout",function(){MCF.logout(!1)}),e(H).on("click","#whatsapp-clear-flows a",function(e){e.preventDefault(),MCF.ajax({function:"whatsapp-clear-flows"})}),e(H).on("click","#tab-translations",function(){let e=H.find(".mc-translations > .mc-nav > ul");if(!e.html()){let i="";for(var t in MC_LANGUAGE_CODES)"en"!=t&&(i+=`<li data-code="${t}"><img src="${MC_URL}/media/flags/${t}.png" />${MC_LANGUAGE_CODES[t]}</li>`);e.html(i)}}),e(H).on("click",".mc-translations .mc-nav li",function(){at.translations.load(e(this).data("code"))}),e(H).on("click",".mc-translations .mc-menu-wide li",function(){H.find(`.mc-translations [data-area="${e(this).data("value")}"]`).mcActive(!0).siblings().mcActive(!1)}),e(H).on("click",".mc-add-translation",function(){H.find(".mc-translations-list > .mc-active").prepend(`<div class="mc-setting mc-type-text mc-new-translation"><input type="text" placeholder="${o("Enter original text...")}"><input type="text" placeholder="${o("Enter translation...")}"></div></div>`)}),e(H).on("input",".mc-search-translation input",function(){let t=e(this).val().toLowerCase();MCF.search(t,()=>{t.length>1&&H.find(".mc-translations .mc-content > .mc-active label").each(function(){let i=e(this).html().toLowerCase();if(i.includes(t)&&i!=Se){let t=H.find(".mc-scroll-area");return t[0].scrollTop=0,t[0].scrollTop=e(this).position().top-80,Se=i,!1}})})}),e(H).on("click",'[data-id="email-piping-sync"]',function(t){a(this)||(MCF.ajax({function:"email-piping",force:!0},t=>{i(!0===t?"Syncronization completed.":t),e(this).mcLoading(!1)}),t.preventDefault())}),e(H).on("click","#tab-automations",function(){at.automations.get(()=>{at.automations.populate(),n(!1)},!0),n()}),e(k).on("click",".mc-add-condition",function(){at.automations.addCondition(e(this).prev())}),e(k).on("change",".mc-condition-1 select",function(){at.automations.updateCondition(this)}),e(k).on("change",".mc-condition-2 select",function(){e(this).parent().next().setClass("mc-hide",["is-set","is-not-set"].includes(e(this).val()))}),e(z).on("click","li",function(){at.automations.populate(e(this).data("value"))}),e(J).on("click","li",function(){at.automations.show(e(this).attr("data-id"))}),e(W).on("click",".mc-add-automation",function(){at.automations.add()}),e(J).on("click","li i",function(){i("The automation will be deleted permanently.","alert",()=>{at.automations.delete(this)})});let me=["Settings > {R} won't work if Settings > {R2} is active."];e(H).on("click",".mc-setting",function(i){let s=e(this).attr("id");switch(e(this).hasClass("mc-type-multi-input")&&(s=e(i.target).parent().attr("id")),s){case"close-chat":case"close-message":H.find("#close-active input").is(":checked")&&H.find("#close-chat input").is(":checked")&&t(me[0].replace("{R}","Messages > Close message").replace("{R2}","Chat > Close chat"),"info");break;case"chat-timetable":case"follow-message":case"queue":case"routing":("queue"==s&&H.find("#queue-active input").is(":checked")||"routing"==s&&H.find("#routing input").is(":checked")||"follow-message"==s&&H.find("#follow-active input").is(":checked")||"chat-timetable"==s&&H.find("#chat-timetable-active input").is(":checked"))&&H.find("#dialogflow-human-takeover-active input").is(":checked")&&t("Since Settings > Artificial Intelligence > Human takeover is active, this option will only take effect during human takeover.","info");break;case"notify-agent-email":case"push-notifications":case"sms":("sms"==s&&H.find("#sms-active-agents input").is(":checked")||"push-notifications"==s&&H.find("#push-notifications-active input").is(":checked")||"notify-agent-email"==s&&H.find("#notify-agent-email input").is(":checked"))&&H.find("#dialogflow-human-takeover-active input").is(":checked")&&t("Since Settings > Artificial Intelligence > Human takeover is active, notifications will be sent only after the human takeover.","info");break;case"privacy":H.find("#privacy-active input").is(":checked")&&H.find("#registration-required select").val()&&t(me[0].replace("{R}","Messages > Privacy message").replace("{R2}","Users > Require registration"),"info");break;case"google-multilingual-translation":e(i.target).is(":checked")&&H.find("#open-ai-active input").is(":checked")&&!H.find("#open-ai-training-data-language select").val()&&t("If your OpenAI training data isn't in English, set the default language under OpenAI > Training data language.","info");break;case"open-ai-prompt":t("Custom prompts may break the chatbot, we advise leaving it empty.","info");break;case"open-ai-tokens":case"open-ai-temperature":case"open-ai-presence-penalty":case"open-ai-frequency-penalty":case"open-ai-logit-bias":case"open-ai-custom-model":t("This is an advanced setting, incorrect values may break the chatbot. If you're unsure, leave it empty.","info");break;case"open-ai-user-train-conversations":e(i.target).is(":checked")&&t("This method is not recommended. See the docs for details.","info")}}),e(P).on("click",'[data-type="online"]',function(){MC_ADMIN_SETTINGS.visitors_registration||t("Settings > Users > Register all visitors must be enabled to see online users.","info")}),e(H).on("click","#dialogflow-active,#dialogflow-welcome,#dialogflow-departments",function(e){t("Warning! We will stop supporting Dialogflow by the end of 2025. All its features will be available through OpenAI. Please use OpenAI instead of Dialogflow.","info")}),e(H).on("change","#registration-required select",function(){let i=e(this).val();["registration-login"].includes(i)&&!H.find('[id="reg-email"] input').is(":checked")&&t("The email field is required to activate the login form.","info"),i&&H.find("#privacy-active input").is(":checked")&&t(me[0].replace("{R}","Messages > Privacy message").replace("{R2}","Users > Require registration"),"info")}),e(te).on("click","#mc-flow-add",function(){dt.genericPanel("flow-add","Enter the flow name",'<div class="mc-setting"><input type="text"></div>',["Add new flow"])}),e(k).on("click","#mc-add-new-flow",function(){st.openAI.flows.set(k.find(".mc-flow-add-box input").val().replace(/[^a-zA-Z\u00C0-\u1FFF\u2C00-\uD7FF\uAC00-\uD7A3]/g,"")),k.mcHideLightbox()}),e(le).on("click",".mc-flow-block",function(){le.find(".mc-flow-block,.mc-flow-add-block").mcActive(!1),e(this).mcActive(!0);let t,i=st.openAI.flows.blocks.get(),s="",a=e(this).attr("data-type"),n=`<div class="mc-title">{R}</div><div data-type="repeater" class="mc-setting mc-type-repeater"><div class="input"><div class="mc-repeater">{R2}</div><div class="mc-btn mc-btn-white mc-repeater-add mc-icon"><i class="mc-icon-plus"></i>${o("Add new item")}</div></div></div>`,r=`<div class="mc-title">${o("Conditions")}</div><div class="mc-flow-conditions"></div><div class="mc-add-condition mc-btn mc-icon mc-btn-white"><i class="mc-icon-plus"></i>${o("Add condition")}</div>`,l=`<div class="mc-title">${o("Message")}</div><div class="mc-setting"><textarea placeholder="${o("The message sent to the user...")}">${i.message}</textarea></div>`,c=st.openAI.getCode.select_user_details();switch(a){case"start":t=`<div class="mc-title">${o("Start event")}</div><div class="mc-setting"><select class="mc-flow-start-select"><option value="message"${"message"==i.start?" selected":""}>${o("User message")}</option><option value="conversation"${"conversation"==i.start?" selected":""}>${o("New conversation started")}</option><option value="load"${"load"==i.start?" selected":""}>${o("On page load")}</option></select></div><div class="mc-title mc-title-flow-start${"message"==i.start?"":" mc-hide"}">${o("User message")}</div><div data-type="repeater" class="mc-setting mc-flow-start-messages mc-type-repeater"><div class="input"><div class="mc-repeater"><div class="repeater-item"><div class="mc-setting"><textarea data-id="message"></textarea></div><i class="mc-icon-close"></i></div></div><div class="mc-btn mc-btn-white mc-repeater-add mc-icon"><i class="mc-icon-plus"></i>${o("Add message")}</div></div></div>${r}<div class="mc-title">${o("Disabled")}</div><div class="mc-setting"><input type="checkbox" id="mc-flow-disabled"${i.disabled?" checked":""}></div>`;break;case"button_list":i.options&&i.options.length||(i.options=[""]);for(d=0;d<i.options.length;d++)s+=`<div class="repeater-item"><div><input data-id type="text" value="${i.options[d]}"></div><i class="mc-icon-close"></i></div>`;t=l+n.replace("{R}",o("Buttons")).replace("{R2}",s);break;case"message":i.attachments&&i.attachments.length||(i.attachments=[""]);for(d=0;d<i.attachments.length;d++)s+=`<div class="repeater-item"><div><input data-id type="text" value="${i.attachments[d]}" placeholder="${o("Enter a link...")}"></div><i class="mc-icon-close"></i></div>`;t=l+n.replace("{R}",o("Attachments")).replace("{R2}",s).replace("</div></div></div>",'</div><i class="mc-repeater-upload mc-btn-icon mc-icon-clip"></i></div></div>');break;case"video":t=`${l}<div class="mc-title">${o("Video URL")}</div><div class="mc-setting"><input type="url" placeholder="${o("Enter a YouTube or Vimeo link...")}" value="${i.url}"></div>`;break;case"get_user_details":i.details&&i.details.length||(i.details=[["","",!1]]);for(d=0;d<i.details.length;d++)s+=`<div class="repeater-item"><div>${c.replace(`"${i.details[d][0]}"`,`"${i.details[d][0]}" selected`)}<div class="mc-setting"><input type="text" placeholder="${o("Enter a description...")}" value="${i.details[d][1]}" /></div><div class="mc-setting"><label>${o("Required")}</label><input type="checkbox" ${i.details[d][2]?" checked":""}></div></div><i class="mc-icon-close"></i></div>`;t=l+n.replace("{R}",o("User details")).replace("{R2}",s).replace("mc-type-repeater","mc-type-repeater mc-repeater-block-user-details");break;case"set_data":t=st.openAI.getCode.set_data(i.data);break;case"action":t=st.openAI.getCode.actions(i.actions);break;case"rest_api":let e=["headers","save_response"];t=`<div class="mc-title">${o("URL")}</div><div class="mc-setting"><input type="url" class="mc-rest-api-url" value="${i.url}"></div><div class="mc-title">${o("Method")}</div><div class="mc-setting"><select class="mc-rest-api-method"><option value="GET"${"GET"==i.method?" selected":""}>GET</option><option value="POST"${"POST"==i.method?" selected":""}>POST</option><option value="PUT"${"PUT"==i.method?" selected":""}>PUT</option><option value="PATH"${"PATH"==i.method?" selected":""}>PATH</option><option value="DELETE"${"DELETE"==i.method?" selected":""}>DELETE</option></select></div><div class="mc-title">${o("Body")}</div><div class="mc-setting"><textarea placeholder="JSON">${i.body}</textarea></div>`;for(var d=0;d<e.length;d++){let s=i[e[d]];s&&s.length||(s=[["",""]]),t+=`<div class="mc-title">${o(MCF.slugToString(e[d]))}</div><div data-type="repeater" class="mc-setting mc-type-repeater mc-repeater-block-rest-api mc-rest-api-${e[d]}"><div class="input"><div class="mc-repeater">`;for(var u=0;u<s.length;u++)t+=`<div class="repeater-item"><div>${1==d?c.replace(`"${s[u][0]}"`,`"${s[u][0]}" selected`):`<div class="mc-setting"><input type="text" placeholder="${o("Key")}" value="${s[u][0]}" /></div>`}<div class="mc-setting"><input type="text" placeholder="${o(1==d?"e.g. data.id":"Value")}" value="${s[u][1]}" /></div></div><i class="mc-icon-close"></i></div>`;t+=`</div><div class="mc-btn mc-btn-white mc-repeater-add mc-icon"><i class="mc-icon-plus"></i>${o("Add new item")}</div></div></div>`}break;case"condition":t=r}if(t+=`<div id="mc-block-delete" class="mc-btn-text"><i class="mc-icon-delete"></i>${o("Delete")}</div>`,dt.genericPanel("flow-block",MCF.slugToString(a),t,["Save changes"],"",!0),"start"==a||"condition"==a){Array.isArray(i.message)||(i.message=[{message:i.message}]);let e=k.find(".mc-flow-start-messages .mc-repeater"),t=at.repeater.set(i.message,e.find(".repeater-item:last-child"));at.automations.setConditions(i.conditions,k.find(".mc-flow-conditions")),t&&e.html(t)}He=[],le.find("> div").each(function(){He.push(e(this).find("> div")[0].scrollTop)})}),e(k).on("click",".mc-flow-block-box #mc-save-changes",function(){let t=st.openAI.flows.blocks.get(),i=k.find(".mc-flow-block-box");switch(t.message=i.find("textarea").val(),t.type){case"start":if(t.message=at.repeater.get(i.find(".mc-flow-start-messages .repeater-item")),t.start=i.find("select").val(),t.disabled=i.find("#mc-flow-disabled").is(":checked"),t.conditions=at.automations.getConditions(i.find(".mc-flow-conditions")),t.message.length&&t.message[0].message.trim().split(" ").length<3)return i.find(".mc-info").mcActive(!0).html(o("The message must contain at least 3 words."));break;case"button_list":t.options=i.find(".mc-repeater input").map(function(){return e(this).val().trim()}).get().filter(function(e){return""!=e});break;case"message":t.attachments=i.find(".mc-repeater input").map(function(){return e(this).val().trim()}).get().filter(function(e){return""!=e});break;case"video":t.url=i.find("input").val();break;case"get_user_details":t.details=i.find(".repeater-item").map(function(){return[[e(this).find("select").val(),e(this).find("input[type=text]").val(),e(this).find("input[type=checkbox]").is(":checked")]]}).get();break;case"action":case"set_data":t["action"==t.type?"actions":"data"]=i.find(".repeater-item").map(function(){return[[e(this).find("select").val(),e(this).find("input").length?e(this).find("input").val().replace(/https?:\/\/|["|:]/g,""):""]]}).get();break;case"rest_api":t.headers=i.find(".mc-rest-api-headers .repeater-item").map(function(){return[[e(this).find("input").eq(0).val(),e(this).find("input").eq(1).val()]]}).get(),t.save_response=i.find(".mc-rest-api-save_response .repeater-item").map(function(){return[[e(this).find("select").val(),e(this).find("input").val()]]}).get(),t.url=i.find(".mc-rest-api-url").val(),t.method=i.find(".mc-rest-api-method").val(),t.body=i.find("textarea").val(),delete t.message;break;case"condition":t.conditions=at.automations.getConditions(i.find(".mc-flow-conditions"))}st.openAI.flows.blocks.set(t),le.find("> div").each(function(){e(this).find("> div")[0].scrollTop=He[e(this).index()]}),k.mcHideLightbox()}),e(k).on("change",".mc-repeater-block-actions select",function(){e(this).parent().next().remove(),e(this).parent().parent().append(st.openAI.getCode.action(e(this).val(),""))}),e(k).on("change",".mc-flow-start-select",function(){k.find(".mc-title-flow-start, .mc-flow-start-messages").setClass("mc-hide","message"!=e(this).val())}),e(le).on("mouseleave",".mc-flow-connectors > div, .mc-flow-block",function(){le.find(".mc-flow-block-cnt").mcActive(!1),Oe=!1,e(this).parent().hasClass("mc-flow-connectors")?st.openAI.flows.blocks.activateLinkedCnts(e(this).closest(".mc-flow-block")):le.find(".mc-flow-connectors > div").mcActive(!1)}),e(le).on("mouseenter",".mc-flow-connectors > div",function(){let t=e(this).closest(".mc-flow-block").parent(),i=st.openAI.flows.blocks.getNextCntIndexes(st.openAI.flows.getActiveIndex(),t.parent().parent().index(),t.index());Oe=!0,le.find("> div").eq(t.parent().parent().index()+1).find(".mc-flow-block-cnt").mcActive(!1).eq(i[e(this).index()]).mcActive(!0)}),e(le).on("mouseenter",".mc-flow-block",function(){st.openAI.flows.blocks.activateLinkedCnts(this)}),e(le).on("click",".mc-flow-add-block",function(){le.find(".mc-flow-block,.mc-flow-add-block").mcActive(!1),e(this).mcActive(!0);let t=st.openAI.flows.steps.get()[st.openAI.flows.blocks.getActiveCntIndex()].map(e=>e.type),i=!t.some(e=>["message","button_list","video","get_user_details","condition"].includes(e)),s=[["set_data","Set data"],["action","Action"],["condition","Condition"],["rest_api","REST API"]],a="";for(var n=0;n<s.length;n++)t.includes(s[n][0])||(a+=`<li data-value="${s[n][0]}">${o(s[n][1])}</li>`);dt.genericPanel("flows-blocks-nav","",`<ul class="mc-menu">${i?`<li>${o("Messages")} <ul><li data-value="message">${o("Send message")}</li><li data-value="button_list">${o("Send button list")}</li><li data-value="video">${o("Send video")}</li></ul></li>`:""}<li>${o("More")} <ul>${i?`<li data-value="get_user_details">${o("Get user details")}</li>`:""}${a}</ul></li></ul>`)}),e(k).on("click","#mc-block-delete",function(){st.openAI.flows.blocks.delete(),k.mcHideLightbox()}),e(re).on("click","li",function(){st.openAI.flows.show(e(this).attr("data-value"))}),e(re).on("click","li i",function(t){return i("The flow will be deleted.","alert",()=>{st.openAI.flows.delete(e(this).parent().attr("data-value"))}),t.preventDefault(),!1}),e(k).on("click",".mc-flows-blocks-nav-box [data-value]",function(){st.openAI.flows.blocks.add(e(this).data("value")),k.mcHideLightbox()}),e(k).on("mouseenter",".mc-flow-scroll",function(){let t=e(this).hasClass("mc-icon-arrow-left");ce=setInterval(()=>{le[0].scrollLeft+=10*(t?-1:1)},10)}),e(k).on("mouseleave",".mc-flow-scroll",function(){clearInterval(ce)}),e(te).on("click","#mc-train-chatbot",function(t){let s="The chatbot has been successfully trained.",n=te.find('.mc-nav [data-value="conversations"]');if(i("<br><br><br><br><br>","info",!1,"mc-embeddings-box"),t.preventDefault(),MC_ADMIN_SETTINGS.cloud&&MCCloud.creditsAlert(this,t))return!1;if(a(this))return!1;if("flows"==te.find(".mc-menu-chatbot .mc-active").attr("data-type"))return st.openAI.flows.save(e=>{i(s,"info",!1,!1,"Success")}),void e(this).mcLoading(!1);if(n.mcActive()){let t=[];for(var r=0;r<Ve.length;r++){let i=te.find(`#mc-chatbot-conversations [data-index="${r}"]`);i.length?(i=[i.find("input").val(),i.find("textarea").val()])[0]==e("<textarea />").html(Ve[r].question).text()&&i[1]==e("<textarea />").html(Ve[r].answer).text()||(Ve[r].question=i[0],Ve[r].answer=i[1],t.push(Ve[r])):(Ve[r].question=!1,Ve[r].answer=!1,t.push(Ve[r]))}return void(t.length?MCF.ajax({function:"open-ai-save-conversation-embeddings",qea:t},t=>{!0===t[0]?i(s,"info",!1,!1,"Success"):st.openAI.train.isError(t[0])||i(t[0]),n.click(),e(this).mcLoading(!1)}):(e(this).mcLoading(!1),i(s,"info",!1,!1,"Success")))}st.openAI.train.errors=[];return st.openAI.train.files(t=>{st.openAI.train.urls=te.find('[data-id="open-ai-sources-url"]').map(function(){return e(this).val().trim()}).get(),st.openAI.train.extract_url=te.find('[data-id="open-ai-sources-extract-url"]').map(function(){return e(this).is(":checked")}).get(),st.openAI.train.website(t=>{st.openAI.train.qea(t=>{st.openAI.train.articles(t=>{st.openAI.init(),te.find("#mc-repeater-chatbot-website .repeater-item i").click(),st.openAI.train.errors.length?(i(o("The chatbot has been trained with errors. Check the console for more details.")+"\n\n<pre>"+st.openAI.train.errors.join("<br>").replaceAll("false,","")+"</pre>","info",!1,"mc-errors-list-box",!1,!0),console.error(st.openAI.train.errors)):st.openAI.train.isError(t)||i(s,"info",!1,!1,"Success"),e(this).mcLoading(!1)})})})}),!1}),e(te).on("click","#mc-table-chatbot-files td i, #mc-table-chatbot-website td i, #mc-chatbot-delete-files, #mc-chatbot-delete-website, #mc-chatbot-delete-all-training, #mc-chatbot-delete-all-training-conversations",function(){let s=e(this).is("i"),n=!!s&&e(this).closest("tr");return s&&n.hasClass("mc-pending")?(st.openAI.train.skip_files.push(n.attr("data-name")),void n.remove()):(i("The training data will be permanently deleted.","alert",()=>{let o=[],r=e(this).attr("id");if(s)o=[n.attr("data-url")];else if("mc-chatbot-delete-all-training"==r)o="all";else if("mc-chatbot-delete-all-training-conversations"==r)o="all-conversations";else{let t=e("mc-chatbot-delete-files"==r?ie:se);t.find("input:checked").length||t.find("input").prop("checked",!0),t.find("tr").each(function(){if(e(this).find("input:checked").length)if(e(this).hasClass("mc-pending"))st.openAI.train.skip_files.push(e(this).attr("data-name")),e(this).remove();else{let t=e(this).attr("data-url");o.push(t),st.openAI.train.sitemap_processed_urls.indexOf(t)>-1&&(st.openAI.train.sitemap_processed_urls[st.openAI.train.sitemap_processed_urls.indexOf(t)]=!1)}})}if(o.length){if(a(this))return;MCF.ajax({function:"open-ai-embeddings-delete",sources_to_delete:o},s=>{st.openAI.init(),"all"==o?te.find('.mc-nav [data-value="info"]').click():"all-conversations"==o&&te.find('.mc-nav [data-value="conversations"]').click(),!0===s?t("Training data deleted."):i(s),e(this).mcLoading(!1)})}}),!1)}),e(te).on("click",'.mc-nav [data-value="conversations"]',function(){let e=te.find("#mc-chatbot-conversations");a(e)||MCF.ajax({function:"open-ai-get-conversation-embeddings"},t=>{if(t.length){let s="";Ve=t;for(var i=0;i<t.length;i++)s+=`<div class="repeater-item" data-value="${t[i].id}" data-index=${i}><div><label>${o("Question")}</label><input data-id="q" type="text" value="${t[i].question}" /></div><div class="mc-qea-repeater-answer"><label>${o("Answer")}</label><textarea data-id="a">${t[i].answer}</textarea></div><i class="mc-icon-close"></i></div>`;e.find(".mc-repeater").html(s)}else e.html(`<p class="mc-no-results">${o("No conversations found.")}</p>`);e.mcLoading(!1)})}),e(te).on("click",'.mc-nav [data-value="info"]',function(){let e=te.find("#mc-chatbot-info");a(e)||MCF.ajax({function:"open-ai-get-information"},t=>{let i=[["files","Files"],["website","Website URLs"],["qea","Q&A"],["flows","Flows"],["articles","Articles"],["conversations","Conversations"]],s=`<h2>${o("Sources")}</h2><p>`;for(var a=0;a<i.length;a++)s+=t[i[a][0]]?`${t[i[a][0]][1]} ${o(i[a][1])} (${t[i[a][0]][0]} ${o("chars")})<br>`:"";s+=`</p><h2>${o("Total detected characters")}</h2><p>${t.total} ${o("chars")+(t.limit?" / "+t.limit+" "+o("limit"):"")}</p><hr><div id="mc-chatbot-delete-all-training" class="mc-btn mc-btn-white">${o("Delete all training data")}</div>`,e.html(s),e.mcLoading(!1)})}),e(te).on("click",".mc-menu-chatbot [data-type]",function(t){let i=e(this).data("type");switch(i){case"flows":case"training":case"playground":let e=te.find(`> [data-id="${i}"]`);te.find("> [data-id]").mcActive(!1),e.mcActive(!0),"flows"==i&&e.mcLoading()&&MCF.ajax({function:"open-ai-flows-get"},t=>{for(s=0;s<t.length;s++)t[s]&&t[s].steps&&t[s].name&&st.openAI.flows.flows.push(t[s]);let i="";for(var s=0;s<t.length;s++)t[s]&&(i+=st.openAI.flows.navCode(t[s].name));re.html(i),re.find("li:first-child").click(),e.mcLoading(!1)});break;case"settings":return at.open("dialogflow",!0),t.preventDefault,!1}}),e(ae).on("click",".mc-enlarger-function-calling",function(){e(this).parent().parent().find(".mc-qea-repeater-answer").addClass("mc-hide")}),e(ae).on("change",'[data-id="open-ai-faq-set-data"] select',function(){e(this).parent().next().find("input").setClass("mc-hide",["transcript","transcript_email","human_takeover","archive_conversation"].includes(e(this).val()))}),e(ae).on("input click",'[data-id="open-ai-faq-answer"]',function(){e(this).prev().find("i").mcActive(e(this).val().length>2&&e(this).val().indexOf(" "))}),e(ae).on("click",".mc-qea-repeater-answer > label > i",function(){let t=e(this).closest(".repeater-item").find('[data-id="open-ai-faq-answer"]');a(this)||st.openAI.rewrite(t.val(),i=>{e(this).mcLoading(!1),i[0]&&t.val(i[1])})}),e(ne).on("click",'[data-value="add"], [data-value="send"]',function(){let t=ne.find("textarea"),s=t.val().trim();if(t.val(""),s&&st.openAI.playground.addMessage(s,ne.find('[data-value="user"], [data-value="assistant"]').attr("data-value")),"send"==e(this).data("value")){let t=st.openAI.playground.messages.length;t&&!a(this)&&MCF.ajax({function:"open-ai-playground-message",messages:st.openAI.playground.messages},s=>{if(s[0]){if(s[1]&&(st.openAI.playground.addMessage(s[1],"assistant",s[6]),s[4])){let e="";for(var a in s[4].usage)["string","number"].includes(typeof s[4].usage[a])&&(e+=`<b>${MCF.slugToString(a)}</b>: ${s[4].usage[a]}<br>`);st.openAI.playground.last_response=s[4],te.find(".mc-playground-info").html(e+`<div id="mc-playground-query" class="mc-btn-text">${o("View code")}</div>${s[4].embeddings?`<div id="mc-playground-embeddings" class="mc-btn-text">${o("Embeddings")}</div>`:""}`),s[4].payload&&st.openAI.playground.messages[t-1].push(s[4].payload)}s[8]&&C.find(`[data-conversation-id="${s[8]}"]`).remove()}else i(s),console.error(s);e(this).mcLoading(!1)})}}),e(te).on("click","#mc-playground-query",function(){i("<pre>"+JSON.stringify(st.openAI.playground.last_response.query,null,4).replaceAll('\\"','"')+"</pre>","info",!1,"mc-playground-query-panel",!1,!0)}),e(te).on("click","#mc-playground-embeddings",function(){let e="",t=st.openAI.playground.last_response.embeddings;for(var s=t.length-1;s>-1;s--)e+=`<span><b>${o("Source")}</b>: ${t[s].source?t[s].source.autoLink({target:"_blank"}):""}<br><b>${o("Score")}</b>: ${t[s].score}<br><span>${t[s].text}</span></span>`;i(e,"info",!1,"mc-playground-embeddings-panel",!1,!0)}),e(ne).on("click",'[data-value="clear"]',function(){st.openAI.playground.messages=[],oe.html(""),te.find(".mc-playground-info").html("")}),e(oe).on("click",".mc-icon-close",function(){let t=e(this).closest("[data-type]");st.openAI.playground.messages.splice(t.index(),1),t.remove()}),e(oe).on("click",".mc-rich-chips .mc-btn",function(){ne.find("textarea").val(e(this).html()),ne.find('[data-value="send"]').click()}),e(ne).on("click",'[data-value="user"], [data-value="assistant"]',function(){let t="user"==e(this).attr("data-value");e(this).attr("data-value",t?"assistant":"user").html('<i class="mc-icon-reload"></i> '+o(t?"Assistant":"User"))}),e(k).on("click","#open-ai-troubleshoot a, #google-troubleshoot a",function(s){let n=e(this).parent().attr("id");return s.preventDefault(),!("google-troubleshoot"!=n&&![!0,"mode"].includes(st.openAI.troubleshoot()))&&(a(this)?void 0:(MCF.ajax({function:e(this).parent().attr("id")},s=>{!0===s?t("Success. No issues found."):i(s),e(this).mcLoading(!1),e(A).find(".mc-admin-list .mc-select li.mc-active").click()}),!1))}),e(X).on("click",".ce-settings__button--delete.ce-settings__button--confirm",function(){let e=X.find(".image-tool--filled img").attr("src");e&&MCF.ajax({function:"delete-file",path:e})}),e(X).on("click",".ul-articles li",function(t){i("The changes will be lost.","alert",()=>{nt.show(e(this).attr("data-id")),X.find(".mc-scroll-area:not(.mc-nav)").scrollTop(0)},!1,!1,!1,!Pe,()=>{e(this).parent().find("li").mcActive(!1),e(this).parent().find(`[data-id="${nt.activeID()}"]`).mcActive(!0)})}),e(X).on("click",".ul-categories li",function(t){nt.categories.show(e(this).attr("data-id"))}),e(X).on("click",".mc-add-article",function(){nt.add()}),e(X).on("click",".mc-add-category",function(){nt.categories.add()}),e(X).on("click",".mc-nav i",function(t){let s=e(this).parent(),n=s.closest("ul"),o=n.hasClass("ul-categories");return i(`The ${o?"category":"article"} will be deleted permanently.`,"alert",()=>{let t=s.attr("data-id");if(o)nt.categories.delete(t);else{if(X.find("#editorjs .image-tool__image-picture").each(function(){MCF.ajax({function:"delete-file",path:e(this).attr("src")})}),!t)return s.remove();a(Q),nt.delete(t,e=>{Q.mcLoading(!1),p(),n.find("li").length>1&&setTimeout(()=>{s.prev().length?s.prev().click():s.next().click(),s.remove()},300)})}}),t.preventDefault(),!1}),e(X).on("click",".mc-menu-wide li",function(){let t=e(this).data("type");"settings"==t?at.open("articles",!0):"reports"==t?ot.open("articles-searches"):(X.attr("data-type",t),nt.categories.update())}),e(X).on("click",".mc-save-articles",function(){a(this)||("categories"==X.attr("data-type")?nt.categories.save(t=>{e(this).mcLoading(!1)}):nt.save(t=>{e(this).mcLoading(!1)}))}),e(X).on("change input","input, textarea, select",function(){Pe=!0}),e(K).on("change",function(){ee.val()||(t("Select a parent category first.","error"),e(this).val(""))}),e(Te).on("click",".mc-nav [id]",function(){let t=e(this).attr("id");ot.active_report=!1,Te.find("#mc-date-picker").val(""),Te.attr("class","mc-area-reports mc-active mc-report-"+t),ot.initReport(e(this).attr("id")),MCF.getURL("report")!=t&&f("?report="+t)}),e(Te).on("change","#mc-date-picker",function(){ot.initReport(!1,e(this).val())}),e(Te).on("click",".mc-report-export",function(){e(this).mcLoading()||ot.export(t=>{e(this).mcLoading(!1),t&&_(t,"mc-export-report-close","Report exported")})}),MCF.getURL("report")&&(Te.mcActive()||x.find(".mc-admin-nav #mc-reports").click(),setTimeout(()=>{Te.find("#"+MCF.getURL("report")).click()},500)),e(A).on("click",".mc-panel-woocommerce > i",function(){st.woocommerce.conversationPanel()}),e(A).on("click",".mc-woocommerce-orders > div > span",function(t){let i=e(this).parent();e(t.target).is("span")&&(i.mcActive()||st.woocommerce.conversationPanelOrder(i.attr("data-id")))}),e(A).on("click",".mc-btn-woocommerce",function(){(Le.mcLoading()||0!=s()&&s().language!=st.itemsPanel.panel_language)&&st.itemsPanel.populate("woocommerce"),Ee.find(".mc-search-btn").mcActive(!0).find("input").get(0).focus(),Ee.mcTogglePopup(this)}),e(Ee).find(".mc-woocommerce-products-list").on("scroll",function(){d(this,!0)&&st.itemsPanel.pagination(this,"woocommerce")}),e(Ee).on("click",".mc-select li",function(){st.itemsPanel.filter(this,"woocommerce")}),e(Ee).on("input",".mc-search-btn input",function(){st.itemsPanel.search(this,"woocommerce")}),e(Ee).on("click",".mc-search-btn i",function(){MCF.searchClear(this,()=>{st.itemsPanel.search(e(this).next(),"woocommerce")})}),e(Ee).on("click",".mc-woocommerce-products-list li",function(){let t=Ee.attr("data-action"),i=e(this).data("id");MCF.null(t)?MCChat.insertText(`{product_card id="${i}"}`):(Le.mcLoading(!0),A.find(".mc-add-cart-btn").mcLoading(!0),MCChat.sendMessage(-1,"",[],e=>{e&&(st.woocommerce.conversationPanelUpdate(i),k.mcHideLightbox())},{event:"woocommerce-update-cart",action:"cart-add",id:i})),MCF.deactivateAll(),k.removeClass("mc-popup-active")}),e(A).on("click",".mc-panel-woocommerce .mc-add-cart-btn",function(){e(this).mcLoading()||(MCChat.user_online?(st.itemsPanel.populate("woocommerce"),Ee.mcShowLightbox(!0,"cart-add")):i("The user is offline. Only the carts of online users can be updated."))}),e(A).on("click",".mc-panel-woocommerce .mc-list-items > a > i",function(t){let i=e(this).parent().attr("data-id");return MCChat.sendMessage(-1,"",[],()=>{st.woocommerce.conversationPanelUpdate(i,"removed")},{event:"woocommerce-update-cart",action:"cart-remove",id:i}),e(this).mcLoading(!0),t.preventDefault(),!1}),e(A).on("click",".mc-panel-ump > i",function(){st.ump.conversationPanel()}),e(A).on("click",".mc-panel-armember > i",function(){st.armember.conversationPanel()}),e(A).on("click",".mc-panel-opencart > i",function(){st.opencart.conversationPanel()}),e(A).on("click",".mc-opencart-orders > a",function(){st.opencart.openOrder(e(this).attr("data-id"))}),e(H).on("click","#opencart-sync a",function(t){t.preventDefault(),a(this)||MCF.ajax({function:"opencart-sync"},t=>{e(this).mcLoading(!1),i(!0===t?"Users successfully imported.":t)})}),e(H).on("click","#perfex-sync a, #whmcs-sync a, #perfex-articles-sync a, #whmcs-articles-sync a, #aecommerce-sync a, #aecommerce-sync-admins a, #aecommerce-sync-sellers a, #martfury-sync a, #martfury-sync-sellers a",function(t){if(a(this))return;let s=e(this).closest("[id]").attr("id"),n=s.indexOf("article")>0;MCF.ajax({function:s},t=>{!0===t?(n||rt.update(),i(n?"Articles successfully imported.":"Users successfully imported.")):i("Error. Response: "+JSON.stringify(t)),e(this).mcLoading(!1)}),t.preventDefault()}),e(A).on("click","#mc-zendesk-btn",function(t){a(this)||(MCF.ajax({function:"zendesk-create-ticket",conversation_id:MCChat.conversation.id},t=>{!0===t?st.zendesk.conversationPanel():i("Error. Response: "+JSON.stringify(t)),e(this).mcLoading(!1)}),t.preventDefault())}),e(A).on("click","#mc-zendesk-update-ticket",function(t){if(!a(this))return MCF.ajax({function:"zendesk-update-ticket",conversation_id:MCChat.conversation.id,zendesk_ticket_id:e(this).closest("[data-id]").attr("data-id")},()=>{e(this).mcLoading(!1)}),t.preventDefault(),!1}),e(k).on("click",".mc-enlarger",function(){e(this).mcActive(!0)}),e(k).on("mouseenter","[data-mc-tooltip-init]",function(){e(this).parent().mcInitTooltips(),e(this).removeAttr("data-mc-tooltip-init"),e(this).trigger("mouseenter")}),e(k).on("click",".mc-language-switcher > i",function(){let t=e(this).parent(),i=t.find("[data-language]").map(function(){return e(this).attr("data-language")}).get(),s="";i.push("en");for(var a in MC_LANGUAGE_CODES)i.includes(a)||(s+=`<div data-language="${a}"><img src="${MC_URL}/media/flags/${a}.png" />${o(MC_LANGUAGE_CODES[a])}</div>`);fe=t,dt.genericPanel("languages","Choose a language",s,[],' data-source="'+t.attr("data-source")+'"',!0)}),e(k).on("click",".mc-language-switcher img",function(){let t=e(this).parent(),s=t.mcActive(),a=!s&&t.attr("data-language");switch(t.parent().attr("data-source")){case"article-categories":nt.categories.show(nt.categories.activeID(),a);break;case"articles":let e=Q.find(".mc-language-switcher .mc-active");i("The changes will be lost.","alert",()=>{let e=t.attr("data-id");e||s?nt.show(e&&!s?e:nt.activeID(!0)):nt.clear()},!1,!1,!1,!Pe,()=>{t.mcActive(!1),e.mcActive(!0)});break;case"automations":at.automations.show(!1,a);break;case"settings":let n=t.parent().find("[data-language].mc-active");at.translations.save(t,s?t.attr("data-language"):!!n.length&&n.attr("data-language")),at.translations.activate(t,a)}t.siblings().mcActive(!1),t.mcActive(!s)}),e(k).on("click",".mc-language-switcher span > i",function(){let t=e(this).parent(),s=t.attr("data-language");i(o("The {T} translation will be deleted.").replace("{T}",o(MC_LANGUAGE_CODES[s])),"alert",()=>{switch(t.parent().attr("data-source")){case"article-categories":nt.categories.translations.delete(s);break;case"articles":nt.translations.delete(s);break;case"automations":at.automations.deleteTranslation(!1,s),at.automations.show();break;case"settings":at.translations.delete(t,s)}t.remove()})}),e(k).on("click",".mc-languages-box [data-language]",function(){let t=e(this).parents().eq(1),s=e(this).attr("data-language"),a=!0;switch(t.attr("data-source")){case"article-categories":nt.categories.translations.add(s);break;case"articles":i("The changes will be lost.","alert",()=>{nt.translations.add(s),k.mcHideLightbox()},!1,!1,!1,!Pe),a=!1;break;case"automations":at.automations.addTranslation(!1,!1,s),at.automations.show(!1,s);break;case"settings":at.translations.add(s)}a&&k.mcHideLightbox()}),e(k).on("click",".mc-lightbox .mc-top-bar .mc-close",function(){k.mcHideLightbox()}),e(k).on("click",".mc-lightbox .mc-info",function(){e(this).mcActive(!1)}),e(k).on("click",".mc-dialog-box a",function(){let t=e(this).closest(".mc-lightbox");e(this).hasClass("mc-confirm")&&be(),e(this).hasClass("mc-cancel")&&ve&&ve(),1==k.find(".mc-lightbox.mc-active").length&&we.mcActive(!1),dt.open_popup=!1,t.mcActive(!1)}),e(k).on("click",".mc-menu-wide li, .mc-nav li",function(){e(this).siblings().mcActive(!1),e(this).mcActive(!0)}),e(k).on("click",".mc-nav:not(.mc-nav-only) li:not(.mc-tab-nav-title)",function(){let t=e(this).closest(".mc-tab"),i=e(t).find(" > .mc-content > div").mcActive(!1).eq(e(this).parent().find("li:not(.mc-tab-nav-title)").index(this));i.mcActive(!0),i.find("textarea").each(function(){e(this).autoExpandTextarea(),e(this).manualExpandTextarea()}),t.find(".mc-scroll-area:not(.mc-nav)").scrollTop(0)}),e(k).mcInitTooltips(),e(k).on("click",'[data-button="toggle"]',function(){let t=k.find("."+e(this).data("show")),i=k.find("."+e(this).data("hide"));t.addClass("mc-show-animation").show(),i.hide(),dt.open_popup=!(!t.hasClass("mc-lightbox")&&!t.hasClass("mc-popup"))&&t}),e(k).on("click",".mc-info-card",function(){e(this).mcActive(!1)}),e(de).on("change",function(){pe?(pe(),pe=!1):(e(ue).mcLoading(e(this).prop("files").length),e(this).mcUploadFiles(t=>{if(e(ue).mcLoading(!1),"success"==(t=JSON.parse(t))[0]){"upload-image"==e(ue).closest("[data-type]").data("type")&&(e(ue).attr("data-value")&&MCF.ajax({function:"delete-file",path:e(ue).attr("data-value")}),e(ue).attr("data-value",t[1]).css("background-image",`url("${t[1]}")`)),he&&he(t[1])}else console.log(t[1])}))}),e(k).on("click",".mc-accordion > div > span",function(t){let i=e(this).parent(),s=e(i).mcActive();e(t.target).is("span")&&(i.siblings().mcActive(!1),i.mcActive(!s))}),e(k).on("mousedown",function(t){if(e(dt.open_popup).length){let i=e(dt.open_popup);i.is(t.target)||0!==i.has(t.target).length||["mc-btn-saved-replies","mc-btn-emoji","mc-btn-woocommerce","mc-btn-shopify","mc-btn-open-ai"].includes(e(t.target).attr("class"))||(i.hasClass("mc-popup")?i.mcTogglePopup():i.hasClass("mc-select")?i.find("ul").mcActive(!1):i.hasClass("mc-menu-mobile")?i.find("i").mcActive(!1):i.hasClass("mc-menu")?i.mcActive(!1):dt.open_popup&&["mc-embeddings-box"].includes(dt.open_popup.attr("id"))||k.mcHideLightbox(),dt.open_popup=!1)}})})}(jQuery),function(e,t){"object"==typeof exports?module.exports=t(e):"function"==typeof define&&define.amd?define("colors",[],function(){return t(e)}):e.Colors=t(e)}(this,function(e,t){function i(e,i,a,n,o){if("string"==typeof i){a=(i=m.txt2color(i)).type,p[a]=i[a],o=o!==t?o:i.alpha}else if(i)for(var c in i)e[a][c]=r(i[c]/l[a][c][1],0,1);return o!==t&&(e.alpha=r(+o,0,1)),s(a,n?e:t)}function s(e,t){var i,s,r,h=t||p,f=m,g=u.options,b=l,v=h.RND,_="",S="",w={hsl:"hsv",rgb:e},y=v.rgb;if("alpha"!==e){for(var k in b)if(!b[k][k]){e!==k&&(S=w[k]||"rgb",h[k]=f[S+"2"+k](h[S])),v[k]||(v[k]={}),i=h[k];for(_ in i)v[k][_]=d(i[_]*b[k][_][1])}y=v.rgb,h.HEX=f.RGB2HEX(y),h.equivalentGrey=g.grey.r*h.rgb.r+g.grey.g*h.rgb.g+g.grey.b*h.rgb.b,h.webSave=s=a(y,51),h.webSmart=r=a(y,17),h.saveColor=y.r===s.r&&y.g===s.g&&y.b===s.b?"web save":y.r===r.r&&y.g===r.g&&y.b===r.b?"web smart":"",h.hueRGB=m.hue2RGB(h.hsv.h),t&&(h.background=function(e,t,i){var s=u.options.grey,a={};return a.RGB={r:e.r,g:e.g,b:e.b},a.rgb={r:t.r,g:t.g,b:t.b},a.alpha=i,a.equivalentGrey=d(s.r*e.r+s.g*e.g+s.b*e.b),a.rgbaMixBlack=o(t,{r:0,g:0,b:0},i,1),a.rgbaMixWhite=o(t,{r:1,g:1,b:1},i,1),a.rgbaMixBlack.luminance=n(a.rgbaMixBlack,!0),a.rgbaMixWhite.luminance=n(a.rgbaMixWhite,!0),u.options.customBG&&(a.rgbaMixCustom=o(t,u.options.customBG,i,1),a.rgbaMixCustom.luminance=n(a.rgbaMixCustom,!0),u.options.customBG.luminance=n(u.options.customBG,!0)),a}(y,h.rgb,h.alpha))}var x,B,A,T=h.rgb,I=h.alpha,$="luminance",C=h.background;return x=o(T,{r:0,g:0,b:0},I,1),x[$]=n(x,!0),h.rgbaMixBlack=x,B=o(T,{r:1,g:1,b:1},I,1),B[$]=n(B,!0),h.rgbaMixWhite=B,g.customBG&&(A=o(T,C.rgbaMixCustom,I,1),A[$]=n(A,!0),A.WCAG2Ratio=function(e,t){var i=1;return i=e>=t?(e+.05)/(t+.05):(t+.05)/(e+.05),d(100*i)/100}(A[$],C.rgbaMixCustom[$]),h.rgbaMixBGMixCustom=A,A.luminanceDelta=c.abs(A[$]-C.rgbaMixCustom[$]),A.hueDelta=function(e,t,i){return(c.max(e.r-t.r,t.r-e.r)+c.max(e.g-t.g,t.g-e.g)+c.max(e.b-t.b,t.b-e.b))*(i?255:1)/765}(C.rgbaMixCustom,A,!0)),h.RGBLuminance=n(y),h.HUELuminance=n(h.hueRGB),g.convertCallback&&g.convertCallback(h,e),h}function a(e,t){var i={},s=0,a=t/2;for(var n in e)s=e[n]%t,i[n]=e[n]+(s>a?t-s:-s);return i}function n(e,t){for(var i=t?1:255,s=[e.r/i,e.g/i,e.b/i],a=u.options.luminance,n=s.length;n--;)s[n]=s[n]<=.03928?s[n]/12.92:c.pow((s[n]+.055)/1.055,2.4);return a.r*s[0]+a.g*s[1]+a.b*s[2]}function o(e,i,s,a){var n={},o=s!==t?s:1,r=a!==t?a:1,l=o+r*(1-o);for(var c in e)n[c]=(e[c]*o+i[c]*r*(1-o))/l;return n.a=l,n}function r(e,t,i){return e>i?i:t>e?t:e}var l={rgb:{r:[0,255],g:[0,255],b:[0,255]},hsv:{h:[0,360],s:[0,100],v:[0,100]},hsl:{h:[0,360],s:[0,100],l:[0,100]},alpha:{alpha:[0,1]},HEX:{HEX:[0,16777215]}},c=e.Math,d=c.round,u={},p={},h={r:.298954,g:.586434,b:.114612},f={r:.2126,g:.7152,b:.0722},g=function(e){this.colors={RND:{}},this.options={color:"rgba(0,0,0,0)",grey:h,luminance:f,valueRanges:l},b(this,e||{})},b=function(e,s){var a,n=e.options;v(e);for(var o in s)s[o]!==t&&(n[o]=s[o]);a=n.customBG,n.customBG="string"==typeof a?m.txt2color(a).rgb:a,p=i(e.colors,n.color,t,!0)},v=function(e){u!==e&&(u=e,p=e.colors)};g.prototype.setColor=function(e,a,n){return v(this),e?i(this.colors,e,a,t,n):(n!==t&&(this.colors.alpha=r(n,0,1)),s(a))},g.prototype.setCustomBackground=function(e){return v(this),this.options.customBG="string"==typeof e?m.txt2color(e).rgb:e,i(this.colors,t,"rgb")},g.prototype.saveAsBackground=function(){return v(this),i(this.colors,t,"rgb",!0)},g.prototype.toString=function(e,t){return m.color2text((e||"rgb").toLowerCase(),this.colors,t)};var m={txt2color:function(e){var t={},i=e.replace(/(?:#|\)|%)/g,"").split("("),s=(i[1]||"").split(/,\s*/),a=i[1]?i[0].substr(0,3):"rgb",n="";if(t.type=a,t[a]={},i[1])for(var o=3;o--;)n=a[o]||a.charAt(o),t[a][n]=+s[o]/l[a][n][1];else t.rgb=m.HEX2rgb(i[0]);return t.alpha=s[3]?+s[3]:1,t},color2text:function(e,t,i){var s=!1!==i&&d(100*t.alpha)/100,a="number"==typeof s&&!1!==i&&(i||1!==s),n=t.RND.rgb,o=t.RND.hsl,r="hex"===e&&a,l="hex"===e&&!r,c="rgb"===e||r?n.r+", "+n.g+", "+n.b:l?"#"+t.HEX:o.h+", "+o.s+"%, "+o.l+"%";return l?c:(r?"rgb":e)+(a?"a":"")+"("+c+(a?", "+s:"")+")"},RGB2HEX:function(e){return((e.r<16?"0":"")+e.r.toString(16)+(e.g<16?"0":"")+e.g.toString(16)+(e.b<16?"0":"")+e.b.toString(16)).toUpperCase()},HEX2rgb:function(e){return e=e.split(""),{r:+("0x"+e[0]+e[e[3]?1:0])/255,g:+("0x"+e[e[3]?2:1]+(e[3]||e[1]))/255,b:+("0x"+(e[4]||e[2])+(e[5]||e[2]))/255}},hue2RGB:function(e){var t=6*e,i=~~t%6,s=6===t?0:t-i;return{r:d(255*[1,1-s,0,0,s,1][i]),g:d(255*[s,1,1,1-s,0,0][i]),b:d(255*[0,0,s,1,1,1-s][i])}},rgb2hsv:function(e){var t,i,s,a=e.r,n=e.g,o=e.b,r=0;return o>n&&(n=o+(o=n,0),r=-1),i=o,n>a&&(a=n+(n=a,0),r=-2/6-r,i=c.min(n,o)),t=a-i,s=a?t/a:0,{h:1e-15>s?p&&p.hsl&&p.hsl.h||0:t?c.abs(r+(n-o)/(6*t)):0,s:a?t/a:p&&p.hsv&&p.hsv.s||0,v:a}},hsv2rgb:function(e){var t=6*e.h,i=e.s,s=e.v,a=~~t,n=t-a,o=s*(1-i),r=s*(1-n*i),l=s*(1-(1-n)*i),c=a%6;return{r:[s,r,o,o,l,s][c],g:[l,s,s,r,o,o][c],b:[o,o,l,s,s,r][c]}},hsv2hsl:function(e){var t=(2-e.s)*e.v,i=e.s*e.v;return i=e.s?1>t?t?i/t:0:i/(2-t):0,{h:e.h,s:e.v||i?i:p&&p.hsl&&p.hsl.s||0,l:t/2}},rgb2hsl:function(e,t){var i=m.rgb2hsv(e);return m.hsv2hsl(t?i:p.hsv=i)},hsl2rgb:function(e){var t=6*e.h,i=e.s,s=e.l,a=.5>s?s*(1+i):s+i-i*s,n=s+s-a,o=~~t,r=a*(a?(a-n)/a:0)*(t-o),l=n+r,c=a-r,d=o%6;return{r:[a,c,n,n,l,a][d],g:[l,a,a,c,n,n][d],b:[n,n,l,a,a,c][d]}}};return g}),function(e,t){"object"==typeof exports?module.exports=t(e,require("jquery"),require("colors")):"function"==typeof define&&define.amd?define(["jquery","colors"],function(i,s){return t(e,i,s)}):t(e,e.jQuery,e.Colors)}(this,function(e,t,i,s){function a(e){return e.value||e.getAttribute("value")||t(e).css("background-color")||"#FFF"}function n(e){return(e=e.originalEvent&&e.originalEvent.touches?e.originalEvent.touches[0]:e).originalEvent?e.originalEvent:e}function o(e){return t(e.find(f.doRender)[0]||e[0])}function r(i){var s=t(this),n=s.offset(),r=t(e),u=f.gap;i?(g=o(s),g._colorMode=g.data("colorMode"),p.$trigger=s,(b||l()).css(f.positionCallback.call(p,s)||{left:(b._left=n.left)-((b._left+=b._width-(r.scrollLeft()+r.width()))+u>0?b._left+u:0),top:(b._top=n.top+s.outerHeight())-((b._top+=b._height-(r.scrollTop()+r.height()))+u>0?b._top+u:0)}).show(f.animationSpeed,function(){!0!==i&&(w.toggle(!!f.opacity)._width=w.width(),m._width=m.width(),m._height=m.height(),v._height=v.height(),h.setColor(a(g[0])),d(!0))}).off(".tcp").on(A,".cp-xy-slider,.cp-z-slider,.cp-alpha",c)):p.$trigger&&t(b).hide(f.animationSpeed,function(){d(!1),p.$trigger=null}).off(".tcp")}function l(){return t("head")[f.cssPrepend?"prepend":"append"]('<style type="text/css" id="tinyColorPickerStyles">'+(f.css||F)+(f.cssAddon||"")+"</style>"),t(C).css({margin:f.margin}).appendTo("body").show(0,function(){p.$UI=b=t(this),I=f.GPU&&b.css("perspective")!==s,v=t(".cp-z-slider",this),m=t(".cp-xy-slider",this),_=t(".cp-xy-cursor",this),S=t(".cp-z-cursor",this),w=t(".cp-alpha",this),y=t(".cp-alpha-cursor",this),f.buildCallback.call(p,b),b.prepend("<div>").children().eq(0).css("width",b.children().eq(0).width()),b._width=this.offsetWidth,b._height=this.offsetHeight}).hide()}function c(e){var i=this.className.replace(/cp-(.*?)(?:\s*|$)/,"$1").replace("-","_");(e.button||e.which)>1||(e.preventDefault&&e.preventDefault(),e.returnValue=!1,g._offset=t(this).offset(),(i="xy_slider"===i?function(e){var t=n(e),i=t.pageX-g._offset.left,s=t.pageY-g._offset.top;h.setColor({s:i/m._width*100,v:100-s/m._height*100},"hsv")}:"z_slider"===i?function(e){var t=n(e).pageY-g._offset.top;h.setColor({h:360-t/v._height*360},"hsv")}:function(e){var t=(n(e).pageX-g._offset.left)/w._width;h.setColor({},"rgb",t)})(e),d(),k.on(T,function(){k.off(".tcp")}).on(B,function(e){i(e),d()}))}function d(e){var t=h.colors,i=t.hueRGB,a=(t.RND.rgb,t.RND.hsl,f.dark),n=f.light,o=h.toString(g._colorMode,f.forceAlpha),r=t.HUELuminance>.22?a:n,l=t.rgbaMixBlack.luminance>.22?a:n,c=(1-t.hsv.h)*v._height,d=t.hsv.s*m._width,p=(1-t.hsv.v)*m._height,b=t.alpha*w._width,k=I?"translate3d":"",x=g[0].value,B=g[0].hasAttribute("value")&&""===x&&e!==s;m._css={backgroundColor:"rgb("+i.r+","+i.g+","+i.b+")"},_._css={transform:k+"("+d+"px, "+p+"px, 0)",left:I?"":d,top:I?"":p,borderColor:t.RGBLuminance>.22?a:n},S._css={transform:k+"(0, "+c+"px, 0)",top:I?"":c,borderColor:"transparent "+r},w._css={backgroundColor:"#"+t.HEX},y._css={transform:k+"("+b+"px, 0, 0)",left:I?"":b,borderColor:l+" transparent"},g._css={backgroundColor:B?"":o,color:B?"":t.rgbaMixBGMixCustom.luminance>.22?a:n},g.text=B?"":x!==o?o:"",e!==s?u(e):$(u)}function u(e){m.css(m._css),_.css(_._css),S.css(S._css),w.css(w._css),y.css(y._css),f.doRender&&g.css(g._css),g.text&&g.val(g.text),f.renderCallback.call(p,g,"boolean"==typeof e?e:s)}var p,h,f,g,b,v,m,_,S,w,y,k=t(document),x=t(),B="touchmove.tcp mousemove.tcp pointermove.tcp",A="touchstart.tcp mousedown.tcp pointerdown.tcp",T="touchend.tcp mouseup.tcp pointerup.tcp",I=!1,$=e.requestAnimationFrame||e.webkitRequestAnimationFrame||function(e){e()},C='<div class="cp-color-picker"><div class="cp-z-slider"><div class="cp-z-cursor"></div></div><div class="cp-xy-slider"><div class="cp-white"></div><div class="cp-xy-cursor"></div></div><div class="cp-alpha"><div class="cp-alpha-cursor"></div></div></div>',F=".cp-color-picker{position:absolute;overflow:hidden;padding:6px 6px 0;background-color:#444;color:#bbb;font-family:Arial,Helvetica,sans-serif;font-size:12px;font-weight:400;cursor:default;border-radius:5px}.cp-color-picker>div{position:relative;overflow:hidden}.cp-xy-slider{float:left;height:128px;width:128px;margin-bottom:6px;background:linear-gradient(to right,#FFF,rgba(255,255,255,0))}.cp-white{height:100%;width:100%;background:linear-gradient(rgba(0,0,0,0),#000)}.cp-xy-cursor{position:absolute;top:0;width:10px;height:10px;margin:-5px;border:1px solid #fff;border-radius:100%;box-sizing:border-box}.cp-z-slider{float:right;margin-left:6px;height:128px;width:20px;background:linear-gradient(red 0,#f0f 17%,#00f 33%,#0ff 50%,#0f0 67%,#ff0 83%,red 100%)}.cp-z-cursor{position:absolute;margin-top:-4px;width:100%;border:4px solid #fff;border-color:transparent #fff;box-sizing:border-box}.cp-alpha{clear:both;width:100%;height:16px;margin:6px 0;background:linear-gradient(to right,#444,rgba(0,0,0,0))}.cp-alpha-cursor{position:absolute;margin-left:-4px;height:100%;border:4px solid #fff;border-color:#fff transparent;box-sizing:border-box}",N=function(e){h=this.color=new i(e),f=h.options,p=this};N.prototype={render:d,toggle:r},t.fn.colorPicker=function(i){var s=this,n=function(){};return i=t.extend({animationSpeed:150,GPU:!0,doRender:!0,customBG:"#FFF",opacity:!0,renderCallback:n,buildCallback:n,positionCallback:n,body:document.body,scrollResize:!0,gap:4,dark:"#222",light:"#DDD"},i),!p&&i.scrollResize&&t(e).on("resize.tcp scroll.tcp",function(){p.$trigger&&p.toggle.call(p.$trigger[0],!0)}),x=x.add(this),this.colorPicker=p||new N(i),this.options=i,t(i.body).off(".tcp").on(A,function(e){-1===x.add(b).add(t(b).find(e.target)).index(e.target)&&r()}),this.on("focusin.tcp click.tcp",function(e){p.color.options=t.extend(p.color.options,f=s.options),r.call(this,e)}).on("change.tcp",function(){h.setColor(this.value||"#FFF"),s.colorPicker.render(!0)}).each(function(){var e=a(this),s=e.split("("),n=o(t(this));n.data("colorMode",s[1]?s[0].substr(0,3):"HEX").attr("readonly",f.preventFocus),i.doRender&&n.css({"background-color":e,color:function(){return h.setColor(e).rgbaMixBGMixCustom.luminance>.22?i.dark:i.light}})})},t.fn.colorPicker.destroy=function(){t("*").off(".tcp"),p.toggle(!1),x=t()}});