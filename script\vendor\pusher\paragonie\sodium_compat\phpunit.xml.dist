<?xml version="1.0" encoding="UTF-8"?>
<phpunit
    backupGlobals="true"
    backupStaticAttributes="false"
    bootstrap="autoload-phpunit.php"
    colors="true"
    convertErrorsToExceptions="true"
    convertNoticesToExceptions="true"
    convertWarningsToExceptions="true"
    processIsolation="false"
    stopOnError="false"
    stopOnFailure="false"
>
    <testsuites>
        <testsuite name="Unit Tests">
            <directory suffix="Test.php">./tests/unit</directory>
        </testsuite>
        <testsuite name="Libsodium Compatibility Tests">
            <directory suffix="Test.php">./tests/compat</directory>
        </testsuite>
    </testsuites>
    <filter>
        <whitelist processUncoveredFilesFromWhitelist="true">
            <directory suffix=".php">./src</directory>
        </whitelist>
    </filter>
</phpunit>