<?php

/*
 * ==========================================================
 * INITIAL CONFIGURATION FILE
 * ==========================================================
 *
 * Insert here the information for the database connection and for other core settings.
 *
 */

define('SUPER_EMAIL', '<EMAIL>');
define('SUPER_PASSWORD', '$2y$10$lBtP6tnTI1L5Bu9zo7NAzOpB08CbNIGqH.ZgibI2.K6.K2sJg2qi2');

// Super admin email for error notifications
define('MC_SUPER_ADMIN_EMAIL', '<EMAIL>');

define('CLOUD_TWILIO_SID', '');
define('CLOUD_TWILIO_TOKEN', '');
define('CLOUD_TWILIO_SENDER', '');

define('CLOUD_DB_NAME', 'crm');
define('CLOUD_DB_USER', 'super_user_db');
define('CLOUD_DB_PASSWORD', '12345678');
define('CLOUD_DB_HOST', 'localhost');
define('CLOUD_URL', 'https://crm.masichat.com');


define('MC_CLOUD', true);
define('MC_CLOUD_KEY', 'Ml1t@S0l');
define('MC_CLOUD_PATH', '/home/<USER>/web/crm.masichat.com/public_html');
define('MC_CLOUD_BRAND_LOGO', 'https://crm.masichat.com/custom/logo cropped.png');
define('MC_CLOUD_BRAND_LOGO_LINK', 'https://crm.masichat.com/custom/logo cropped.png');
define('MC_CLOUD_BRAND_ICON', 'https://crm.masichat.com/custom/logo fav.png');
define('MC_CLOUD_BRAND_ICON_PNG', 'https://crm.masichat.com/custom/logo fav.png');
define('MC_CLOUD_BRAND_NAME', 'MC Masi Chat');
define('MC_CLOUD_MANIFEST_URL', 'https://crm.masichat.com/manifest.json');
define('MC_CLOUD_MEMBERSHIP_TYPE', 'messages-agents');

define('CLOUD_SMTP_HOST', 'mail.masichat.com');
define('CLOUD_SMTP_USERNAME', '<EMAIL>');
define('CLOUD_SMTP_PASSWORD', 'Ml1t@S0l');
define('CLOUD_SMTP_PORT', '587');
define('CLOUD_SMTP_SENDER', '<EMAIL>');
define('CLOUD_SMTP_SENDER_NAME', 'Masi Chat');

define('CLOUD_PUSHER_ID', *******);
define('CLOUD_PUSHER_KEY', '3539679ce2281079a5ea');
define('CLOUD_PUSHER_SECRET', '8002993f1bd41b93d30e');
define('CLOUD_PUSHER_CLUSTER', 'ap2');

define('ONESIGNAL_APP_ID', '************************************');
define('ONESIGNAL_API_KEY', 'yy6l6mnvvutqul4k5xmjrh6ly');

define('PAYMENT_PROVIDER', 'paystack'); // allowed values: rapyd, verifone, yoomoney, razorpay, manual
define('PAYSTACK_SECRET_KEY', 'sk_test_76d397a967886b9df14eb2dd86f8aedeb6d6a2ca');
define('PAYSTACK_PUBLIC_KEY', 'pk_test_ff3c35e48c089bc58a3861d92e0a3e43cd893802');
define('PAYSTACK_CURRENCY', 'zar');
define('PAYSTACK_PLANS_ENDPOINT', 'https://api.paystack.co/plan');
define('PAYSTACK_API_BASE', 'https://api.paystack.co');
define('PAYSTACK_REGION', 'ZA');
define('ENABLE_DEBUG_LOGGING', true);
define('LOG_FILE_PATH', __DIR__.'/../account/debug.log');
define('PAYSTACK_PRODUCT_ID_WHITE_LABEL', 'PLN_narwo53sk2niyqc');  // Uncomment with actual plan ID

//define('ENVATO_PURCHASE_CODE', 'e775655f-0725-4a41-a0fc-06fd390d4928');
define('OPEN_EXCHANGE_RATE_APP_ID', '4d5a35d4c80943c69139f48b6c8d9640');

define('GOOGLE_CLIENT_ID', '************-fmuojlgrgv7ei7akq7qsgvmdh7o75kgf.apps.googleusercontent.com');
define('GOOGLE_CLIENT_SECRET', '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');

define('WHATSAPP_APP_ID', '***************');
define('WHATSAPP_APP_SECRET', '********************************');
define('WHATSAPP_CONFIGURATION_ID', '****************');
define('WHATSAPP_APP_TOKEN', '***************|0u178sFE0MCWEgGZGYmLJ_4gZc8');
define('WHATSAPP_VERIFY_TOKEN', '********************************');

define('MESSENGER_APP_ID', '****************');
define('MESSENGER_APP_SECRET', '********************************');
define('MESSENGER_CONFIGURATION_ID', '****************');
define('MESSENGER_VERIFY_TOKEN', '********************************');
define('MESSENGER_APP_TOKEN', '****************|mqa24mJfR_u79VI49KIAro9O3Sc');

define('OPEN_AI_KEY', '**************************************************************************************************************************************************');

//define('RAZORPAY_KEY_ID', '');
//define('RAZORPAY_KEY_SECRET', '');
//define('RAZORPAY_CURRENCY', '');

//define('RAPYD_ACCESS_KEY', '');
//define('RAPYD_SECRET_KEY', '');
//define('RAPYD_TEST_MODE', );
//define('RAPYD_CURRENCY', '');
//define('RAPYD_COUNTRY', '');

//define('VERIFONE_SECRET_WORD', '');
//define('VERIFONE_SECRET_KEY', '');
//define('VERIFONE_MERCHANT_ID', '');
//define('VERIFONE_CURRENCY', '');

//define('YOOMONEY_SHOP_ID', '');
//define('YOOMONEY_KEY_SECRET', '');
//define('YOOMONEY_CURRENCY', '');

//define('PAYMENT_MANUAL_LINK', '');
//define('PAYMENT_MANUAL_CURRENCY', 'ZAR');

//define('SHOPIFY_CLIENT_ID', '');
//define('SHOPIFY_CLIENT_SECRET', '');
//define('SHOPIFY_APP_ID', '');
//define('SHOPIFY_PLANS', [['100 messages', ''], ['5000 messages', 'MC_PLAN_ID'], ['50000 messages', 'MC_PLAN_ID'], ['100000 messages', 'MC_PLAN_ID']]);

//define('MC_CLOUD_AWS_S3', ['amazon-s3-access-key' => '', 'amazon-s3-secret-access-key' => '', 'amazon-s3-bucket-name' => '', 'amazon-s3-backup-bucket-name' => '', 'amazon-s3-region' => '']);
define('MC_CLOUD_DOCS', 'https://masichat.com/knowledge-base/');
//define('STRIPE_PRODUCT_ID_WHITE_LABEL', '');
define('CLOUD_IP', '');
//define('MC_CLOUD_DEFAULT_LANGUAGE_CODE', 'zh');
//define('MC_CLOUD_DEFAULT_RTL', true);
define('DIRECT_CHAT_URL', 'https://chat.app.masichat.com');
define('WEBSITE_URL', 'https://app.masichat.com');
define('ARTICLES_URL', 'https://articles.app.masichat.com');
define('SUPER_BRANDING', true);
//define('MC_CLOUD_EMAIL_BODY_AGENTS', '<link href="https://fonts.googleapis.com/css?family=Roboto:400,500,900" rel="stylesheet" type="text/css"><div style="font-family:\'Roboto\',sans-serif;text-align:left;max-width:560px;margin:auto;"><div style="display:none>{message}</div><a href="https://masichat.com"><img style="width:200px;" src="https://masichat.com/media/logo.png" alt="logo"></a></div><div style="background:#FFF;padding:30px;border-radius:6px;border:1px solid rgb(218, 222, 223);margin:30px auto;max-width:500px"><p style="font-family:\'Roboto\',sans-serif;text-align:left;letter-spacing:.3px;font-size:15px;line-height:28px;color:#486d85;margin:0;">{message}{attachments}<table style="margin-top:30px;"><tr><td><img style="width:35px;border-radius:50%" src="{sender_profile_image}"></td><td><b style="font-size:13px;color:rgb(128,128,128);padding-left:5px;">{sender_name}</b></td></tr></table><a href="{conversation_link}" style="font-family:\'Roboto\',sans-serif;background-color: #009bfc;color: #FFF;font-size: 14px;line-height: 36px;letter-spacing: 0.3px;font-weight: 500;border-radius: 6px;text-decoration: none;height: 35px;display: inline-block;padding: 0 25px;margin-top: 30px;">Click here to reply</a></p></div><div style="color:#444444;font-size:12px;line-height:20px;padding:0;text-align:left;"><p style="font-family:\'Roboto\',sans-serif;font-size:12px; line-height:20px;color:#a0abb2;max-width:560px;margin: 30px auto">This email was sent to you by Masi Chat. By using our services, you agree to our <a href="https://masichat.com/privacy" target="_blank" style="color:#a0abb2;text-decoration:none;">Privacy Policy</a>.<br />&copy; Schiocco LTD. All rights reserved.</p></div></div>');
//define('MC_CLOUD_EMAIL_BODY_USERS', '<link href="https://fonts.googleapis.com/css?family=Roboto:400,500,900" rel="stylesheet" type="text/css"><div style="font-family:\'Roboto\',sans-serif;text-align:left;max-width:560px;margin:auto;"><div style="display:none>{message}</div><a href="https://masichat.com"><img style="width:200px;" src="https://masichat.com/media/logo.png" alt="logo"></a></div><div style="background:#FFF;padding:30px;border-radius:6px;border:1px solid rgb(218, 222, 223);margin:30px auto;max-width:500px"><p style="font-family:\'Roboto\',sans-serif;text-align:left;letter-spacing:.3px;font-size:15px;line-height:28px;color:#486d85;margin:0;">{message}{attachments}<table style="margin-top:30px;"><tr><td><img style="width:35px;border-radius:50%" src="{sender_profile_image}"></td><td><b style="font-size:13px;color:rgb(128,128,128);padding-left:5px;">{sender_name}</b> from Masi Chat</td></tr></table><a href="{conversation_link}" style="font-family:\'Roboto\',sans-serif;background-color: #009bfc;color: #FFF;font-size: 14px;line-height: 36px;letter-spacing: 0.3px;font-weight: 500;border-radius: 6px;text-decoration: none;height: 35px;display: inline-block;padding: 0 25px;margin-top: 30px;">Click here to reply</a></p></div><div style="color:#444444;font-size:12px;line-height:20px;padding:0;text-align:left;"><p style="font-family:\'Roboto\',sans-serif;font-size:12px; line-height:20px;color:#a0abb2;max-width:560px;margin: 30px auto">This email was sent to you by Masi Chat. By using our services, you agree to our <a href="https://masichat.com/privacy" target="_blank" style="color:#a0abb2;text-decoration:none;">Privacy Policy</a>.<br />&copy; Schiocco LTD. All rights reserved.</p></div></div>');
//define('CLOUD_ADDONS', [['title' => 'AAAA', 'description' => 'AAA', 'price' => 5.00], ['title' => 'BBBB', 'description' => 'AAA', 'price' => 9.15]]);



?>